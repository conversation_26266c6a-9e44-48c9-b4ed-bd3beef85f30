<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="CI-CD suite" verbose="5">
	<listeners>
		<listener
			class-name="com.utilities.testng.AnnotationTransformer"></listener>
		<listener class-name="com.utilities.testng.ExtentReporterNG"></listener>
	</listeners>
	<test name="Juno Regression">
		
		
	<packages>
      <package name="org.lenskart.test.customer"/>
    </packages>
    
   <classes>
			<!-- <class name="org.lenskart.test.sanity.JunoProdSanity" /> -->
			 <class name="org.lenskart.test.sanity.SessionSanity" />
			<class name="org.lenskart.test.sanity.PaymentServiceSanity" />
			<class name="org.lenskart.test.sanity.OrderPaymentSanity" />
			<class name="org.lenskart.test.sanity.InventoryServiceSanity" />
			<class name="org.lenskart.test.sanity.CustomerSanity" />
			<class name="org.lenskart.test.sanity.MoneyServiceSanity" />
			<class name="org.lenskart.test.sanity.ProductServiceSanity" />
			<class name="org.lenskart.test.utility.customer.GetPrescriptionUsecases" />
			<class name="org.lenskart.test.utility.customer.UpdatePrescriptionUsecases" />
			<class name="org.lenskart.test.utility.CampaignUsecases" />
			<class name="org.lenskart.test.utility.CheckPincodeUsecases" />
			<class name="org.lenskart.test.utility.CountryStateUsecases" />
			<class name="org.lenskart.test.utility.FetchLKCountriesUseCases" />
			<class name="org.lenskart.test.utility.GenerateOtpV2CopyAPI" />
			<class name="org.lenskart.test.utility.GetRedisApiUsecases" />
			<class name="org.lenskart.test.utility.LenskartExchangeOfferUsecases" />
			<class name="org.lenskart.test.utility.MenuUsecases" />
			<class name="org.lenskart.test.utility.OffersUsecases" />
			<class name="org.lenskart.test.utility.RetrieveDeeplink" />
			<class name="org.lenskart.test.session.AuthenticateSessionUsecases" />
			<class name="org.lenskart.test.session.CreateSessionUsecases" />
			<class name="org.lenskart.test.session.DeleteSessionUsecases" />
			<class name="org.lenskart.test.session.GetMeSessionUsecases" />
			<class name="org.lenskart.test.session.LogoutUsecases" />
			<class name="org.lenskart.test.session.PatchSessionUsecases" />
			<class name="org.lenskart.test.session.UpdateSessionUsecases" />
			<class name="org.lenskart.test.session.ValidateOnlySessionUsecases" />
			<class name="org.lenskart.test.session.ValidateSessionUsecases" />
			
			<class name="org.lenskart.test.utility.BuyOnCallUsecases" />
		
	</classes>
		
	</test> <!-- Default test -->
</suite> <!-- Default suite -->