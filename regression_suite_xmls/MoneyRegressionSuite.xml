<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Money suite">
	<test name="CreateTransaction">
		<classes>
			<class name="org.lenskart.test.money.CreateTransactionUsecases">
				<methods>
					<include name="create_money_transaction_with_two_gv_sc_pd_successcase" />
				</methods>
			</class>
		</classes>
	</test>
	<test name="RollbackTransaction">
		<classes>
			<class name="org.lenskart.test.money.RollbackTransactionUsecases">
				<methods>
					<include name="rollback_Money_Transaction_with_gv_sc_scsucesscase" />
				</methods>
			</class>
		</classes>
	</test>
	<test name="GetFranchiseCreditService">
		<classes>
			<class name="org.lenskart.test.money.GetFranchiseCreditServiceUsecases" />
			<class name="org.lenskart.test.money.GetGiftVoucherCodeUsecases" />
			<class name="org.lenskart.test.money.GetStoreCreditCodeUsecases" />
			<class name="org.lenskart.test.money.GetStoreCreditEmailUsecases" />
		</classes>
	</test> <!-- Default test -->
</suite> <!-- Default suite -->
