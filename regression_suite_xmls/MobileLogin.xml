<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd" >

<suite name="org.lenskart.test.customer.mobilelogin">

	<test name="SendOtpUsecases">
		<classes>
			<class name="org.lenskart.test.customer.mobilelogin.SendOtpUsecases" />
		</classes>
	</test>

	<test name="MobileAuthenticateUseCases">
		<classes>
			<class
				name="org.lenskart.test.customer.mobilelogin.MobileAuthenticateUseCases" />
		</classes>
	</test>

	<test name="GetAccountsByMobileNumberUsecases">
		<classes>
			<class
				name="org.lenskart.test.customer.mobilelogin.GetAccountsByMobileNumberUsecases" />
		</classes>
	</test>

	<test name="SendVerificationEmail">
		<classes>
			<class name="org.lenskart.test.customer.mobilelogin.SendVerificationEmail" />
		</classes>
	</test>

	<test name="VerifyEmail">
		<classes>
			<class name="org.lenskart.test.customer.mobilelogin.VerifyEmail" />
		</classes>
	</test>

	<test name="VerifyOtpUseCases">
		<classes>
			<class name="org.lenskart.test.customer.mobilelogin.VerifyOtpUseCases" />
		</classes>
	</test>

	<test name="CartOrderAndSCMerge">
		<classes>
			<class name="org.lenskart.test.customer.mobilelogin.CartOrderAndSCMerge" />
		</classes>
	</test>

	<test name="NewSignUpAndMobileWalletAccountUseCases">
		<classes>
			<class
				name="org.lenskart.test.customer.emailandmobilelogin.NewSignUpAndMobileWalletAccountUseCases" />
		</classes>
	</test>

	<test name="NewSignUpAndNewMobileNumber">
		<classes>
			<class
				name="org.lenskart.test.customer.emailandmobilelogin.NewSignUpAndNewMobileNumber" />
		</classes>
	</test>

</suite>
