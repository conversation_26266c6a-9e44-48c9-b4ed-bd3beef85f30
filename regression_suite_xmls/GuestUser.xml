<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd" >

<suite name="org.lenskart.test.customer.guest">

	<test name="EmailVerifiedMobileAccountsUsecases">
		<classes>
			<class
				name="org.lenskart.test.customer.guest.EmailVerifiedMobileAccountsUsecases" />
		</classes>
	</test>

	<test name="ExistingMobileOnlyCustomerUsecases">
		<classes>
			<class
				name="org.lenskart.test.customer.guest.ExistingMobileOnlyCustomerUsecases" />
		</classes>
	</test>

	<test name="MultipleEmailAccountsUsecases">
		<classes>
			<class
				name="org.lenskart.test.customer.guest.MultipleEmailAccountsUsecases" />
		</classes>
	</test>

	<test name="NewTelephoneNumberUsecases">
		<classes>
			<class name="org.lenskart.test.customer.guest.NewTelephoneNumberUsecases" />
		</classes>
	</test>

	<test name="SingleEmailCustomerWithTelephoneUsecases">
		<classes>
			<class
				name="org.lenskart.test.customer.guest.SingleEmailCustomerWithTelephoneUsecases" />
		</classes>
	</test>
</suite>