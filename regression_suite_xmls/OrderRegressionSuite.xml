<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd" >

<suite name="org.lenskart.test.order">

	<test name="CreateOrderUsecases">
		<classes>
			<class name="org.lenskart.test.order.CreateOrderUsecases" />
		</classes>
	</test>
	<test name="FetchLatestOrderUsecases">
		<classes>
			<class name="org.lenskart.test.order.FetchLatestOrderUsecases" />
		</classes>
	</test>
	<test name="GetAllOrdersUsecases">
		<classes>
			<class name="org.lenskart.test.order.GetAllOrdersUsecases" />
		</classes>
	</test>
	<test name="GetItemPrescriptionUsecases">
		<classes>
			<class name="org.lenskart.test.order.GetItemPrescriptionUsecases" />
		</classes>
	</test>
	<test name="GetOrdersUsecases">
		<classes>
			<class name="org.lenskart.test.order.GetOrdersUsecases" />
		</classes>
	</test>

	<test name="GetSavedPrescriptionUsecases">
		<classes>
			<class name="org.lenskart.test.order.GetSavedPrescriptionUsecases" />
		</classes>
	</test>

	<test name="UpdateBillingAddressUsecases">
		<classes>
			<class name="org.lenskart.test.order.UpdateBillingAddressUsecases" />
		</classes>
	</test>

	<test name="UpdateOrderItemPrescriptionUsecases">
		<classes>
			<class name="org.lenskart.test.order.UpdateOrderItemPrescriptionUsecases" />
		</classes>
	</test>
	<test name="UpdateOrderStatusUsecases">
		<classes>
			<class name="org.lenskart.test.order.UpdateOrderStatusUsecases" />
		</classes>
	</test>
	<test name="UpdateShippingAddresUsecase">
		<classes>
			<class name="org.lenskart.test.order.UpdateShippingAddresUsecase" />
		</classes>
	</test>
</suite>