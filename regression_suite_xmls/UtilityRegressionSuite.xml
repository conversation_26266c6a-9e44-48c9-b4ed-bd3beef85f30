<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd" >

<suite name="org.lenskart.test.utility">

	<test name="AddPincodeUsecases">
		<classes>
			<class name="org.lenskart.test.utility.AddPincodeUsecases" />
		</classes>
	</test>
	<test name="BuyOnCallUsecases">
		<classes>
			<class name="org.lenskart.test.utility.BuyOnCallUsecases" />
		</classes>
	</test>
	<test name="CampaignUsecases">
		<classes>
			<class name="org.lenskart.test.utility.CampaignUsecases" />
		</classes>
	</test>
	<test name="CheckPincodeUsecases">
		<classes>
			<class name="org.lenskart.test.utility.CheckPincodeUsecases" />
		</classes>
	</test>
	<test name="CountryStateUsecases">
		<classes>
			<class name="org.lenskart.test.utility.CountryStateUsecases" />
		</classes>
	</test>
	<test name="EventApiUsecases">
		<classes>
			<class name="org.lenskart.test.utility.EventApiUsecases" />
		</classes>
	</test>
	<test name="GenerateOtpV2CopyAPI">
		<classes>
			<class name="org.lenskart.test.utility.GenerateOtpV2CopyAPI" />
		</classes>
	</test>
	<test name="GetPincodeUsescases">
		<classes>
			<class name="org.lenskart.test.utility.GetPincodeUsescases" />
		</classes>
	</test>
	<test name="GetRedisApiUsecases">
		<classes>
			<class name="org.lenskart.test.utility.GetRedisApiUsecases" />
		</classes>
	</test>
	<test name="GiftVoucherCampaign">
		<classes>
			<class name="org.lenskart.test.utility.GiftVoucherCampaign" />
		</classes>
	</test>
	<test name="MenuUsecases">
		<classes>
			<class name="org.lenskart.test.utility.MenuUsecases" />
		</classes>
	</test>
	<test name="OffersUsecases">
		<classes>
			<class name="org.lenskart.test.utility.OffersUsecases" />
		</classes>
	</test>
	<test name="PincodeFromExcelUsecases">
		<classes>
			<class name="org.lenskart.test.utility.PincodeFromExcelUsecases" />
		</classes>
	</test>
	<test name="QueueServiceAPITestcases">
		<classes>
			<class name="org.lenskart.test.utility.QueueServiceAPITestcases" />
		</classes>
	</test>
	<test name="RetriveByPincodeUsecases">
		<classes>
			<class name="org.lenskart.test.utility.RetriveByPincodeUsecases" />
		</classes>
	</test>
	<test name="VerifyOtpV2CopyAPI">
		<classes>
			<class name="org.lenskart.test.utility.VerifyOtpV2CopyAPI" />
		</classes>
	</test>
	</suite>
	