<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd" >

<suite name="org.lenskart.test.product">

	<test name="AutoSuggestionUsecases">
		<classes>
			<class name="org.lenskart.test.product.AutoSuggestionUsecases" />
		</classes>
	</test>
	<test name="GetWishlistUsecases">
		<classes>
			<class name="org.lenskart.test.product.GetWishlistUsecases" />
		</classes>
	</test>
	<test name="PostWhishlistUsecases">
		<classes>
			<class name="org.lenskart.test.product.PostWhishlistUsecases" />
		</classes>
	</test>
	<test name="UpdateWishlistUsecases">
		<classes>
			<class name="org.lenskart.test.product.UpdateWishlistUsecases" />
		</classes>
	</test>
	<test name="DeleteWishlistUsecases">
		<classes>
			<class name="org.lenskart.test.product.DeleteWishlistUsecases" />
		</classes>
	</test>
	<test name="DeleteProductFromWishlistUsecases">
		<classes>
			<class name="org.lenskart.test.product.DeleteProductFromWishlistUsecases" />
		</classes>
	</test>
	<test name="GetCategoryBestSellerUsecases">
		<classes>
			<class name="org.lenskart.test.product.GetCategoryBestSellerUsecases" />
		</classes>
	</test>
	<test name="GetOffersOfProductUsecases">
		<classes>
			<class name="org.lenskart.test.product.GetOffersOfProductUsecases" />
		</classes>
	</test>

	<test name="GetProductDetailForMultiplePidsUsecases">
		<classes>
			<class
				name="org.lenskart.test.product.GetProductDetailForMultiplePidsUsecases" />
		</classes>
	</test>

	<test name="GetProductV2Usecases">
		<classes>
			<class name="org.lenskart.test.product.GetProductV2Usecases" />
		</classes>
	</test>
	<test name="GetSolutionApi">
		<classes>
			<class name="org.lenskart.test.product.GetSolutionApi" />
		</classes>
	</test>

	<test name="GetSubcategoryUsecases">
		<classes>
			<class name="org.lenskart.test.product.GetSubcategoryUsecases" />
		</classes>
	</test>
	<test name="GetSubscriptionForGivenProduct">
		<classes>
			<class name="org.lenskart.test.product.GetSubscriptionForGivenProduct" />
		</classes>
	</test>
	<test name="GetTemplateUsecases">
		<classes>
			<class name="org.lenskart.test.product.GetTemplateUsecases" />
		</classes>
	</test>
	<test name="GetTemplateWithTemplateIdANDPowerTypeusecases">
		<classes>
			<class
				name="org.lenskart.test.product.GetTemplateWithTemplateIdANDPowerTypeusecases" />
		</classes>
	</test>
	<test name="GetV2BuyPackageUsecases">
		<classes>
			<class name="org.lenskart.test.product.GetV2BuyPackageUsecases" />
		</classes>
	</test>
	<test name="HomeServiceUsecases">
		<classes>
			<class name="org.lenskart.test.product.HomeServiceUsecases" />
		</classes>
	</test>
	<test name="OfferForProductUsecases">
		<classes>
			<class name="org.lenskart.test.product.OfferForProductUsecases" />
		</classes>
	</test>
	<test name="PostOOSsubscriptionUsecases">
		<classes>
			<class name="org.lenskart.test.product.PostOOSsubscriptionUsecases" />
		</classes>
	</test>
	<test name="PowerTempleteUsecases">
		<classes>
			<class name="org.lenskart.test.product.PowerTempleteUsecases" />
		</classes>
	</test>

	<test name="UpdateBuyPackageAPI">
		<classes>
			<class name="org.lenskart.test.product.UpdateBuyPackageAPI" />
		</classes>
	</test>

</suite>