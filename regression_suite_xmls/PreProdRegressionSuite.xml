<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Regression Suite">

	<test name="RegressionTests">
		<classes>
			<class name="org.lenskart.test.utility.ditto.share.CreateDittoOpinionUsecases" />
			<class name="org.lenskart.test.utility.ditto.share.GetDittoImageSafe" />
			<class name="org.lenskart.test.utility.ditto.share.GetDittoOpinionUsecases" />
			<class name="org.lenskart.test.utility.ditto.share.GetOpinionResultUsecases" />
			<class name="org.lenskart.test.utility.ditto.share.PostLoginUsescases" />
			<class name="org.lenskart.test.utility.ditto.share.SubmitFeedbackUsescases" />
			
			<class name="org.lenskart.test.utility.ditto.profile.AddToDittoListUsecases" />
			<class name="org.lenskart.test.utility.ditto.profile.DeleteAllDittoListUsecases" />
			<class name="org.lenskart.test.utility.ditto.profile.DeleteDittoIdUsecases" />
			<class name="org.lenskart.test.utility.ditto.profile.GetDittoListUsecases" />			
			<class name="org.lenskart.test.utility.ditto.profile.SetDefaultDittoIdUsecases" />
			
			<class name="org.lenskart.test.utility.customer.AddPrescriptionUsecases" />
			<class name="org.lenskart.test.utility.customer.GetPrescriptionUsecases" />
			<class name="org.lenskart.test.utility.customer.NewUpdatePrescriptionApiUseCases" />
			<class name="org.lenskart.test.utility.customer.UpdatePrescriptionUsecases" />
			
			<class name="org.lenskart.test.utility.CampaignUsecases" />
			<class name="org.lenskart.test.utility.CheckPincodeUsecases" />
			<class name="org.lenskart.test.utility.CountryStateUsecases" />
			<class name="org.lenskart.test.utility.EventApiUsecases" />
			<class name="org.lenskart.test.utility.FetchLKCountriesUseCases" />
			<class name="org.lenskart.test.utility.GenerateOtpV2CopyAPI" />
			<class name="org.lenskart.test.utility.GetPincodeUsescases" />
			<class name="org.lenskart.test.utility.GetRedisApiUsecases" />
			<class name="org.lenskart.test.utility.GiftVoucherCampaign" />
			<class name="org.lenskart.test.utility.LenskartExchangeOfferUsecases" />
			<class name="org.lenskart.test.utility.MenuUsecases" />
			<class name="org.lenskart.test.utility.OffersUsecases" />
			<class name="org.lenskart.test.utility.RetrieveDeeplink" />
			<class name="org.lenskart.test.utility.RetriveByPincodeUsecases" />
			<class name="org.lenskart.test.utility.VerifyOtpV2CopyAPI" />
			
			<class name="org.lenskart.test.session.AuthenticateSessionUsecases" />
			<class name="org.lenskart.test.session.CreateSessionUsecases" />
			<class name="org.lenskart.test.session.DeleteSessionUsecases" />
			<class name="org.lenskart.test.session.GetMeSessionUsecases" />
			<class name="org.lenskart.test.session.LogoutUsecases" />
			<class name="org.lenskart.test.session.PatchSessionUsecases" />
			<class name="org.lenskart.test.session.UpdateSessionUsecases" />
			<class name="org.lenskart.test.session.ValidateOnlySessionUsecases" />
			<class name="org.lenskart.test.session.ValidateSessionUsecases" />
			
			<class name="org.lenskart.test.php.CheckPincode" />
			
			<class name="org.lenskart.test.payment.v2.CreatePaymentUsecases" />
			
			
									
		</classes>
	</test> <!-- Test -->
</suite> <!-- Suite -->
