7.39.1:
  date: 2024-06-14
  chores:
    - Bumped postman-request dependency

7.39.0:
  date: 2024-04-17
  new features:
    - GH-1399 Added support for max body size in EdgeGrid auth
  fixed bugs:
    - >-
      GH-1400 Fixed a bug where NTLM auth state was not handled correctly when
      configured at folder or collection level
    - >-
      GH-1403 Fixed a bug where Digest Auth nonce count value was not updated
      for successive requests

7.38.0:
  date: 2024-04-03
  new features:
    - GH-1401 Deprecated legacy sandbox APIs

7.37.3:
  date: 2024-04-03
  chores:
    - Updated dependencies

7.37.2:
  date: 2024-04-01
  fixed bugs:
    - >-
      GH-1394 Fixed a bug where scheme and parameters name in Digest Auth were
      matched in a case-sensitive manner
  chores:
    - Updated dependencies

7.37.1:
  date: 2024-03-13
  chores:
    - Updated dependencies

7.37.0:
  date: 2024-02-28
  new features:
    - GH-1377 Add feature to resolve packages in scripts using `pm.require`

7.36.3:
  date: 2024-02-01
  fixed bugs:
    - >-
      GH-1374 Fixed a bug where execution.skipRequest listeners were not getting
      garbage collected
    - >-
      Fixed a bug where vault variables were not getting resolved for request
      URL without protocol

7.36.2:
  date: 2024-01-19
  chores:
    - >-
      GH-1362 Added an argument to item callback to identify if the item
      execution was skipped
    - GH-1368 Added support for domain property in vault variables

7.36.1:
  date: 2023-12-20
  chores:
    - GH-1358 Added new vaultSecrets variable scope

7.36.0:
  date: 2023-11-18
  new features:
    - >-
      GH-1336 Added support for fetching execution location context through
      script
  chores:
    - Updated dependencies

7.35.0:
  date: 2023-11-02
  new features:
    - GH-1354 Added support for skipping request execution through script

7.34.0:
  date: 2023-10-19
  new features:
    - GH-1340 Added support for ASAP authentication
  chores:
    - Updated dependencies

7.33.0:
  date: 2023-08-04
  new features:
    - >-
      GH-1326 Added support for uploading binary files via base64 encoded string
      in formdata and file mode body
  fixed bugs:
    - GH-1312 Fixed a bug where dns lookup used to fail on Node.js v20
  chores:
    - Updated dependencies

7.32.3:
  date: 2023-06-08
  fixed bugs:
    - GH-1306 Fixed a bug where JWT base64 secret was not decoded correctly
  chores:
    - Updated dependencies

7.32.2:
  date: 2023-04-10
  chores:
    - Updated dependencies

7.32.1:
  date: 2023-04-05
  fixed bugs:
    - >-
      GH-1284 Fixed a bug where JSON comments were not stripped from raw body
      for JSON `Content-Type`

7.32.0:
  date: 2023-03-31
  new features:
    - GH-1274 Added support for stripping JSON comments in raw body
  fixed bugs:
    - GH-1279 Fixed a bug in JWT where empty header prefix was not handled
  chores:
    - Updated dependencies

7.31.3:
  date: 2023-03-10
  fixed bugs:
    - GH-1272 Fixed `content-type` check for detecting SSE stream

7.31.2:
  date: 2023-03-10
  chores:
    - Updated dependencies

7.31.1:
  date: 2023-02-22
  chores:
    - Updated dependencies

7.31.0:
  date: 2023-02-08
  new features:
    - GH-1252 Added support for JWT authentication
    - GH-1260 Throw error when an invalid `entrypoint` is provided
    - GH-1261 Added support for refreshing OAuth tokens
    - GH-1264 Added support for Server-Sent Events
  chores:
    - Dropped support for Node.js < v12

7.30.1:
  date: 2023-01-25
  chores:
    - GH-1253 Added contract-tests for tough-cookie
    - GH-1256 Bumped tough-cookie dependency
    - GH-1254 Migrate to GitHub Actions
    - Updated dependencies

7.30.0:
  date: 2022-11-28
  new features:
    - GH-1237 Added support for request auth mutation via prerequest scripts
  chores:
    - GH-1244 Updated httpntlm dependencies for NTLMv2 support
    - Updated dependencies

7.29.2:
  date: 2022-06-06
  chores:
    - Updated dependencies

7.29.1:
  date: 2022-05-24
  chores:
    - Updated dependencies

7.29.0:
  date: 2022-01-10
  new features:
    - >-
      Added `insecureHTTPParser` requester and protocolProfileBehavior option to
      use an insecure HTTP parser that accepts invalid HTTP headers
  fixed bugs:
    - >-
      Fixed a bug in `pm.sendRequest` where request body for the following
      methods: GET, COPY, HEAD, PURGE, and UNLOCK was dropped incorrectly
  chores:
    - GH-1188 Dropped Node.js v8 step from travis build
    - GH-1188 Updated nyc configuration
    - GH-1188 Updated ESLint rules
    - GH-1188 Updated dependencies

7.28.4:
  date: 2021-08-16
  fixed bugs:
    - >-
      Fixed a bug where NTLM could not complete authentication if a single
      `www-authenticate` header was sent with multiple schemes
  chores:
    - Updated dependencies

7.28.3:
  date: 2021-07-25
  fixed bugs:
    - >-
      GH-1163 Fixed a bug where `protocolProfileBehavior.disableUrlEncoding`
      option was not respected in OAuth 1.0 authorizer
  chores:
    - Updated dependencies

7.28.2:
  date: 2021-06-24
  fixed bugs:
    - >-
      GH-1161 Fixed a bug where cookies set in the request and response callback
      don't account for redirects
  chores:
    - Bumped postman-collection dependency to v4
    - Updated dependencies

7.28.1:
  date: 2021-06-16
  fixed bugs:
    - >-
      GH-1158 Fixed a bug where invalid OAuth 1.0 signature was generated for
      encoded query parameters
  chores:
    - Added secure codecov publish script
    - Updated dependencies

7.28.0:
  date: 2021-04-11
  new features:
    - GH-1143 Updated history object to include request and response headers
  chores:
    - Updated dependencies

7.27.0:
  date: 2021-03-24
  new features:
    - >-
      GH-1130 Added support to disable TLSv1.3 in tlsDisabledProtocols protocol
      profile behavior
    - >-
      GH-1128 Added `systemHeaders` requester option to add default system
      headers
    - GH-1139 Added support for request body mutation via prerequest scripts
  chores:
    - Updated dependencies

7.26.10:
  date: 2021-01-03
  chores:
    - GH-1113 Updated browser requester to accept cookie jar
    - Updated dependencies

7.26.9:
  date: 2020-12-15
  chores:
    - GH-1110 Updated browser requester to accept custom agentClass

7.26.8:
  date: 2020-10-21
  chores:
    - Updated dependencies

7.26.7:
  date: 2020-10-07
  chores:
    - >-
      GH-1096 Updated .npmignore to prevent the addition of tests and config
      files in the published package
    - GH-1096 Added system test for published package content
    - GH-1097 Updated dependencies
    - GH-1098 Automated releases and publish process

7.26.6:
  date: 2020-09-16
  chores:
    - Updated dependencies

7.26.5:
  date: 2020-08-31
  fixed bugs:
    - >-
      GH-1086 Fixed a bug where custom header prefix in OAuth2 was being used
      without trimming
  chores:
    - Updated dependencies

7.26.4:
  date: 2020-08-18
  fixed bugs:
    - >-
      GH-1063 Fixed a bug where OAuth1 helper was calculating wrong signature
      when callback and verifier parameters are left empty
  chores:
    - Updated dependencies

7.26.3:
  date: 2020-07-30
  fixed bugs:
    - >-
      GH-1059 Fixed a bug where response status messages with utf8 characters
      were not encoded properly
  chores:
    - Updated dependencies

7.26.2:
  date: 2020-07-13
  chores:
    - Updated dependencies

7.26.1:
  date: 2020-06-14
  fixed bugs:
    - GH-1050 Handle invalid private key errors in OAuth1 RSA signatures
    - >-
      GH-1052 Fixed a bug where digest auth was not generating correct
      Authorization header when `qop` field was set to `auth-int`
  chores:
    - Updated dependencies

7.26.0:
  date: 2020-06-05
  new features:
    - GH-1044 Added `agents` requester option to define custom requesting agents
    - >-
      GH-1018 Added support for using custom Authorization header prefix via
      `headerPrefix` property in OAuth 2.0
    - |
      GH-1011 Added support for following signature methods in OAuth 1.0
        1. HMAC-SHA512
        2. RSA-SHA1
        3. RSA-SHA256
        4. RSA-SHA512
    - >-
      GH-1011 Added support for OAuth 1.0 [body hash extension]
      (https://tools.ietf.org/id/draft-eaton-oauth-bodyhash-00.html)
    - |
      GH-1011 Added support for following additional parameters defined in
      [OAuth 1.0a spec](https://oauth.net/core/1.0a)
        1. oauth_callback
        2. oauth_verifier
    - |
      GH-1039 Added support for following algorithms in Digest auth
        1. SHA-256
        2. SHA-256-sess
        3. SHA-512-256
        4. SHA-512-256-sess
    - >-
      GH-1041 Added support to include AWS auth data in query params using
      `addAuthDataToQuery` option
  fixed bugs:
    - GH-1045 Fetch cookies on `Requester~dryRun` using the encoded URL object
    - >-
      GH-1046 Treat query params and urlencoded body params as case-sensitive in
      `Requester~dryRun`
  chores:
    - Updated dependencies

7.25.0:
  date: 2020-05-15
  new features:
    - GH-1014 Added `agent` requester option to define a custom requesting agent
  fixed bugs:
    - >-
      GH-1005 Fixed a bug where encoded request url was not set in the request
      and response callbacks
  chores:
    - GH-1000 Updated digest tests with new local digest server
    - GH-1009 Migrated integration tests to use global-local servers
    - GH-1008 Bubble up history in request and response callback on error
    - Updated dependencies

7.24.2:
  date: 2020-04-08
  chores:
    - Updated dependencies

7.24.1:
  date: 2020-03-31
  fixed bugs:
    - >-
      GH-994 Fixed a bug where OAuth1 was calculating wrong signature for
      request with urlencoded body having disabled params

7.24.0:
  date: 2020-03-15
  new features:
    - GH-984 Added `disabledSystemHeaders` protocol profile behavior
    - >-
      GH-985 Added method `Requester~dryRun` to dry run the given request
      instance
  fixed bugs:
    - >-
      GH-982 Fixed a bug where `Content-Type` header was sent even if all the
      params are disabled for form-data and urlencoded body
    - >-
      GH-983 Fixed a bug where the system added `Content-Type` header was not
      added when the given header is disabled
  chores:
    - Updated dependencies

7.23.0:
  date: 2020-03-02
  new features:
    - GH-965 Updated URL encoding behavior as per WHATWG URL standard
    - >-
      GH-965 Added `useWhatWGUrlParser` requester option to use WHATWG URL
      parser and encoder
    - GH-965 Added `disableUrlEncoding` protocol profile behavior
    - >-
      GH-959 Added an option to toggle parameter encoding in Authorization
      header of OAuth1
  fixed bugs:
    - >-
      GH-957 Fixed a bug where expiry was wrong for cookies passed to response
      callback when cookies had `Max-Age` field
    - GH-973 Fixed request headers updated by AWS auth in manifest
  chores:
    - GH-953 Updated dependencies

7.22.0:
  date: 2020-01-10
  new features:
    - GH-949 Improved performance for large number of iterations
    - GH-953 Added support for brotli decompression
  chores:
    - GH-943 Added `nyc` and `codecov` for code coverage checks
    - GH-951 Added sanity check for edgegrid auth params in sign function
    - GH-953 Updated dependencies

7.21.0:
  date: 2019-12-02
  new features:
    - >-
      GH-937 Added ability to use OAuth2 tokens with unknown types as Bearer
      token in OAuth2 authentication
    - GH-934 Added ability to pull domain name from username for NTLM auth
  fixed bugs:
    - >-
      GH-939 Fixed a bug where IPv6 localhost request through IPv4 localhost
      proxy was failing.
    - >-
      GH-928 Fixed a bug where Basic Auth was failing if credentials had
      non-ASCII characters
    - >-
      GH-929 Fixed a bug where error was thrown when `username` or `password`
      fields were empty for NTLM auth
    - >-
      GH-933 Fixed a bug where NTLM could not complete authentication if
      multiple `www-authenticate` headers were sent
  chores:
    - >-
      GH-942 Convert response into a JSON serializable object before executing
      the script
    - Updated dependencies

7.20.1:
  date: 2019-11-13
  chores:
    - Updated dependencies

7.20.0:
  date: 2019-11-12
  new features:
    - GH-921 Added ability to preserve non-JSON data types in console logs
    - >-
      GH-921 Added an option `script.serializeLogs` to allow sending logs as a
      serialized string which can be parsed using `teleport-javascript`
  fixed bugs:
    - >-
      GH-918 Fixed a bug where requests having binary body with AWS, Hawk or
      EdgeGrid authentication caused the Runtime to crash
  chores:
    - Updated dependencies

7.19.0:
  date: 2019-10-16
  new features:
    - GH-908 Added support for Akamai EdgeGrid authentication
  fixed bugs:
    - GH-914 Fixed a bug where form-data with object value crashes the process
  chores:
    - GH-911 Added reused session data to history object

7.18.0:
  date: 2019-10-01
  new features:
    - >-
      GH-898 Added `ignoreProxyEnvironmentVariables` runner option to opt-out of
      implicit proxy configured using environment variables
  chores:
    - Updated dependencies

7.17.1:
  date: 2019-09-06
  fixed bugs:
    - >-
      GH-889 Fixed a bug where system header was not updated correctly in case
      of multiple `cookie` headers
  chores:
    - Updated dependencies

7.17.0:
  date: 2019-09-04
  new features:
    - GH-870 Added support for request body options
  fixed bugs:
    - >-
      GH-885 Fixed a bug where collection execution won't stop or abort on async
      script errors
  chores:
    - Updated dependencies

7.16.3:
  date: 2019-08-19
  chores:
    - GH-879 Added `followAuthorizationHeader` protocol profile behavior
    - Updated dependencies

7.16.2:
  date: 2019-08-14
  chores:
    - Updated dependencies

7.16.1:
  date: 2019-08-02
  chores:
    - Updated dependencies

7.16.0:
  date: 2019-08-01
  new features:
    - GH-865 Added support for asynchronous CookieJar
    - GH-865 Added support for programmatic cookie access via scripts
    - >-
      GH-861 Added support to process `Visualizer` templates provided as a
      result of script execution
    - GH-861 Added visualizer data in `item` callback
  fixed bugs:
    - >-
      GH-855 Fixed resolution of all `*.localhost` names to the loopback address
      (127.0.0.1)
    - >-
      GH-869 Fixed a bug where GraphQL requests with AWS or Hawk authentication
      caused the collection run to hang
  chores:
    - Updated dependencies

7.15.2:
  date: 2019-06-25
  chores:
    - Updated dependencies

7.15.1:
  date: 2019-06-18
  fixed bugs:
    - Fixed a bug where non-string raw bodies crash the process
    - >-
      Fixed a bug where disabled headers with the same name as of system headers
      don't get overwritten in the original request
  chores:
    - >-
      GH-839 Added `includePayloadHash` option in Hawk auth to make payload hash
      optional
    - >-
      GH-848 Refactored requester to avoid duplicate computation for response
      object
    - Updated dependencies

7.15.0:
  date: 2019-06-07
  new features:
    - GH-840 Added responseStart callback
    - >-
      GH-845 Added `maxResponseSize` requester option which aborts the request
      if the response size exceeds the threshold
  fixed bugs:
    - GH-837 Fixed a bug where invalid url crashes the process
  chores:
    - Updated dependencies

7.14.0:
  date: 2019-05-16
  new features:
    - >-
      GH-832 Added support for multi entrypoints by id or name which follows the
      given order
  chores:
    - Updated dependencies

7.13.0:
  date: 2019-04-26
  new features:
    - GH-818 Added support for multiple files in form-data body
    - GH-819 Handle CookieStore events on `execution.cookies.${id}`
  chores:
    - GH-815 Allow empty string as variables in GraphQL body
    - Updated dependencies

7.12.0:
  date: 2019-04-08
  new features:
    - GH-800 Added support for GraphQL request body
    - GH-799 Added support for payload hash in Hawk authentication
  chores:
    - GH-804 Take control of URL encoding from `postman-request`
    - >-
      Fixed URL encoding issues while calculating payload hash in multiple
      authorizer modules
    - Updated dependencies

7.11.0:
  date: 2019-03-19
  new features:
    - GH-786 Added history in request and response callback
    - GH-787 Added TLS protocol profile behavior
  breaking changes:
    - GH-786 Moved timings out of response instance
  chores:
    - GH-775 Added tests for proxy authentication
    - Updated dependencies

7.10.0:
  date: 2019-03-01
  new features:
    - GH-763 Added support for extending the root CA certificates
    - GH-753 Added support for PFX client certificates
    - GH-739 Added API Key type authentication helper
  fixed bugs:
    - GH-768 Used high-resolution time to calculate client overhead timings
  chores:
    - GH-755 Added `connection` system header
    - GH-745 Switched to a YAML changelog
    - Updated dependencies

7.9.1:
  date: 2019-02-20
  chores:
    - Updated `postman-sandbox` to v3.2.4

7.9.0:
  date: 2019-02-19
  new features:
    - GH-744 Added `system` property to all the headers added via requester
    - >-
      GH-740 Added requester option to set `Cache-Control` and `Postman-Token`
      system headers
    - GH-747 Added additional timings properties to track client overhead
  chores:
    - Updated dependencies

7.8.0:
  date: 2019-02-15
  new features:
    - GH-735 Added support for request mutation via prerequest scripts
    - >-
      GH-729 Added `timings` requester option to bubble up detailed
      request-response timings
  chores:
    - Updated dependencies

7.7.1:
  date: 2019-02-7
  fixed bugs:
    - >-
      GH-732 Fixed a bug where deep dependencies path was incorrect while
      fetching version

7.7.0:
  date: 2019-02-1
  new features:
    - GH-707 Avoid executing disabled `prerequest` and `test` scripts
    - GH-713 Expose Runtime and its dependencies package version via an API
    - >-
      GH-707 Uses Collection SDK `Request~getHeaders` method to get request
      headers object
  fixed bugs:
    - >-
      GH-723 Fixed a bug where interrupting the script execution crashes the
      process
  chores:
    - >-
      Updated `postman-collection` to v3.4.2 which avoids substitution of
      disabled variables
    - Updated dependencies

7.6.1:
  date: 2019-01-3
  fixed bugs:
    - >-
      Updated `postman-collection` to v3.4.1 which fixes a bug where poly
      chained variables are not resolved correctly
  chores:
    - Updated dependencies

7.6.0:
  date: 2018-12-25
  new features:
    - GH-694 Respect form-data fields ordering
  bug fixex:
    - >-
      GH-690 Fixed a bug that caused requests to fail when protocol of URL is
      not in lowercase
  chores:
    - Updated dependencies

7.5.0:
  date: 2018-12-6
  new features:
    - GH-670 Added requester options to configure redirects behavior
    - GH-675 Inherit protocolProfileBehavior from parent ItemGroup(s)
    - |
      GH-678 Added support for protocolProfileBehavior to override following
      requester options:
      -> strictSSL
      -> maxRedirects
      -> followRedirects
      -> followOriginalHttpMethod
      -> removeRefererHeaderOnRedirect
  chores:
    - Updated dependencies

7.4.2:
  date: 2018-11-7
  chores:
    - GH-667 Swapped dependency on hawk for request provided implementation
    - Updated dependencies
    - Switched over to Travis for Windows builds

7.4.1:
  date: 2018-11-4
  chores:
    - >-
      GH-665 Corrected `postman-collection` reference to a precise version:
      3.3.0
    - GH-664 Switched to Travis for Windows builds
    - Updated dependencies
    - >-
      GH-662 Added `.gitattributes` to enforce consistent line endings for `.js`
      files.
    - Migrated tests to use `chai.expect` assertions

7.4.0:
  date: 2018-09-21
  new features:
    - GH-636 Added support for `disabled` property in request body
  breaking changes:
    - GH-640 Dropped support for `sendBodyWithGetRequests` requester option
  chores:
    - Updated dependencies

7.3.0:
  date: 2018-08-21
  new features:
    - GH-624 Added support for `contentType` to form data request bodies
    - GH-619 Added support for multi entrypoints by id or name
    - GH-616 Improved certificate lookup error handling
    - GH-625 Improved file loading errors
  chores:
    - GH-627 Removed NSP and associated apparatus
    - Updated dependencies

7.2.0:
  date: 2018-06-30
  new features:
    - >-
      Added support for variable change tracking in scripts. Variable scopes in
      `result` of script callbacks like `script`, `test` and `prerequest` will
      now have a `mutations` object. This contains only the mutations made
      within that script, if any.
  chores:
    - Updated dependencies

7.1.6:
  date: 2018-05-24
  fixed bugs:
    - >-
      Updated `postman-request` to `v2.86.1-postman.1`, which fixes
      https://nodesecurity.io/advisories/664
  chores:
    - Updated dependencies

7.1.5:
  date: 2018-04-10
  chores:
    - GH-565 Updated `postman-sandbox` to `v3.0.6`

7.1.4:
  date: 2018-04-9
  fixed bugs:
    - >-
      GH-563 Updated `postman-sandbox` to `v3.0.5`, which fixes assertion
      centric bugs
    - >-
      GH-554 Updated `postman-request` to `v2.85.1-postman.1`, which fixes
      https://nodesecurity.io/advisories/566
    - >-
      GH-553 Fixed a bug that prevented JavaScript keywords from being used as
      keys in request bodies
  chores:
    - Updated dependencies

7.1.3:
  date: 2018-01-2
  fixed bugs:
    - >-
      GH-480 Removed blacklisting of headers for aws auth. All the headers are
      now included in the signature calculation
    - >-
      GH-510 Updated `postman-request` to `v2.81.1-postman.4`, which contains a
      formdata `RangeError` bugfix

7.1.2:
  date: 2017-12-8
  fixed bugs:
    - GH-500 Fixed entrypoint detection error
  docs:
    - GH-491 Updated entrypoint documentation
  chores:
    - GH-498 Cleared OAuth2 state conflicts for duplicate parameters
    - GH-492 Removed redundant AWS auth region fallback
    - GH-487 Accelerated memory leack checker script
    - GH-490 Switched to variable lists from POJOs
    - Updated dependencies

7.1.1:
  date: 2017-11-30
  chores:
    - Dropped support for legacy `serviceName` property in `aws` auth.
    - Updated dependencies.

7.1.0:
  date: 2017-11-21
  new features:
    - Runtime now adds `system: true` to all the query parameters that it sets
    - More useful error messages for assertion failures in legacy `tests`
    - >-
      Digest auth does not attempt retries for invalid
      credentials/configuration. It will continue to retry for missing
      configuration.
    - >-
      Auth will maintain its state across a collection run. Digest auth no
      longer needs to send 2 requests for every digest auth protected item in a
      collection
    - Added support for custom DNS lookup
  breaking changes:
    - |
      `restrictedAddresses` option is now moved to `network` option
        - In v7.0.0
            runner.run(collection, {
                restrictedAddresses: {'x.x.x.x': true}
            }});
        - In v7.1.0
            runner.run(collection, {
                network: {
                    restrictedAddresses: {'x.x.x.x': true}
                }
            }});

7.0.1:
  date: 2017-11-8
  fixed bugs:
    - >-
      Fixed a bug where the assertions for legacy `tests` failures did not have
      an `error` object.
  chores:
    - Updated dependencies

7.0.0:
  date: 2017-11-7
  breaking changes:
    - |
      GH-453,447 Added default timeout value to 3 min.
        - Timeout options take a positive value in milliseconds. e.g.
            runner.run(collection, { timeout: {
                global: 5 * 60 * 1000, // total timeout of 5 minutes
                request: 500, // timeout for individual requests
                script: 200 // timeout for individual scripts
            } });
        - If you are increasing the default timeout for `script`/`request`, make sure `global` timeout is sufficiantly larger than that.
        - Use `0` for no timeout (infinite timeout).
    - |
      The signature for `assertion` and `test` callbacks have changed.
        - The `assertion` callback is now passed with an array of assertions.
        - All assertions, both `pm.test` and legacy `tests` global are now available in the `assertion` callback.
        - The legacy `tests` are no longer available in results parameter in `test` callback.
    - >
      GH-427 The `entrypoint` option now supports selecting requests as well as
      folders
        - To execute a folder/request using id or name
        runner.run(collection, { entrypoint: {
            execute: `${desiredIdOrName}`,
            lookupStrategy: 'idOrName
        }});
        - To execute a folder/request using a path
        runner.run(collection, { entrypoint: {
            execute: `${desiredId}`,
            lookupStrategy: 'path',
            path: ['grand_parent_folder_id', 'parent_folder_id']
        }});
    - >-
      GH-428 Advanced auth flows are now enabled by default(`interactive` flag
      has been removed)
  new features:
    - >-
      GH-424 Added support for collection level variables, collection/folder
      level authentication and scripts
  fixed bugs:
    - >-
      Invalid values in `entrypoint` now results in an error (when
      `abortOnError` is set to `true`)
  chores:
    - Updated dependencies

6.4.2:
  date: 2017-11-2
  new features:
    - GH-435 Added option to blacklist certain IP addresses from being hit
    - GH-436 Improved HTTP request error handling
  chores:
    - Updated dependencies.

6.4.1:
  date: 2017-10-13
  chores:
    - GH-417 Made Bearer token case insensitive
    - Updated dependencies.

6.4.0:
  date: 2017-09-28
  new features:
    - Improved flows for NTLM, Digest, and OAuth2
    - GH-382 Added script timeout option
    - GH-408 Prevented max replay errors from bubling up
    - GH-386 Added support for bearer-token auth
    - GH-373 Added response callback
    - GH-368 All new auth-interface
  fixed bugs:
    - GH-400 Fixed digest-md5-sess
    - GH-402 Fix OAuth1 camelcased timestamp
    - GH-350 Fixed abort-after-pause
  docs:
    - GH-366 Documented exception callback
  chores:
    - GH-360 Shifted the auth sign function from the Collection SDK
    - GH-394 Refurbished interactive mode for auth
    - GH-367 Made basic-auth username optional

6.3.1-2:
  date: 2017-08-28
  fixed bugs:
    - GH-361 Prevented empty/missing request urls from crashing `pm.sendRequest`

6.3.0:
  date: 2017-08-21
  fixed bugs:
    - >-
      GH-358 Updated `postman-sandbox` to `v2.3.2`, which contains a header
      assertion bugfix.

6.2.6:
  date: 2017-08-18
  new features:
    - GH-351 Prevented files from being uploaded via `pm.sendRequest`
    - >-
      GH-345 Queued `pm.sendRequest` through the request command, emitted the
      `request` event
    - GH-337 Prevented headers with falsy keys from being sent with requests
  chores:
    - GH-353,354 Bumped Collection SDK to `v2.1.1` and Sandbox to `v2.3.1`
    - GH-342 Expanded documentation for the `assertion` event
    - GH-327 Ensured that the `tunnel` value is set from the request protocol
    - Updated dependencies

6.2.5:
  date: 2017-07-19
  fixed bugs:
    - >-
      GH-323 Fixed a regression that prevented cookies from being passed across
      requests

6.2.4:
  date: 2017-07-11
  fixed bugs:
    - >-
      Fixed a bug that prevented the `Content-Length` header from being set for
      file uploads in `binary` mode.

6.2.3:
  date: 2017-07-5
  new features:
    - Support for updated `ProxyConfig` from Collection SDK v2.0.0
    - Custom proxies now have higher preference than system proxies

6.2.2:
  date: 2017-06-28
  chores:
    - >-
      GH-298 Bumped Postman Sandbox to v2.3.0, which includes support for
      synchronous csv-parse
    - GH-297 Bumped Postman Collection SDK to v1.2.9, with critical bugfixes
    - Updated other dependencies

6.2.1:
  date: 2017-06-23
  fixed bugs:
    - >-
      Fixed a bug which caused auth variables to not be resolved when sending
      requests

6.2.0:
  date: 2017-06-15
  new features:
    - GH-233 Added support for authorization mechanisms
    - GH-266 Added suport for NTLM auth
    - >
      Runtime now supports another event, `io`, which provides information about
      intermediate requests that may be sent as part of authentication or other
      flows.

        io: function inputOutput (err, cursor, trace, ...otherArgs) {
            // err, cursor: Same as arguments for "start"
            // trace: An object which looks like this:
            // {
            //     -- Indicates the type of IO event, may be HTTP, File, etc. Any requests sent out as a part of
            //     -- auth flows, replays, etc will show up here.
            //     type: 'http',
            //
            //     -- Indicates what this IO event originated from, (collection, auth flows, etc)
            //     source: 'collection
            // }
        }
  chores:
    - Updated dependencies, pruned lodash3
    - GH-281 Used updated Sandbox with momentjs included

6.1.6:
  date: 2017-05-16
  chores:
    - Updated `postman-sandbox` to `v2.1.5`.

6.1.5:
  date: 2017-05-15
  chores:
    - >-
      Updated `postman-sandbox` to `v2.1.4`, which uses
      `postman-collection@1.2.5`

6.1.4:
  date: 2017-05-12
  chores:
    - >-
      Updated `postman-sandbox` to v2.1.3 and `postman-collection` to v1.2.5,
      which introduce `pm.variables` in the scripts

6.1.3:
  date: 2017-05-09
  chores:
    - >-
      Updated `postman-collection` to v1.2.4, which contains a bugfix for
      response size calculation

6.1.2:
  date: 2017-05-08
  fixed bugs:
    - >-
      Ensure that we do not stop the request from being sent if there are errors
      loading certificates or proxies

6.1.1:
  date: 2017-05-08
  fixed bugs:
    - Fixed the behavior for `beforeRequest` and `request` triggers
    - >-
      Updated `postman-collection` to v1.2.3, which contains bugfixes for
      OAuth1, and addition of some helper methods

6.1.0:
  date: 2017-04-25
  new features:
    - Initial version of `pm` API in the sandbox

6.0.1:
  date: 2017-04-10
  fixed bugs:
    - >-
      Fixed a bug that caused script run results to be sent as `POJO`s instead
      of `VariableScope` instances.

6.0.0:
  date: 2017-04-05
  breaking changes:
    - >-
      Updated `postman-collection` to v1.1.0, which contains a bugfix for
      handling multi-valued response headers
    - |
      The structure of script run results has changed

            // v5.x
            run.start({
                prerequest: function (err, cursor, results, item) {
                    var result = results[0].result // changed
                    // 1. result.masked is removed
                    // 2. result.globals now actually holds postman Global VariableScope
                    // 3. result.globals.* have now been moved to result.*
                },
                test: function (err, cursor, results, item) {
                    var result = results[0].result // changed (see below for changes)
                    // 1. result.masked is removed
                    // 2. result.globals now actually holds postman Global VariableScope
                    // 3. result.globals.* have now been moved to result.*
                }
            });

            // v6.x
            run.start({
                prerequest: function (err, cursor, results, item) {
                    // results[0].result now has the following structure:
                    // {
                    //     target: 'prerequest
                    //     environment: <VariableScope>
                    //     globals: <VariableScope>
                    //     data: <Object of data variables>
                    //     return: <contains set next request params, etc>
                    // }
                },
                test: function (err, cursor, results, item) {
                    // results[0].result now has the following structure:
                    // {
                    //     target: 'test
                    //     environment: <VariableScope>
                    //     globals: <VariableScope>
                    //     response: <Response>
                    //     request: <Request>
                    //     data: <Object of data variables>
                    //     cookies: <Array of "Cookie">
                    //     tests: <Object>
                    //     return: <contains set next request params, etc>
                    // }
                }
            });
    - >
      The deprecated parameters for `legacyRequest` & `legacyResponse` are no
      longer provided in the `request` event. Instead, the API now provides
      `cookies`

            // v5.x
            run.start({
                request: function (err, cursor, response, request, item, legacyResponse, legacyRequest) {
                    // do something
                }
            });

            // v6.x
            run.start({
                request: function (err, cursor, response, request, item, cookies) {
                    // you now get Cookies as the last parameter!
                }
            });

5.0.0:
  date: 2017-03-16
  breaking changes:
    - |
      CertificateManager is no longer supported, in favor of certificate list

            // v4.x
            var runner = new Runner();

            runner.run(collection, {
               requester: {
                   certificateManager: myCertManager
               }
            });

            // v5.x
            var runner = new Runner();

            runner.run(collection, {
                certificates: new sdk.CertificateList(/* list */)
            });
    - |
      Proxy handling

            // v4.x
            var runner = new Runner();

            runner.run(collection, {
                requester: {
                    proxyManager: myProxyManager
                }
            });

            // v5.x
            var runner = new Runner();

            runner.run(collection, {
                // Resolves system proxy
                systemProxy: function (url, callback) {
                    return callback(null, new ProxyConfig());
                },
                // static list
                proxies: new ProxyConfigList(/* list */)
            });
    - |
      File resolver (or reader)

            // v4.x
            var runner = new Runner();

            runner.run(collection, {
                requester: {
                    fileResolver: require('fs')
                }
            });

            // v5.x
            var runner = new Runner();

            runner.run(collection, {
                fileResolver: require('fs')
            });

4.1.1:
  date: 2017-03-14
  fixed bugs:
    - Fixed a bug which caused certificate resolution to return empty content
    - Ensure that proxy lookups return a falsey value by default
    - >-
      Updated the version of `postman-sandbox` to v1.0.2, which contains a
      bugfix for undefined values in `tests` object of the sandbox.

4.1.0:
  date: 2017-03-07
  new features:
    - Use `CertificateList` to resolve certificates if provided
  chores:
    - Updated `postman-collection` to v1.0
    - Updated `lodash` to v4.x

4.0.4:
  date: 2017-02-20
  new features:
    - Support for multilevel folders
  chores:
    - Updated `postman-collection` to v0.5.12 which contains minor improvements

4.0.3:
  date: 2017-01-31
  fixed bugs:
    - >-
      Updated `postman-collection` to v0.5.11 which contains bugfixes for UTF-8
      responses, and variables in URL host
    - >-
      Updated `postman-request` which contains a bugfix for URL parameter
      encoding

4.0.2:
  date: 2017-01-06
  fixed bugs:
    - >-
      Updated postman-sandbox to v1.0.1 which fixes issue with runtime not
      initialising in early Node v4.x

4.0.1:
  date: 2017-01-02
  new features:
    - >-
      Improved the proxy handling logic, it now relies on the SDK for correct
      resolution
  breaking changes:
    - Runtime no longer accepts a `proxyManager`

4.0.0:
  date: 2016-12-29
  breaking changes:
    - >-
      Removed the UVM, and started using `postman-sandbox` for script execution,
      which has memory and performance improvements

3.0.10:
  date: 2016-12-15
  fixed bugs:
    - Fixed a bug which caused no headers to be set in the Browser requester

3.0.9:
  date: 2016-12-14
  fixed bugs:
    - >-
      Do not try to set Host and User-Agent headers when sending requests
      through Chrome
    - >-
      Ensure that we do not flood the console with warnings about unsupported
      options

3.0.8:
  date: 2016-12-09
  fixed bugs:
    - >-
      Fixed a bug which caused the `done` event callback to be called twice on
      timeouts

3.0.7:
  date: 2016-11-30
  fixed bugs:
    - >-
      Fixed a bug which caused the cookieJar to be overridden even if it is
      provided

3.0.6:
  date: 2016-11-29
  fixed bugs:
    - >-
      Ensure that we use a default cookiejar in case one is not provided, so
      that they are available in tests

3.0.5:
  date: 2016-11-17
  fixed bugs:
    - >-
      Updated `postman-collection` to v0.5.7 (contains a bugfix for handling
      disabled form parameters)
    - Ensure that the disabled parameters are not sent
  chores:
    - >-
      Use [lodash3](https://www.npmjs.com/package/lodash3) instead of vanilla
      lodash

3.0.4:
  date: 2016-11-17
  chores:
    - >-
      Updated the version of `postman-request` which now conforms to
      specification of WHATWG URL, and correctly encodes URL parameters

3.0.3:
  date: 2016-11-09
  fixed bugs:
    - >-
      Updated the version of `postman-request`, which contains a fix for invalid
      URL param encoding

3.0.2:
  date: 2016-10-14
  fixed bugs:
    - >-
      Updated the version of `postman-collection`, which contains a fix for Hawk
      authentication

3.0.1:
  date: 2016-10-13
  fixed bugs:
    - Ensure that the http reason phrase is bubbled up from the response

3.0.0:
  date: 2016-10-10
  breaking changes:
    - >-
      Changed the runtime API to receive a VariableScope instead of plain object
      for environments and globals
  new features:
    - Restricted scopes of test and pre-request scripts

2.5.4:
  date: 2016-09-28
  fixed bugs:
    - Fixed a bug with comma is not being escaped in query strings

2.5.3:
  date: 2016-09-26
  fixed bugs:
    - >-
      Changed runtime behavior ro allow access to the `window` object on
      browsers
    - Fixed a bug that caused `false` to not be cast to a string in the sandbox
    - Ensure that correct request headers are given out from runtime

2.5.2:
  date: 2016-09-21
  fixed bugs:
    - >-
      Updated version of `postman-collection`, which contains bugfixes for AWS
      Auth and file uploads

2.5.1:
  date: 2016-09-16
  fixed bugs:
    - Fixed a bug that caused utf-8 values to not be encoded properly
    - Updated SDK version to 0.5.0 which contains fixes for AWS auth and OAuth1

2.5.0:
  date: 2016-09-12
  new features:
    - Optimized memory usage by evaluating SugarJS only once per run
    - >-
      Added a workaround for Windows, where the localhost detection for IPv6 was
      not working correctly in some cases

2.4.5:
  date: 2016-08-25
  fixed bugs:
    - >-
      Fixed a bug that caused runtime to crash on invalid file path for formdata
      or binary files

2.4.4:
  date: 2016-08-25
  fixed bugs:
    - Fixed a bug that caused incorrect host headers to be inserted in requests

2.4.3:
  date: 2016-08-23
  fixed bugs:
    - >-
      Use `postman-request` instead of the `request` library, which contains
      fixes to support deflate encoding

2.4.2:
  date: 2016-08-18
  new features:
    - >-
      Runtime now uses the length of iteration data as the default iteration
      count (if data is provided)
    - >-
      Added functionality to bubble up the proxy configuration in the `request`
      event

2.4.1:
  date: 2016-08-12
  fixed bugs:
    - >-
      Fixed a bug that caused the Runtime sandbox to fail when installed with
      npm@2
    - >-
      Updated the proxy fetching logic to use URLs as a string instead of an SDK
      object

2.4.0:
  date: 2016-08-12
  new features:
    - Changes to the Node script sandbox, SugarJS now works correctly.
    - Ensure that `getResponseCookie` is case-insensitive always
    - >-
      Check to ensure that Runtime does not crash if the path for file uploads
      is empty
    - >-
      Ensured that the Accept header is always set (unless the user has
      overridden it)
    - Headers that are added by Runtime are now always bubbled up
    - Added support for specifying a delay between two iterations
    - Requester now has the ability to fetch proxy configuration externally

2.3.2:
  date: 2016-08-05
  new features:
    - Added support for resolving binary files on the fly

2.3.1:
  date: 2016-08-05
  new features:
    - Added support for client side SSL authentication

2.3.0:
  date: 2016-08-04
  new features:
    - Ability to insert delays between requests
    - >-
      Ability to stop a run on any sort of failure (test case failure as well as
      errors)
    - Updated the requester behavior to try IPv6 when the server is "localhost"
    - Added a check to ensure that sandbox globals are filtered

2.2.5:
  date: 2016-07-30
  fixed bugs:
    - >-
      Added support for exposing "responseCookies" array and "getResponseCookie"
      function in the sandbox
    - >-
      Fixed file handling behavior, now the runner will ignore files (with a
      warning) if no fileResolver is provided

2.2.4:
  date: 2016-07-29
  fixed bugs:
    - Fixed a bug that caused non-file form data to be ignored

2.2.3:
  date: 2016-07-29
  fixed bugs:
    - Disabled file uploads if no fileResolver is provided
    - >-
      Ensure that URL encoding is done in an XHR compatible way by the request
      library
    - Allow aborting of individual HTTP requests
    - Parse XHR headers using the Postman Collection SDK
    - Updated the SDK version to v0.4.6

2.2.2:
  date: 2016-07-25
  chores:
    - Updated the version of the request module

2.2.1:
  date: 2016-07-21
  fixed bugs:
    - Allow setting of duplicate headers (same name, but different value)
    - Do not send a request body if the body type is set, but it is empty

2.2.0:
  date: 2016-07-19
  new features:
    - Added an option to abort a run on test failures (as well as errors)
  fixed bugs:
    - Fixed a bug which caused `done` to be called twice if it threw an error

2.1.1:
  date: 2016-07-13
  fixed bugs:
    - >-
      Fixed a bug that caused the Runtime to crash when used with stopOnError
      and with multiple iterations
  chores:
    - Updated to postman-collection@0.4.1
    - Added more test cases

2.1.0:
  date: 2016-07-12
  initial release:
    - Initial Release
