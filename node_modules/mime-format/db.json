{"application/x-www-form-urlencoded": {"type": "text", "format": "plain"}, "application/pdf": {"type": "embed", "format": "pdf"}, "application/ecmascript": {"type": "text", "format": "script"}, "application/javascript": {"type": "text", "format": "script"}, "application/json": {"type": "text", "format": "json"}, "application/json5": {"type": "text", "format": "json"}, "application/jwt": {"type": "text", "format": "jwt"}, "application/xml": {"type": "text", "format": "xml"}, "application/xml-dtd": {"type": "text", "format": "xml"}, "application/xml+dtd": {"type": "text", "format": "xml"}, "application/xml-external-parsed-entity": {"type": "text", "format": "xml"}, "application/xml-patch+xml": {"type": "text", "format": "xml"}, "application/3gpdash-qoe-report+xml": {"type": "text", "format": "xml"}, "application/3gpp-ims+xml": {"type": "text", "format": "xml"}, "application/atom+xml": {"type": "text", "format": "xml"}, "application/atomcat+xml": {"type": "text", "format": "xml"}, "application/atomdeleted+xml": {"type": "text", "format": "xml"}, "application/atomsvc+xml": {"type": "text", "format": "xml"}, "application/auth-policy+xml": {"type": "text", "format": "xml"}, "application/beep+xml": {"type": "text", "format": "xml"}, "application/calendar+xml": {"type": "text", "format": "xml"}, "application/ccmp+xml": {"type": "text", "format": "xml"}, "application/ccxml+xml": {"type": "text", "format": "xml"}, "application/cdfx+xml": {"type": "text", "format": "xml"}, "application/cea-2018+xml": {"type": "text", "format": "xml"}, "application/cellml+xml": {"type": "text", "format": "xml"}, "application/cnrp+xml": {"type": "text", "format": "xml"}, "application/conference-info+xml": {"type": "text", "format": "xml"}, "application/cpl+xml": {"type": "text", "format": "xml"}, "application/csta+xml": {"type": "text", "format": "xml"}, "application/cstadata+xml": {"type": "text", "format": "xml"}, "application/dash+xml": {"type": "text", "format": "xml"}, "application/davmount+xml": {"type": "text", "format": "xml"}, "application/dialog-info+xml": {"type": "text", "format": "xml"}, "application/docbook+xml": {"type": "text", "format": "xml"}, "application/dskpp+xml": {"type": "text", "format": "xml"}, "application/dssc+xml": {"type": "text", "format": "xml"}, "application/emergencycalldata.comment+xml": {"type": "text", "format": "xml"}, "application/emergencycalldata.deviceinfo+xml": {"type": "text", "format": "xml"}, "application/emergencycalldata.providerinfo+xml": {"type": "text", "format": "xml"}, "application/emergencycalldata.serviceinfo+xml": {"type": "text", "format": "xml"}, "application/emergencycalldata.subscriberinfo+xml": {"type": "text", "format": "xml"}, "application/emma+xml": {"type": "text", "format": "xml"}, "application/emotionml+xml": {"type": "text", "format": "xml"}, "application/epp+xml": {"type": "text", "format": "xml"}, "application/fdt+xml": {"type": "text", "format": "xml"}, "application/framework-attributes+xml": {"type": "text", "format": "xml"}, "application/gml+xml": {"type": "text", "format": "xml"}, "application/gpx+xml": {"type": "text", "format": "xml"}, "application/held+xml": {"type": "text", "format": "xml"}, "application/ibe-key-request+xml": {"type": "text", "format": "xml"}, "application/ibe-pkg-reply+xml": {"type": "text", "format": "xml"}, "application/im-iscomposing+xml": {"type": "text", "format": "xml"}, "application/inkml+xml": {"type": "text", "format": "xml"}, "application/its+xml": {"type": "text", "format": "xml"}, "application/kpml-request+xml": {"type": "text", "format": "xml"}, "application/kpml-response+xml": {"type": "text", "format": "xml"}, "application/load-control+xml": {"type": "text", "format": "xml"}, "application/lost+xml": {"type": "text", "format": "xml"}, "application/lostsync+xml": {"type": "text", "format": "xml"}, "application/mads+xml": {"type": "text", "format": "xml"}, "application/marcxml+xml": {"type": "text", "format": "xml"}, "application/mathml+xml": {"type": "text", "format": "xml"}, "application/mathml-content+xml": {"type": "text", "format": "xml"}, "application/mathml-presentation+xml": {"type": "text", "format": "xml"}, "application/mbms-associated-procedure-description+xml": {"type": "text", "format": "xml"}, "application/mbms-deregister+xml": {"type": "text", "format": "xml"}, "application/mbms-envelope+xml": {"type": "text", "format": "xml"}, "application/mbms-msk+xml": {"type": "text", "format": "xml"}, "application/mbms-msk-response+xml": {"type": "text", "format": "xml"}, "application/mbms-protection-description+xml": {"type": "text", "format": "xml"}, "application/mbms-reception-report+xml": {"type": "text", "format": "xml"}, "application/mbms-register+xml": {"type": "text", "format": "xml"}, "application/mbms-register-response+xml": {"type": "text", "format": "xml"}, "application/mbms-schedule+xml": {"type": "text", "format": "xml"}, "application/mbms-user-service-description+xml": {"type": "text", "format": "xml"}, "application/media-policy-dataset+xml": {"type": "text", "format": "xml"}, "application/media_control+xml": {"type": "text", "format": "xml"}, "application/mediaservercontrol+xml": {"type": "text", "format": "xml"}, "application/metalink+xml": {"type": "text", "format": "xml"}, "application/metalink4+xml": {"type": "text", "format": "xml"}, "application/mets+xml": {"type": "text", "format": "xml"}, "application/mods+xml": {"type": "text", "format": "xml"}, "application/mrb-consumer+xml": {"type": "text", "format": "xml"}, "application/mrb-publish+xml": {"type": "text", "format": "xml"}, "application/msc-ivr+xml": {"type": "text", "format": "xml"}, "application/msc-mixer+xml": {"type": "text", "format": "xml"}, "application/nlsml+xml": {"type": "text", "format": "xml"}, "application/oebps-package+xml": {"type": "text", "format": "xml"}, "application/omdoc+xml": {"type": "text", "format": "xml"}, "application/p2p-overlay+xml": {"type": "text", "format": "xml"}, "application/patch-ops-error+xml": {"type": "text", "format": "xml"}, "application/pidf+xml": {"type": "text", "format": "xml"}, "application/pidf-diff+xml": {"type": "text", "format": "xml"}, "application/pls+xml": {"type": "text", "format": "xml"}, "application/poc-settings+xml": {"type": "text", "format": "xml"}, "application/problem+xml": {"type": "text", "format": "xml"}, "application/provenance+xml": {"type": "text", "format": "xml"}, "application/prs.xsf+xml": {"type": "text", "format": "xml"}, "application/pskc+xml": {"type": "text", "format": "xml"}, "application/rdf+xml": {"type": "text", "format": "xml"}, "application/reginfo+xml": {"type": "text", "format": "xml"}, "application/resource-lists+xml": {"type": "text", "format": "xml"}, "application/resource-lists-diff+xml": {"type": "text", "format": "xml"}, "application/rfc+xml": {"type": "text", "format": "xml"}, "application/rlmi+xml": {"type": "text", "format": "xml"}, "application/rls-services+xml": {"type": "text", "format": "xml"}, "application/rsd+xml": {"type": "text", "format": "xml"}, "application/rss+xml": {"type": "text", "format": "xml"}, "application/samlassertion+xml": {"type": "text", "format": "xml"}, "application/samlmetadata+xml": {"type": "text", "format": "xml"}, "application/sbml+xml": {"type": "text", "format": "xml"}, "application/scaip+xml": {"type": "text", "format": "xml"}, "application/sep+xml": {"type": "text", "format": "xml"}, "application/shf+xml": {"type": "text", "format": "xml"}, "application/simple-filter+xml": {"type": "text", "format": "xml"}, "application/smil+xml": {"type": "text", "format": "xml"}, "application/soap+xml": {"type": "text", "format": "xml"}, "application/sparql-results+xml": {"type": "text", "format": "xml"}, "application/spirits-event+xml": {"type": "text", "format": "xml"}, "application/srgs+xml": {"type": "text", "format": "xml"}, "application/sru+xml": {"type": "text", "format": "xml"}, "application/ssdl+xml": {"type": "text", "format": "xml"}, "application/ssml+xml": {"type": "text", "format": "xml"}, "application/tei+xml": {"type": "text", "format": "xml"}, "application/thraud+xml": {"type": "text", "format": "xml"}, "application/ttml+xml": {"type": "text", "format": "xml"}, "application/urc-grpsheet+xml": {"type": "text", "format": "xml"}, "application/urc-ressheet+xml": {"type": "text", "format": "xml"}, "application/urc-targetdesc+xml": {"type": "text", "format": "xml"}, "application/urc-uisocketdesc+xml": {"type": "text", "format": "xml"}, "application/vcard+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp-prose+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp-prose-pc3ch+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.access-transfer-events+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.bsf+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.mid-call+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.sms+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.srvcc-ext+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.srvcc-info+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.state-and-event-info+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp.ussd+xml": {"type": "text", "format": "xml"}, "application/vnd.3gpp2.bcmcsinfo+xml": {"type": "text", "format": "xml"}, "application/vnd.adobe.xdp+xml": {"type": "text", "format": "xml"}, "application/vnd.amundsen.maze+xml": {"type": "text", "format": "xml"}, "application/vnd.apple.installer+xml": {"type": "text", "format": "xml"}, "application/vnd.avistar+xml": {"type": "text", "format": "xml"}, "application/vnd.balsamiq.bmml+xml": {"type": "text", "format": "xml"}, "application/vnd.biopax.rdf+xml": {"type": "text", "format": "xml"}, "application/vnd.chemdraw+xml": {"type": "text", "format": "xml"}, "application/vnd.citationstyles.style+xml": {"type": "text", "format": "xml"}, "application/vnd.criticaltools.wbs+xml": {"type": "text", "format": "xml"}, "application/vnd.ctct.ws+xml": {"type": "text", "format": "xml"}, "application/vnd.cyan.dean.root+xml": {"type": "text", "format": "xml"}, "application/vnd.dece.ttml+xml": {"type": "text", "format": "xml"}, "application/vnd.dm.delegation+xml": {"type": "text", "format": "xml"}, "application/vnd.dvb.notif-aggregate-root+xml": {"type": "text", "format": "xml"}, "application/vnd.dvb.notif-container+xml": {"type": "text", "format": "xml"}, "application/vnd.dvb.notif-generic+xml": {"type": "text", "format": "xml"}, "application/vnd.dvb.notif-ia-msglist+xml": {"type": "text", "format": "xml"}, "application/vnd.dvb.notif-ia-registration-request+xml": {"type": "text", "format": "xml"}, "application/vnd.dvb.notif-ia-registration-response+xml": {"type": "text", "format": "xml"}, "application/vnd.dvb.notif-init+xml": {"type": "text", "format": "xml"}, "application/vnd.emclient.accessrequest+xml": {"type": "text", "format": "xml"}, "application/vnd.eprints.data+xml": {"type": "text", "format": "xml"}, "application/vnd.eszigno3+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.aoc+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.cug+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvcommand+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvdiscovery+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvprofile+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvsad-bc+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvsad-cod+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvsad-npvr+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvservice+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvsync+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.iptvueprofile+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.mcid+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.overload-control-policy-dataset+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.pstn+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.sci+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.simservs+xml": {"type": "text", "format": "xml"}, "application/vnd.etsi.tsl+xml": {"type": "text", "format": "xml"}, "application/vnd.geocube+xml": {"type": "text", "format": "xml"}, "application/vnd.google-earth.kml+xml": {"type": "text", "format": "xml"}, "application/vnd.gov.sk.e-form+xml": {"type": "text", "format": "xml"}, "application/vnd.gov.sk.xmldatacontainer+xml": {"type": "text", "format": "xml"}, "application/vnd.hal+xml": {"type": "text", "format": "xml"}, "application/vnd.handheld-entertainment+xml": {"type": "text", "format": "xml"}, "application/vnd.informedcontrol.rms+xml": {"type": "text", "format": "xml"}, "application/vnd.infotech.project+xml": {"type": "text", "format": "xml"}, "application/vnd.iptc.g2.catalogitem+xml": {"type": "text", "format": "xml"}, "application/vnd.iptc.g2.conceptitem+xml": {"type": "text", "format": "xml"}, "application/vnd.iptc.g2.knowledgeitem+xml": {"type": "text", "format": "xml"}, "application/vnd.iptc.g2.newsitem+xml": {"type": "text", "format": "xml"}, "application/vnd.iptc.g2.newsmessage+xml": {"type": "text", "format": "xml"}, "application/vnd.iptc.g2.packageitem+xml": {"type": "text", "format": "xml"}, "application/vnd.iptc.g2.planningitem+xml": {"type": "text", "format": "xml"}, "application/vnd.irepository.package+xml": {"type": "text", "format": "xml"}, "application/vnd.las.las+xml": {"type": "text", "format": "xml"}, "application/vnd.liberty-request+xml": {"type": "text", "format": "xml"}, "application/vnd.llamagraphics.life-balance.exchange+xml": {"type": "text", "format": "xml"}, "application/vnd.marlin.drm.actiontoken+xml": {"type": "text", "format": "xml"}, "application/vnd.marlin.drm.conftoken+xml": {"type": "text", "format": "xml"}, "application/vnd.marlin.drm.license+xml": {"type": "text", "format": "xml"}, "application/vnd.mozilla.xul+xml": {"type": "text", "format": "xml"}, "application/vnd.ms-office.activex+xml": {"type": "text", "format": "xml"}, "application/vnd.ms-playready.initiator+xml": {"type": "text", "format": "xml"}, "application/vnd.ms-printdevicecapabilities+xml": {"type": "text", "format": "xml"}, "application/vnd.ms-printing.printticket+xml": {"type": "text", "format": "xml"}, "application/vnd.ms-printschematicket+xml": {"type": "text", "format": "xml"}, "application/vnd.nokia.conml+xml": {"type": "text", "format": "xml"}, "application/vnd.nokia.iptv.config+xml": {"type": "text", "format": "xml"}, "application/vnd.nokia.landmark+xml": {"type": "text", "format": "xml"}, "application/vnd.nokia.landmarkcollection+xml": {"type": "text", "format": "xml"}, "application/vnd.nokia.n-gage.ac+xml": {"type": "text", "format": "xml"}, "application/vnd.nokia.pcd+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.contentaccessdownload+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.contentaccessstreaming+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.dae.svg+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.dae.xhtml+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.mippvcontrolmessage+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.spdiscovery+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.spdlist+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.ueprofile+xml": {"type": "text", "format": "xml"}, "application/vnd.oipf.userprofile+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.bcast.associated-procedure-parameter+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.bcast.drm-trigger+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.bcast.imd+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.bcast.notification+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.bcast.sgdd+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.bcast.smartcard-trigger+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.bcast.sprov+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.cab-address-book+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.cab-feature-handler+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.cab-pcc+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.cab-subs-invite+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.cab-user-prefs+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.dd2+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.drm.risd+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.group-usage-list+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.pal+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.poc.detailed-progress-report+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.poc.final-report+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.poc.groups+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.poc.invocation-descriptor+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.poc.optimized-progress-report+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.scidm.messages+xml": {"type": "text", "format": "xml"}, "application/vnd.oma.xcap-directory+xml": {"type": "text", "format": "xml"}, "application/vnd.omads-email+xml": {"type": "text", "format": "xml"}, "application/vnd.omads-file+xml": {"type": "text", "format": "xml"}, "application/vnd.omads-folder+xml": {"type": "text", "format": "xml"}, "application/vnd.openblox.game+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.custom-properties+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.customxmlproperties+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.drawing+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.drawingml.chart+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.extended-properties+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.comments+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.presprops+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.slide+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.tags+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.template.main+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.theme+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.themeoverride+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-package.core-properties+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml": {"type": "text", "format": "xml"}, "application/vnd.openxmlformats-package.relationships+xml": {"type": "text", "format": "xml"}, "application/vnd.otps.ct-kip+xml": {"type": "text", "format": "xml"}, "application/vnd.paos+xml": {"type": "text", "format": "xml"}, "application/vnd.poc.group-advertisement+xml": {"type": "text", "format": "xml"}, "application/vnd.pwg-xhtml-print+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.moml+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-audit+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-audit-conf+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-audit-conn+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-audit-dialog+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-audit-stream+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-conf+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-dialog+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-dialog-base+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-dialog-fax-detect+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-dialog-fax-sendrecv+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-dialog-group+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-dialog-speech+xml": {"type": "text", "format": "xml"}, "application/vnd.radisys.msml-dialog-transform+xml": {"type": "text", "format": "xml"}, "application/vnd.recordare.musicxml+xml": {"type": "text", "format": "xml"}, "application/vnd.route66.link66+xml": {"type": "text", "format": "xml"}, "application/vnd.software602.filler.form+xml": {"type": "text", "format": "xml"}, "application/vnd.solent.sdkm+xml": {"type": "text", "format": "xml"}, "application/vnd.sun.wadl+xml": {"type": "text", "format": "xml"}, "application/vnd.syncml+xml": {"type": "text", "format": "xml"}, "application/vnd.syncml.dm+xml": {"type": "text", "format": "xml"}, "application/vnd.syncml.dmddf+xml": {"type": "text", "format": "xml"}, "application/vnd.syncml.dmtnds+xml": {"type": "text", "format": "xml"}, "application/vnd.tmd.mediaflex.api+xml": {"type": "text", "format": "xml"}, "application/vnd.uoml+xml": {"type": "text", "format": "xml"}, "application/vnd.wv.csp+xml": {"type": "text", "format": "xml"}, "application/vnd.wv.ssp+xml": {"type": "text", "format": "xml"}, "application/vnd.xmi+xml": {"type": "text", "format": "xml"}, "application/vnd.yamaha.openscoreformat.osfpvg+xml": {"type": "text", "format": "xml"}, "application/vnd.zzazz.deck+xml": {"type": "text", "format": "xml"}, "application/voicexml+xml": {"type": "text", "format": "xml"}, "application/watcherinfo+xml": {"type": "text", "format": "xml"}, "application/wsdl+xml": {"type": "text", "format": "xml"}, "application/wspolicy+xml": {"type": "text", "format": "xml"}, "application/x-dtbncx+xml": {"type": "text", "format": "xml"}, "application/x-dtbook+xml": {"type": "text", "format": "xml"}, "application/x-dtbresource+xml": {"type": "text", "format": "xml"}, "application/x-xliff+xml": {"type": "text", "format": "xml"}, "application/xacml+xml": {"type": "text", "format": "xml"}, "application/xaml+xml": {"type": "text", "format": "xml"}, "application/xcap-att+xml": {"type": "text", "format": "xml"}, "application/xcap-caps+xml": {"type": "text", "format": "xml"}, "application/xcap-diff+xml": {"type": "text", "format": "xml"}, "application/xcap-el+xml": {"type": "text", "format": "xml"}, "application/xcap-error+xml": {"type": "text", "format": "xml"}, "application/xcap-ns+xml": {"type": "text", "format": "xml"}, "application/xcon-conference-info+xml": {"type": "text", "format": "xml"}, "application/xcon-conference-info-diff+xml": {"type": "text", "format": "xml"}, "application/xenc+xml": {"type": "text", "format": "xml"}, "application/xhtml+xml": {"type": "text", "format": "xml"}, "application/xhtml-voice+xml": {"type": "text", "format": "xml"}, "application/xmpp+xml": {"type": "text", "format": "xml"}, "application/xop+xml": {"type": "text", "format": "xml"}, "application/xproc+xml": {"type": "text", "format": "xml"}, "application/xslt+xml": {"type": "text", "format": "xml"}, "application/xspf+xml": {"type": "text", "format": "xml"}, "application/xv+xml": {"type": "text", "format": "xml"}, "application/yin+xml": {"type": "text", "format": "xml"}, "message/imdn+xml": {"type": "text", "format": "xml"}, "model/vnd.collada+xml": {"type": "text", "format": "xml"}, "model/vnd.moml+xml": {"type": "text", "format": "xml"}, "model/x3d+xml": {"type": "text", "format": "xml"}, "application/alto-costmap+json": {"type": "text", "format": "json"}, "application/alto-costmapfilter+json": {"type": "text", "format": "json"}, "application/alto-directory+json": {"type": "text", "format": "json"}, "application/alto-endpointcost+json": {"type": "text", "format": "json"}, "application/alto-endpointcostparams+json": {"type": "text", "format": "json"}, "application/alto-endpointprop+json": {"type": "text", "format": "json"}, "application/alto-endpointpropparams+json": {"type": "text", "format": "json"}, "application/alto-error+json": {"type": "text", "format": "json"}, "application/alto-networkmap+json": {"type": "text", "format": "json"}, "application/alto-networkmapfilter+json": {"type": "text", "format": "json"}, "application/calendar+json": {"type": "text", "format": "json"}, "application/coap-group+json": {"type": "text", "format": "json"}, "application/csvm+json": {"type": "text", "format": "json"}, "application/jose+json": {"type": "text", "format": "json"}, "application/jrd+json": {"type": "text", "format": "json"}, "application/json-patch+json": {"type": "text", "format": "json"}, "application/jsonml+json": {"type": "text", "format": "json"}, "application/jwk+json": {"type": "text", "format": "json"}, "application/jwk-set+json": {"type": "text", "format": "json"}, "application/ld+json": {"type": "text", "format": "json"}, "application/manifest+json": {"type": "text", "format": "json"}, "application/merge-patch+json": {"type": "text", "format": "json"}, "application/ppsp-tracker+json": {"type": "text", "format": "json"}, "application/problem+json": {"type": "text", "format": "json"}, "application/rdap+json": {"type": "text", "format": "json"}, "application/reputon+json": {"type": "text", "format": "json"}, "application/scim+json": {"type": "text", "format": "json"}, "application/vcard+json": {"type": "text", "format": "json"}, "application/vnd.api+json": {"type": "text", "format": "json"}, "application/vnd.bekitzur-stech+json": {"type": "text", "format": "json"}, "application/vnd.collection+json": {"type": "text", "format": "json"}, "application/vnd.collection.doc+json": {"type": "text", "format": "json"}, "application/vnd.collection.next+json": {"type": "text", "format": "json"}, "application/vnd.coreos.ignition+json": {"type": "text", "format": "json"}, "application/vnd.document+json": {"type": "text", "format": "json"}, "application/vnd.drive+json": {"type": "text", "format": "json"}, "application/vnd.geo+json": {"type": "text", "format": "json"}, "application/vnd.hal+json": {"type": "text", "format": "json"}, "application/vnd.heroku+json": {"type": "text", "format": "json"}, "application/vnd.hyperdrive+json": {"type": "text", "format": "json"}, "application/vnd.ims.lis.v2.result+json": {"type": "text", "format": "json"}, "application/vnd.ims.lti.v2.toolconsumerprofile+json": {"type": "text", "format": "json"}, "application/vnd.ims.lti.v2.toolproxy+json": {"type": "text", "format": "json"}, "application/vnd.ims.lti.v2.toolproxy.id+json": {"type": "text", "format": "json"}, "application/vnd.ims.lti.v2.toolsettings+json": {"type": "text", "format": "json"}, "application/vnd.ims.lti.v2.toolsettings.simple+json": {"type": "text", "format": "json"}, "application/vnd.mason+json": {"type": "text", "format": "json"}, "application/vnd.micro+json": {"type": "text", "format": "json"}, "application/vnd.miele+json": {"type": "text", "format": "json"}, "application/vnd.oftn.l10n+json": {"type": "text", "format": "json"}, "application/vnd.oracle.resource+json": {"type": "text", "format": "json"}, "application/vnd.pagerduty+json": {"type": "text", "format": "json"}, "application/vnd.siren+json": {"type": "text", "format": "json"}, "application/vnd.vel+json": {"type": "text", "format": "json"}, "application/vnd.xacml+json": {"type": "text", "format": "json"}, "application/x-web-app-manifest+json": {"type": "text", "format": "json"}, "application/ogg": {"type": "audio", "format": "ogg"}}