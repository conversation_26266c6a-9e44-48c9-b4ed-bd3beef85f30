# newman:ubuntu

This image runs newman on node v16 on Ubuntu 22.04

Build the image,

```terminal
docker build -t postman/newman:ubuntu --build-arg NEWMAN_VERSION="full semver version" .
```

Or get it from [docker hub](https://registry.hub.docker.com/u/postman/newman:ubuntu/)

```terminal
docker pull postman/newman:ubuntu
```

Then run it

```terminal
docker --volume="/home/<USER>/collections:/etc/newman" -t postman/newman:ubuntu run JSONBlobCoreAPI.json.postman_collection -r json --reporter-json-export newman-report.json
```
For newman-docker to be able to use collections and environment files saved on the host machine, and to save reports generated by newman, a directory containing the collection and environment needs to be mounted on to the docker instance on run time, preferably at `/etc/newman`, which is the default working directory. If you mount to a different location, then
  - You can either pass the full path to your collection and environment files to newman. For instance, if you mount to `/etc/newman`,

```terminal
docker --volume="/home/<USER>/collection:/etc/newman" -t postman/newman:ubuntu run JSONBlobCoreAPI.json.postman_collection -r json --reporter-json-export newman-report.json
```
  - You can change the working directory while running the image to the location you mounted to, using the `-w` or `--workdir` flag.

```terminal
docker run --volume="/home/<USER>/collections:/etc/newman" -t postman/newman:ubuntu run JSONBlobCoreAPI.json.postman_collection -r json --reporter-json-export newman-report.json
```

In case you don't need to save newman's report to the host, and your collection is available online and it does not require any environment, then you can forgo mounting your collections directory, and directly pass the collection url to newman

```terminal
docker run -t postman/newman:ubuntu run https://www.getpostman.com/collections/8a0c9bc08f062d12dcda
```
