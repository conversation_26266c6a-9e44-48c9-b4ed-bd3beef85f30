exports['az'] = require('./locales/az');
exports['ar'] = require('./locales/ar');
exports['cz'] = require('./locales/cz');
exports['de'] = require('./locales/de');
exports['de_AT'] = require('./locales/de_AT');
exports['de_CH'] = require('./locales/de_CH');
exports['en'] = require('./locales/en');
exports['en_AU'] = require('./locales/en_AU');
exports['en_AU_ocker'] = require('./locales/en_AU_ocker');
exports['en_BORK'] = require('./locales/en_BORK');
exports['en_CA'] = require('./locales/en_CA');
exports['en_GB'] = require('./locales/en_GB');
exports['en_IE'] = require('./locales/en_IE');
exports['en_IND'] = require('./locales/en_IND');
exports['en_US'] = require('./locales/en_US');
exports['en_ZA'] = require('./locales/en_ZA');
exports['es'] = require('./locales/es');
exports['es_MX'] = require('./locales/es_MX');
exports['he'] = require('./locales/he');
exports['fa'] = require('./locales/fa');
exports['fi'] = require('./locales/fi');
exports['fr'] = require('./locales/fr');
exports['fr_CA'] = require('./locales/fr_CA');
exports['fr_CH'] = require('./locales/fr_CH');
exports['ge'] = require('./locales/ge');
exports['hy'] = require('./locales/hy');
exports['hr'] = require('./locales/hr');
exports['id_ID'] = require('./locales/id_ID');
exports['it'] = require('./locales/it');
exports['ja'] = require('./locales/ja');
exports['ko'] = require('./locales/ko');
exports['nb_NO'] = require('./locales/nb_NO');
exports['ne'] = require('./locales/ne');
exports['nl'] = require('./locales/nl');
exports['nl_BE'] = require('./locales/nl_BE');
exports['pl'] = require('./locales/pl');
exports['pt_BR'] = require('./locales/pt_BR');
exports['pt_PT'] = require('./locales/pt_PT');
exports['ro'] = require('./locales/ro');
exports['ru'] = require('./locales/ru');
exports['sk'] = require('./locales/sk');
exports['sv'] = require('./locales/sv');
exports['tr'] = require('./locales/tr');
exports['uk'] = require('./locales/uk');
exports['vi'] = require('./locales/vi');
exports['zh_CN'] = require('./locales/zh_CN');
exports['zh_TW'] = require('./locales/zh_TW');
