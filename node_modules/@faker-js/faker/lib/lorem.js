
/**
 *
 * @namespace faker.lorem
 */
var Lorem = function (faker) {
  var self = this;
  var Helpers = faker.helpers;

  /**
   * generates a word of a specified length
   *
   * @method faker.lorem.word
   * @param {number} length length of the word that should be returned. Defaults to a random length
   */
  self.word = function (length) {
    var hasRightLength = function(word) { return word.length === length; };
    var properLengthWords;
    if(typeof length === 'undefined') {
      properLengthWords = faker.definitions.lorem.words;
    } else {
      properLengthWords = faker.definitions.lorem.words.filter(hasRightLength);
    }
    return faker.random.arrayElement(properLengthWords);
  };

  /**
   * generates a space separated list of words
   *
   * @method faker.lorem.words
   * @param {number} num number of words, defaults to 3
   */
  self.words = function (num) {
    if (typeof num == 'undefined') { num = 3; }
    var words = [];
    for (var i = 0; i < num; i++) {
      words.push(faker.lorem.word());
    }
    return words.join(' ');
  };

  /**
   * sentence
   *
   * @method faker.lorem.sentence
   * @param {number} wordCount defaults to a random number between 3 and 10
   * @param {number} range
   */
  self.sentence = function (wordCount, range) {
    if (typeof wordCount == 'undefined') { wordCount = faker.datatype.number({ min: 3, max: 10 }); }
    // if (typeof range == 'undefined') { range = 7; }

    // strange issue with the node_min_test failing for captialize, please fix and add faker.lorem.back
    //return  faker.lorem.words(wordCount + Helpers.randomNumber(range)).join(' ').capitalize();

    var sentence = faker.lorem.words(wordCount);
    return sentence.charAt(0).toUpperCase() + sentence.slice(1) + '.';
  };

  /**
   * slug
   *
   * @method faker.lorem.slug
   * @param {number} wordCount number of words, defaults to 3
   */
  self.slug = function (wordCount) {
    var words = faker.lorem.words(wordCount);
    return Helpers.slugify(words);
  };

  /**
   * sentences
   *
   * @method faker.lorem.sentences
   * @param {number} sentenceCount defautls to a random number between 2 and 6
   * @param {string} separator defaults to `' '`
   */
  self.sentences = function (sentenceCount, separator) {
    if (typeof sentenceCount === 'undefined') { sentenceCount = faker.datatype.number({ min: 2, max: 6 });}
    if (typeof separator == 'undefined') { separator = " "; }
    var sentences = [];
    for (sentenceCount; sentenceCount > 0; sentenceCount--) {
      sentences.push(faker.lorem.sentence());
    }
    return sentences.join(separator);
  };

  /**
   * paragraph
   *
   * @method faker.lorem.paragraph
   * @param {number} sentenceCount defaults to 3
   */
  self.paragraph = function (sentenceCount) {
    if (typeof sentenceCount == 'undefined') { sentenceCount = 3; }
    return faker.lorem.sentences(sentenceCount + faker.datatype.number(3));
  };

  /**
   * paragraphs
   *
   * @method faker.lorem.paragraphs
   * @param {number} paragraphCount defaults to 3
   * @param {string} separator defaults to `'\n \r'`
   */
  self.paragraphs = function (paragraphCount, separator) {
    if (typeof separator === "undefined") {
      separator = "\n \r";
    }
    if (typeof paragraphCount == 'undefined') { paragraphCount = 3; }
    var paragraphs = [];
    for (paragraphCount; paragraphCount > 0; paragraphCount--) {
      paragraphs.push(faker.lorem.paragraph());
    }
    return paragraphs.join(separator);
  }

  /**
   * returns random text based on a random lorem method
   *
   * @method faker.lorem.text
   * @param {number} times
   */
  self.text = function loremText (times) {
    var loremMethods = ['lorem.word', 'lorem.words', 'lorem.sentence', 'lorem.sentences', 'lorem.paragraph', 'lorem.paragraphs', 'lorem.lines'];
    var randomLoremMethod = faker.random.arrayElement(loremMethods);
    return faker.fake('{{' + randomLoremMethod + '}}');
  };

  /**
   * returns lines of lorem separated by `'\n'`
   *
   * @method faker.lorem.lines
   * @param {number} lineCount defaults to a random number between 1 and 5
   */
  self.lines = function lines (lineCount) {
    if (typeof lineCount === 'undefined') { lineCount = faker.datatype.number({ min: 1, max: 5 });}
    return faker.lorem.sentences(lineCount, '\n')
  };

  return self;
};


module["exports"] = Lorem;
