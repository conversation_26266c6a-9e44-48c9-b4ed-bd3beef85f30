/**
 *
 * @namespace faker.commerce
 */
var Commerce = function (faker) {
  var self = this;

  /**
   * color
   *
   * @method faker.commerce.color
   */
  self.color = function() {
    return faker.random.arrayElement(faker.definitions.commerce.color);
  };

  /**
   * department
   *
   * @method faker.commerce.department
   */
  self.department = function() {
    return faker.random.arrayElement(faker.definitions.commerce.department);
  };

  /**
   * productName
   *
   * @method faker.commerce.productName
   */
  self.productName = function() {
    return faker.commerce.productAdjective() + " " +
              faker.commerce.productMaterial() + " " +
              faker.commerce.product();
  };

  /**
   * price
   *
   * @method faker.commerce.price
   * @param {number} min
   * @param {number} max
   * @param {number} dec
   * @param {string} symbol
   *
   * @return {string}
   */
  self.price = function(min, max, dec, symbol) {
    min = min || 1;
    max = max || 1000;
    dec = dec === undefined ? 2 : dec;
    symbol = symbol || '';

    if (min < 0 || max < 0) {
      return symbol + 0.00;
    }

    var randValue = faker.datatype.number({ max: max, min: min });

    return symbol + (Math.round(randValue * Math.pow(10, dec)) / Math.pow(10, dec)).toFixed(dec);
  };

  /*
  self.categories = function(num) {
      var categories = [];

      do {
          var category = faker.random.arrayElement(faker.definitions.commerce.department);
          if(categories.indexOf(category) === -1) {
              categories.push(category);
          }
      } while(categories.length < num);

      return categories;
  };

  */
  /*
  self.mergeCategories = function(categories) {
      var separator = faker.definitions.separator || " &";
      // TODO: find undefined here
      categories = categories || faker.definitions.commerce.categories;
      var commaSeparated = categories.slice(0, -1).join(', ');

      return [commaSeparated, categories[categories.length - 1]].join(separator + " ");
  };
  */

  /**
   * productAdjective
   *
   * @method faker.commerce.productAdjective
   */
  self.productAdjective = function() {
    return faker.random.arrayElement(faker.definitions.commerce.product_name.adjective);
  };

  /**
   * productMaterial
   *
   * @method faker.commerce.productMaterial
   */
  self.productMaterial = function() {
    return faker.random.arrayElement(faker.definitions.commerce.product_name.material);
  };

  /**
   * product
   *
   * @method faker.commerce.product
   */
  self.product = function() {
    return faker.random.arrayElement(faker.definitions.commerce.product_name.product);
  };

  /**
   * productDescription
   *
   * @method faker.commerce.productDescription
   */
  self.productDescription = function() {
    return faker.random.arrayElement(faker.definitions.commerce.product_description);
  };

  return self;
};

module['exports'] = Commerce;
