package org.lenskart.core.util;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.CartPathConstants;
import org.lenskart.core.constant.ExpressDeliveryPathConstants;
import org.lenskart.pojo.cart.FittingTypeEnum;
import org.lenskart.pojo.cart.UpdateItem;
import org.testng.Assert;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import com.lenskart.juno.schema.v2.cart.AbandonedLead;
import com.lenskart.juno.schema.v2.cart.CartItem;
import com.lenskart.juno.schema.v2.cart.CartItems;
import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.AmountBulkOrder;
import com.lenskart.juno.schema.v2.common.GiftMessage;
import com.lenskart.juno.schema.v2.common.Hto;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;
import com.utilities.RestAssuredUtils;

import io.restassured.response.Response;

public class CartUtil {

	private final static String createCartURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.CreateCart)
			.trim();
	private final static String createCartURL_bULKITEMS = (Environments.SERVICES_ENVIRONMENT
			+ CartPathConstants.CreateCartBulkItems).trim();
	private final static String getCartURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart).trim();
	private final static String updateCartStatusURL = (Environments.SERVICES_ENVIRONMENT
			+ CartPathConstants.UpdateCartStatus).trim();
	private final static String addCouponURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddCoupon).trim();
	private final static List<String> x_Api_Client = Arrays.asList("android", "Desktop", "MobileSite", "ios");
	private final static String saveAddressURL = (Environments.SERVICES_ENVIRONMENT
			+ CartPathConstants.SaveShippingAddress).trim();
	private final static String addGVURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddGV).trim();
	private final static String removeGVURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.RemoveGV).trim();
	private final static String removeSCURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.RemoveStoreCredit)
			.trim();
	private final static String addStoreCreditURL = (Environments.SERVICES_ENVIRONMENT
			+ CartPathConstants.AddStoreCredit).trim();
	private final static String addStepForAbandonedLeadURL = (Environments.SERVICES_ENVIRONMENT
			+ CartPathConstants.addStepForAbandonedLead).trim();
	private final static String getDeliveryOptionsURL = (Environments.SERVICES_ENVIRONMENT
			+ ExpressDeliveryPathConstants.GetDeliveryOptions).trim();
	private final static String updateDeliveryOptionURL = (Environments.SERVICES_ENVIRONMENT
			+ ExpressDeliveryPathConstants.updateDeliveryOptions).trim();

	private final static String increaseItemCountURL = Environments.SERVICES_ENVIRONMENT
			+ CartPathConstants.GATEWAY_INCREASE_CART_ITEM_COUNT;

	private static final Logger log = Logger.getLogger(CartUtil.class);
	private static JSONObject reqJsonObject = null;
	private static String mongoCartCollectionName = "cart";
	// private static JSONObject cart_response = null;
	private static JaxbAnnotationModule module;
	private static ObjectMapper objectMapper;

	static {
		module = new JaxbAnnotationModule();
		objectMapper = new ObjectMapper();
		objectMapper.registerModule(module);
		objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
		objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}

	private static MongoConnectionUtility mangoCollectionName = null;
	private static MySQLConnectionUtility mySqlCollectionName = null;

	public static String getCartCollectionName() {
		return mongoCartCollectionName;
	}

	public static MongoConnectionUtility getCartMongoConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("CartService");
		mangoCollectionName = new MongoConnectionUtility(pf);
		return mangoCollectionName;
	}

	public static MySQLConnectionUtility getCartMysqlConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("CartService");
		mySqlCollectionName = new MySQLConnectionUtility(pf);
		return mySqlCollectionName;
	}

	public static List<Document> getCartDetails(Map<String, Object> parms) throws Exception {
		List<Document> get_cart_details = mangoCollectionName.executeQuery(getCartCollectionName(), parms);
		return get_cart_details;
	}

	public static List<NameValuePair> headers(String sessionToken, String xApiClient) {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		return headers;
	}

	public static JSONObject createCart(String sessionToken, String productId, String xApiClient) throws Exception {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		// Header
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		String json = objectMapper.writeValueAsString(reqObj);
		reqJsonObject = new JSONObject(json);

		log.debug("-----------Create cart request----------");
		log.debug("Request URL" + createCartURL);
		log.debug("Request Object" + reqJsonObject);
		log.debug("Header" + headers);
		log.debug("----------------------------------------");

		httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
		log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
		cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Add to cart is failing");
		return cart_response;
	}

	public static JSONObject createCart_SG(String sessionToken, String productId, String xApiClient) throws Exception {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		// Header
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-Country-Code", "SG"));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		String json = objectMapper.writeValueAsString(reqObj);
		reqJsonObject = new JSONObject(json);

		log.debug("-----------Create cart request----------");
		log.debug("Request URL" + createCartURL);
		log.debug("Request Object" + reqJsonObject);
		log.debug("Header" + headers);
		log.debug("----------------------------------------");

		httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
		log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
		cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Add to cart is failing");
		return cart_response;
	}

	public static JSONObject HTOGenericCart(String SessionToken, String xApiClient, String city, String apointmentDate,
			String LenskartAtHome, String SlotId, String CartTotalAmount) {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			JSONObject sessionJsonObject = SessionUtil.getMeSessionDetails(SessionToken);
			JSONObject resultObject = sessionJsonObject.getJSONObject("result");
			JSONObject attrsObject = resultObject.getJSONObject("attrs");
			try {
				if (!attrsObject.getJSONObject("cartIds").toString().isEmpty()) {
					JSONObject cartIdArray = attrsObject.getJSONObject("cartIds");
					if (cartIdArray.length() != 0) {
						log.debug("-------------------------UpdateCartStatusToInActive ----------------------------");
						CartUtil.cartStatus_InActive(SessionToken, "ios");
						Thread.sleep(500);
					}
				}
			} catch (Exception e) {
				log.debug("CartId not there w.r.t Login ID");
			}
			CartItem reqObj = new CartItem();
			Hto htoObj = new Hto();
			if (!((city.contentEquals("null")) || (city.contentEquals(""))))
				htoObj.setCity(city);
			if (!((apointmentDate.contentEquals("null")) || (apointmentDate.contentEquals(""))))
				htoObj.setDate(java.time.LocalDate.now().plusDays(Long.parseLong(apointmentDate)).toString());
			else
				htoObj.setDate(java.time.LocalDate.now().toString());

			if (!((LenskartAtHome.contentEquals("null")) || (LenskartAtHome.contentEquals(""))))
				htoObj.setLenskartAtHome(LenskartAtHome);

			if (!((SlotId.contentEquals("null")) || (SlotId.contentEquals(""))))
				htoObj.setSlotId(Integer.valueOf(SlotId));

			reqObj.setHto(htoObj);

			reqObj.setProductId((long) 47552);

			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("Accept", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			log.debug(createCartURL);
			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("HTO CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;
	}

	public static JSONObject HTOCart(String SessionToken, String xApiClient) {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			JSONObject sessionJsonObject = SessionUtil.getMeSessionDetails(SessionToken);
			JSONObject resultObject = sessionJsonObject.getJSONObject("result");
			JSONObject attrsObject = resultObject.getJSONObject("attrs");
			try {
				if (!attrsObject.getJSONObject("cartIds").toString().isEmpty()) {
					JSONObject cartIdArray = attrsObject.getJSONObject("cartIds");
					if (cartIdArray.length() != 0) {
						log.debug("-------------------------UpdateCartStatusToInActive ----------------------------");
						CartUtil.cartStatus_InActive(SessionToken, "ios");
						Thread.sleep(500);
					}
				}
			} catch (Exception e) {
				log.debug("CartId not there w.r.t Login ID");
			}
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			CartItem reqObj = new CartItem();
			// Set HTO Object
			Hto htoObj = new Hto();
			htoObj.setCity("Faridabad");
			htoObj.setDate("2017-04-13");
			htoObj.setLenskartAtHome("CASE3_HEC");
			htoObj.setSlotId(6729405);
			reqObj.setHto(htoObj);
			// Set HTO Product ID
			reqObj.setProductId(Long.parseLong("47552"));
			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);
			log.debug("-----------Create cart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");
			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("HTO CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Session Token: " + SessionToken
					+ "\nHTO Cart API:" + GenericUtil.printAPICallDetails(createCartURL, reqJsonObject, cart_response));
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;

	}

	public static JSONObject HTOCartWithStoreInventory(String SessionToken, String xApiClient) {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			JSONObject sessionJsonObject = SessionUtil.getMeSessionDetails(SessionToken);
			JSONObject resultObject = sessionJsonObject.getJSONObject("result");
			JSONObject attrsObject = resultObject.getJSONObject("attrs");
			try {
				if (!attrsObject.getJSONObject("cartIds").toString().isEmpty()) {
					JSONObject cartIdArray = attrsObject.getJSONObject("cartIds");
					if (cartIdArray.length() != 0) {
						log.debug("-------------------------UpdateCartStatusToInActive ----------------------------");
						CartUtil.cartStatus_InActive(SessionToken, "ios");
						Thread.sleep(500);
					}
				}
			} catch (Exception e) {
				log.debug("CartId not there w.r.t Login ID");
			}
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			CartItem reqObj = new CartItem();
			// Set HTO Object
			Hto htoObj = new Hto();
			htoObj.setCity("Faridabad");
			htoObj.setDate(2017 - 04 - 13);
			htoObj.setLenskartAtHome("CASE3_HEC");
			htoObj.setSlotId(6729405);
			reqObj.setHto(htoObj);
			// Set HTO Product ID
			reqObj.setProductId(Long.parseLong("47552"));
			reqObj.setStoreInventory(1);
			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);
			log.debug("-----------Create cart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");
			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("HTO CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Session Token: " + SessionToken
					+ "\nHTO Cart API:" + GenericUtil.printAPICallDetails(createCartURL, reqJsonObject, cart_response));
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;

	}

	public static JSONObject createCart(String SessionToken, String ProductId, String powerType, String packageId,
			String xApiClient) throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
			CartItem reqObj = new CartItem();
			reqObj.setProductId(Long.parseLong(ProductId));
			reqObj.setPowerType(powerType);
			reqObj.setPackageId(packageId);
			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------Create cart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");

			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;
	}

	public static JSONObject addItemToCart(String SessionToken, String ProductId, String powerType, String packageId,
			String xApiClient) throws JSONException {
		return createCart(SessionToken, ProductId, powerType, packageId, xApiClient);
	}

	public static JSONObject createCart(String SessionToken, String ProductId, String powerType, String qty,
			Prescription prescription, String xApiClient) throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
			CartItem reqObj = new CartItem();
			log.debug("product " + ProductId);
			reqObj.setProductId(Long.parseLong(ProductId));
			reqObj.setPowerType(powerType);
			reqObj.setQuantity(Integer.parseInt(qty));
			reqObj.setPrescription(prescription);
			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------Create cart request----------");
			log.debug("Create Cart Request Object : " + reqJsonObject);
			log.debug("----------------------------------------");

			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return cart_response;
	}

	public static Response createBulkCart(boolean isBulkOrder, boolean isVirtualOrder, String ProductId,int qty, double margin, double subTotal, double total,
			int statusCode) throws Exception {
		CartItems cartItems = new CartItems();
		cartItems.setIsBulkOrder(isBulkOrder);
		cartItems.setIsVirtualOrder(isVirtualOrder);

		List<CartItem> items = new ArrayList<CartItem>();
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(ProductId));
		reqObj.setQuantity(qty);
		reqObj.setStoreInventory(0);
		reqObj.setIsFranchise(true);
		reqObj.setIsBothEye("N");

		AmountBulkOrder amountBulkOrder = new AmountBulkOrder();
		amountBulkOrder.setCurrencyCode(GenericUtil.CountryProperties("currency_code"));
		amountBulkOrder.setMargin(new BigDecimal(margin));
		amountBulkOrder.setTax(new BigDecimal(0));
		amountBulkOrder.setSubTotal(new BigDecimal(subTotal));
		amountBulkOrder.setTotal(new BigDecimal(total));
		reqObj.setAmount(amountBulkOrder);

		AmountBulkOrder totals = new AmountBulkOrder();
		totals.setCurrencyCode("INR");
		totals.setMargin(new BigDecimal(margin));
		totals.setTax(new BigDecimal(0));
		totals.setSubTotal(new BigDecimal(subTotal));
		totals.setTotal(new BigDecimal(total));

		items.add(reqObj);

		cartItems.setItems(items);
		cartItems.setTotals(totals);

		String json = objectMapper.writeValueAsString(cartItems);

		Response response = RestAssuredUtils.POST(CartPathConstants.bulkOrder,
				HeadersUtil.headers_sessionToken(SessionUtil.createNewSession()),json);
		Assert.assertEquals(response.statusCode(), statusCode);
		return response;
	}

	public static JSONObject createCart(String SessionToken, String ProductId, String powerType, String xApiClient)
			throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
			CartItem reqObj = new CartItem();
			reqObj.setProductId(Long.parseLong(ProductId));
			reqObj.setPowerType(powerType);
			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------Create cart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");

			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;
	}

	public static JSONObject createCart(String SessionToken, CartItem reqObj, String xApiClient) throws Exception {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			// headers.add(new BasicNameValuePair("X-B3-TraceId",
			// GenericUtil.getRandomHexTraceId()));

			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------Create cart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");
			GenericUtil.curlBuilder("POST", null, createCartURL, headers, reqJsonObject.toString());
			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
					GenericUtil.printAPICallDetails(createCartURL, reqJsonObject, cart_response));
		} catch (AssertionError e) {
			e.printStackTrace();
		}
		return cart_response;
	}

	public static JSONObject createCart(String SessionToken, String appVersion, CartItem reqObj, String xApiClient)
			throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-Build-Version", appVersion));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------Create cart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");

			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
					GenericUtil.printAPICallDetails(createCartURL, reqJsonObject, cart_response));
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;
	}

	public static JSONObject createCart(String SessionToken, JSONObject reqObj, String xApiClient)
			throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqObj);
			log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
					GenericUtil.printAPICallDetails(createCartURL, reqJsonObject, cart_response));
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;
	}

	public static JSONObject addToCart(String sessionToken, String productId, String xApiClient) throws Exception {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;

		// Header
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));

		String json = objectMapper.writeValueAsString(reqObj);
		reqJsonObject = new JSONObject(json);

		log.debug("-----------AddToCart request----------");
		log.debug(reqJsonObject);
		log.debug("----------------------------------------");

		httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
		log.debug("ADD TO CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
		cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		return cart_response;
	}

	public static JSONObject createCart_SG(String SessionToken, CartItem reqObj, String xApiClient)
			throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-Country-Code", "SG"));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------Create cart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");

			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			// Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
			// GenericUtil.printAPICallDetails(createCartURL, reqJsonObject,
			// cart_response));
		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;
	}

	public static JSONObject addCoupon(String sessionToken, String couponCode) throws Exception {
		String addCoupon_url = addCouponURL.replace("COUPON_CODE", couponCode);

		log.debug("AddCoupon request URL : " + addCoupon_url);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", "Desktop"));

		HttpResponse httpResponse = RequestUtil.postRequest(addCoupon_url, header, null, param);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);

		return respJsonObjectData;
	}

	public static JSONObject increaseItemCount(String SessionToken, String xApiClient, int itemId, int itemCount)
			throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject incItemCount_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
			List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
			queryParams.add(new BasicNameValuePair("count", String.valueOf(itemCount)));

			httpresponse = RequestUtil.putRequest(increaseItemCountURL.replace("ITEM_ID", String.valueOf(itemId)),
					queryParams, headers);
			log.debug("INCREASE CART ITEM COUNT RESPONSE CODE :" + httpresponse.getStatusLine().getStatusCode());
			incItemCount_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return incItemCount_response;
	}

	public static JSONObject getCart(String sessionToken, String xApiclient) throws Exception {
		String getCart_url = getCartURL;

		log.debug("Fetch Cart request URL : " + getCart_url);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiclient));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.getRequest(getCart_url, header, param);

		JSONObject respJsonObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObject);
		return respJsonObject;
	}

	public static JSONObject getCart(String sessionToken, String xApiclient, String applyWallet) throws Exception {
		String getCart_url = getCartURL;

		log.debug("Fetch Cart request URL : " + getCart_url);
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("applyWallet", applyWallet));

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiclient));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.getRequest(getCart_url, header, param);

		JSONObject respJsonObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObject);
		return respJsonObject;
	}

	public static JSONObject addGiftVoucher(String sessionToken, String gvCode) throws Exception {

		String addGV_url = addGVURL.replace("GV", gvCode);

		log.debug("AddCoupon request URL : " + addGV_url);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.postRequest(addGV_url, header, param);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);

		return respJsonObjectData;
	}

	public static JSONObject removeStoreCredit(String sessionToken, String scCode) throws Exception {
		String removeSC_url = removeSCURL.replace("STORECREDITCODE", scCode);

		log.debug("RemoveGV request URL : " + removeSC_url);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.deleteRequest(removeSC_url, null, header);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);
		return respJsonObjectData;
	}

	public static JSONObject removeGiftVoucher(String sessionToken, String gvCode) throws Exception {
		String removeGV_url = removeGVURL.replace("GV", gvCode);

		log.debug("RemoveGV request URL : " + removeGV_url);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.deleteRequest(removeGV_url, null, header);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);
		return respJsonObjectData;
	}

	public static JSONObject addStoreCredit(String sessionToken, String storeCreditCode, Integer storeCreditAmount)
			throws Exception {
		String addStoreCredit_URL = addStoreCreditURL.replace("STORECREDITCODE", storeCreditCode);
		addStoreCredit_URL = addStoreCredit_URL.replace("STORECREDITAMOUNT", Integer.toString(storeCreditAmount));

		log.debug("AddCoupon request URL : " + addStoreCredit_URL);
		Thread.sleep(200);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.postRequest(addStoreCredit_URL, header);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);

		return respJsonObjectData;
	}

	public static JSONObject clearCart(String sessionToken) throws Exception {
		String clearCart_URL = Environments.SERVICES_ENVIRONMENT + CartPathConstants.ClearCart;

		log.debug(" request URL : " + clearCart_URL);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));
		// header.add(new BasicNameValuePair("X-B3-TraceId",
		// GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.deleteRequest(clearCart_URL, header);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);
		return respJsonObjectData;
	}

	public static void updateDeliveryOption(String sessionToken, String pincode) throws Exception {
		String updateDeliveryOption_URL = Environments.SERVICES_ENVIRONMENT
				+ "/v2/carts/expressdelivery/UpdateDeliveryOption/EXPRESS";

		log.debug(" request URL : " + updateDeliveryOption_URL);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("pincode", pincode));

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.postRequest(updateDeliveryOption_URL, header, null, query);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);

	}

	public static JSONObject saveAddress(String sessionToken, String xApiClient, Address addressObj,
			GiftMessage giftMsgObj) throws Exception {
		String addressJson = objectMapper.writeValueAsString(addressObj);
		String giftJson = objectMapper.writeValueAsString(giftMsgObj);
		String json = "{\"address\":" + addressJson + ",\"giftMessage\":" + giftJson + "}";
		reqJsonObject = new JSONObject(json);
		log.debug("Save Address request URL : " + saveAddressURL);
		log.debug("Request JSON:" + reqJsonObject);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
		log.debug("header " + header);
		GenericUtil.curlBuilder("POST", null, saveAddressURL, header, reqJsonObject.toString());
		HttpResponse httpResponse = RequestUtil.postRequest(saveAddressURL, header, param, reqJsonObject);
		log.debug("nsbdjksd " + httpResponse);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200,
				GenericUtil.printAPICallDetails(saveAddressURL, reqJsonObject, respJsonObjectData));
		return respJsonObjectData;
	}

	public static JSONObject saveAddress(String sessionToken, String appVersion, String xApiClient, Address addressObj,
			GiftMessage giftMsgObj) throws Exception {
		String addressJson = objectMapper.writeValueAsString(addressObj);
		String giftJson = objectMapper.writeValueAsString(giftMsgObj);
		String json = "{\"address\":" + addressJson + ",\"giftMessage\":" + giftJson + "}";
		reqJsonObject = new JSONObject(json);
		log.debug("Save Address request URL : " + saveAddressURL);
		log.debug("Request JSON:" + reqJsonObject);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		header.add(new BasicNameValuePair("X-Build-Version", appVersion));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
		log.debug("header " + header);
		HttpResponse httpResponse = RequestUtil.postRequest(saveAddressURL, header, param, reqJsonObject);
		log.debug("nsbdjksd " + httpResponse);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200,
				GenericUtil.printAPICallDetails(saveAddressURL, reqJsonObject, respJsonObjectData));
		return respJsonObjectData;
	}

	public static JSONObject cartStatus_InActive(String sessionToken, String xApiClient) throws Exception {

		log.debug("updateCartStatus request URL : " + updateCartStatusURL);
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("status", "Inactive"));
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
		HttpResponse httpResponse = RequestUtil.putRequest(updateCartStatusURL, param, header);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);
		return respJsonObjectData;
	}

	public static JSONObject cartStatus_Lock(String sessionToken, String xApiClient) throws Exception {

		log.debug("Update Cart Status request URL : " + updateCartStatusURL);
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("status", "Lock"));

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.putRequest(updateCartStatusURL, param, header);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);
		return respJsonObjectData;
	}

	public static JSONObject cartStatus_UnLock(String sessionToken, String xApiClient) throws Exception {

		log.debug("Delete Item From Cart request URL : " + updateCartStatusURL);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		param.add(new BasicNameValuePair("status", "Unlock"));

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.putRequest(updateCartStatusURL, param, header);
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("httpResponse" + respJsonObjectData);
		return respJsonObjectData;
	}

	public static HashMap<JSONObject, String> createGuestCart(String productId, String xApiClient) throws Exception {
		HashMap<JSONObject, String> map = new HashMap<JSONObject, String>();
		String sessionToken = SessionUtil.createNewSession();
		log.debug("UnauthSessionToken" + sessionToken);
		JSONObject CartResponseObj = CartUtil.createCart(sessionToken, productId, xApiClient);
		log.debug("-----------create cart response-----------");
		log.debug(CartResponseObj);
		map.put(CartResponseObj, sessionToken);
		return map;
	}

	public static HashMap<JSONObject, String> createLoginedCart(String registered_emailId, String password,
			String productId, String xApiClient) throws Exception {
		HashMap<JSONObject, String> map = new HashMap<JSONObject, String>();
		String sessionToken = CustomerUtil.get_sessionId_after_user_authentication(registered_emailId, password,
				xApiClient);
		log.debug("authSessionToken" + sessionToken);
		JSONObject sessionJsonObject = SessionUtil.getMeSessionDetails(sessionToken);
		JSONObject resultObject = sessionJsonObject.getJSONObject("result");
		JSONObject attrsObject = resultObject.getJSONObject("attrs");
		try {
			if (!attrsObject.getJSONObject("cartIds").toString().isEmpty()) {
				JSONObject cartIdArray = attrsObject.getJSONObject("cartIds");
				if (cartIdArray.length() != 0) {
					log.debug("-------------------------UpdateCartStatusToInActive ----------------------------");
					CartUtil.cartStatus_InActive(sessionToken, ApplicationConstants.XApiClient.IOS);
					Thread.sleep(500);
				}
			}
		} catch (Exception e) {
			log.debug("CartId not there w.r.t Login ID");
		}
		JSONObject CartResponseObj = CartUtil.createCart(sessionToken, productId, xApiClient);
		log.debug("-----------create cart response-----------");
		log.debug(CartResponseObj);

		/*
		 * JSONObject LoggedInResultObj = CartResponseObj.getJSONObject("result");
		 * String b = LoggedInResultObj.toString();
		 * 
		 * CartResponse node1 = objectMapper.readValue(b, CartResponse.class);
		 */
		map.put(CartResponseObj, sessionToken);
		return map;

	}

	public static HashMap<JSONObject, String> createNewRegUserCart(String productId, String xApiClient)
			throws Exception {
		HashMap<JSONObject, String> map = new HashMap<JSONObject, String>();
		// create a new user
		String registered_emailId = "GVTest" + GenericUtil.createRandomNumber(8) + "@example.com";
		String password = "password";
		String mobileNum = "8" + GenericUtil.createRandomNumber(9);
		String sessionToken = CustomerUtil.registerNewCustomer("create", "Cart", registered_emailId, password,
				mobileNum);// Authenticated

		log.debug("authSessionToken" + sessionToken);
		JSONObject CartResponseObj = CartUtil.createCart(sessionToken, productId, xApiClient);
		log.debug("-----------create cart response-----------");
		log.debug(CartResponseObj);

		/*
		 * JSONObject LoggedInResultObj = CartResponseObj.getJSONObject("result");
		 * String b = LoggedInResultObj.toString();
		 * 
		 * CartResponse node1 = objectMapper.readValue(b, CartResponse.class);
		 */
		map.put(CartResponseObj, sessionToken);
		return map;

	}

	public static HttpResponse addStepForAbandonedLeadfn(String sessionToken, String xApiClient, String contenttype,
			AbandonedLead AbandonedLeadObj) throws Exception {

		String addStepForAbandonedLead_URL = addStepForAbandonedLeadURL;
		log.debug("AddAbandonedLead request URL : " + addStepForAbandonedLeadURL);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		header.add(new BasicNameValuePair("Content-Type", contenttype));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		String json = objectMapper.writeValueAsString(AbandonedLeadObj);
		reqJsonObject = new JSONObject(json);
		log.debug(reqJsonObject);
		HttpResponse httpResponse = RequestUtil.postRequest(addStepForAbandonedLead_URL, header, param, reqJsonObject);
		return httpResponse;
	}

	public static JSONObject getDeliveryOptions(String cartId, String pincode, String sessionToken, String xApiclient)
			throws Exception {
		String getDeliveryOptions_url = getDeliveryOptionsURL.replace("CART_ID", String.valueOf(cartId));

		log.debug("getDeliveryOptions request URL : " + getDeliveryOptions_url);
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("pincode", pincode));

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiclient));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		HttpResponse httpResponse = RequestUtil.getRequest(getDeliveryOptions_url, header, param);

		JSONObject respJsonObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		JSONObject resultObj = respJsonObject.getJSONObject("result");
		log.debug("httpResponse" + respJsonObject);

		return resultObj;
	}

	public JSONObject updateDeliveryOptionfn(String deliveryOption, String pincode, String sessionToken,
			String xApiclient) throws Exception {
		String updateDeliveryOption_URL = updateDeliveryOptionURL.replace("DELIVERYOPTION", deliveryOption);
		updateDeliveryOption_URL = updateDeliveryOption_URL.replace("PINCODE", pincode);
		log.debug("updateDeliveryOptionURL request URL : " + updateDeliveryOption_URL);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", xApiclient));
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		log.debug("header" + header);
		List<NameValuePair> bodyParams = null;

		HttpResponse httpResponse = RequestUtil.postRequest(updateDeliveryOption_URL, header, bodyParams);
		log.debug(httpResponse);
		JSONObject respJsonObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		JSONObject resultObj = respJsonObject.getJSONObject("result");
		log.debug("httpResponse" + respJsonObject);

		return resultObj;
	}

	public static List<Document> getAllCarts(Map<String, Object> parms) throws Exception {

		List<Document> get_cart_details = mangoCollectionName.executeQuery(getCartCollectionName(), parms);
		return get_cart_details;
	}

	public static HttpResponse addToCart_single_subscription(String SessionToken, String xApiclient, String ProductId,
			String powerType, String subscriptionId) throws JSONException {
		HttpResponse httpresponse = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiclient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			CartItem reqObj = new CartItem();
			reqObj.setProductId(Long.parseLong(ProductId));
			reqObj.setPowerType(powerType);
			reqObj.setSubscriptionId(subscriptionId);
			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------AddToCart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");

			httpresponse = RequestUtil.postRequest(createCartURL, headers, null, reqJsonObject);
			// Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(),
			// 200, "Response is not proper");

		} catch (Exception e) {
			e.printStackTrace();
		}

		return httpresponse;
	}

	public static JSONObject addToCart_bulkItems_subscription(String SessionToken, String xApiclient, String ProductId,
			String powerType, String subscriptionId) throws JSONException {
		HttpResponse httpresponse = null;
		JSONObject cart_response = null;
		try {
			// Header
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", SessionToken));
			headers.add(new BasicNameValuePair("X-Api-Client", xApiclient));
			headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

			CartItem reqObj = new CartItem();
			reqObj.setProductId(Long.parseLong(ProductId));
			reqObj.setPowerType(powerType);
			reqObj.setSubscriptionId(subscriptionId);
			String json = objectMapper.writeValueAsString(reqObj);
			reqJsonObject = new JSONObject(json);

			log.debug("-----------AddToCart request----------");
			log.debug(reqJsonObject);
			log.debug("----------------------------------------");

			httpresponse = RequestUtil.postRequest(createCartURL_bULKITEMS, headers, null, reqJsonObject);
			cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response is not proper");

		} catch (Exception e) {
			e.printStackTrace();
		}

		return cart_response;
	}

	public static JSONObject getCartBasedOnApplyWallet(String sessionToken, String xApiClient, String applyWallet)
			throws Exception {
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		if (applyWallet != null) {
			query.add(new BasicNameValuePair("applyWallet", applyWallet));
		}
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not coorect");
		return responseGetCartForWallet;
	}

	public static JSONObject getCartBasedOnApplyWallet(String sessionToken, String appVersion, String xApiClient,
			String applyWallet) throws Exception {
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Build-Version", appVersion));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		if (applyWallet != null) {
			query.add(new BasicNameValuePair("applyWallet", applyWallet));
		}
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not coorect");
		return responseGetCartForWallet;
	}

	public static JSONObject createCart1(String sessionToken, String xApiclient, String productId)
			throws URISyntaxException, Exception {
		// Header
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiclient));

		JSONObject cartObject = new JSONObject();
		cartObject.put("productId", productId);

		log.debug("-----------Create cart request----------");
		log.debug("Request URL" + createCartURL);
		log.debug("Request Object" + cartObject);
		log.debug("Header" + headers);
		log.debug("----------------------------------------");

		HttpResponse httpresponse = RequestUtil.postRequest(createCartURL, headers, null, cartObject);
		log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
		JSONObject cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Add to cart is failing");
		return cart_response;
	}

	public static JSONObject contactLensCart(String sessionToken, String xApiclient, String productId,
			String numberOfbox, String isBotheye) throws URISyntaxException, Exception {

		String power = ProductUtil.getPowerForAProduct(productId)
				.getString(ProductUtil.getPowerForAProduct(productId).length() - 1);
		String subscriptionId = JunoV1Util.getSubscriptionIdWithProductId(productId, "false");
		// Header
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiclient));

		List<Long> relatedProducts = new ArrayList<Long>();
		JSONObject cartObject = new JSONObject();
		JSONObject prescription = new JSONObject();
		JSONObject left = new JSONObject();
		left.put("boxes", numberOfbox);
		left.put("sph", power);
		JSONObject right = new JSONObject();
		right.put("boxes", numberOfbox);
		right.put("sph", power);
		prescription.put("left", left);
		prescription.put("right", right);
		prescription.put("gender", "female");
		prescription.put("dob", "09-01-1999");
		prescription.put("powerType", ApplicationConstants.PowerTypes.CONTACT_LENS);
		prescription.put("prescriptionType", ApplicationConstants.PowerTypes.CONTACT_LENS);

		cartObject.put("productId", productId);
		cartObject.put("isBothEye", isBotheye);
		cartObject.put("subscriptionId", subscriptionId);
		cartObject.put("prescription", prescription);
		cartObject.put("productId", productId);
		cartObject.put("relatedProducts", relatedProducts);
		log.info("cartObject: " + cartObject);

		HttpResponse httpresponse = RequestUtil.postRequest(createCartURL, headers, null, cartObject);
		log.debug("CREATE CART RESPONSE CODE:" + httpresponse.getStatusLine().getStatusCode());
		JSONObject cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Add to cart is failing");
		return cart_response;
	}

	public static JSONObject updateItemPOS(String itemId, String sessionToken, String xApiClient, String barCode,
			Integer storeInventory, Boolean shipToStoreRequired, String localFittingFacility,
			Boolean isLocalFittingRequired, FittingTypeEnum fittingType, int statusCode) throws Exception {
		final String updateItemPOSURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(CartPathConstants.updateItem_POS, itemId);
		UpdateItem updateItem = new UpdateItem();
		updateItem.setBarCode(barCode);
		updateItem.setStoreInventory(storeInventory);
		updateItem.setShipToStoreRequired(shipToStoreRequired);
		updateItem.setIsLocalFittingRequired(isLocalFittingRequired);
		updateItem.setLocalFittingFacility(localFittingFacility);
		updateItem.setFittingType(fittingType);
		String json = objectMapper.writeValueAsString(updateItem);
		reqJsonObject = new JSONObject(json);
		HttpResponse httpresponse = RequestUtil.putRequest(updateItemPOSURL, headers(sessionToken, xApiClient),
				reqJsonObject);
		JSONObject cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), statusCode);
		return cart_response;
	}

	public static void makeCartInactive(String customerSession) throws Exception {
		String clearCartURL = Environments.SERVICES_ENVIRONMENT + "/v2/carts/status";

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));
		headers.add(new BasicNameValuePair("X-Session-Token", customerSession));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("status", "Inactive"));

		HttpResponse httpResponse = RequestUtil.putRequest(clearCartURL, query, headers);
		RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(httpResponse.getStatusLine().getStatusCode());

	}

	public static JSONObject CreateReturnPickupAddress() throws Exception {
		JSONObject pickupAddress = new JSONObject();
		pickupAddress.put("addressline1", "Test");
		pickupAddress.put("addressline2", "Test");
		pickupAddress.put("city", "Bangalore");
		pickupAddress.put("country", "India");
		pickupAddress.put("defaultAddress", true);
		pickupAddress.put("email", "<EMAIL>");
		pickupAddress.put("firstName", "Test1");
		pickupAddress.put("floor", "1");
		pickupAddress.put("gender", "Male");
		pickupAddress.put("landmark", "Tesst");
		pickupAddress.put("lastName", "Tesst2");
		pickupAddress.put("liftAvailable", "Yes");
		pickupAddress.put("locality", "TesstNagar");
		pickupAddress.put("phone", "12" + GenericUtil.createRandomNumber(8));
		pickupAddress.put("phoneCode", "+91");
		pickupAddress.put("postcode", "560040");
		pickupAddress.put("state", "Karnataka");
		return pickupAddress;
	}
	
	public static void clearCart(String loggedInSessiontoken, String x_api_client) throws Exception {
		String clearCart_URL = Environments.SERVICES_ENVIRONMENT + "/v2/carts/items/";
		log.info(clearCart_URL);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", loggedInSessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", "android"));
		log.info(loggedInSessiontoken);
		log.info("header " + header);
		GenericUtil.curlBuilder("DELETE", null, clearCart_URL, header, null);
		HttpResponse httpResponse = RequestUtil.deleteRequest(clearCart_URL, header);
		log.info("httpResponse " + httpResponse);
		JSONObject jsonClearCart = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(httpResponse.getStatusLine().getStatusCode());
		log.info(jsonClearCart);
		boolean clearCartStatus = (httpResponse.getStatusLine().getStatusCode() == 200
				|| httpResponse.getStatusLine().getStatusCode() == 404);
		Assert.assertTrue(clearCartStatus, "\nClearCart :"
				+ GenericUtil.printAPICallDetails(loggedInSessiontoken, clearCart_URL, null, jsonClearCart));
		log.info(httpResponse.getStatusLine().getStatusCode());
		if (httpResponse.getStatusLine().getStatusCode() == 404) {
			CartUtil.getCart(loggedInSessiontoken, x_api_client);
			makeCartInactive(loggedInSessiontoken);
		}
	}
	
	public static JSONObject removeItemFromCart(String loggedInSessiontoken, String itemID,String x_api_client) throws Exception {	
		String delItemfromCartURL =
			      (Environments.SERVICES_ENVIRONMENT + CartPathConstants.DeleteItemsFromCart).trim();
		String delItemfromCart_URL = delItemfromCartURL.replace("ITEM_ID", String.valueOf(itemID));
	    List<NameValuePair> param = new ArrayList<NameValuePair>();
	    param.add(new BasicNameValuePair("count","1"));
	    
	    List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", loggedInSessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", x_api_client));
	//	GenericUtil.curlBuilder("DELETE", null, removeItem_URL, header, null);
	    HttpResponse httpResponse = RequestUtil.deleteRequest(delItemfromCart_URL, param, header);
		JSONObject jsonClearCart = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(jsonClearCart);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200);
		return jsonClearCart;
	}
	
	public static void validateCart(String SessionToken, CartItem reqObj, String xApiClient) throws URISyntaxException, Exception {
		String request = GenericUtil.objectMapper().writeValueAsString(reqObj);
		Response response = RestAssuredUtils.POST(CartPathConstants.validateCart,
				HeadersUtil.headers_session_client(SessionToken,xApiClient), request);
			Assert.assertTrue(response.jsonPath().getBoolean("result[0].isSuccess"));
	}

}
