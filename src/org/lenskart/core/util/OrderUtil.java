package org.lenskart.core.util;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Vector;
import java.util.stream.IntStream;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.CartPathConstants;
import org.lenskart.core.constant.InventoryPathConstant;
import org.lenskart.core.constant.OrderPathConstants;
import org.lenskart.pojo.order.BackSyncRequest;
import org.lenskart.pojo.order.Item;
import org.lenskart.pojo.order.ItemTracking;
import org.lenskart.pojo.order.Order;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.util.Strings;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import com.lenskart.juno.schema.v2.cart.CartItem;
import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.Hto;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.lenskart.juno.schema.v2.common.PrescriptionType;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;
import com.utilities.RestAssuredUtils;

import io.restassured.response.Response;

public class OrderUtil {
	private static final Logger log = GenericUtil.InitLogger(OrderUtil.class);
	private static HashMap<String, String> product = new HashMap<>();

	private static MongoConnectionUtility dao = null;
	private static JaxbAnnotationModule module;
	private static ObjectMapper objectMapper;

	// classification Types
	public static final String HTO = "HTO";
	public static final String loyalty_services = "loyalty_services";
	public static final String prescription_lens = "prescription_lens";
	public static final String contact_lens_solution = "contact_lens_solution";
	public static final String non_powerwise_contact_lens = "non_powerwise_contact_lens";
	public static final String contact_lens = "contact_lens";
	public static final String eyeframe = "eyeframe";
	public static final String sunglasses = "sunglasses";

	static {
		module = new JaxbAnnotationModule();
		objectMapper = new ObjectMapper();
		objectMapper.registerModule(module);
		objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
		objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}

	public static MongoConnectionUtility getOrderMongoConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("OrderService");
		dao = new MongoConnectionUtility(pf);
		return dao;
	}

	public static MongoConnectionUtility getOrderMongoConnectionObject(String propertyFileName) throws IOException {
		PropertyFactory pf = new PropertyFactory(propertyFileName);
		dao = new MongoConnectionUtility(pf);
		return dao;
	}

	public static void closeOrderMongoConnection() throws IOException {
		if (dao != null) {
			dao.closeMongoConnection();
		}
	}

	private static String mongoOrderCollectionName = "order_v2";

	public static String getCustomerCollectionName() {
		return mongoOrderCollectionName;
	}

	public static JSONObject getAllOrdersFromAPI(String sessionToken, String x_Api_client) throws Exception {
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GATEWAY_GET_ORDER_BY_ORDERID_PATH;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "x_Api_Client"));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		List<NameValuePair> queryParams = null;
		HttpResponse httpresponse_getAllOrders = RequestUtil.getRequest(RequestUrl, header, queryParams);
		JSONObject responseJson_getAllOrders = RequestUtil.convertHttpResponseToJsonObject(httpresponse_getAllOrders);
		return responseJson_getAllOrders;

	}

	public static List<Document> getAllOrders(Map<String, Object> parms) throws Exception {

		List<Document> get_order_details = dao.executeQuery(getCustomerCollectionName(), parms);

		return get_order_details;
	}

	public static List<Document> getAllOrders1(String email, String fromDate, String toDate) throws Exception {

		List<Document> get_order_details = dao.orderCancelationQuery(getCustomerCollectionName(), email, fromDate,
				toDate);
		log.info("get_order_details: " + get_order_details);

		return get_order_details;
	}

	public static List<Document> getAllOrders(Map<String, Object> parms, String collectionName) throws Exception {

		List<Document> get_order_details = dao.executeQuery(collectionName, parms);

		return get_order_details;
	}

	public static String createOrder(String x_Api_Client, String sessiontoken, String method) throws Exception {
		String RequestUrl_createOrder = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_POST_ORDERS_PATH);
		log.debug("session: " + sessiontoken);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrder = new JSONObject();
		JSONObject payments = new JSONObject();
		JSONArray paymentList = new JSONArray();
		JSONObject paymentList1 = new JSONObject();
		paymentList1.put("method", method);
		paymentList1.put("gateway", "PU");
		paymentList.put(paymentList1);
		JSONObject card = new JSONObject();
		paymentList1.put("card", card);
		payments.put("paymentList", paymentList);
		Requestparams_createOrder.put("payments", payments);
		JSONObject address = new JSONObject();
		address.put("firstName", "test");
		address.put("lastName", "test");
		address.put("phone", "1262183385");
		address.put("email", "<EMAIL>");
		address.put("addressline1", "test dont proceed this order");
		address.put("city", "test dont proceed this order");
		address.put("state", "test dont proceed this order");
		address.put("postcode", "560038");
		address.put("country", "IN");
		Requestparams_createOrder.put("billingAddress", address);

		log.debug("CreateOrder URL of Post: " + RequestUrl_createOrder);
		HttpResponse httpResponseCreateOrder = RequestUtil.postRequest(RequestUrl_createOrder, headers,
				Requestparams_createOrder);
		JSONObject responseJSON_CreateOrder = RequestUtil.convertHttpResponseToJsonObject(httpResponseCreateOrder);
		Assert.assertEquals(httpResponseCreateOrder.getStatusLine().getStatusCode(), 200,
				"SessionToken: " + sessiontoken + "\nOrder API:" + GenericUtil.printAPICallDetails(
						RequestUrl_createOrder, Requestparams_createOrder, responseJSON_CreateOrder));
		JSONObject result = responseJSON_CreateOrder.getJSONObject("result");
		String orderId = result.getString("id");
		Assert.assertEquals(200, responseJSON_CreateOrder.getInt("status"), "Response status is not correct");
		return orderId;
	}

	public static int get_Inventory_valid_product_store(String product_id, String store_id, String sessiontoken)
			throws Exception {
		String RequstUrlGetInventory = Environments.SERVICES_ENVIRONMENT
				+ String.format(InventoryPathConstant.GET_INVENTORY_PATH, product_id);
		log.debug("--------------------------GET INVENTORY DETAILS WHEN VAILD PRODUCT AND STORE ID " + store_id
				+ "----------------------------------");
		log.debug("Request Header for Get:" + RequstUrlGetInventory);

		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("storeid", store_id));

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsegetinventory = RequestUtil.getRequest(RequstUrlGetInventory, headers, queryparam);
		JSONObject responseJson_Getinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsegetinventory);
		Assert.assertEquals(httpResponsegetinventory.getStatusLine().getStatusCode(), 200, "SessionToken: "
				+ sessiontoken + "\nGet Inventory API (GET) :"
				+ GenericUtil.printAPICallDetails(RequstUrlGetInventory, null, responseJson_Getinventory_result));

		JSONObject responseJson_Getinventory = responseJson_Getinventory_result.getJSONObject("result");
		Assert.assertNotNull(responseJson_Getinventory.getString("productId"), "product id cannot be null");
		Assert.assertNotNull(responseJson_Getinventory.getString("storeId"), "store id cannot be null");
		int qty = responseJson_Getinventory.getInt("quantity");

		return qty;
	}

	public static void updateOrderItemPrescription_usecase(String sessiontoken, String orderId, String itemId,
			String powerType) throws Exception {

		String RequestUrl_updateOrderItemPrescription = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_PUT_POWER_BY_ORDERID_AND_ITEMID_PATH, orderId, itemId);
		log.debug("URL of Put: " + RequestUrl_updateOrderItemPrescription);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_updateOrderItemPrescription = new JSONObject();

		Requestparams_updateOrderItemPrescription.put("left", ApplicationUtil.leftEye());
		Requestparams_updateOrderItemPrescription.put("right", ApplicationUtil.rightEye());
		Requestparams_updateOrderItemPrescription.put("notes", "Test order power update");
		Requestparams_updateOrderItemPrescription.put("powerType", powerType);
		Requestparams_updateOrderItemPrescription.put("prescriptionType", powerType.toUpperCase());
		Requestparams_updateOrderItemPrescription.put("userName", ApplicationConstants.username);

		HttpResponse httpResponseUpdateOrderItemPrescription = RequestUtil.putRequest(
				RequestUrl_updateOrderItemPrescription, null, headers, Requestparams_updateOrderItemPrescription);
		JSONObject responseJSON_UpdateOrderItemPrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseUpdateOrderItemPrescription);

		Assert.assertEquals(httpResponseUpdateOrderItemPrescription.getStatusLine().getStatusCode(), 200,
				GenericUtil.printAPICallDetails(sessiontoken, RequestUrl_updateOrderItemPrescription,
						Requestparams_updateOrderItemPrescription, responseJSON_UpdateOrderItemPrescription));

		if (httpResponseUpdateOrderItemPrescription.getStatusLine().getStatusCode() == 200) {
			JSONArray items = responseJSON_UpdateOrderItemPrescription.getJSONObject("result").getJSONArray("items");
			if (items.length() > 0) {
				for (int j = 0; j < items.length(); j++) {
					// log.debug("Prescription
					// details"+prescription.getJSONObject(j).get("prescription"));
					if (items.getJSONObject(j).getString("id").equals(itemId)) {
						Assert.assertEquals(items.getJSONObject(j).getString("powerRequired"), "POWER_SUBMITTED",
								"Power is not submitted");
						JSONObject presJSON = items.getJSONObject(j).getJSONObject("prescription");
						Assert.assertEquals(presJSON.getString("powerType"), powerType, "powerType is not correct");
					}
				}
			}
		}
		log.debug("-------------Item prescription is updated properly-----------------");
	}

	public static void updateOrderItemPrescription_usecase(String sessiontoken, String orderId, String itemId,
			String powerType, JSONObject leftEye, JSONObject rightEye) throws Exception {

		String RequestUrl_updateOrderItemPrescription = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_PUT_POWER_BY_ORDERID_AND_ITEMID_PATH, orderId, itemId);
		log.debug("URL of Put: " + RequestUrl_updateOrderItemPrescription);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));
		headers.add(new BasicNameValuePair("X-auth-token", ApplicationConstants.xAuthToken));
		// headers.add(new BasicNameValuePair("X-B3-TraceId",
		// RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_updateOrderItemPrescription = new JSONObject();

		Requestparams_updateOrderItemPrescription.put("left", leftEye);
		Requestparams_updateOrderItemPrescription.put("right", rightEye);
		// Requestparams_updateOrderItemPrescription.put("notes", "Test order power
		// update");
		Requestparams_updateOrderItemPrescription.put("powerType", powerType);
		// Requestparams_updateOrderItemPrescription.put("prescriptionType",
		// powerType.toUpperCase());
		Requestparams_updateOrderItemPrescription.put("userName", ApplicationConstants.username);
		GenericUtil.curlBuilder("PUT", null, RequestUrl_updateOrderItemPrescription, headers,
				Requestparams_updateOrderItemPrescription.toString());
		HttpResponse httpResponseUpdateOrderItemPrescription = RequestUtil.putRequest(
				RequestUrl_updateOrderItemPrescription, null, headers, Requestparams_updateOrderItemPrescription);
		JSONObject responseJSON_UpdateOrderItemPrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseUpdateOrderItemPrescription);

		Assert.assertEquals(httpResponseUpdateOrderItemPrescription.getStatusLine().getStatusCode(), 200,
				GenericUtil.printAPICallDetails(sessiontoken, RequestUrl_updateOrderItemPrescription,
						Requestparams_updateOrderItemPrescription, responseJSON_UpdateOrderItemPrescription));

		if (httpResponseUpdateOrderItemPrescription.getStatusLine().getStatusCode() == 200) {
			JSONArray items = responseJSON_UpdateOrderItemPrescription.getJSONObject("result").getJSONArray("items");
			if (items.length() > 0) {
				for (int j = 0; j < items.length(); j++) {
					if (items.getJSONObject(j).getString("id").equals(itemId)) {
						Assert.assertEquals(items.getJSONObject(j).getString("powerRequired"), "POWER_SUBMITTED",
								"Power is not submitted");
						JSONObject presJSON = items.getJSONObject(j).getJSONObject("prescription");
						Assert.assertEquals(presJSON.getString("powerType"), powerType, "powerType is not correct");
					}
				}
			}
		}
		log.debug("-------------Item prescription is updated properly-----------------");
	}

	public static void updateOrderItemPrescription_usecase(String sessiontoken, String orderId, String itemId,
			String productId, String powerType) throws Exception {

		String RequestUrl_updateOrderItemPrescription = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_PUT_POWER_BY_ORDERID_AND_ITEMID_PATH, orderId, itemId);
		log.debug("URL of Put: " + RequestUrl_updateOrderItemPrescription);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));
		// headers.add(new BasicNameValuePair("X-B3-TraceId",
		// RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_updateOrderItemPrescription = new JSONObject();

		JSONObject power = ProductUtil.getAvailablePowersForAProduct(productId, powerType);
		JSONArray powerTypeList = power.getJSONObject("result").getJSONArray("powerTypeList");
		JSONObject left = new JSONObject();
		JSONObject right = new JSONObject();
		for (int i = 0; i < powerTypeList.length(); i++) {
			String type = powerTypeList.getJSONObject(i).getString("type");
			JSONArray valueArray = power.getJSONObject("result").getJSONArray("powerTypeList").getJSONObject(i)
					.getJSONArray("powerDataList").getJSONObject(0).getJSONArray("value");
			if (valueArray.length() >= 1) {
				left.put(type, valueArray.getString(valueArray.length() - 1));
				right.put(type, valueArray.getString(valueArray.length() - 1));
			}
		}

		Requestparams_updateOrderItemPrescription.put("left", left);
		Requestparams_updateOrderItemPrescription.put("right", right);
		Requestparams_updateOrderItemPrescription.put("notes", "Test order power update");
		Requestparams_updateOrderItemPrescription.put("powerType", powerType);
		Requestparams_updateOrderItemPrescription.put("prescriptionType", powerType.toUpperCase());
		Requestparams_updateOrderItemPrescription.put("userName", ApplicationConstants.username);

		HttpResponse httpResponseUpdateOrderItemPrescription = RequestUtil.putRequest(
				RequestUrl_updateOrderItemPrescription, null, headers, Requestparams_updateOrderItemPrescription);
		JSONObject responseJSON_UpdateOrderItemPrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseUpdateOrderItemPrescription);

		Assert.assertEquals(httpResponseUpdateOrderItemPrescription.getStatusLine().getStatusCode(), 200,
				GenericUtil.printAPICallDetails(sessiontoken, RequestUrl_updateOrderItemPrescription,
						Requestparams_updateOrderItemPrescription, responseJSON_UpdateOrderItemPrescription));

		if (httpResponseUpdateOrderItemPrescription.getStatusLine().getStatusCode() == 200) {
			JSONArray items = responseJSON_UpdateOrderItemPrescription.getJSONObject("result").getJSONArray("items");
			if (items.length() > 0) {
				for (int j = 0; j < items.length(); j++) {
					// log.debug("Prescription
					// details"+prescription.getJSONObject(j).get("prescription"));
					if (items.getJSONObject(j).getString("id").equals(itemId)) {
						Assert.assertEquals(items.getJSONObject(j).getString("powerRequired"), "POWER_SUBMITTED",
								"Power is not submitted");
						JSONObject presJSON = items.getJSONObject(j).getJSONObject("prescription");
						Assert.assertEquals(presJSON.getString("powerType"), powerType, "powerType is not correct");
					}
				}
			}
		}
		log.debug("-------------Item prescription is updated properly-----------------");
	}

	public static JSONObject getAllOrdersFromAPIWithEmail(String email, String sessionToken) throws Exception {

		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GATEWAY_GET_ALL_ORDERS;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "x_Api_Client"));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));

		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("email", email));

		HttpResponse httpresponse_getAllOrders = RequestUtil.getRequest(RequestUrl, header, queryparam);
		JSONObject jsonResponse_getAllOrders = RequestUtil.convertHttpResponseToJsonObject(httpresponse_getAllOrders);

		return jsonResponse_getAllOrders;

	}

	public static JSONObject getAllOrdersFromAPIWithPhone(String phone, String sessionToken) throws Exception {

		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GATEWAY_GET_ALL_ORDERS;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "x_Api_Client"));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));

		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("phone", phone));

		HttpResponse httpresponse_getAllOrders = RequestUtil.getRequest(RequestUrl, header, queryparam);
		JSONObject jsonResponse_getAllOrders = RequestUtil.convertHttpResponseToJsonObject(httpresponse_getAllOrders);
		return jsonResponse_getAllOrders;

	}

	public static String returnOrderStatusAndStateFromSalesFlatOrderTable(String orderId) {
		String sql = "Select status,state from sales_flat_order where increment_id =" + orderId;
		return sql;
	}

	public static String returnOrderStateFromOrdersTable(String orderId) {
		String sql = "Select status,state from orders where increment_id =" + orderId;
		return sql;
	}

	public static String updateOrderStatusInOrdersTable(String orderId, String state, String status) {
		String sql = "Update orders set state= " + state + ",status=" + status + " where increment_id =" + orderId;
		return sql;
	}

	public static void creditWalletForOrder(String sessionToken, String mobileNumber, String amount, String walletType,
			String orderId, String narration, String expiryDate) throws Exception {
		String RequestUrlcreditWalletForOrder = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.CREDIT_WALLET_FOR_ORDER, orderId);
		JSONArray reqObject = new JSONArray();
		JSONObject requestObject = new JSONObject();
		UUID merchantRefId = UUID.randomUUID();
		requestObject.put("amount", amount);
		requestObject.put("merchantRefId", String.valueOf(merchantRefId));
		requestObject.put("mobileNumber", mobileNumber);
		requestObject.put("narration", narration);
		requestObject.put("walletType", walletType);
		if (expiryDate != null)
			requestObject.put("expiryDate", expiryDate);
		reqObject.put(requestObject);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-Auth-Token", "8e8b0816-4c73-4f08-8f7d-022dcd186a91"));

		HttpResponse httpResponseCreditWalletTransactionForOrder = RequestUtil
				.postRequestFormData(RequestUrlcreditWalletForOrder, headers, null, reqObject);
		JSONObject creditResponse = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseCreditWalletTransactionForOrder);
		Assert.assertEquals(httpResponseCreditWalletTransactionForOrder.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject creditResponseResult = creditResponse.getJSONObject("result");
		Assert.assertEquals(creditResponseResult.getInt("code"), 0, "code mismatch");
		Assert.assertEquals(creditResponseResult.getString("message"), "Ok", "message mismatch");
		JSONArray data = creditResponseResult.getJSONArray("data");
		for (int i = 0; i < data.length(); i++) {
			JSONObject dataJson = data.getJSONObject(i);
			Assert.assertEquals(dataJson.getInt("code"), 0, "code mismatch");
			Assert.assertEquals(dataJson.getString("message"), "Ok", "message mismatch");
			Assert.assertNotNull(dataJson.getString("txnRefNum"), "txnRefNum mismatch");
			Assert.assertNotNull(dataJson.getString("txnNum"), "txnNum mismatch");
		}
	}

	public static JSONObject getOrder(String sessiontoken, String x_Api_Client, String orderId) throws Exception {
		String RequestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_GET_ORDER_PATH, orderId);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Accept", "application/json"));
		header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		log.info("GetOrder URL of Get: " + RequestUrl);
		HttpResponse httpresponse_getOrders = RequestUtil.getRequest(RequestUrl, header, null);
		JSONObject responseJson_GetOrder = RequestUtil.convertHttpResponseToJsonObject(httpresponse_getOrders);
		Assert.assertEquals(httpresponse_getOrders.getStatusLine().getStatusCode(), 200, " Status code is  correct");
		return responseJson_GetOrder;

	}

	public static JSONObject getOrder(String sessiontoken, String x_Api_Client, String orderId, String authToken)
			throws Exception {
		String RequestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_GET_ORDER_PATH, orderId);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Accept", "application/json"));
		header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		header.add(new BasicNameValuePair("x-auth-token", authToken));

		GenericUtil.curlBuilder("POST", null, RequestUrl, header, null);
		HttpResponse httpresponse_getOrders = RequestUtil.getRequest(RequestUrl, header, null);
		JSONObject responseJson_GetOrder = RequestUtil.convertHttpResponseToJsonObject(httpresponse_getOrders);
		Assert.assertEquals(httpresponse_getOrders.getStatusLine().getStatusCode(), 200, " Status code is  correct");
		return responseJson_GetOrder;

	}

	public static JSONObject cancelInvoice(String sessiontoken, String x_Api_Client, String orderId, String reasonId,
			String reasonText) throws Exception {
		String RequestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_PUT_CANCEL_ORDER_PATH, orderId);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));

		JSONObject Requestparams_CancelOrder = new JSONObject();
		Requestparams_CancelOrder.put("reasonId", reasonId);
		Requestparams_CancelOrder.put("reasonText", reasonText);

		HttpResponse httpresponse_CancelOrder = RequestUtil.putRequest(RequestUrl, null, header,
				Requestparams_CancelOrder);
		JSONObject responseJson_putOrder = RequestUtil.convertHttpResponseToJsonObject(httpresponse_CancelOrder);
		log.info("responseJson_GetOrder   " + responseJson_putOrder);
		Assert.assertEquals(httpresponse_CancelOrder.getStatusLine().getStatusCode(), 200, " Status code is  correct");
		Assert.assertEquals(responseJson_putOrder.getJSONObject("result").getJSONObject("status").getString("status"),
				"CANCELLATION_IN_PROCESS", "status mismatch");
		return responseJson_putOrder;

	}

	public static JSONObject updateOrderStatus(String sessiontoken, String x_Api_Client, String orderId, String status,
			String state) throws Exception {
		String requestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_PUT_STATUS_BY_ORDERID_PATH, orderId);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));

		JSONObject statusRequest = new JSONObject();
		statusRequest.put("state", state);
		statusRequest.put("status", status);

		HttpResponse httpRequest = RequestUtil.putRequest(requestUrl, headers, statusRequest);
		JSONObject orderstatusResponse = RequestUtil.convertHttpResponseToJsonObject(httpRequest);
		log.info("orderstatusResponse: " + orderstatusResponse);
		Assert.assertEquals(httpRequest.getStatusLine().getStatusCode(), 200, " Status code is  correct");
		return orderstatusResponse;

	}

	public static JSONObject updateOrderStatusWithEmail(String sessiontoken, String x_Api_Client, String orderId,
			String status, String state, String email) throws Exception {
		String requestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_PUT_STATUS_BY_ORDERID_PATH, orderId);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));

		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("email", email));

		JSONObject statusRequest = new JSONObject();
		statusRequest.put("state", state);
		statusRequest.put("status", status);

		HttpResponse httpRequest = RequestUtil.putRequest(requestUrl, param, headers, statusRequest);
		JSONObject orderstatusResponse = RequestUtil.convertHttpResponseToJsonObject(httpRequest);
		log.info("orderstatusResponse: " + orderstatusResponse);
		Assert.assertEquals(httpRequest.getStatusLine().getStatusCode(), 200, " Status code is  correct");
		return orderstatusResponse;
	}

	public static JSONObject createOrderPayment(String x_Api_Client, String sessiontoken, String method)
			throws Exception {
		String RequestUrl_createOrder = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_POST_ORDERS_PATH);
		log.debug("session: " + sessiontoken);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		// headers.add(new BasicNameValuePair("X-B3-TraceId",
		// RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrder = new JSONObject();
		JSONObject payments = new JSONObject();
		JSONArray paymentList = new JSONArray();
		JSONObject paymentList1 = new JSONObject();

		paymentList1.put("method", method);
		paymentList1.put("gateway", "PU");
		paymentList.put(paymentList1);

		JSONObject card = new JSONObject();
		paymentList1.put("card", card);
		payments.put("paymentList", paymentList);
		Requestparams_createOrder.put("payments", payments);

		JSONObject address = new JSONObject();
		address.put("firstName", "test");
		address.put("lastName", "test");
		address.put("phone", "9970814760");
		address.put("email", "<EMAIL>");
		address.put("addressline1", "test dont proceed this order");
		address.put("city", "test dont proceed this order");
		address.put("state", "test dont proceed this order");
		address.put("postcode", "560038");
		address.put("country", "IN");

		Requestparams_createOrder.put("billingAddress", address);

		log.debug("CreateOrder URL of Post: " + RequestUrl_createOrder);

		HttpResponse httpResponseCreateOrder = RequestUtil.postRequest(RequestUrl_createOrder, headers,
				Requestparams_createOrder);
		JSONObject responseJSON_CreateOrder = RequestUtil.convertHttpResponseToJsonObject(httpResponseCreateOrder);
		Assert.assertEquals(httpResponseCreateOrder.getStatusLine().getStatusCode(), 200,
				"SessionToken: " + sessiontoken + "\nOrder API:" + GenericUtil.printAPICallDetails(
						RequestUrl_createOrder, Requestparams_createOrder, responseJSON_CreateOrder));

		JSONObject result = responseJSON_CreateOrder.getJSONObject("result");
		return result;

	}

	public static String createOrder(String x_Api_Client, String sessiontoken, String method, String telephone)
			throws Exception {
		String RequestUrl_createOrder = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_POST_ORDERS_PATH);
		log.debug("session: " + sessiontoken);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrder = new JSONObject();
		JSONObject payments = new JSONObject();
		JSONArray paymentList = new JSONArray();
		JSONObject paymentList1 = new JSONObject();
		paymentList1.put("method", method);
		paymentList1.put("gateway", "PU");
		paymentList.put(paymentList1);
		JSONObject card = new JSONObject();
		paymentList1.put("card", card);
		payments.put("paymentList", paymentList);
		Requestparams_createOrder.put("payments", payments);
		JSONObject address = new JSONObject();
		address.put("firstName", "test");
		address.put("lastName", "test");
		address.put("phone", telephone);
		address.put("email", "<EMAIL>");
		address.put("addressline1", "test dont proceed this order");
		address.put("city", "test dont proceed this order");
		address.put("state", "test dont proceed this order");
		address.put("postcode", "560038");
		address.put("country", "IN");
		Requestparams_createOrder.put("billingAddress", address);

		log.debug("CreateOrder URL of Post: " + RequestUrl_createOrder);
		HttpResponse httpResponseCreateOrder = RequestUtil.postRequest(RequestUrl_createOrder, headers,
				Requestparams_createOrder);
		JSONObject responseJSON_CreateOrder = RequestUtil.convertHttpResponseToJsonObject(httpResponseCreateOrder);
		Assert.assertEquals(httpResponseCreateOrder.getStatusLine().getStatusCode(), 200,
				"SessionToken: " + sessiontoken + "\nOrder API:" + GenericUtil.printAPICallDetails(
						RequestUrl_createOrder, Requestparams_createOrder, responseJSON_CreateOrder));
		JSONObject result = responseJSON_CreateOrder.getJSONObject("result");
		String orderId = result.getString("id");
		Assert.assertEquals(200, responseJSON_CreateOrder.getInt("status"), "Response status is not correct");
		return orderId;
	}

	public void getState() throws Exception {
		Map<String, Vector> STATE = new HashMap<String, Vector>();
		Vector<String> vector = new Vector<String>();

		Vector<String> vector2 = new Vector<String>();
		Vector<String> vector1 = new Vector<String>();
		Vector<String> vector3 = new Vector<String>();
		Vector<String> vector4 = new Vector<String>();
		Vector<String> vector5 = new Vector<String>();
		Vector<String> vector6 = new Vector<String>();

		// Inserting elements into the table
		vector2.add("PENDING_PAYMENT");
		vector2.add("PROCESSING");
		vector2.add("HOLDED");
		vector2.add("CANCELED");
		STATE.put("NEW", vector2);

		vector1.add("PENDING_PAYMENT");
		vector1.add("PROCESSING");
		vector1.add("NEW");
		vector1.add("CANCELED");
		vector1.add("CLOSED");
		STATE.put("HOLDED", vector1);

		vector3.add("PAYMENT_REVIEW");
		vector3.add("PROCESSING");
		STATE.put("PENDING_PAYMENT", vector3);

		vector4.add("PROCESSING");
		STATE.put("PAYMENT_REVIEW", vector4);

		vector6.add("PENDING_PAYMENT");
		vector6.add("HOLDED");
		vector6.add("COMPLETE");
		vector6.add("CANCELED");
		vector6.add("CLOSED");
		STATE.put("PROCESSING", vector6);

		vector5.add("CLOSED");
		STATE.put("COMPLETE", vector5);

		// System.out.println(STATE);

	}

	@Test(enabled = true)

	public void getStatus() throws Exception {

		Map<String, Vector> STATUS = new HashMap<String, Vector>();

		Vector<String> vector1 = new Vector<String>();
		Vector<String> vector2 = new Vector<String>();
		Vector<String> vector3 = new Vector<String>();
		Vector<String> vector4 = new Vector<String>();
		Vector<String> vector5 = new Vector<String>();
		Vector<String> vector6 = new Vector<String>();
		Vector<String> vector7 = new Vector<String>();
		Vector<String> vector8 = new Vector<String>();

		vector1.add("PENDING");
		vector1.add("PENDING_FOLLOWEDUP");
		vector1.add("PROCESSING_NEW");
		vector1.add("MONEY_ACCEPTED_OFFLINE_NO");
		vector1.add("PENDING_PAYMENT_CHECK");
		vector1.add("MONEY_ACCEPTED_OFFLINE_DELIVERED");
		vector1.add("PROCESSING_STORECREDIT");
		vector1.add("PENDING_PARTIALPAYMENT");
		STATUS.put("NEW", vector2);

		vector2.add("PENDING_PAYMENT");
		STATUS.put("PENDING_PAYMENT", vector2);

		vector3.add("PAYMENT_REVIEW");
		STATUS.put("PAYMENT_REVIEW", vector3);

		vector4.add("PROCESSING");
		vector4.add("PROCESSING_CONFIRMATION_PENDING");
		vector4.add("PROCESSING_POWER_FOLLOWUP");
		vector4.add("PROCESSING_POWER_FOLLOWUP_HTO");
		vector4.add("PROCESSING_RED_FLAG");
		vector4.add("PROCESSING_PACKED");
		vector4.add("PROCESSING_BLACKLIST");
		vector4.add("PROCESSING_CHARGEBACK");
		vector4.add("PROCESSING_POWER_FOLLOWUP_VERIFY");
		vector4.add("PROCESSING_POWERFOLLOWUP_CONFIRM");
		vector4.add("PROCESSING_POWER_FOLLOWUP_CONFIR");
		STATUS.put("PROCESSING", vector4);

		vector5.add("COMPLETE");
		vector5.add("COMPLETE_SHIPPED");
		vector5.add("COMPLETE_CHARGEBACK");
		vector5.add("COMPLETE_REDFLAG");
		vector5.add("COMPLETE_DELIVERED_IN_1_DAY");
		vector5.add("COMPLETE_DELIVERED_IN_2_DAYS");
		vector5.add("COMPLETE_DELIVERED_IN_3_DAYS");
		vector5.add("COMPLETE_DELIVERED_IN_5_DAYS");
		vector5.add("COMPLETE_DELIVERED_IN_7_PLUS_DAY");
		vector5.add("COMPLETE_INCORRECT_ADDRESS");
		vector5.add("COMPLETE_DELAY_SHIPMENT");
		vector5.add("COMPLETE_DELIVERED_TAMPERED");
		vector5.add("COMPLETE_LOST_BY_COURIER");
		vector5.add("COMPLETE_NO_SERVICE");
		vector5.add("COMPLETE_ON_HOLD");
		vector5.add("DELIVERED");
		vector5.add("AWAITED_RTO");
		vector5.add("INTERNAL_RTO");
		vector5.add("COMPLETE_ARRANGED_RTO");
		vector5.add("COMPLETE_RECEIVED_RTO");
		vector5.add("PREDELIVERY_PRODUCTCHANGE");
		STATUS.put("COMPLETE", vector5);

		vector6.add("CLOSED");
		vector6.add("RETURN_CHANGED");
		vector6.add("CLOSED_CHARGEBACK");
		vector6.add("CLOSED_LOST_BY_COURIER");
		vector6.add("RETURN_REFUNDED");
		vector6.add("INTERNAL_RTO");
		vector6.add("RETURN_REFUNDED_PART");
		vector6.add("RETURN_REFUNDED_ONLINE");
		vector6.add("RETURN_REFUNDED_CHECK");
		vector6.add("LOST_REFUNDED_ONLINE");
		vector6.add("LOST_REFUNDED_CHECK");
		vector6.add("PREDELIVERY_PRODUCTCHANGE");
		vector6.add("PREDELIVERY_REFUND");
		vector6.add("PRE_DELIVERY_STORECREDIT");
		vector6.add("RTO");
		vector6.add("CLOSED_AWAITED_RTO");
		vector6.add("TESTORDER");
		vector6.add("ORDER_NOT_CONFIRMED");
		vector6.add("RETURN_RESHIP");
		vector6.add("RETURN_REFUNDED_SC_DELAY");
		vector6.add("RETURN_REFUNDED_STORE_CREDIT");
		vector6.add("SC_REFUNDED_RECEIVING_PENDING");
		vector6.add("FC_REFUNDED_RECEIVING_PENDING");
		vector6.add("LOST_REFUNDED_STORE_CREDIT");
		vector6.add("RETURN_REFUNDED_FRANCHISE_CREDIT");
		vector6.add("RETURN_RESHIP_DELIVERED");
		vector6.add("RETURN_REFUNDED_NEFT");
		vector6.add("RETURN_REFUNDED_OFFLINE");
		vector6.add("RETURN_CHANGE_REFUNDED");
		vector6.add("INITIATED_REVERSE");
		STATUS.put("CLOSED", vector6);

		vector7.add("CANCELED");
		vector7.add("CANCELED_PAYMENT_CHECK");
		vector7.add("PREDELIVERY_CANCELLATION");
		vector7.add("CANCELED_CUSTOMER_REQUEST");
		vector7.add("CANCELED_DUPLIACTE");
		vector7.add("CANCELED_FAKE");
		vector7.add("CANCELED_NRE");
		STATUS.put("CANCELED", vector7);

		vector8.add("HOLDED");
		vector8.add("HOLD_COD_CONFIRMATION");
		vector8.add("HOLD_POSTPONE_DELIVERY");
		vector8.add("HOLD_ADDRESS_CONFIRMATION");
		vector8.add("HOLD_POWER_CONFIRMATION");
		vector8.add("HOLD_PAYMENT_CONFIRMATION");
		vector8.add("HOLD_CANCELLATION");
		vector8.add("HOLD_DUPLICATE");
		STATUS.put("HOLDED", vector8);

		log.info("status :" + STATUS);

	}

	public static JSONObject giveMeOrder(Boolean isPowerEyeglass, Boolean isPowerSunglass, String apiClient,
			String paymentMethod, String... input) throws Exception {
		return giveMeOrder(null, isPowerEyeglass, isPowerSunglass, apiClient, paymentMethod, input);
	}

	public static JSONObject giveMeOrder(String session, Boolean isPowerEyeglass, Boolean isPowerSunglass,
			String apiClient, String paymentMethod, String... input) throws Exception {
		String x_api_client = apiClient;
		String sessionToken;
		if (session == null) {
			sessionToken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
					ApplicationConstants.userPassword, x_api_client);
		} else {
			sessionToken = session;
		}
		CartUtil.clearCart(sessionToken);
		JSONObject cart_response = null;
		CartItem reqObj = new CartItem();
		for (String i : input) {
			log.info(!i.equalsIgnoreCase("HTO") && !i.equalsIgnoreCase("Gold") && !i.equalsIgnoreCase("insurance"));
			if (!i.equalsIgnoreCase("HTO") && !i.equalsIgnoreCase("Gold") && !i.equalsIgnoreCase("insurance")) {
				JSONObject subCategoryResponse = JunoV1Util.returnAllSubCategoryId("men", i);
				JSONObject getCategoryIdResult = subCategoryResponse.getJSONObject("result");
				JSONArray subcategory = getCategoryIdResult.getJSONArray("subcategory");
				String categoryId = subcategory.getJSONObject(0).getString("id");
				JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
				if (isPowerEyeglass && i.equalsIgnoreCase("eyeglasses")) {
					product = ApplicationUtil.getProductDetails(jsonResponse_category,
							ApplicationConstants.PowerTypes.SINGLE_VISION, false);
					reqObj.setPackageId(product.get("packageId"));
					reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
				} else if (isPowerSunglass && i.equalsIgnoreCase("sunglasses")) {
					product = ApplicationUtil.getProductDetails(jsonResponse_category,
							ApplicationConstants.PowerTypes.SUNGLASSES, false);
					reqObj.setPackageId(product.get("packageId"));
					reqObj.setPowerType(ApplicationConstants.PowerTypes.SUNGLASSES);
				} else {
					product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
				}
				// Add to Cart

				if (i.equalsIgnoreCase("contact_lenses")) {
					Prescription presObj = new Prescription();
					presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
					presObj.setDob("12-Aug-92");
					presObj.setGender("Female");
					// Left Power
					Power leftPower = new Power();
					leftPower.setSph("-0.75");
					leftPower.setBoxes(Integer.valueOf("1"));

					presObj.setLeft(leftPower);
					// Right Power
					Power rightPower = new Power();
					rightPower.setSph("-0.75");
					rightPower.setBoxes(Integer.valueOf("1"));

					presObj.setRight(rightPower);
					presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
					presObj.setUserName("user1");
					reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
					reqObj.setSubscriptionId("6");
					reqObj.setIsBothEye("Y");
					reqObj.setPrescription(presObj);
				}
			}
			if (i.equalsIgnoreCase("HTO")) {
				// Set HTO Object
				Hto htoObj = new Hto();
				htoObj.setCity("Faridabad");
				htoObj.setDate(2017 - 04 - 13);
				htoObj.setLenskartAtHome("CASE3_HEC");
				htoObj.setSlotId(6729405);
				reqObj.setHto(htoObj);
				// Set HTO Product ID
				reqObj.setProductId(Long.parseLong("47552"));
			}

			if (i.equalsIgnoreCase("Gold")) {
				reqObj.setProductId(Long.parseLong(ApplicationConstants.goldPid));
			}

			if (i.equalsIgnoreCase("insurance")) {
				reqObj.setProductId(Long.parseLong(ApplicationConstants.insurancePid));
			}

			if (!i.equalsIgnoreCase("HTO") && !i.equalsIgnoreCase("Gold") && !i.equalsIgnoreCase("insurance")) {
				reqObj.setProductId(Long.parseLong(product.get("productId")));
			}

			cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
		}
		String cartId = cart_response.getJSONObject("result").getString("id");
		Assert.assertNotNull(cartId);
		Address address = ApplicationUtil.shipping_address();
		CartUtil.saveAddress(sessionToken, x_api_client, address, null);
		JSONObject payment_response = null;
		if (paymentMethod.equalsIgnoreCase("nb")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, "PU:162B", paymentMethod, "PU");
		} else if (paymentMethod.equalsIgnoreCase("cod")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, null);
		} else if (paymentMethod.equalsIgnoreCase("QRCODE")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, "payuwallet", "PU:QRCODE");
		} else if (paymentMethod.equalsIgnoreCase("UPI")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, "payuwallet", "PU:UPI");
		} else {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, "PU");
		}
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2, "Response is not proper");

		JSONObject payment_response_result = payment_response.getJSONObject("result");
		// String orderId =
		// payment_response_result.getJSONObject("order").getString("id");
		return payment_response_result;

	}

	public static JSONObject giveMeOrderForNexS(String session, String fr0, String fr1, String fr2, String pincode,
			String storeCreditCode, String storeCreditAmount, String paymentMethod) throws Exception {

		String x_api_client = XApiClient.DESKTOP;
		String sessionToken = session;
		CartUtil.clearCart(sessionToken);
		JSONObject cart_response = null;
		CartItem reqObj = new CartItem();
		if (Strings.isNotNullAndNotEmpty(fr0)) {
			String[] split = fr0.split(",");
			for (String i : split) {
				if (!i.equalsIgnoreCase(ApplicationConstants.goldPid)) {
					JSONObject jsonResponse_product = JunoV1Util.getProductDetails(i, x_api_client);
					Assert.assertNotNull(jsonResponse_product.getJSONObject("result"), "product API is not working");
					reqObj.setProductId(Long.parseLong(i));
				}
				if (i.equalsIgnoreCase(ApplicationConstants.goldPid)) {
					reqObj.setProductId(Long.parseLong(ApplicationConstants.goldPid));
				}
				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		if (Strings.isNotNullAndNotEmpty(fr1)) {
			String[] split = fr1.split(",");
			for (String i : split) {
				if (!i.equalsIgnoreCase(ApplicationConstants.goldPid)) {
					product = ApplicationUtil.getProductDetails(i, ApplicationConstants.PowerTypes.SINGLE_VISION, false,
							x_api_client);
					reqObj.setProductId(Long.parseLong(i));
					reqObj.setPackageId(product.get("packageId"));
					reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
				}
				if (i.equalsIgnoreCase(ApplicationConstants.goldPid)) {
					reqObj.setProductId(Long.parseLong(ApplicationConstants.goldPid));
				}
				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		if (Strings.isNotNullAndNotEmpty(fr2)) {
			log.info(fr2);
			String[] split = fr2.split(",");
			for (String i : split) {
				if (!i.equalsIgnoreCase(ApplicationConstants.goldPid)) {
					product = ApplicationUtil.getProductDetails(i, ApplicationConstants.PowerTypes.SUNGLASSES, false,
							x_api_client);
					reqObj.setProductId(Long.parseLong(i));
					reqObj.setPackageId(product.get("packageId"));
					reqObj.setPowerType(ApplicationConstants.PowerTypes.SUNGLASSES);
				}
				if (i.equalsIgnoreCase(ApplicationConstants.goldPid)) {
					reqObj.setProductId(Long.parseLong(ApplicationConstants.goldPid));
				}
				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		String cartId = cart_response.getJSONObject("result").getString("id");
		Assert.assertNotNull(cartId);
		Address address = ApplicationUtil.addShippingAddress1(pincode);
		CartUtil.saveAddress(sessionToken, x_api_client, address, null);

		if (Strings.isNotNullAndNotEmpty(storeCreditCode)) {
			String addStoreCreditURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddStoreCredit).trim();
			String addStoreCredit_URL = addStoreCreditURL.replace("STORECREDITCODE", storeCreditCode);
			addStoreCredit_URL = addStoreCredit_URL.replace("STORECREDITAMOUNT", Integer.toString(1));
			JSONObject scResponse = CartUtil.addStoreCredit(sessionToken, storeCreditCode,
					Integer.valueOf(storeCreditAmount));
			Assert.assertTrue(scResponse.has("result"), "API Response: "
					+ GenericUtil.printAPICallDetails(sessionToken, addStoreCredit_URL, null, scResponse));
			log.debug(
					"*********" + GenericUtil.printAPICallDetails(sessionToken, addStoreCredit_URL, null, scResponse));
			JSONArray discounts = scResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
			for (int i = 0; i < discounts.length(); i++) {
				if (discounts.getJSONObject(i).getString("type").equals("sc")) {
					Assert.assertEquals(discounts.getJSONObject(i).getString("code"), storeCreditCode,
							"sc code mismatch");
				}
			}
			double amountTobePaid = scResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
			if (amountTobePaid <= 0) {
				paymentMethod = "sc";
			}
		}
		JSONObject payment_response = null;
		if (paymentMethod.equalsIgnoreCase("nb")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, "PU:162B", paymentMethod, "PU");
		} else if (paymentMethod.equalsIgnoreCase("cod")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, null);
		} else if (paymentMethod.equalsIgnoreCase("QRCODE")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, "payuwallet", "PU:QRCODE");
		} else if (paymentMethod.equalsIgnoreCase("UPI")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, "payuwallet", "PU:UPI");
		} else if (paymentMethod.equalsIgnoreCase("sc")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, null);
		} else {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, "PU");
		}
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2, "Response is not proper");
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		return payment_response_result;
	}

	public static JSONObject giveMeOrderForNexS(String session, String fr0, String fr1, String fr2, String loyalty,
			String cl, String lensType, String pincode, String storeCreditCode, String storeCreditAmount,
			String paymentMethod) throws Exception {
		String x_api_client = XApiClient.DESKTOP;
		String sessionToken = session;
		CartUtil.clearCart(sessionToken, x_api_client);
		JSONObject cart_response = null;
		ArrayList<Long> pids = new ArrayList<Long>();
		CartItem reqObj = new CartItem();
		if (Strings.isNotNullAndNotEmpty(fr0)) {
			String[] split = fr0.split(",");
			for (String i : split) {
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(i, x_api_client);
				Assert.assertNotNull(jsonResponse_product.getJSONObject("result"), "product API is not working");
				reqObj.setProductId(Long.parseLong(i));
				pids.add(Long.parseLong(i));
				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		if (Strings.isNotNullAndNotEmpty(fr1)) {
			String[] split = fr1.split(",");
			for (String i : split) {
				reqObj = new CartItem();
				product = ApplicationUtil.getProductDetails(i, lensType, false, x_api_client);
				reqObj.setProductId(Long.parseLong(i));
				pids.add(Long.parseLong(i));
				reqObj.setPackageId(product.get("packageId"));
				reqObj.setPowerType(lensType);
				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		if (Strings.isNotNullAndNotEmpty(fr2)) {
			log.info(fr2);
			String[] split = fr2.split(",");
			for (String i : split) {
				reqObj = new CartItem();
				product = ApplicationUtil.getProductDetails(i, ApplicationConstants.PowerTypes.SUNGLASSES, false,
						x_api_client);
				reqObj.setProductId(Long.parseLong(i));
				pids.add(Long.parseLong(i));
				reqObj.setPackageId(product.get("packageId"));
				reqObj.setPowerType(ApplicationConstants.PowerTypes.SUNGLASSES);
				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		if (Strings.isNotNullAndNotEmpty(loyalty)) {
			log.info(loyalty);
			String[] split = loyalty.split(",");
			for (String i : split) {
				reqObj = new CartItem();
				reqObj.setProductId(Long.parseLong(i));
				pids.add(Long.parseLong(i));
				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		if (Strings.isNotNullAndNotEmpty(cl)) {
			log.info(cl);
			String[] split = cl.split(",");
			for (String i : split) {
				reqObj = new CartItem();
				reqObj.setProductId(Long.parseLong(i));
				pids.add(Long.parseLong(i));
				reqObj.setQuantity(2);
				Prescription presObj = new Prescription();
				presObj.setDob("");
				presObj.setGender("");
				presObj.setNotes("");
				presObj.setUserName("lenskart user");
				presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));

				Power leftPower = new Power();
				leftPower.setSph("-0.75");
//				leftPower.setCyl("0.00");
//				leftPower.setAxis("0");
//				leftPower.setAp("0.00");// leftPower.setSph("Call Me/Email Me for Power");
				leftPower.setBoxes(Integer.valueOf("1"));
				presObj.setLeft(leftPower);

				Power rightPower = new Power();
				rightPower.setSph("-0.75");
//				rightPower.setCyl("0.00");
//				rightPower.setAxis("0");
//				rightPower.setAp("0.00");
				rightPower.setBoxes(Integer.valueOf("1"));
				presObj.setRight(rightPower);

				reqObj.setPrescription(presObj);

				cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
			}
		}
		JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
		log.info("PIDs added - "+ pids);
		for(int i=0;i<items.length();i++) {
			if(!pids.contains(items.getJSONObject(i).getLong("productId"))) 
				cart_response =	CartUtil.removeItemFromCart(sessionToken, items.getJSONObject(i).getString("id"), x_api_client);
		}
//		JSONArray items1 = cart_response.getJSONObject("result").getJSONArray("items");
//		log.info("PIDs added - "+ pids);
//		for(int i=0;i<items1.length();i++) {
//			if(!pids.contains(items1.getJSONObject(i).getLong("productId"))) 
//				cart_response =	CartUtil.removeItemFromCart(sessionToken, items1.getJSONObject(i).getString("id"), x_api_client);
//		}
//		
//		JSONArray items2 = cart_response.getJSONObject("result").getJSONArray("items");
//		log.info("PIDs added - "+ pids);
//		for(int i=0;i<items2.length();i++) {
//			if(!pids.contains(items2.getJSONObject(i).getLong("productId"))) 
//				cart_response =	CartUtil.removeItemFromCart(sessionToken, items2.getJSONObject(i).getString("id"), x_api_client);
//		}
		String cartId = cart_response.getJSONObject("result").getString("id");
		Assert.assertNotNull(cartId);
		
		JSONArray cartDiscounts = cart_response.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
		IntStream.range(0, cartDiscounts.length()).forEach(i->{
			try {
				if(cartDiscounts.getJSONObject(i).getString("type").equals("gv")) {
					CartUtil.removeGiftVoucher(sessionToken, cartDiscounts.getJSONObject(i).getString("code"));
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		});
		Address address = null;
		if(Environments.country.name() =="IN")
		 address = ApplicationUtil.addShippingAddress1(pincode);
		else if(Environments.country.name() =="SG")
			 address = ApplicationUtil.addShippingAddressSingapore(pincode);
		else if(Environments.country.name() =="AE")
			 address = ApplicationUtil.addShippingAddressUAE(pincode);
		else if(Environments.country.name() =="TH")
			 address = ApplicationUtil.addShippingAddressTH(pincode);
		else if(Environments.country.name() =="SA")
			 address = ApplicationUtil.addShippingAddressSA(pincode);
			

		CartUtil.saveAddress(sessionToken, x_api_client, address, null);

		if (Strings.isNotNullAndNotEmpty(storeCreditCode)) {
			String addStoreCreditURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddStoreCredit).trim();
			String addStoreCredit_URL = addStoreCreditURL.replace("STORECREDITCODE", storeCreditCode);
			addStoreCredit_URL = addStoreCredit_URL.replace("STORECREDITAMOUNT", Integer.toString(1));
			JSONObject scResponse = CartUtil.addStoreCredit(sessionToken, storeCreditCode,
					Integer.valueOf(storeCreditAmount));
			Assert.assertTrue(scResponse.has("result"), "API Response: "
					+ GenericUtil.printAPICallDetails(sessionToken, addStoreCredit_URL, null, scResponse));
			log.debug(
					"*********" + GenericUtil.printAPICallDetails(sessionToken, addStoreCredit_URL, null, scResponse));
			JSONArray discounts = scResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
			for (int i = 0; i < discounts.length(); i++) {
				if (discounts.getJSONObject(i).getString("type").equals("sc")) {
					Assert.assertEquals(discounts.getJSONObject(i).getString("code"), storeCreditCode,
							"sc code mismatch");
				}
			}
			double amountTobePaid = scResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
			if (amountTobePaid <= 0) {
				paymentMethod = "sc";
			}
		}
		JSONObject payment_response = null;
		if (paymentMethod.equalsIgnoreCase("nb")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, "PU:162B", paymentMethod, "PU");
		} else if (paymentMethod.equalsIgnoreCase("cod")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, null);
		} else if (paymentMethod.equalsIgnoreCase("offlinecash")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, null);
		}else if (paymentMethod.equalsIgnoreCase("QRCODE")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, "payuwallet", "PU:QRCODE");
		} else if (paymentMethod.equalsIgnoreCase("UPI")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, "payuwallet", "PU:UPI");
		} else if (paymentMethod.equalsIgnoreCase("sc")) {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, null);
		} else {
			payment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, "PU");
		}
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2, "Response is not proper");
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		return payment_response_result;
	}

	public static JSONObject giveMeOrderForCommunication(Boolean isPowerEyeglass, Boolean isPowerSunglass,
			String apiClient, String paymentMethod, String email, String mobileNumber, String... input)
			throws Exception {
		String x_api_client = apiClient;
		String sessionToken = CustomerUtil.get_sessionId_after_user_authentication("<EMAIL>",
				"password123", x_api_client);
		CartUtil.clearCart(sessionToken);
		JSONObject cart_response = null;
		for (String i : input) {
			JSONObject subCategoryResponse = JunoV1Util.returnAllSubCategoryId("men", i);
			JSONObject getCategoryIdResult = subCategoryResponse.getJSONObject("result");
			JSONArray subcategory = getCategoryIdResult.getJSONArray("subcategory");
			String categoryId = subcategory.getJSONObject(0).getString("id");
			JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
			CartItem reqObj = new CartItem();
			if (isPowerEyeglass && i.equalsIgnoreCase("eyeglasses")) {
				product = ApplicationUtil.getProductDetails(jsonResponse_category,
						ApplicationConstants.PowerTypes.SINGLE_VISION, false);
				reqObj.setPackageId(product.get("packageId"));
				reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
			} else if (isPowerSunglass && i.equalsIgnoreCase("sunglasses")) {
				product = ApplicationUtil.getProductDetails(jsonResponse_category,
						ApplicationConstants.PowerTypes.SUNGLASSES, false);
				reqObj.setPackageId(product.get("packageId"));
				reqObj.setPowerType(ApplicationConstants.PowerTypes.SUNGLASSES);
			} else {
				product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
			}
			// Add to Cart

			if (i.equals("contact_lenses")) {
				Prescription presObj = new Prescription();
				presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
				presObj.setDob("12-Aug-92");
				presObj.setGender("Female");
				// Left Power
				Power leftPower = new Power();
				leftPower.setSph("-0.75");
				leftPower.setBoxes(Integer.valueOf("1"));

				presObj.setLeft(leftPower);
				// Right Power
				Power rightPower = new Power();
				rightPower.setSph("-0.75");
				rightPower.setBoxes(Integer.valueOf("1"));

				presObj.setRight(rightPower);
				presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
				presObj.setUserName("user1");
				reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
				reqObj.setSubscriptionId("6");
				reqObj.setIsBothEye("Y");
				reqObj.setPrescription(presObj);
			}

			if (i.equalsIgnoreCase("HTO")) {
				// Set HTO Object
				Hto htoObj = new Hto();
				htoObj.setCity("Faridabad");
				htoObj.setDate(2017 - 04 - 13);
				htoObj.setLenskartAtHome("CASE3_HEC");
				htoObj.setSlotId(6729405);
				reqObj.setHto(htoObj);
				// Set HTO Product ID
				reqObj.setProductId(Long.parseLong("47552"));
			}

			if (!i.equalsIgnoreCase("HTO")) {
				reqObj.setProductId(Long.parseLong(product.get("productId")));
			}
			cart_response = CartUtil.createCart(sessionToken, reqObj, x_api_client);
		}
		String cartId = cart_response.getJSONObject("result").getString("id");
		Assert.assertNotNull(cartId);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);
		CartUtil.saveAddress(sessionToken, x_api_client, address, null);
		JSONObject orderPayment_response = null;
		if (paymentMethod.equalsIgnoreCase("nb")) {
			orderPayment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, "PU:162B", paymentMethod,
					"PU");
		} else if (paymentMethod.equalsIgnoreCase("cod")) {
			orderPayment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, null);
		} else {
			orderPayment_response = PaymentUtil.orderPayment(sessionToken, x_api_client, null, paymentMethod, "PU");
		}
		Assert.assertEquals(orderPayment_response.getJSONObject("result").length(), 2, "Response is not proper");

		JSONObject payment_response_result = orderPayment_response.getJSONObject("result");
		// String orderId =
		// payment_response_result.getJSONObject("order").getString("id");
		return payment_response_result;
	}

	public static JSONObject syncStatus(String session, String orderID, String itemId, String itemLevelState,
			String itemLevelStatus, boolean itemLevelcancellable, boolean itemLevelreturnable) throws Exception {
		String requestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.GATEWAY_PATCH_ORDER_SYNC_STATUS, orderID);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", session));
		header.add(new BasicNameValuePair("X-auth-token", ApplicationConstants.xAuthToken));
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject payload = new JSONObject();
		JSONObject orders = new JSONObject();
		JSONArray itemsArray = new JSONArray();
		JSONObject status = new JSONObject();
		status.put("state", itemLevelState);
		status.put("status", itemLevelStatus);
		status.put("cancellable", itemLevelcancellable);
		status.put("returnable", itemLevelreturnable);

		JSONObject items = new JSONObject();
		items.put("itemId", itemId);
		items.put("status", status);

		itemsArray.put(items);

		orders.put("items", itemsArray);
		payload.put("order", orders);

		HttpResponse httpresponse_patchStatus = RequestUtil.patchRequest(requestUrl, header, payload);
		Assert.assertEquals(httpresponse_patchStatus.getStatusLine().getStatusCode(), 200);
		JSONObject responseJson_patchStatus = RequestUtil.convertHttpResponseToJsonObject(httpresponse_patchStatus);
		return responseJson_patchStatus;
	}

	public static JSONObject backSync(String orderId, BackSyncRequest backSyncRequest, int statusCode)
			throws Exception {
		String requestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.PATCH_BACKSYNC, orderId);
		List<NameValuePair> headers = HeadersUtil.headers_session_authToken(SessionUtil.createNewSession(),
				ApplicationConstants.xAuthToken);

		ObjectMapper objectMapper1 = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT)
				.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
				.setSerializationInclusion(JsonInclude.Include.USE_DEFAULTS);
		String json = objectMapper1.writeValueAsString(backSyncRequest);

		JSONObject payload = new JSONObject(json);
		HttpResponse httpresponse = RequestUtil.patchRequest(requestUrl, headers, payload);
		JSONObject response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), statusCode);
		return response;
	}

	public static JSONObject fetchOrderaByAggregatorAPI(String sessionToken, String orderId, int statusCode)
			throws Exception {
		String requestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_ORDER_AGGREGATOR_API;
		List<NameValuePair> headers = HeadersUtil.headers(sessionToken, XApiClient.ANDROID,
				ApplicationConstants.xAuthToken);
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("orderId", orderId));
		HttpResponse httpResponse = RequestUtil.getRequest(requestUrl, headers, queryParams);
		JSONObject response = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), statusCode);
		return response;
	}

	public static JSONObject backSync(String orderId, String itemId, String itemTrackingStatus, String updatedAt,
			int statusCode) throws Exception {
		String requestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.PATCH_BACKSYNC, orderId);
		List<NameValuePair> headers = HeadersUtil.headers_session_authToken(SessionUtil.createNewSession(),
				ApplicationConstants.xAuthToken);

		SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
		SimpleDateFormat formatter1 = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");
		String strDate = formatter1.format(new Date());
		Calendar c = Calendar.getInstance();
		c.add(Calendar.DATE, 4);
		Calendar d = Calendar.getInstance();
		d.add(Calendar.DATE, 7);
		String dispatchDate = formatter.format(c.getTime());
		String deliveryDate = formatter.format(d.getTime());
		Order order = new Order();
		List<Item> items = new ArrayList<Item>();

		ItemTracking itemTracking = new ItemTracking();
		itemTracking.setCourierTrackingNum("Test");
		itemTracking.setCourier("test");
		itemTracking.setCourierTrackingUrl("test.com");
		itemTracking.setExpectedDeliveryDate(deliveryDate);
		itemTracking.setExpectedDispatchDate(dispatchDate);
		itemTracking.setItemTrackingStatus(itemTrackingStatus);
		itemTracking.setUpdatedAt(updatedAt);

		Item item = new Item();
		item.setItemId(itemId);
		item.setItemTracking(itemTracking);

		items.add(item);

		order.setItems(items);
		BackSyncRequest backSyncRequest = new BackSyncRequest();
		backSyncRequest.setOrder(order);

		ObjectMapper objectMapper1 = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT)
				.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
				.setSerializationInclusion(JsonInclude.Include.USE_DEFAULTS);
		String json = objectMapper1.writeValueAsString(backSyncRequest);

		JSONObject payload = new JSONObject(json);
		HttpResponse httpresponse = RequestUtil.patchRequest(requestUrl, headers, payload);
		JSONObject response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), statusCode);
		return response;
	}

	public static void updatePower(String orderId, String client, JSONObject leftEye, JSONObject rightEye)
			throws Exception {
		String session = SessionUtil.createNewSessionWithApiClient(client);
		JSONObject result = OrderUtil.getOrder(session, client, orderId, ApplicationConstants.xAuthToken);
		JSONArray items = result.getJSONObject("result").getJSONArray("items");
		IntStream.range(0, items.length()).forEach(i -> {
			String itemId;
			try {
				itemId = items.getJSONObject(i).getString("id");
				if (items.getJSONObject(i).getString("powerRequired").equals("POWER_REQUIRED")) {
					String powerType = items.getJSONObject(i).getJSONObject("prescription").getString("powerType");
					Thread.sleep(2000);
					OrderUtil.updateOrderItemPrescription_usecase(session, orderId, itemId, powerType, leftEye,
							rightEye);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		});
	}

	public static Boolean confirmButton(String orderId) throws Exception {
		JSONObject result = OrderUtil.getOrder(SessionUtil.createNewSessionWithApiClient(XApiClient.ANDROID),
				XApiClient.ANDROID, orderId, ApplicationConstants.xAuthToken);
		JSONArray items = result.getJSONObject("result").getJSONArray("items");
		Boolean confirmButton =true;
		for(int i=0;i<=items.length()-1;i++) {
			if (items.getJSONObject(i).getString("powerRequired").equals("POWER_REQUIRED")) { 
				confirmButton=false;
			}
		}
		return confirmButton;
	}

	public static void confirmOrder(String session, String xApiClient, String orderId) throws Exception {
		String confirmOrderUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPathConstants.CONFIRM_ORDER, orderId);
		Response response = RestAssuredUtils.PUT(confirmOrderUrl,
				HeadersUtil.headers_session_client(session, xApiClient));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}
}
