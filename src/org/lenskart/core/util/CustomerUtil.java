package org.lenskart.core.util;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.CustomerPathConstants;
import org.lenskart.core.constant.MobileLoginPathConstants;
import org.lenskart.core.constant.SessionPathConstants;
import org.lenskart.core.constant.UtilityPathConstants;
import org.testng.Assert;

import com.mongodb.BasicDBObject;
import com.mongodb.MongoException;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.DeleteResult;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;

public class CustomerUtil {

	private static final Logger log = GenericUtil.InitLogger(CustomerUtil.class);
	private final static String registerCustomerURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_REGISTER_A_CUSTOMER_PATH;
	private static String deleteCustomerURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_DELETE_A_CUSTOMER_PATH;
	private final static String authSessionURL = Environments.SERVICES_ENVIRONMENT
			+ SessionPathConstants.GATEWAY_AUTHENTICATE_SESSION_PATH;
	private final static String addMeAddress = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_POST_MY_ADDRESS_PATH;
	private final static String generateForgotPasswordTokenURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_GENERATE_FORGOT_PASSWORD_TOKEN_PATH;

	private final static String getMeCustomerURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_GET_MY_DETAILS_PATH;
	private final static String getMeCustomerAllAddressesURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_GET_ALL_MY_ADDRESSES_PATH;

	private final static String requestUrlSendOtp = Environments.SERVICES_ENVIRONMENT
			+ MobileLoginPathConstants.SEND_OTP_PATH;
	public final static String requestUrlVerifyOtp = Environments.SERVICES_ENVIRONMENT
			+ MobileLoginPathConstants.VERIFY_OTP_PATH;
	private final static String requestUrlMobileAuthenticate = Environments.SERVICES_ENVIRONMENT
			+ MobileLoginPathConstants.MOBILE_AUTHENTICATE_PATH;
	private final static String requestUrlTurnOnOffOtpRequest = Environments.SERVICES_ENVIRONMENT
			+ String.format(MobileLoginPathConstants.OTP_ENABLE_DISABLE_PATH, "enable");
	private final static String requestUrlGetAccountsByMobile = Environments.SERVICES_ENVIRONMENT
			+ MobileLoginPathConstants.GET_ACCOUNTS_BY_MOBILE_NUMBER_PATH;
	private final static String requestUrSendEmail = Environments.SERVICES_ENVIRONMENT
			+ MobileLoginPathConstants.SEND_EMAIL_PATH;
	private final static String requestUrlVerifyEmail = Environments.SERVICES_ENVIRONMENT
			+ MobileLoginPathConstants.VERIFY_EMAIL_PATH;

	private final static String updateMeCustomerURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_POST_MY_DETAILS_INTERNAL_PATH;

	private final static String updateMeCustomerByEmailURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_POST_MY_DETAILS_VIA_EMAIL_PATH;

	private final static String addPrescriptionsURL = Environments.SERVICES_ENVIRONMENT
			+ UtilityPathConstants.POST_CUSTOMER_PRESCRIPTION_PATH;
	private final static String getPrescriptionURL = Environments.SERVICES_ENVIRONMENT
			+ UtilityPathConstants.GET_CUSTOMER_PRESCRIPTION_PATH;

	final static String postCreateAddressURL = Environments.SERVICES_ENVIRONMENT
			+ CustomerPathConstants.GATEWAY_POST_MY_ADDRESS_PATH;

	private static String mongoCustomerCollectionName = "customer_v2";
	private static MongoConnectionUtility dao = null;

	public static String getCustomerCollectionName() {
		return mongoCustomerCollectionName;
	}

	public static MongoConnectionUtility getCustomerMongoConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("CustomerService");
		dao = new MongoConnectionUtility(pf);
		return dao;
	}

	public static void closeCustomerMongoConnectionObject() throws IOException {
		if (dao != null) {
			dao.closeMongoConnection();
		}
	}

	public static String registerNewCustomer(String firstName, String lastName, String email, String password,
			String mobileNumber) throws Exception {
		String referalCode = null;
		return registerNewCustomer(firstName, lastName, email, password, mobileNumber, referalCode);
	}

	public static String registerNewCustomer(String firstName, String lastName, String email, String password,
			String mobileNumber, String referralCode) throws Exception {
		String sessionId = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));

		JSONObject payloadJson = new JSONObject();
		payloadJson.put("firstName", firstName);
		payloadJson.put("lastName", lastName);
		payloadJson.put("email", email);
		payloadJson.put("password", password);
		payloadJson.put("mobile", mobileNumber);
		if (referralCode != null) {
			payloadJson.put("referCode", referralCode);
		}

		log.debug("Payload JSON : " + payloadJson);
		HttpResponse httpResponse = RequestUtil.postRequest(registerCustomerURL, headers, payloadJson);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Register New Customer API :"
				+ GenericUtil.printAPICallDetails(registerCustomerURL, payloadJson, responseObject));
		JSONObject resultObj = responseObject.getJSONObject("result");
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();

		log.debug("Response code for registering new user : " + httpStatusCode);
		if (httpStatusCode == 200) {
			log.debug("USER CREATED WITH EMAIL : " + email);
		} else {
			throw new Exception("Failed to create new user with email : " + email);
		}
		String newToken = resultObj.getString("token");

		return newToken;
	}

	public static List<Document> getCustomerDocumentsListfromDB(String email) throws Exception {
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("email", email));
		return dao.executeQuery(mongoCustomerCollectionName, queryParams);
	}

	public static Document getCustomerDocument(String email) throws Exception {
		List<Document> resultList = getCustomerDocumentsListfromDB(email);
		if (resultList != null && !resultList.isEmpty()) {
			if (resultList.size() > 1) {
				throw new MongoException("Multiple records found");
			}
		} else {
			throw new MongoException("No result found..!");
		}
		return resultList.get(0);
	}

	public static JSONObject queryDBDocumentForCustomer(String email) throws Exception {
		Document sessionDoc = getCustomerDocument(email);
		String jsonString = sessionDoc.toJson();
		// PS : This JSON file param values will not be as expected. WATCH OUT!!
		return new JSONObject(jsonString);
	}

	public static List<Document> getCustomerDocumentsListfromDBForMobile(String telephone) throws Exception {
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("telephone", telephone));
		return dao.executeQuery(mongoCustomerCollectionName, queryParams);
	}

	public static List<Document> getCustomerDocumentsListfromDB(List<NameValuePair> queryParams) throws Exception {
		return dao.executeQuery(mongoCustomerCollectionName, queryParams);
	}

	public static Document getCustomerDocumentForMobile(String telephone) throws Exception {
		List<Document> resultList = getCustomerDocumentsListfromDBForMobile(telephone);
		if (resultList != null && !resultList.isEmpty()) {
			if (resultList.size() > 1) {
				throw new MongoException("Multiple records found");
			}
		} else {
			throw new MongoException("No result found..!");
		}
		return resultList.get(0);
	}

	public static JSONObject queryDBDocumentForCustomerForMobile(String telephone) throws Exception {
		Document sessionDoc = getCustomerDocumentForMobile(telephone);
		String jsonString = sessionDoc.toJson();
		return new JSONObject(jsonString);
	}

	public static long getCustomerIdFromMongoForCustomerEmail(String email) throws Exception {
		List<Document> resultList = getCustomerDocumentsListfromDB(email);
		if (resultList != null && !resultList.isEmpty()) {
			if (resultList.size() > 1) {
				throw new MongoException("Multiple records found");
			}
		} else {
			throw new MongoException("No result found..!");
		}
		Long id = resultList.get(0).getLong("_id");
		return id;
	}

	/**
	 * Admin API to delete the customer is no more in use. This will throw 404
	 * 
	 * @param customer_collection
	 * @param email
	 * @throws Exception
	 * 
	 */
	@Deprecated
	public static void delete_customer(String email) throws Exception {
		String sessionId = SessionUtil.createNewSession();
		long userId = getCustomerIdFromMongoForCustomerEmail(email);

		log.debug("CUSTOMER ID RETURNED : " + userId);
		String delete_url = deleteCustomerURL.replace("CUSTOMER_ID", String.valueOf(userId));

		log.debug("Delete user request URL : " + delete_url);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Accept", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));

		HttpResponse httpResponse = RequestUtil.deleteRequest(delete_url, headers);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();

		log.debug("Response code for DELETING user : " + httpStatusCode);

		if (httpStatusCode == 204) {
			log.debug("USER DELETED : " + email);
		} else {
			throw new Exception(
					"Status code received : " + httpStatusCode + "Failed to delete the customer with email : " + email);
		}
	}

	public static boolean delete_customer_from_mongo_collection(String mongoDBName, String customer_collection,
			String email) throws Exception {
		dao.selectMongoDB(mongoDBName);
		return delete_customer_from_mongo_collection(email);
	}

	public static boolean delete_customer_from_mongo_collection(String email) throws Exception {
		MongoCollection<Document> collection = dao.getCollection(mongoCustomerCollectionName);

		BasicDBObject document = new BasicDBObject();
		document.put("email", email);
		DeleteResult delResult = collection.deleteOne(document);
		log.debug("DELETE RESULT " + delResult);

		if (delResult.getDeletedCount() > 1) {

			throw new MongoException("Multiple user records were deleted with email id :" + email);
		}
		// return true if one customer record is deleted.
		return true;
	}

	public static String get_sessionId_after_user_authentication(String email, String password, String xApiClient)
			throws Exception {
		String sessionId = SessionUtil.createNewSession();

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-APi-Client", xApiClient));

		JSONObject user_credentialJson = new JSONObject();
		user_credentialJson.put("username", email);
		user_credentialJson.put("password", password);

		log.debug("header" + headers);
		GenericUtil.curlBuilder("POST", null, authSessionURL, headers, user_credentialJson.toString());
		HttpResponse httpResponse = RequestUtil.postRequest(authSessionURL, headers, null, user_credentialJson);
		log.debug("http response : " + httpResponse);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);
		JSONObject resultObj = responseObject.getJSONObject("result");
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Session Token: " + sessionId + " \n"
				+ GenericUtil.printAPICallDetails(authSessionURL, user_credentialJson, responseObject));
		String response_username = resultObj.getString("username");
		String mergedSessionId = resultObj.getString("token");
		Assert.assertEquals(response_username, email);
		Assert.assertNotEquals(mergedSessionId, sessionId);
		return mergedSessionId;
	}

	public static String get_sessionId_after_user_authentication(String sessionId, String email, String password,
			String xApiClient) throws Exception {

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-APi-Client", xApiClient));

		JSONObject user_credentialJson = new JSONObject();
		user_credentialJson.put("username", email);
		user_credentialJson.put("password", password);

		HttpResponse httpResponse = RequestUtil.postRequest(authSessionURL, headers, user_credentialJson);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		JSONObject resultObj = responseObject.getJSONObject("result");
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200,
				GenericUtil.printAPICallDetails(authSessionURL, user_credentialJson, responseObject));

		String response_username = resultObj.getString("username");
		String mergedSessionId = resultObj.getString("token");
		Assert.assertEquals(response_username, email);
		Assert.assertNotEquals(mergedSessionId, sessionId);
		return mergedSessionId;
	}

	public static String get_sessionId_after_user_authentication(String sessionId, String email, String password,
			String xApiClient, String userAgent) throws Exception {

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-APi-Client", xApiClient));
		headers.add(new BasicNameValuePair("User-Agent", userAgent));

		JSONObject user_credentialJson = new JSONObject();
		user_credentialJson.put("username", email);
		user_credentialJson.put("password", password);

		HttpResponse httpResponse = RequestUtil.postRequest(authSessionURL, headers, user_credentialJson);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		JSONObject resultObj = responseObject.getJSONObject("result");
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200,
				GenericUtil.printAPICallDetails(authSessionURL, user_credentialJson, responseObject));
		String response_username = resultObj.getString("username");
		String mergedSessionId = resultObj.getString("token");
		Assert.assertEquals(response_username, email);
		Assert.assertNotEquals(mergedSessionId, sessionId);
		return mergedSessionId;
	}

	public static JSONObject addAddressToCustomer(String sessionId, String firstName) throws Exception {

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));

		String unique_email = "test" + GenericUtil.createRandomNumber(3) + "@example.com";
		JSONObject addressObj = createAddressObj(firstName, unique_email, "9999999999", "shipping", "Address line - 1",
				"Address line - 2", 5, true, "Indiranagar", "near police station", "Bengaluru", "Karnataka", "560038",
				"IN", true);

		HttpResponse httpResponse = RequestUtil.postRequest(addMeAddress, headers, addressObj);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Response code received : " + httpStatusCode);
		return responseObject;
	}

	public static JSONObject addAddressToCustomer(String sessionId, JSONObject addressObj) throws Exception {

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));

		HttpResponse httpResponse = RequestUtil.postRequest(addMeAddress, headers, addressObj);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Response code received : " + httpStatusCode);
		return responseObject;
	}

	@SuppressWarnings("unchecked")
	public static List<Document> getCustomerAddressesFromMongo(String customer_collection, String userEmail)
			throws Exception {
		Document doc = getCustomerDocument(userEmail);
		List<Document> addressArray = (List<Document>) doc.get("addresses");
		return addressArray;
	}

	public static JSONObject createAddressObj(String customerName, String email, String phone, String addressType,
			String addressline1, String addressline2, int floor, boolean listAvailable, String locality,
			String landmark, String city, String state, String postcode, String country, boolean defaultAddress)
			throws JSONException {

		JSONObject addressObj = new JSONObject();
		addressObj.put("firstName", customerName);
		addressObj.put("lastName", customerName);
		addressObj.put("email", email);
		addressObj.put("phone", phone);
		addressObj.put("addressType", addressType);
		addressObj.put("addressline1", addressline1);
		// addressObj.put("addressline2", addressline2);
		// addressObj.put("floor", floor);
		// addressObj.put("listAvailable", listAvailable);
		addressObj.put("locality", locality);
		addressObj.put("landmark", landmark);
		addressObj.put("city", city);
		addressObj.put("state", state);
		addressObj.put("postcode", postcode);
		addressObj.put("country", country);
		addressObj.put("defaultAddress", defaultAddress);

		return addressObj;
	}

	public static String generateForgotPasswordToken(String emailAddress) throws Exception {
		String fp_token = null;

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", SessionUtil.createNewSession()));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));

		JSONObject payload = new JSONObject();
		payload.put("emailAddress", emailAddress);
		HttpResponse httpResponse = RequestUtil.postRequest(generateForgotPasswordTokenURL, headers);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Response code received : " + httpStatusCode);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		JSONObject resultObj = responseObject.getJSONObject("result");
		fp_token = resultObj.getString("token");
		return fp_token;
	}

	public static JSONObject getMeCustomerDetails(String authenticatedSessionId, String apiClient) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", authenticatedSessionId));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", apiClient));

		HttpResponse httpResponse = RequestUtil.getRequest(getMeCustomerURL, headers, null);

		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Response code received : " + httpStatusCode);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		JSONObject resultObj = responseObject.getJSONObject("result");

		return resultObj;
	}

	public static void deleteCustomer(String sessionId, String email, String apiClient) throws Exception {
		// String sessionId = SessionUtil.createNewSession();
		JSONObject custDetails = getMeCustomerDetails(sessionId, apiClient);

		long userId = custDetails.getLong("id");

		log.debug("CUSTOMER ID RETURNED : " + userId);
		String delete_url = deleteCustomerURL.replace("CUSTOMER_ID", String.valueOf(userId));

		log.debug("Delete user request URL : " + delete_url);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Accept", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionId));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));

		HttpResponse httpResponse = RequestUtil.deleteRequest(delete_url, headers);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();

		log.debug("Response code for DELETING user : " + httpStatusCode);

		if (httpStatusCode == 204) {
			log.debug("USER DELETED : " + email);
		} else {
			throw new Exception(
					"Status code received : " + httpStatusCode + "Failed to delete the customer with email : " + email);
		}
	}

	public static JSONObject updateMeCustomer(String authenticatedSessionId, String apiClient,
			HashMap<String, Object> updateDetails) throws Exception {
		JSONObject payloCustomerObject = new JSONObject(updateDetails);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", authenticatedSessionId));
		headers.add(new BasicNameValuePair("X-Api-Client", apiClient));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));

		HttpResponse httpResponse = RequestUtil.postRequest(updateMeCustomerURL, headers, payloCustomerObject);

		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);

		// if(responseObject == null) {
		// Assert.assertFalse(true, "Empty response received. " +
		// GenericUtil.printAPICallDetails(authenticatedSessionId,
		// updateMeCustomerURL,
		// payloCustomerObject, responseObject));
		// }
		// else {
		Assert.assertEquals(httpStatusCode, 200, GenericUtil.printAPICallDetails(authenticatedSessionId,
				updateMeCustomerURL, payloCustomerObject, responseObject));
		// }
		log.debug("response of UpdateMeCustomer API:  " + responseObject);
		return responseObject;
	}

	public static JSONObject updateMeCustomerByEmailURL(String authenticatedSessionId, String apiClient,
			HashMap<String, Object> updateDetails) throws Exception {
		JSONObject payloCustomerObject = new JSONObject(updateDetails);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", authenticatedSessionId));
		headers.add(new BasicNameValuePair("X-Api-Client", apiClient));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));

		HttpResponse httpResponse = RequestUtil.postRequest(updateMeCustomerByEmailURL, headers, payloCustomerObject);

		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);

		Assert.assertEquals(httpStatusCode, 200, GenericUtil.printAPICallDetails(authenticatedSessionId,
				updateMeCustomerURL, payloCustomerObject, responseObject));

		return responseObject;
	}

	public static void updateMeCustomerWithDitto(String authenticatedSessionId, String dittoId) throws Exception {
		JSONObject updateMeCustomerPayload = new JSONObject();

		updateMeCustomerPayload.put("firstName", "test");
		updateMeCustomerPayload.put("lastName", "test");
		updateMeCustomerPayload.put("group", 10);
		updateMeCustomerPayload.put("storeId", 1);
		updateMeCustomerPayload.put("telephone", "9970814760");
		updateMeCustomerPayload.put("autogroupchange", false);
		updateMeCustomerPayload.put("websiteid", 30);
		updateMeCustomerPayload.put("confirmation", false);
		updateMeCustomerPayload.put("isActive", false);
		JSONArray dittos = new JSONArray();
		dittos.put(dittoId);
		updateMeCustomerPayload.put("dittos", dittos);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", authenticatedSessionId));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));

		HttpResponse httpResponse_1 = RequestUtil.postRequest(updateMeCustomerURL, headers, updateMeCustomerPayload);
		log.debug("httpResponse of UpdateMeCustomer API:  " + httpResponse_1);
		int httpStatusCode = httpResponse_1.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Status code received : " + httpStatusCode);
		JSONObject responseObject_1 = RequestUtil.convertHttpResponseToJsonObject(httpResponse_1);
		log.debug("response of UpdateMeCustomer API:  " + responseObject_1);
	}

	public static JSONObject sendOtp(String x_Api_Client, String sessiontoken, String telephone) throws Exception {
		log.debug("session: " + sessiontoken);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		headers.add(new BasicNameValuePair("X-auth-token", ApplicationConstants.xAuthToken));

		JSONObject requestparams_sendOtp = new JSONObject();
		requestparams_sendOtp.put("telephone", telephone);

		HttpResponse httpResponseSendOtp = RequestUtil.postRequest(requestUrlSendOtp, headers, requestparams_sendOtp);
		JSONObject responseJSON_sendOtp = RequestUtil.convertHttpResponseToJsonObject(httpResponseSendOtp);
		Assert.assertEquals(httpResponseSendOtp.getStatusLine().getStatusCode(), 200, "Response Status is not proper");
		return responseJSON_sendOtp;
	}

	public static JSONObject mobileAuthenticate(String x_Api_Client, String sessiontoken, String code, String telephone,
			String referCode) throws Exception {
		log.debug("session: " + sessiontoken);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));

		JSONObject requestparams_authenticateMobile = new JSONObject();
		requestparams_authenticateMobile.put("telephone", telephone);
		requestparams_authenticateMobile.put("code", code);
		requestparams_authenticateMobile.put("phoneCode", GenericUtil.CountryProperties("phone_code"));

		if (referCode != null)
			requestparams_authenticateMobile.put("referCode", referCode);
		
		GenericUtil.curlBuilder("POST", null, requestUrlMobileAuthenticate, headers,
				requestparams_authenticateMobile.toString());

		HttpResponse httpResponseAuthenticateMobile = RequestUtil.postRequest(requestUrlMobileAuthenticate, headers,
				requestparams_authenticateMobile);
		JSONObject responseJSON_mobileAuthenticate = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseAuthenticateMobile);
		Assert.assertEquals(httpResponseAuthenticateMobile.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		return responseJSON_mobileAuthenticate;
	}

	public static JSONObject verifyOtp(String x_Api_Client, String sessiontoken, String code, String telephone)
			throws Exception {
		log.debug("session: " + sessiontoken);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));

		JSONObject requestparams_verifyOtp = new JSONObject();
		requestparams_verifyOtp.put("telephone", telephone);
		requestparams_verifyOtp.put("code", code);

		HttpResponse httpResponseVerifyOtp = RequestUtil.postRequest(requestUrlVerifyOtp, headers,
				requestparams_verifyOtp);
		JSONObject responseJSON_verifyOtp = RequestUtil.convertHttpResponseToJsonObject(httpResponseVerifyOtp);
		Assert.assertEquals(httpResponseVerifyOtp.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		return responseJSON_verifyOtp;
	}

	public static JSONObject mobileAuthenticateWithoutReferCode(String x_Api_Client, String sessiontoken, String code,
			String telephone) throws Exception {
		return mobileAuthenticate(x_Api_Client, sessiontoken, code, telephone, null);
	}

	public static JSONObject enableDisableOTP(String x_Api_Client, String sessiontoken) throws Exception {
		log.debug("session: " + sessiontoken);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		HttpResponse httpEnableDisableResponse = RequestUtil.postRequest(requestUrlTurnOnOffOtpRequest, headers);
		JSONObject responseJSON_enableDisable = RequestUtil.convertHttpResponseToJsonObject(httpEnableDisableResponse);
		Assert.assertEquals(httpEnableDisableResponse.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		return responseJSON_enableDisable;
	}

	public static JSONObject getAccountsByMobile(String x_Api_Client, String loggedInsessionToken) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", loggedInsessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));

		HttpResponse httpresponse = RequestUtil.getRequest(requestUrlGetAccountsByMobile, headers, null);
		JSONObject responseJSON_getAccounts = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response Status is not proper");
		return responseJSON_getAccounts;
	}

	public static JSONObject sendEmail(String x_Api_Client, String loggedInsessionToken, String email)
			throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", loggedInsessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));

		JSONObject payloadJson = new JSONObject();
		payloadJson.put("email", email);

		HttpResponse httpSendEmailResponse = RequestUtil.postRequest(requestUrSendEmail, headers, payloadJson);
		JSONObject responseJSON_sendEmail = RequestUtil.convertHttpResponseToJsonObject(httpSendEmailResponse);
		Assert.assertEquals(httpSendEmailResponse.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");

		return responseJSON_sendEmail;
	}

	public static JSONObject verifyEmail(String x_Api_Client, String loggedInsessionToken, String email,
			String telephone, String customerId) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", loggedInsessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));

		JSONObject payloadJson = new JSONObject();
		payloadJson.put("email", encodeString(email));
		payloadJson.put("telephone", encodeString(telephone));
		payloadJson.put("customerId", encodeString(customerId));

		HttpResponse httpVerifyEmailResponse = RequestUtil.postRequest(requestUrlVerifyEmail, headers, payloadJson);
		JSONObject responseJSON_verifyEmail = RequestUtil.convertHttpResponseToJsonObject(httpVerifyEmailResponse);
		Assert.assertEquals(httpVerifyEmailResponse.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");

		return responseJSON_verifyEmail;
	}

	public static String encodeString(String str) {
		String encodedString = Base64.getEncoder().encodeToString(str.getBytes());
		return encodedString;
	}

	public static JSONObject getAllAddressesForCustomer(String x_Api_Client, String sessionToken) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));

		HttpResponse httpGetAllAddressesResponse = RequestUtil.getRequest(getMeCustomerAllAddressesURL, headers, null);
		JSONObject responseJSONGetAllAddresses = RequestUtil
				.convertHttpResponseToJsonObject(httpGetAllAddressesResponse);
		Assert.assertEquals(httpGetAllAddressesResponse.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		return responseJSONGetAllAddresses;
	}

	public static JSONObject addPrescription_UseCase(String sessionToken, String xApiClient, String customerEmail,
			String customerPhone, String powerType, String optomId, String statusCode) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-Api-Auth-Token", MoneyUtil.generateAuthToken()));

		JSONObject addPrescriptionParams = new JSONObject();
		JSONObject prescriptionParams = new JSONObject();
		JSONArray prescription = new JSONArray();

		prescriptionParams.put("optomId", optomId);
		prescriptionParams.put("notes", "test notes");
		prescriptionParams.put("powerType", powerType);
		prescriptionParams.put("left", ApplicationUtil.leftEyePrescriptionParams());
		prescriptionParams.put("right", ApplicationUtil.rightEyePrescriptionParams());

		prescription.put(prescriptionParams);
		addPrescriptionParams.put("customerEmail", customerEmail);
		addPrescriptionParams.put("customerPhone", customerPhone);
		addPrescriptionParams.put("prescription", prescription);

		HttpResponse httpresponse_addPrescription = RequestUtil.postRequest(addPrescriptionsURL, headers,
				addPrescriptionParams);
		JSONObject responseJson_addPrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpresponse_addPrescription);
		log.debug((addPrescriptionParams) + "-----Request-----");
		log.debug((responseJson_addPrescription) + "-------Response------");
		log.debug(httpresponse_addPrescription.getStatusLine().getStatusCode() + "-----Status Code------");
		Assert.assertEquals(httpresponse_addPrescription.getStatusLine().getStatusCode(), Integer.parseInt(statusCode),
				" Status code is not correct");
		return responseJson_addPrescription;

	}

	public static JSONObject getPrescription(String sessionToken, String xApiClient, String customerEmail,
			String customerPhone, String powerType) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		if (xApiClient.equals("") || sessionToken.equals("")) {
			headers.add(new BasicNameValuePair("X-Api-Auth-Token",
					MoneyUtil.generateAuthToken(SessionUtil.createNewSession(), XApiClient.ANDROID)));
		} else {
			headers.add(
					new BasicNameValuePair("X-Api-Auth-Token", MoneyUtil.generateAuthToken(sessionToken, xApiClient)));
		}

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("customerEmail", customerEmail));
		query.add(new BasicNameValuePair("customerPhone", customerPhone));
		query.add(new BasicNameValuePair("powerType", powerType));

		HttpResponse httpResponse_getPrescription = RequestUtil.getRequest(getPrescriptionURL, headers, query);
		JSONObject responseJSON_getPrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpResponse_getPrescription);
		log.debug((responseJSON_getPrescription) + "-------Response------");
		log.debug(httpResponse_getPrescription.getStatusLine().getStatusCode() + "-----Status Code------");
		Assert.assertEquals(httpResponse_getPrescription.getStatusLine().getStatusCode(), 200,
				" Status code is not correct");
		return responseJSON_getPrescription;
	}

	public static JSONObject getPrescription_withXApiAuthToken(String sessionToken, String xApiClient,
			String customerEmail, String customerPhone, String powerType, String xApiAuthToken) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-Api-Auth-Token", xApiAuthToken));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("customerEmail", customerEmail));
		query.add(new BasicNameValuePair("customerPhone", customerPhone));
		query.add(new BasicNameValuePair("powerType", powerType));

		HttpResponse httpResponse_getPrescription = RequestUtil.getRequest(getPrescriptionURL, headers, query);
		JSONObject responseJSON_getPrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpResponse_getPrescription);
		log.debug((responseJSON_getPrescription) + "-------Response------");
		log.debug(httpResponse_getPrescription.getStatusLine().getStatusCode() + "-----Status Code------");
		Assert.assertEquals(httpResponse_getPrescription.getStatusLine().getStatusCode(), 200,
				" Status code is not correct");
		return responseJSON_getPrescription;
	}

	public static JSONObject getPrescription(String sessionToken, String xApiClient, String customerEmail,
			String customerPhone, String powerType, String statusCode) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		if (xApiClient.equals("") || sessionToken.equals("")) {
			headers.add(new BasicNameValuePair("X-Api-Auth-Token",
					MoneyUtil.generateAuthToken(SessionUtil.createNewSession(), XApiClient.ANDROID)));
		} else {
			headers.add(
					new BasicNameValuePair("X-Api-Auth-Token", MoneyUtil.generateAuthToken(sessionToken, xApiClient)));
		}

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("customerEmail", customerEmail));
		query.add(new BasicNameValuePair("customerPhone", customerPhone));
		query.add(new BasicNameValuePair("powerType", powerType));

		HttpResponse httpResponse_getPrescription = RequestUtil.getRequest(getPrescriptionURL, headers, query);
		JSONObject responseJSON_getPrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpResponse_getPrescription);
		log.debug((responseJSON_getPrescription) + "-------Response------");
		log.debug(httpResponse_getPrescription.getStatusLine().getStatusCode() + "-----Status Code------");
		Assert.assertEquals(httpResponse_getPrescription.getStatusLine().getStatusCode(), Integer.parseInt(statusCode),
				" Status code is not correct");
		return responseJSON_getPrescription;
	}

	public static JSONObject updatePrescription(String sessionToken, String xApiClient, String id_fromGetPrescription,
			JSONObject getPrescription_Payload_forUpdatePrescription, String statusCode) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		String updatePrescriptionsURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(UtilityPathConstants.PUT_CUSTOMER_PRESCRIPTION_PATH, id_fromGetPrescription);
		HttpResponse httpresponse_updatePrescription = RequestUtil.putRequest(updatePrescriptionsURL, headers,
				getPrescription_Payload_forUpdatePrescription);
		JSONObject responseJson_updatePrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpresponse_updatePrescription);
		Assert.assertEquals(httpresponse_updatePrescription.getStatusLine().getStatusCode(),
				Integer.parseInt(statusCode), " Status code is not correct");
		return responseJson_updatePrescription;
	}

	public static JSONObject newUpdatePrescription(String sessionToken, String xApiClient,
			JSONObject UpdatePrescriptionParams, String statusCode) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		String updatePrescriptionsURL = Environments.SERVICES_ENVIRONMENT
				+ UtilityPathConstants.PUT_CUSTOMER_NEW_UPDATE_PRESCRIPTION_PATH;
		HttpResponse httpresponse_updatePrescription = RequestUtil.putRequest(updatePrescriptionsURL, headers,
				UpdatePrescriptionParams);
		JSONObject responseJson_updatePrescription = RequestUtil
				.convertHttpResponseToJsonObject(httpresponse_updatePrescription);
		Assert.assertEquals(httpresponse_updatePrescription.getStatusLine().getStatusCode(),
				Integer.parseInt(statusCode), " Status code is not correct");
		return responseJson_updatePrescription;

	}

	public static String mobileAuthenticateSession(String x_Api_Client, String telephone) throws Exception {

		JSONObject response = mobileAuthenticate(x_Api_Client, SessionUtil.createNewSession(),
				ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), telephone, null);
		return response.getJSONObject("result").getString("token");
	}

	public static JSONObject getMobileAuthenticationResultObject(String x_Api_Client, String telephone)
			throws Exception {

		JSONObject response = mobileAuthenticate(x_Api_Client, SessionUtil.createNewSession(),
				ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), telephone, null);
		return response.getJSONObject("result");
	}

	public static JSONObject whatsApp_OptIn_OptOut(String x_Api_Client, String sessionToken, String optingValue)
			throws Exception {
		String whatsAppOpting_URL = Environments.SERVICES_ENVIRONMENT + CustomerPathConstants.PUT_whatsAppOpting_PATH;

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("optingValue", optingValue));

		HttpResponse httpResponse = RequestUtil.putRequest(whatsAppOpting_URL, query, header);
		JSONObject reponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);
		return reponse;
	}

	public static JSONObject checkWhatsAppOpingStatus(String sessionToken) throws Exception {
		String checkWhatsAppOpingStatus_URL = Environments.SERVICES_ENVIRONMENT
				+ CustomerPathConstants.GET_checkWhatsAppOpingStatus_PATH;

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.ANDROID));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));

		HttpResponse httpResponse = RequestUtil.getRequest(checkWhatsAppOpingStatus_URL, header, null);
		JSONObject reponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);
		return reponse;

	}

	public static JSONObject createAddressMe(String sessionToken, String customerFirstName, String customerLastName,
			String customerEmail, String customerTelephone) throws Exception {
		// Heads Up: This api is for creating address of cx without cart creation
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));

		// JSONArray Address = new JSONArray();
		JSONObject payload = new JSONObject();
		payload.put("firstName", customerFirstName);
		payload.put("lastName", customerLastName);
		payload.put("email", customerEmail);
		payload.put("phone", customerTelephone);
		payload.put("addressType", "billing");
		payload.put("addressline1", "#53, 1st Main Road, 5th Cross");
		payload.put("addressline2", "Margosa Avenue, Near Chinnaswamy Stadium");
		payload.put("floor", "3");
		payload.put("liftAvailable", "true");
		payload.put("landmark", "Alex Villa");
		payload.put("state", "Karnataka");
		payload.put("postcode", "560040");
		payload.put("country", "India");
		payload.put("city", "Bangalore");
		HttpResponse createAddressesMeResponse = RequestUtil.postRequest(postCreateAddressURL, header, payload);
		Assert.assertEquals(createAddressesMeResponse.getStatusLine().getStatusCode(), 200, "Incorrect Status Code");
		JSONObject createAddressesMeResponseObject = RequestUtil
				.convertHttpResponseToJsonObject(createAddressesMeResponse);
		JSONObject createAddressesMeResponseResult = createAddressesMeResponseObject.getJSONObject("result");
		return createAddressesMeResponseResult;

	}

	public static JSONObject fetchAddressMe(String sessionToken) throws Exception {
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));

		JSONObject getCustomerDetails = CustomerUtil.getMeCustomerDetails(sessionToken,
				ApplicationConstants.XApiClient.DESKTOP);
		log.info(getCustomerDetails);
		getCustomerDetails = CustomerUtil.getMeCustomerDetails(sessionToken, ApplicationConstants.XApiClient.DESKTOP);
		String customerFirstName = getCustomerDetails.getString("firstName");
		String customerLastName = getCustomerDetails.getString("lastName");
		String customerEmail = getCustomerDetails.getString("email");
		String customerTelephone = getCustomerDetails.getString("telephone");
		JSONObject createAddressesResponseResult = CustomerUtil.createAddressMe(sessionToken, customerFirstName,
				customerLastName, customerEmail, customerTelephone);
		String addressId = createAddressesResponseResult.getString("id");
		log.info(addressId);
		final String readAddressMeURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(CustomerPathConstants.GATEWAY_GET_MY_ADDRESS_PATH, addressId);
		HttpResponse readAddressesResponse = RequestUtil.getRequest(readAddressMeURL, header, null);
		Assert.assertEquals(readAddressesResponse.getStatusLine().getStatusCode(), 200, "Incorrect Status Code");
		JSONObject readAddressesResponseObject = RequestUtil.convertHttpResponseToJsonObject(readAddressesResponse);
		JSONObject readAddressesResponseResult = readAddressesResponseObject.getJSONObject("result");
		return readAddressesResponseResult;

	}

	public static String createPreSaleUser(String email, String name, String username, String password,
			String sessionToken) throws Exception {
		final String createPreSalesCheckoutUserURL = Environments.SERVICES_ENVIRONMENT
				+ CustomerPathConstants.POST_CREATE_PRESALE_USER;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Auth-Token", ApplicationConstants.xAuthToken));

		JSONObject payload = new JSONObject();
		payload.put("email", email);
		payload.put("name", name);
		payload.put("username", username);
		payload.put("password", password);
		HttpResponse createPreSaleUserResponse = RequestUtil.postRequest(createPreSalesCheckoutUserURL, header,
				payload);
		Assert.assertEquals(createPreSaleUserResponse.getStatusLine().getStatusCode(), 200, "Incorrect Status Code");
		JSONObject createPreSaleUserResponseObject = RequestUtil
				.convertHttpResponseToJsonObject(createPreSaleUserResponse);
		String createPreSaleUserResponseResult = createPreSaleUserResponseObject.getString("result");
		return createPreSaleUserResponseResult;

	}

	public static JSONObject createCustomerAdmin(String sessionToken, JSONObject payload, int customerId)
			throws Exception {

		final String createCustomerAdminURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(CustomerPathConstants.POST_CREATE_CUSTOMER_ADMIN, customerId);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Auth-Token", ApplicationConstants.xAuthToken));
		HttpResponse createCustomerAdminResponse = RequestUtil.postRequest(createCustomerAdminURL, header, payload);
		Assert.assertEquals(createCustomerAdminResponse.getStatusLine().getStatusCode(), 200, "Invalid Status Code");
		JSONObject createCustomerAdminObject = RequestUtil.convertHttpResponseToJsonObject(createCustomerAdminResponse);
		JSONObject createCustomerAdminResult = createCustomerAdminObject.getJSONObject("result");
		return createCustomerAdminResult;
	}

	public static JSONArray getCustomerAdmin(String sessionToken, String email) throws Exception {
		final String getCustomerAdminURL = Environments.SERVICES_ENVIRONMENT + CustomerPathConstants.GET_CUSTOMER_ADMIN;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Auth-Token", ApplicationConstants.xAuthToken));

		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("email", email));
		HttpResponse getCustomerAdminResponse = RequestUtil.getRequest(getCustomerAdminURL, header, queryParams);
		Assert.assertEquals(getCustomerAdminResponse.getStatusLine().getStatusCode(), 200, "Invalid Status Code");
		JSONObject getCustomerAdminObject = RequestUtil.convertHttpResponseToJsonObject(getCustomerAdminResponse);
		JSONArray getCustomerAdminResult = getCustomerAdminObject.getJSONArray("result");
		return getCustomerAdminResult;
	}

	public static JSONObject getCustomerAdmin(String sessionToken, String email, int status) throws Exception {
		final String getCustomerAdminURL = Environments.SERVICES_ENVIRONMENT + CustomerPathConstants.GET_CUSTOMER_ADMIN;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Auth-Token", ApplicationConstants.xAuthToken));

		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("email", email));
		HttpResponse getCustomerAdminResponse = RequestUtil.getRequest(getCustomerAdminURL, header, queryParams);
		Assert.assertEquals(getCustomerAdminResponse.getStatusLine().getStatusCode(), status, "Invalid Status Code");
		JSONObject getCustomerAdminObject = RequestUtil.convertHttpResponseToJsonObject(getCustomerAdminResponse);
		return getCustomerAdminObject;
	}

	public static JSONObject createAddressAdmin(String sessionToken, String customerFirstName, String customerLastName,
			String customerEmail, String customerTelephone, String customerId) throws Exception {
		final String createAddressAdminURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(CustomerPathConstants.POST_CREATE_ADDRESS_ADMIN, customerId);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Auth-Token", ApplicationConstants.xAuthToken));

		// JSONArray Address = new JSONArray();
		JSONObject payload = new JSONObject();
		payload.put("firstName", customerFirstName);
		payload.put("lastName", customerLastName);
		payload.put("email", customerEmail);
		payload.put("phone", customerTelephone);
		payload.put("addressType", "billing");
		payload.put("addressline1", "#53, 1st Main Road, 5th Cross");
		payload.put("addressline2", "Margosa Avenue, Near Chinnaswamy Stadium");
		payload.put("floor", "3");
		payload.put("liftAvailable", "true");
		payload.put("landmark", "Alex Villa");
		payload.put("state", "Karnataka");
		payload.put("postcode", "560040");
		payload.put("country", "India");
		payload.put("city", "Bangalore");
		HttpResponse createAddressAdminResponse = RequestUtil.postRequest(createAddressAdminURL, header, payload);
		Assert.assertEquals(createAddressAdminResponse.getStatusLine().getStatusCode(), 200, "Incorrect Status Code");
		JSONObject createAddressAdminObject = RequestUtil.convertHttpResponseToJsonObject(createAddressAdminResponse);
		JSONObject createAddressAdminResult = createAddressAdminObject.getJSONObject("result");
		return createAddressAdminResult;

	}
}