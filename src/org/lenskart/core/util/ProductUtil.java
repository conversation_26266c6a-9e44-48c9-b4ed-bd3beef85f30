package org.lenskart.core.util;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.JunoV1PathConstants;
import org.lenskart.core.constant.JunoV2ProductPathConstants;
import org.mortbay.util.ajax.JSON;
import org.testng.Assert;

import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;
import com.utilities.RestAssuredUtils;
import com.utilities.SolrConnectionUtility;

import io.restassured.response.Response;

public class ProductUtil {
	private static final Logger log = GenericUtil.InitLogger(ProductUtil.class);
	public static String userEmail = "<EMAIL>";
	public static String password = "India@123";
	public static String CartTestEmail = "<EMAIL>";
	public static String CartTestPassword = "123456";

	public static String productId = "123512";
	public static String productId1 = "7603";
	public static String eyeglass_productId = "123512";
	public static String sunglass_productId = "93877";
	public static String contact_lens_productId = "38723";
	public static String acc_productId = "122900";
	public static String x_Api_Client = Environments.X_API_CLIENT;
	private static SolrConnectionUtility solr_dao = null;
	private static final String mongoSessionCollectionName = "juno";
	private static final String PackageCollectionName = "buy_packages";
	static MongoConnectionUtility dao = null;

	public static final class PowerTypes {
		public static final String SINGLE_VISION = "single_vision";
		public static final String BIFOCAL = "bifocal";
		public static final String ZERO_POWER = "zero_power";
		public static final String SUNGLASSES = "sunglasses";
		public static final String CONTACT_LENS = "CONTACT_LENS";
		public static final String TINTED_SV = "TINTED_SV";
	}

	public static JSONObject shippingAddress() throws JSONException {
		JSONObject address = new JSONObject();
		address.put("firstName", "Test");
		address.put("lastName", "Test");
		address.put("phone", "9970814760");
		address.put("email", "<EMAIL>");
		address.put("addressline1", "Test order please don't proceed");
		address.put("city", "Test order please don't proceed");
		address.put("state", "KA");
		address.put("postcode", "560038");
		address.put("country", "IN");
		return address;
	}

	public static String categoryId(String categoryName) {
		HashMap<String, String> category = new HashMap<>();
		category.put("category_eyeglasses_FFF", "3329");
		category.put("category_eyeglasses", "3194");
		category.put("category_sunglasses", "2498");
		category.put("category_accessories", "7137");
		category.put("category_reading_eyeglass", "4890");
		category.put("category_contact_lens", "4938");
		category.put("category_aqualens", "7251");

		return category.get(categoryName);
	}

	public static String gvCode = "LKAUTOMATION";
	public static String storeCreditCode = "5480-62QS1-QWPD";

	public static SolrConnectionUtility getCategorySolrConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		solr_dao = new SolrConnectionUtility(pf);
		return solr_dao;
	}

	public static SolrConnectionUtility getProductSolrConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		solr_dao = new SolrConnectionUtility(pf);
		return solr_dao;
	}

	public static String getSessionCollectionName() {
		return mongoSessionCollectionName;
	}

	public static MongoConnectionUtility getSessionMongoConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		dao = new MongoConnectionUtility(pf);
		return dao;
	}

	public static void closeSessionMongoConnectionObject() throws IOException {
		if (dao != null) {
			dao.closeMongoConnection();
		}
	}

	public static String getFrameTypeFromProductDetails(JSONObject product_response) throws JSONException {
		String frame_type = null;
		JSONArray specifications = product_response.getJSONObject("result").getJSONArray("specifications");
		for (int i = 0; i < specifications.length(); i++) {
			if (specifications.getJSONObject(i).getString("name").equals("technical")) {
				JSONArray items = specifications.getJSONObject(i).getJSONArray("items");
				for (int j = 0; j < items.length(); j++) {
					if (items.getJSONObject(j).getString("name").equals("Frame Type")) {
						frame_type = items.getJSONObject(j).getString("value").toLowerCase().trim().replace(" ", "_");
					}
				}
			}
		}
		Assert.assertNotNull(frame_type, "frame_type is not present for this product_id");
		if (product_response.getJSONObject("result").getJSONArray("prescriptionType").length() > 1)
			return frame_type;
		else
			return null;
	}

	public static Boolean getPrescriptionTypeDetails(JSONObject product_response) throws JSONException {
		// TODO Auto-generated method stub
		Boolean prescriptionType = null;
		JSONArray prescriptionTypeArray = product_response.getJSONObject("result").getJSONArray("prescriptionType");
		for (int i = 0; i < prescriptionTypeArray.length(); i++) {
			if (prescriptionTypeArray.getJSONObject(i).getString("id").equals("sunglasses")) {
				prescriptionType = true;
				break;
			} else {
				prescriptionType = false;
			}
		}

		return prescriptionType;
	}

	public static HashMap<String, String> getProductDetails(JSONObject jsonResponse_category, String power_type,
			Boolean additionalOption, Boolean relatedProducts) throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
		JSONArray product_list = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		for (int i = product_list.length() - 1; i >= 0; i--) {
			if (power_type.equals("single_vision") || power_type.equals("bifocal") || power_type.equals("zero_power")
					|| power_type.equals("sunglasses")) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_Api_Client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
				if (frame_type != null) {
					JSONObject jsonResponse_Package = JunoV1_1Util.getPackageIdFromBuyOptionApiV1_1(
							productDetails.get("productId"), power_type, frame_type, x_Api_Client);
					if (!jsonResponse_Package.isNull("result")
							&& !jsonResponse_Package.getJSONObject("result").isNull("packages")) {
						if (jsonResponse_Package.getJSONObject("result").getJSONArray("packages").length() > 0) {
							JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
							for (int j = 0; j < packages.length(); j++) {
								productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
								Assert.assertNotNull(productDetails.get("packageId"),
										"package_id is not available for this product");
								if (additionalOption) {
									if (!packages.getJSONObject(j).isNull("addons")) {
										JSONArray addons = packages.getJSONObject(j).getJSONArray("addons");
										for (int z = 0; z < addons.length(); z++) {
											if (addons.getJSONObject(z).getString("title")
													.equals("Scratch Resistant")) {
												productDetails.put("coating_id",
														addons.getJSONObject(z).getString("id"));
												break;
											}
										}
										break;
									}
								} else {
									break;
								}
							}

							break;
						}
					}
				}
				if (additionalOption) {

				}
			} else if (power_type.equalsIgnoreCase("CONTACT_LENS")) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_Api_Client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				if (relatedProducts) {
					System.out.println(jsonResponse_product);
					JSONObject result1 = (JSONObject) jsonResponse_product.get("result");

					if (result1.has("crossSells")) {
						JSONArray relItemArray = result1.getJSONArray("crossSells");
						System.out.println("CrossSEll" + relItemArray);
						if (relItemArray.length() == 0) {
							log.debug("CrossSEll Products does not exists");
						} else {
							int no = GenericUtil.randomNumber(relItemArray.length());
							JSONObject relObj = relItemArray.getJSONObject(no);
							productDetails.put("relatedProductIds", relObj.get("id").toString());
						}
					}
				}

				if (jsonResponse_product.getJSONObject("result").has("subscription")) {

				}

			} else {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_Api_Client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");

			}

			System.out.println("Product Id and Package Id");
			for (String key : productDetails.keySet()) {
				System.out.println("Keys: " + key + "  Values:" + productDetails.get(key));
			}
		}
		return productDetails;
	}

	private static List<Document> getPackageDetails(Map<String, Object> parms) {
		List<Document> get_package_details = dao.executeQuery(getPackageCollectionName(), parms);
		return get_package_details;
	}

	public static String getPackageCollectionName() {
		return PackageCollectionName;
	}

	public static HashMap<String, String> getProductDetails(JSONObject jsonResponse_category, String power_type,
			Boolean additionalOption, Boolean relatedProducts, String x_Api_Client)
			throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
		JSONArray product_list = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		Integer PrdListSize = product_list.length();

		if (PrdListSize <= 0)
			productDetails.put("NoOfProducts", "0");
		else {
			int i = GenericUtil.randomNumber(PrdListSize);
			productDetails.put("NoOfProducts", PrdListSize.toString());
			Boolean eyeglasscond1 = power_type.equals("single_vision") || power_type.equals("bifocal")
					|| power_type.equals("zero_power");
			Boolean sunglassCond2 = power_type.equals("sunglasses");
			if (eyeglasscond1 || sunglassCond2) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_Api_Client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
				productDetails.put("frame_type", frame_type);
				Boolean prescriptionType = getPrescriptionTypeDetails(jsonResponse_product);
				if ((eyeglasscond1 && frame_type != null)
						|| (sunglassCond2 && frame_type != null && prescriptionType == true)) {
					JSONObject jsonResponse_Package = JunoV1Util.getPackageIdFromBuyOptionApi(
							productDetails.get("productId"), power_type, frame_type, x_Api_Client);
					if (!jsonResponse_Package.isNull("result")) {
						if (!jsonResponse_Package.getJSONObject("result").isNull("packages")) {
							JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
							Integer PkgSize = packages.length();
							if (PkgSize <= 0)
								productDetails.put("NoOfPackages", "0");
							else {
								int j = GenericUtil.randomNumber(PkgSize);
								productDetails.put("NoOfPackages", PkgSize.toString());
								productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
								Assert.assertNotNull(productDetails.get("packageId"),
										"package_id is not available for this product");
								if (additionalOption) {
									if (!packages.getJSONObject(j).isNull("addons")) {
										JSONArray addons = packages.getJSONObject(j).getJSONArray("addons");
										Integer AddOnSize = addons.length();
										if (AddOnSize <= 0)
											productDetails.put("NoOfAddOns", "0");
										else {
											productDetails.put("NoOfAddOns", AddOnSize.toString());
											int k = GenericUtil.randomNumber(AddOnSize);
											productDetails.put("addOns", packages.getJSONObject(k).getString("id"));
											for (int z = 0; z < addons.length(); z++) {
												if (addons.getJSONObject(z).getString("title")
														.equals("Scratch Resistant")) {
													productDetails.put("coating_id",
															addons.getJSONObject(z).getString("id"));

													break;
												}

											}
										}
									}
								} else {

								}

							}
						}
					}
				}
				if (additionalOption) {

				}
			} else if (power_type.equalsIgnoreCase("CONTACT_LENS")) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_Api_Client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				if (relatedProducts) {
					System.out.println(jsonResponse_product);
					JSONObject result1 = (JSONObject) jsonResponse_product.get("result");

					if (result1.has("crossSells")) {
						JSONArray relItemArray = result1.getJSONArray("crossSells");
						System.out.println("CrossSEll" + relItemArray);
						if (relItemArray.length() == 0) {
							log.debug("CrossSEll Products does not exists");
						} else {
							int no = GenericUtil.randomNumber(relItemArray.length());
							JSONObject relObj = relItemArray.getJSONObject(no);
							productDetails.put("relatedProductIds", relObj.get("id").toString());
						}
					}
				}

				if (jsonResponse_product.getJSONObject("result").has("subscription")) {

				}

			} else {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_Api_Client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");

			}

			System.out.println("Product Id and Package Id");
			for (String key : productDetails.keySet()) {
				System.out.println("Keys: " + key + "  Values:" + productDetails.get(key));
			}
		}
		return productDetails;
	}

	public static HashMap<String, String> getProductDetailsV1_1(JSONObject jsonResponse_category, String power_type,
			Boolean additionalOption, Boolean relatedProducts) throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
		JSONArray product_list = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		int i = GenericUtil.randomNumber(product_list.length());
		Boolean eyeglasscond1 = power_type.equals("single_vision") || power_type.equals("bifocal")
				|| power_type.equals("zero_power");
		Boolean sunglassCond2 = power_type.equals("sunglasses");
		if (eyeglasscond1 || sunglassCond2) {
			productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
			JSONObject jsonResponse_product = JunoV1_1Util.getProductDetailsV1_1(productDetails.get("productId"),
					x_Api_Client);
			Assert.assertNotNull(jsonResponse_product, "product API is not working");
			String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
			Boolean prescriptionType = getPrescriptionTypeDetails(jsonResponse_product);
			if ((eyeglasscond1 && frame_type != null)
					|| (sunglassCond2 && frame_type != null && prescriptionType == true)) {
				JSONObject jsonResponse_Package = JunoV1_1Util.getPackageIdFromBuyOptionApiV1_1(
						productDetails.get("productId"), power_type, frame_type, x_Api_Client);
				if (!jsonResponse_Package.isNull("result")) {
					if (!jsonResponse_Package.getJSONObject("result").isNull("packages")) {
						JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
						int j = GenericUtil.randomNumber(packages.length());
						productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
						Assert.assertNotNull(productDetails.get("packageId"),
								"package_id is not available for this product");
						if (additionalOption) {
							if (!packages.getJSONObject(j).isNull("addons")) {
								JSONArray addons = packages.getJSONObject(j).getJSONArray("addons");
								for (int z = 0; z < addons.length(); z++) {
									if (addons.getJSONObject(z).getString("title").equals("Scratch Resistant")) {
										productDetails.put("coating_id", addons.getJSONObject(z).getString("id"));
										break;
									}
								}

							}
						} else {

						}

					}
				}
			}
			if (additionalOption) {

			}
		} else if (power_type.equalsIgnoreCase("CONTACT_LENS")) {
			productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
			JSONObject jsonResponse_product = JunoV1_1Util.getProductDetailsV1_1(productDetails.get("productId"),
					x_Api_Client);
			Assert.assertNotNull(jsonResponse_product, "product API is not working");
			if (relatedProducts) {
				System.out.println(jsonResponse_product);
				JSONObject result1 = (JSONObject) jsonResponse_product.get("result");

				if (result1.has("crossSells")) {
					JSONArray relItemArray = result1.getJSONArray("crossSells");
					System.out.println("CrossSEll" + relItemArray);
					if (relItemArray.length() == 0) {

					} else {
						int no = GenericUtil.randomNumber(relItemArray.length());
						JSONObject relObj = relItemArray.getJSONObject(no);
						productDetails.put("relatedProductIds", relObj.get("id").toString());
					}
				}
			}

			if (jsonResponse_product.getJSONObject("result").has("subscription")) {

			}

		} else {
			productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
			JSONObject jsonResponse_product = JunoV1_1Util.getProductDetailsV1_1(productDetails.get("productId"),
					x_Api_Client);
			Assert.assertNotNull(jsonResponse_product, "product API is not working");

		}

		System.out.println("Product Id and Package Id");
		for (String key : productDetails.keySet()) {
			System.out.println("Keys: " + key + "  Values:" + productDetails.get(key));
		}
		return productDetails;
	}

	public static ArrayList<String> checkProductAvailability(ArrayList<String> list) {
		// TODO Auto-generated method stub

		try {
			if (list != null) {
				for (int i = 0; i < list.size(); i++) {
					String restURL = (Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
							+ String.valueOf(list.get(i)));
					List<NameValuePair> params = new ArrayList<NameValuePair>();
					params.add(new BasicNameValuePair("view", "summary"));
					List<NameValuePair> header = new ArrayList<NameValuePair>();
					header.add(new BasicNameValuePair("Content-Type", "application/json"));
					header.add(new BasicNameValuePair("X-Api-Client", Environments.X_API_CLIENT));
					HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, params);
					if (httpresponse.getStatusLine().getStatusCode() != 200) {
						list.remove(list.get(i));
						i--;
					}
				}
			} else {
				// Do nothing
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}

	public static ArrayList<String> checkProductAvailabilityV1_1(ArrayList<String> list) {
		// TODO Auto-generated method stub

		try {
			if (list != null) {
				for (int i = 0; i < list.size(); i++) {
					String restURL = Environments.SERVICES_ENVIRONMENT
							+ String.format(JunoV1PathConstants.PRODUCT_V1_1_PATH, String.valueOf(list.get(i)));
					List<NameValuePair> params = new ArrayList<NameValuePair>();
					params.add(new BasicNameValuePair("view", "summary"));
					List<NameValuePair> header = new ArrayList<NameValuePair>();
					header.add(new BasicNameValuePair("Content-Type", "application/json"));
					header.add(new BasicNameValuePair("X-Api-Client", Environments.X_API_CLIENT));
					HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, params);
					if (httpresponse.getStatusLine().getStatusCode() != 200) {
						list.remove(list.get(i));
						i--;
					}
				}
			} else {
				// Do nothing
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}

	public static JSONObject saveWishlist(String x_session_Token, String pid) throws URISyntaxException, Exception {
		String RequestUrlSaveWishlist = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.WISHLIST_POST_V2;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", x_session_Token));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject obj = new JSONObject();
		obj.put("productId", pid);
		HttpResponse httpResponse = RequestUtil.postRequest(RequestUrlSaveWishlist, headers, obj);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "invalid httpResponse");
		int pids = responseJson.getJSONObject("result").getJSONArray("productIds").length();

		Assert.assertEquals(responseJson.getJSONObject("result").getInt("numberOfProducts"), pids,
				"response is not correct");
		return responseJson;

	}

	public static JSONObject saveBulkWishlist(String x_session_Token, List<String> list)
			throws URISyntaxException, Exception {
		String RequestUrlSaveWishlist = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.SAVE_BULK_WISHLIST;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", x_session_Token));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-Auth-Token", ApplicationConstants.xAuthToken));

		JSONArray payload = new JSONArray();
		JSONObject obj;
		for (String pid : list) {
			obj = new JSONObject();
			obj.put("productId", pid);
			payload.put(obj);
		}
		HttpResponse httpResponse = RequestUtil.postRequestJSONArray(RequestUrlSaveWishlist, headers, null, payload);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "invalid httpResponse");
		return responseJson;
	}

	public static void deleteWishlist(String x_session_Token) throws URISyntaxException, Exception {
		String RequestUrl_Delete_Wishlist = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.DELETE_WISHLIST_V2;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-session-token", x_session_Token));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		HttpResponse httpResponse = RequestUtil.deleteRequest(RequestUrl_Delete_Wishlist, headers);
		RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "status code is incorrect");
	}

	public static JSONObject getWishlist(String x_session_Token) throws URISyntaxException, Exception {
		String requestUrlGetWishlist = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.WISHLIST_GET_V2;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-session-token", x_session_Token));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("attribute", "true"));

		HttpResponse httpResponse = RequestUtil.getRequest(requestUrlGetWishlist, headers, queryParams);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		int pids = responseJson.getJSONObject("result").getJSONArray("productIds").length();
		Assert.assertEquals(responseJson.getJSONObject("result").getInt("numberOfProducts"), pids,
				"response is not correct");
		return responseJson;
	}

	public static JSONObject getWishlist(String x_session_Token, int pids) throws URISyntaxException, Exception {
		String requestUrlGetWishlist = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.WISHLIST_GET_V2;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-session-token", x_session_Token));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("attribute", "true"));

		HttpResponse httpResponse = RequestUtil.getRequest(requestUrlGetWishlist, headers, queryParams);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(responseJson.getJSONObject("result").getInt("numberOfProducts"), pids,
				"response is not correct");
		return responseJson;

	}

	public static JSONObject getProductDetails(String product) throws URISyntaxException, Exception {
		String restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH + product;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "android"));
		HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response is not proper for" + restURL);
		JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		return respJsonObjectproduct;
	}

	public static int getStatusCodeforProduct(String product) throws URISyntaxException, Exception {
		String restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH + product;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "desktop"));
		HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
		RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		return httpresponse.getStatusLine().getStatusCode();
	}

	public static JSONArray getPowerForAProduct(String productID) throws Exception {
		String restURL = Environments.SERVICES_ENVIRONMENT + String.format(JunoV1PathConstants.GET_POWERS, productID);
		log.info(restURL);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "desktop"));
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("contact_Lens", ""));
		HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, queryParams);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response is not proper for" + restURL);
		JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		JSONArray valueArray = respJsonObjectproduct.getJSONObject("result").getJSONArray("powerTypeList")
				.getJSONObject(1).getJSONArray("powerDataList").getJSONObject(0).getJSONArray("value");
		return valueArray;
	}

	public static JSONObject getAvailablePowersForAProduct(String productID, String powerType) throws Exception {
		String restURL = Environments.SERVICES_ENVIRONMENT + String.format(JunoV1PathConstants.GET_POWERS, productID);
		log.info(restURL);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "desktop"));
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		if (powerType.toLowerCase().contains("lens"))
			queryParams.add(new BasicNameValuePair("contact_Lens", ""));
		else
			queryParams.add(new BasicNameValuePair("power_type", powerType));
		HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, queryParams);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response is not proper for" + restURL);
		JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		return respJsonObjectproduct;
	}

	public static String getProductPricesFromCatalogProductPricesTable(String productId, String authToken,
			boolean needQueryParams, String countryCode) throws Exception {
		HttpResponse httpresponse;
		String restURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.PRODUCT_PRICES, productId);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Auth-Token", authToken));
		if (needQueryParams) {
			List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
			queryParams.add(new BasicNameValuePair("countryCode", countryCode));
			httpresponse = RequestUtil.getRequest(restURL, header, queryParams);
		} else {
			httpresponse = RequestUtil.getRequest(restURL, header, null);
		}
		String statusCode = String.valueOf(httpresponse.getStatusLine().getStatusCode());
		JSONObject repsone = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		GenericUtil.jsonToString(repsone);
		String statusCode_response = statusCode + " " + GenericUtil.jsonToString(repsone);
		return statusCode_response;
	}

	public static String getProductIdFromTheGivenCategory(String category, String xApiClient)
			throws URISyntaxException, Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(category),
				xApiClient);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		return product.get("productId");
	}

	public static HashMap<String, String> getProductIdAndPackageIdFromTheGivenCategory(String category,
			String xApiClient) throws URISyntaxException, Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(category),
				xApiClient);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		return product;
	}

	public static String getProductIdFromTheGivenCategory(String category, String xApiClient, String powerType)
			throws URISyntaxException, Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(category),
				xApiClient);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType,
				Boolean.parseBoolean(""));
		return product.get("productId");
	}

	public static Response getTierDetails(String xApiClient) {
		return RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_TIER_DETAILS,
				HeadersUtil.headers_apiClient(xApiClient));
	}

	public static Response catalogUserLogin(String xAuthToken, String id, String password) throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("id", id);
		payload.put("password", password);
		return RestAssuredUtils.POST(JunoV2ProductPathConstants.POST_CATALOG_USER_LOGIN,
				HeadersUtil.headers_authToken(xAuthToken), GenericUtil.jsonToString(payload));
	}

	public static String catalogUserLoginToken() throws Exception {
		if (Environments.dbConnectionFlag) {
			MongoConnectionUtility mongoConnectionObject = null;
			mongoConnectionObject = UtilityUtil.getMongoConnectionObject("readall");
			mongoConnectionObject.initMongoDBConnection();
			Map<String, Object> object = new HashMap<String, Object>();
			JSONArray array = new JSONArray();
			array.put("UPDATE");
			log.info(array);
			JSONObject query = new JSONObject();
			query.put("$all", array);
			log.info(query);

			object.put("permissions", JSON.parse(query.toString()));

			List<Document> resultList = mongoConnectionObject.executeQuery("juno", "catalog_user", object);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document.getString("_id"));
			log.info(document.getString("password"));
			Base64.Decoder decoder = Base64.getDecoder();
			String decodedPassword = new String(decoder.decode(document.getString("password")));

			Response response = ProductUtil.catalogUserLogin(ApplicationConstants.xAuthToken, document.getString("_id"),
					decodedPassword);
			mongoConnectionObject.closeMongoConnection();
			return response.jsonPath().getString("result.token");
		} else {
			Response response = ProductUtil.catalogUserLogin(ApplicationConstants.xAuthToken, "<EMAIL>",
					"123456");
			return response.jsonPath().getString("result.token");
		}
	}

	public static String returnFhtPackageId() {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.FETCH_FHT_PACKAGE);
		Assert.assertEquals(response.statusCode(), 200);
		return response.jsonPath().getString("result.package_list[1].id");
	}

	public static Response getTemplateList() {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_TEMPLATE_LIST,
				HeadersUtil.panelAuthToken("NouqFyR8Bz2jk+BhAUdrIttcyApnkGmOCeVJwD890Ys="));
		Assert.assertEquals(response.statusCode(), 200);
		return response;
	}

	public static JSONObject getFirstTemplateDetails() throws JSONException {
		Response templatelistResponse = ProductUtil.getTemplateList();
		String id = templatelistResponse.jsonPath().getString("result.templateList[0].id");
		String powerType = templatelistResponse.jsonPath().getString("result.templateList[0].powerList[0]");
		if (powerType.contains("Single Vision"))
			powerType = PowerTypes.SINGLE_VISION;
		else if (powerType.contains("Bifocal/Progressive"))
			powerType = PowerTypes.BIFOCAL;
		else if (powerType.contains("Zero"))
			powerType = PowerTypes.ZERO_POWER;

		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("powerType", powerType);

		Map<String, String> pathparams = new HashMap<String, String>();
		pathparams.put("id", id);

		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_TEMPLATE_DETAILS, pathparams,
				HeadersUtil.panelAuthToken("NouqFyR8Bz2jk+BhAUdrIttcyApnkGmOCeVJwD890Ys="), queryparams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.templateList[0].powerPackageList[0].powerType"),
				powerType);
		return GenericUtil.stringToJson(response.asString()).getJSONObject("result").getJSONArray("templateList")
				.getJSONObject(0);
	}

	public static int coatingCount() throws URISyntaxException, IOException, JSONException {
		String getCoatingUrl_v2 = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_BUYOPTION_COATING_V2;
		HttpResponse httpResponse = RequestUtil.getRequest(getCoatingUrl_v2);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("responseObject: " + responseObject);
		Assert.assertEquals(responseObject.get("status"), 200, "status mismatch");
		return responseObject.getJSONObject("result").getJSONArray("coatingList").length();
	}

	public static JSONObject getLastCoatingDetails() throws URISyntaxException, IOException, JSONException {
		String getCoatingUrl_v2 = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_BUYOPTION_COATING_V2;
		HttpResponse httpResponse = RequestUtil.getRequest(getCoatingUrl_v2);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("responseObject: " + responseObject);
		Assert.assertEquals(responseObject.get("status"), 200, "status mismatch");
		int lastObject = responseObject.getJSONObject("result").getJSONArray("coatingList").length() - 1;
		return responseObject.getJSONObject("result").getJSONArray("coatingList").getJSONObject(lastObject);
	}

	public static double getLenskartPrice(String product) throws URISyntaxException, Exception {
		return getProductDetails(product).getJSONObject("result").getJSONArray("prices").getJSONObject(1)
				.getDouble("price");
	}

	public static JSONObject getCategorydetails(String categoryId) throws JSONException {
		HashMap<String, String> queryparams = new HashMap<>();
		queryparams.put("page", "0");
		queryparams.put("page-size", "50");
		Response respone = RestAssuredUtils.GET(String.format(JunoV2ProductPathConstants.CATEGORY_PATH_V2, categoryId),
				HeadersUtil.headers_apiClient(XApiClient.DESKTOP), queryparams);
		return new JSONObject(respone.asString());
	}

	public static Response searchTrending() throws JSONException {
		return RestAssuredUtils.GET(JunoV2ProductPathConstants.SEARCH_TRENDING,
				HeadersUtil.headers_apiClient(XApiClient.ANDROID));
	}

	public static Response searchAlgolia(String query) throws JSONException {
		JSONObject request = new JSONObject();
		request.put("query", query);
		request.put("objectID", query);
		request.put("page", 0);
		request.put("page-size", 15);
		return RestAssuredUtils.POST(JunoV2ProductPathConstants.PRODUCT_SEARCH,
				HeadersUtil.headers_apiClient(XApiClient.MOBILESITE), request.toString());
	}
}
