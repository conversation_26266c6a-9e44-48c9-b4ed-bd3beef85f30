package org.lenskart.core.util;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;

import org.apache.http.HttpResponse;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.testng.Assert;

import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.utilities.GenericUtil;
import com.utilities.RequestUtil;

public class ApplicationUtil {

	private static final Logger log = GenericUtil.InitLogger(ApplicationUtil.class);

	public static JSONObject shippingAddress() throws JSONException {
		JSONObject address = new JSONObject();
		address.put("firstName", "test");
		address.put("lastName", "test");
		address.put("gender", "female");
		address.put("postcode", "560038");
		address.put("addressline1", "Test order please don't proceed");
		address.put("city", "Test order please don't proceed");
		address.put("country", "IN");
		address.put("email", "<EMAIL>");
		address.put("phone", "1262183385");
		address.put("state", "test");
		return address;
	}

	public static JSONObject createPaymentBillingAddress() throws JSONException {
		JSONObject address = new JSONObject();
		address.put("firstName", "test");
		address.put("lastName", "test");
		address.put("gender", "female");
		address.put("pincode", "560038");
		address.put("addressline1", "addressline1");
		address.put("city", "Bangalore");
		address.put("country", "IN");
		address.put("email", "<EMAIL>");
		address.put("phone", "1262183385");
		address.put("state", "Karnataka");
		return address;
	}

	public static JSONObject leftEyePrescription() throws JSONException {
		JSONObject left = new JSONObject();
		left.put("sph", "7.75");
		left.put("cyl", "8.75");
		left.put("axis", "10");
		return left;
	}

	public static JSONObject rightEyePrescription() throws JSONException {
		JSONObject right = new JSONObject();
		right.put("sph", "-10.25");
		right.put("cyl", "-4.50");
		right.put("axis", "4");
		return right;
	}
	
	public static JSONObject leftEye() throws JSONException {
		JSONObject left = new JSONObject();
		left.put("sph", "*****");
		return left;
	}

	public static JSONObject rightEye() throws JSONException {
		JSONObject right = new JSONObject();
		right.put("sph", "*****");
		return right;
	}

	public static Prescription prescriptionContactLens() throws JSONException {
		Prescription presObj = new Prescription();
		presObj.setDob("12-Aug-90");
		presObj.setGender("Male");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setAxis("1");
		leftPower.setBoxes(Integer.valueOf("2"));
		leftPower.setCyl("-0.75");

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setAxis("1");
		rightPower.setBoxes(Integer.valueOf("2"));
		rightPower.setCyl("-0.75");

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf("CONTACT_LENS"));
		presObj.setUserName("user1");
		return presObj;
	}
	
	public static Prescription prescriptionCL() throws JSONException {
		Prescription presObj = new Prescription();
		presObj.setDob("12-Aug-90");
		presObj.setGender("Male");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));
		

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));


		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf("CONTACT_LENS"));
		presObj.setUserName("user1");
		return presObj;
	}
	
//	public static String getproductId(String productName) {
//		HashMap<String, String> product = new HashMap<>();
//		product.put("eyeglass_product", "119237");
//		product.put("eyeglass_Product_liquidation", "113117");
//		product.put("product_testing", "2");
//		product.put("sunglass_Product", "93870");
//		product.put("product_new", "8254");
//		product.put("product_id", "144112");
//		return product.get(productName);
//	}

	public static String getcategoryId(String categoryName) {
		HashMap<String, String> category = new HashMap<>();
		category.put("category_eyeglasses_FFF", "2");//5105
		category.put("category_eyeglasses", "3194");
		category.put("category_sunglasses", "4975");
		category.put("category_accessories", "7137");
		category.put("category_reading_eyeglass", "4890");
		category.put("category_contact_lens", "4938");
		category.put("category_contact_lens_image", "4938");
		category.put("category_aqualens", "7251");
		category.put("category_VC", "4893");
		category.put("category_vc", "4845");
		//// Premium Product
		category.put("category_wayfarer_sunglasses", "3264");
		category.put("category_rayban_sunglasses", "2781");
		category.put("category_oakley_sunglasses", "2782");
		category.put("category_vogue_sunglasses", "2783");
		category.put("category_carrera_sunglasses", "3336");
		category.put("category_french_connection_sunglasses", "4220");
		category.put("category_tommy_hilfiger_sunglasses", "4278");
		category.put("category_rayban_eyeglasses", "2785");
		category.put("category_oakley_eyeglasses", "2784");
		category.put("category_vogue_eyeglasses", "2786");
		category.put("category_carrera_eyeglasses", "3335");
		category.put("category_french_connection_eyeglasses", "4219");
		category.put("category_tommy_hilfiger_eyeglasses", "4277");
		//// Non Premium Product
		category.put("category_sunglasses_VC", "2587");
		category.put("category_sunglasses_mask", "4827");
		category.put("category_sunglasses_JJ", "2840");
		category.put("category_sunglasses_ice_cube", "7473");
		category.put("category_sunglasses_sunpocket", "7311");
		category.put("category_sunglasses_superman", "7489");
		category.put("category_sunglasses_parim", "3831");
		category.put("category_sunglasses_baolulai", "3762");
		category.put("category_sunglasses_chhota_bheem", "4275");
		category.put("category_eyeglasses_VC", "2473");
		category.put("category_eyeglasses_mask", "7418");
		category.put("category_eyeglasses_JJ", "4704");
		category.put("category_eyeglasses_art", "7462");
		category.put("category_eyeglasses_baolulai", "7571");
		category.put("category_eyeglasses_chhota_bheem", "4227");
		category.put("category_contact_lens_aqualens", "7244");
		category.put("category_eyeglass_halfrim", "2472");
		category.put("category_eyeglass_rimless", "2474");
		category.put("category_eyeglass_new", "5413");
		category.put("category_new", "8229");
		category.put("category_power_option", "8254");
		category.put("category_myopic_package", "5071");
		return category.get(categoryName);
	}

	public static String getFrameTypeFromProductDetails(JSONObject product_response) throws JSONException {
		String frame_type = null;
		JSONArray specifications = product_response.getJSONObject("result").getJSONArray("specifications");
		for (int i = 0; i < specifications.length(); i++) {
			if (specifications.getJSONObject(i).getString("name").equals("technical")) {
				JSONArray items = specifications.getJSONObject(i).getJSONArray("items");
				for (int j = 0; j < items.length(); j++) {
					if (items.getJSONObject(j).getString("name").equals("Frame Type")) {
						frame_type = items.getJSONObject(j).getString("value").toLowerCase().trim().replace(" ", "_");
					}
				}
			}
		}
		Assert.assertNotNull(frame_type, "frame_type is not present for this product_id");
		return frame_type;
	}

	public static HashMap<String, String> getProductDetails(JSONObject jsonResponse_category, String power_type,
			Boolean additionalOption) throws URISyntaxException, Exception {
		return getProductDetails(jsonResponse_category, power_type, additionalOption, Environments.X_API_CLIENT);
	}
	
	public static HashMap<String, String> getProductDetails(JSONObject jsonResponse_category, String power_type,
			Boolean additionalOption, String apiClient) throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
		JSONArray product_list = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		for (int i = product_list.length() - 1; i >= 0; i--) {
			if (power_type.equals("single_vision") || power_type.equals("bifocal") || power_type.equals("zero_power")
					|| power_type.equals("sunglasses")) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						apiClient);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
				if (frame_type != null) {
					JSONObject jsonResponse_Package = JunoV1Util.getPackageIdFromBuyOptionApi(
							productDetails.get("productId"), power_type, frame_type, apiClient);
					if (!jsonResponse_Package.isNull("result")
							&& !jsonResponse_Package.getJSONObject("result").isNull("packages")) {
						if (jsonResponse_Package.getJSONObject("result").getJSONArray("packages").length() > 0) {
							JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
							for (int j = 0; j < packages.length(); j++) {
								productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
								Assert.assertNotNull(productDetails.get("packageId"),
										"package_id is not available for this product");
								if (additionalOption) {
									if (!packages.getJSONObject(j).isNull("addons")) {
										JSONArray addons = packages.getJSONObject(j).getJSONArray("addons");
										for (int z = 0; z < addons.length(); z++) {
											if (addons.getJSONObject(z).getString("title")
													.equals("Scratch Resistant")) {
												productDetails.put("coating_id",
														addons.getJSONObject(z).getString("id"));
												break;
											}
										}
										break;
									}
								} else {
									break;
								}
							}

							break;
						}
					}
				}
				if (additionalOption) {

				}
			} else if (additionalOption && power_type.equals(ApplicationConstants.PowerTypes.CONTACT_LENS)) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						apiClient);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				if (jsonResponse_product.getJSONObject("result").has("subscription")) {
					break;
				}

			} else {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						apiClient);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				break;
			}
		}
		log.info("Product Id and Package Id");
		for (String key : productDetails.keySet()) {
			log.info("Keys: " + key + "  Values:" + productDetails.get(key));
		}
		return productDetails;
	}
	
	public static HashMap<String, String> getProductDetails(String pid, String power_type,
			Boolean additionalOption, String apiClient) throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
			if (power_type.equals("single_vision") || power_type.equals("bifocal") || power_type.equals("zero_power")
					|| power_type.equals("sunglasses")) {
				productDetails.put("productId", pid);
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						apiClient);
				Assert.assertNotNull(jsonResponse_product.getJSONObject("result"));
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
				if (frame_type != null) {
					JSONObject jsonResponse_Package = JunoV1Util.getPackageIdFromBuyOptionApi(
							productDetails.get("productId"), power_type, frame_type, apiClient);
					Assert.assertNotNull(jsonResponse_Package.getJSONObject("result"));
					if (!jsonResponse_Package.isNull("result")
							&& !jsonResponse_Package.getJSONObject("result").isNull("packages")) {
						if (jsonResponse_Package.getJSONObject("result").getJSONArray("packages").length() > 0) {
							JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
							for (int j = 0; j < packages.length(); j++) {
								productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
								Assert.assertNotNull(productDetails.get("packageId"),
										"package_id is not available for this product");
								if (additionalOption) {
									if (!packages.getJSONObject(j).isNull("addons")) {
										JSONArray addons = packages.getJSONObject(j).getJSONArray("addons");
										for (int z = 0; z < addons.length(); z++) {
											if (addons.getJSONObject(z).getString("title")
													.equals("Scratch Resistant")) {
												productDetails.put("coating_id",
														addons.getJSONObject(z).getString("id"));
												break;
											}
										}
										break;
									}
								} else {
									break;
								}
							}

						}
					}
				}
			} else if (additionalOption && power_type.equals(ApplicationConstants.PowerTypes.CONTACT_LENS)) {
				productDetails.put("productId", pid);
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						apiClient);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
			} else {
				productDetails.put("productId", pid);
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						apiClient);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
			}
		log.info("Product Id and Package Id");
		for (String key : productDetails.keySet()) {
			log.info("Keys: " + key + "  Values:" + productDetails.get(key));
		}
		return productDetails;
	}
	

	public static HashMap<String, String> getProductDetailsV1_1(JSONObject jsonResponse_category, String power_type,
			Boolean additionalOption) throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
		JSONArray product_list = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		for (int i = product_list.length() - 1; i >= 0; i--) {
			if (power_type.equals("single_vision") || power_type.equals("bifocal") || power_type.equals("zero_power")
					|| power_type.equals("sunglasses")) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1_1Util.getProductDetailsV1_1(productDetails.get("productId"),
						ApplicationConstants.XApiClient.ANDROID);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
				if (frame_type != null) {
					JSONObject jsonResponse_Package = JunoV1_1Util.getPackageIdFromBuyOptionApiV1_1(
							productDetails.get("productId"), power_type, frame_type,
							ApplicationConstants.XApiClient.ANDROID);
					if (!jsonResponse_Package.isNull("result")) {
						if (!jsonResponse_Package.getJSONObject("result").isNull("packages")) {
							JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
							for (int j = 0; j < packages.length(); j++) {
								productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
								Assert.assertNotNull(productDetails.get("packageId"),
										"package_id is not available for this product");
								if (additionalOption) {
									if (!packages.getJSONObject(j).isNull("addons")) {
										JSONArray addons = packages.getJSONObject(j).getJSONArray("addons");
										for (int z = 0; z < addons.length(); z++) {
											if (addons.getJSONObject(z).getString("title")
													.equals("Scratch Resistant")) {
												productDetails.put("coating_id",
														addons.getJSONObject(z).getString("id"));
												break;
											}
										}
										break;
									}
								} else {
									break;
								}
							}

							break;
						}
					}
				}
				if (additionalOption) {

				}
			} else if (additionalOption && power_type.equals("CONTACT_LENS")) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1_1Util.getProductDetailsV1_1(productDetails.get("productId"),
						ApplicationConstants.XApiClient.ANDROID);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				if (jsonResponse_product.getJSONObject("result").has("subscription")) {
					break;
				}

			} else {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1_1Util.getProductDetailsV1_1(productDetails.get("productId"),
						ApplicationConstants.XApiClient.ANDROID);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				break;
			}
		}
		log.debug("Product Id and Package Id");
		for (String key : productDetails.keySet()) {
			log.debug("Keys: " + key + "  Values:" + productDetails.get(key));
		}
		return productDetails;
	}

	public static HashMap<String, String> getProductDetails_For_BOGO(JSONObject jsonResponse_category,
			String framePrice, String packageType, String power_type, Boolean additionalOption)
			throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
		JSONArray product_list = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		for (int i = 0; i < product_list.length(); i++) {
			if (product_list.getJSONObject(i).getJSONArray("prices").getJSONObject(1).getString("price")
					.contains(framePrice)) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
			}
		}
		JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
				Environments.X_API_CLIENT);
		Assert.assertNotNull(jsonResponse_product, "product API is not working");
		String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
		JSONObject jsonResponse_Package = JunoV1Util.getPackageIdFromBuyOptionApi(
				productDetails.get("productId"), power_type, frame_type, Environments.X_API_CLIENT);
		if (jsonResponse_Package.getJSONObject("result").getJSONArray("packages").length() > 0) {
			JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
			for (int j = 0; j < packages.length(); j++) {
				if (packages.getJSONObject(j).getString("label").equals(packageType)) {
					log.info(packages.getJSONObject(j).getString("id"));
					productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
					Assert.assertNotNull(productDetails.get("packageId"),
							"package_id is not available for this product");
				}
			}
		}
		log.info("Product Id and Package Id");
		for (String key : productDetails.keySet()) {
			log.info("Keys: " + key + "  Values:" + productDetails.get(key));
		}
		return productDetails;
	}

	public static Address saveShippingAddress() {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Test order please don't proceed");
		address.setCountry("IN");
		// address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("1262183385");
		address.setState("test");
		address.setPostcode("121003");
		return address;
	}
	
	public static Address shipping_address() {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Test order please don't proceed");
		address.setCountry("IN");
		// address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("1262183385");
		address.setState("test");
		address.setPostcode("121003");
		return address;
	}

	public static Address saveShippingAddress_singpore() {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Test order please don't proceed");
		address.setCountry("IN");
		// address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("1262183385");
		address.setState("test");
		address.setPostcode("110001");
		return address;
	}

	public static Address saveShippingAddress(String email, String mobileNumber) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Test order please don't proceed");
		address.setCountry("IN");
		address.setGender("female");
		address.setEmail(email);
		address.setPhone(mobileNumber);
		address.setState("test");
		address.setPostcode("560038");
		return address;
	}
	
	public static Address addShippingAddress(String email, String pincode) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Test order please don't proceed");
		address.setCountry("IN");
		address.setGender("female");
		address.setEmail(email);
		address.setPhone("1262183385");
		address.setState("test");
		address.setPostcode(pincode);
		return address;
	}
	
	public static Address addShippingAddress(String pincode) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Test order please don't proceed");
		address.setCountry("IN");
		address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("1262183385");
		address.setState("test");
		address.setPostcode(pincode);
		return address;
	}
	
	public static Address addShippingAddress1(String pincode) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("FARIDABAD");
		address.setCountry("IN");
		address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("1262183385");
		address.setState("HARYANA");
		address.setPostcode(pincode);
		return address;
	}
	
	public static Address addShippingAddressSingapore(String pincode) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Singapore");
		address.setCountry("SG");
		address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("68856257");
		address.setPhoneCode("+65");
		address.setState("Singapore");
		address.setPostcode(pincode);
		return address;
	}
	
	public static Address addShippingAddressUAE(String pincode) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("UAE");
		address.setCountry("AE");
		address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("585858585");
		address.setPhoneCode("+971");
		address.setState("UAE");
		address.setPostcode(pincode);
		return address;
	}
	
	public static Address addShippingAddressTH(String pincode) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Bangkok");
		address.setCountry("TH");
		address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("21031033");
		address.setPhoneCode("+66");
		address.setState("Bangkok");
		address.setPostcode(pincode);
		return address;
	}
	
	public static Address addShippingAddressSA(String pincode) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please dont proceed");
		address.setCity("Riyadh");
		address.setCountry("SA");
		address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone("500000001");
		address.setPhoneCode("+966");
		address.setState("SAUDI ARABIA");
		address.setPostcode(pincode);
		return address;
	}
	
	public static Address addShippingAddress2(String pincode,String phone) {
		Address address = new Address();
		address.setFirstName("test");
		address.setLastName("test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("FARIDABAD");
		address.setCountry("IN");
		address.setGender("female");
		address.setEmail("<EMAIL>");
		address.setPhone(phone);
		address.setState("HARYANA");
		address.setPostcode(pincode);
		return address;
	}

	public static JSONObject addPrescriptionParams(String customerPhone, String notes, String leftpd, String leftcyl,
			String leftsph, String leftaxis, String rightpd, String rightcyl, String rightsph, String rightaxis,
			String optomId, String powerType) throws Exception {
		JSONObject addPrescriptionParams = new JSONObject();
		JSONObject prescriptionParams = new JSONObject();
		JSONObject leftParams = new JSONObject();
		JSONObject rightParams = new JSONObject();
		JSONArray prescription = new JSONArray();

		leftParams.put("pd", leftpd);
		leftParams.put("cyl", leftcyl);
		leftParams.put("sph", leftsph);
		leftParams.put("axis", leftaxis);

		rightParams.put("pd", rightpd);
		rightParams.put("cyl", rightcyl);
		rightParams.put("sph", rightsph);
		rightParams.put("axis", rightaxis);

		prescriptionParams.put("optomId", optomId);
		prescriptionParams.put("notes", notes);
		prescriptionParams.put("powerType", powerType);
		prescriptionParams.put("left", leftParams);
		prescriptionParams.put("right", rightParams);

		prescription.put(prescriptionParams);

		addPrescriptionParams.put("customerPhone", customerPhone);
		addPrescriptionParams.put("prescription", prescription);

		return addPrescriptionParams;
	}

	public static JSONObject leftEyePrescriptionParams() throws JSONException {
		JSONObject left = new JSONObject();
		left.put("sph", "10.75");
		left.put("cyl", "-10.75");
		left.put("axis", "1");
		left.put("pd", "25");
		return left;
	}

	public static JSONObject rightEyePrescriptionParams() throws JSONException {
		JSONObject right = new JSONObject();
		right.put("sph", "-10.25");
		right.put("cyl", "-4.50");
		right.put("axis", "4");
		right.put("pd", "25");
		return right;
	}

	public static JSONObject UpdatePrescriptionParams(int id, String optomId, String powerType, String leftpd,
			String leftcyl, String leftsph, String leftaxis, String rightpd, String rightcyl, String rightsph,
			String rightaxis) throws Exception {
		JSONObject UpdatePrescriptionParams = new JSONObject();
		JSONObject leftParams = new JSONObject();
		JSONObject rightParams = new JSONObject();

		leftParams.put("pd", leftpd);
		leftParams.put("cyl", leftcyl);
		leftParams.put("sph", leftsph);
		leftParams.put("axis", leftaxis);

		rightParams.put("pd", rightpd);
		rightParams.put("cyl", rightcyl);
		rightParams.put("sph", rightsph);
		rightParams.put("axis", rightaxis);

		UpdatePrescriptionParams.put("left", leftParams);
		UpdatePrescriptionParams.put("right", rightParams);
		UpdatePrescriptionParams.put("id", id);
		UpdatePrescriptionParams.put("optomId", optomId);
		UpdatePrescriptionParams.put("powerType", powerType);

		return UpdatePrescriptionParams;
	}

	public static JSONObject UpdatePrescriptionParams(String optomId, String powerType, String leftpd, String leftcyl,
			String leftsph, String leftaxis, String rightpd, String rightcyl, String rightsph, String rightaxis,
			String notes, String userName) throws Exception {
		JSONObject UpdatePrescriptionParams = new JSONObject();
		JSONObject leftParams = new JSONObject();
		JSONObject rightParams = new JSONObject();

		leftParams.put("pd", leftpd);
		leftParams.put("cyl", leftcyl);
		leftParams.put("sph", leftsph);
		leftParams.put("axis", leftaxis);

		rightParams.put("pd", rightpd);
		rightParams.put("cyl", rightcyl);
		rightParams.put("sph", rightsph);
		rightParams.put("axis", rightaxis);

		UpdatePrescriptionParams.put("left", leftParams);
		UpdatePrescriptionParams.put("right", rightParams);
		UpdatePrescriptionParams.put("optomId", optomId);
		UpdatePrescriptionParams.put("powerType", powerType);
		UpdatePrescriptionParams.put("userName", userName);
		UpdatePrescriptionParams.put("notes", notes);

		return UpdatePrescriptionParams;
	}

	public static int discountCalculation(int eachitemtotal, int totalamount, int sc) {

		int discountOfeachItem = (eachitemtotal * sc) / totalamount;
		return discountOfeachItem;

	}

	public static String returnProductDeliveryType(String storeInventory, boolean isPackagePresent) {
		if (storeInventory.equals("1")) {
			return "OTC";
		} else {
			if (isPackagePresent)
				return "DTC";
			else
				return "B2B";
		}
	}

	public static String returnCategoryId(String categoryType, String type) throws Exception {
		String url = Environments.SERVICES_ENVIRONMENT
				+ String.format("/v2/products/subcategory/gender/men/catalog/%s", categoryType);
		HttpResponse httpResponseGetCategoryId = RequestUtil.getRequest(url);
		JSONObject getCategoryId = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCategoryId);
		Assert.assertEquals(httpResponseGetCategoryId.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject getCategoryIdResult = getCategoryId.getJSONObject("result");
		JSONArray subcategory = getCategoryIdResult.getJSONArray("subcategory");
		try {
			Assert.assertTrue(subcategory.length() > 0, "subcategory array is empty");
			for (int i = 0; i < subcategory.length(); i++) {
				if (subcategory.getJSONObject(i).getString("name").equalsIgnoreCase(type))
					return subcategory.getJSONObject(i).getString("id");
				JSONArray children = subcategory.getJSONObject(i).getJSONArray("children");
				for (int j = 0; j < children.length(); j++) {
					if (children.getJSONObject(j).getString("name").equalsIgnoreCase(type)) {
						return children.getJSONObject(j).getString("id");
					}
				}
			}
			String categoryId = subcategory.getJSONObject(0).getString("id");
			return categoryId;
		} catch (Exception ex) {
			log.debug("NO CATEGORY ID IS PRESENT IN SUBCATEGORY API");
			return null;
		}
	}

	public static void cancelOrderUsingIVR(String orderId) throws URISyntaxException, IOException {
		String url = "https://www.lenskart.com/voicetree/index.php?unique=" + orderId + "&responsecode=Response_2";
		HttpResponse httpResponse = RequestUtil.getRequest(url, null, null);

		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Response code received : " + httpStatusCode);
	}
}
