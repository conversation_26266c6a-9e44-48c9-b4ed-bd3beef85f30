package org.lenskart.core.util;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.JunoV1PathConstants;
import org.lenskart.core.constant.UtilityPathConstants;
import org.lenskart.test.juno.v1.subscriptions.CLSubscriptionPathConstants;
import org.testng.Assert;

import com.mongodb.MongoException;
import com.utilities.ElasticSearchConnectionUtility;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RedisConnectionUtility;
import com.utilities.RequestUtil;
import com.utilities.SolrConnectionUtility;

public class JunoV1Util {

	private static final Logger log = GenericUtil.InitLogger(JunoV1Util.class);
	private static MongoConnectionUtility mongo_dao = null;
	private static RedisConnectionUtility redis_dao = null;
	private static MySQLConnectionUtility mysql_dao = null;
	private static SolrConnectionUtility solr_dao = null;
	private static ElasticSearchConnectionUtility es_dao = null;
	private static String mongowishlistCollectionName = "wishlist";
	private static String mongoFilterCollectionName = "filters";
	private static String mongoUtilityCollectionName1;

	public static String getCollectionName() {
		return mongowishlistCollectionName;
	}

	public static String getCollectionName1() {
		return mongoUtilityCollectionName1;
	}

	public static void setCollectionName(String collectionName) {
		mongoUtilityCollectionName1 = collectionName;
	}

	public static MongoConnectionUtility getJunoV1MongoConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		mongo_dao = new MongoConnectionUtility(pf);
		return mongo_dao;
	}
	
	public static MongoConnectionUtility getCommonMongoConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("Common");
		mongo_dao = new MongoConnectionUtility(pf);
		return mongo_dao;
	}

	public static ElasticSearchConnectionUtility getElasticSearchConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		 es_dao = new ElasticSearchConnectionUtility(pf);
		return  es_dao;
	}

	public static RedisConnectionUtility getJunoV1RedisConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		redis_dao = new RedisConnectionUtility(pf);
		return redis_dao;
	}

	public static MySQLConnectionUtility getJunoV1MySQLConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		mysql_dao = new MySQLConnectionUtility(pf);
		return mysql_dao;
	}

	public static SolrConnectionUtility getCategorySolrConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		solr_dao = new SolrConnectionUtility(pf);
		return solr_dao;
	}

	public static SolrConnectionUtility getProductSolrConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoV1Service");
		solr_dao = new SolrConnectionUtility(pf);
		return solr_dao;
	}

	public static SolrConnectionUtility getSolrConnectionObject() throws IOException {
		PropertyFactory pf = new PropertyFactory("JunoServices");
		solr_dao = new SolrConnectionUtility(pf);
		return solr_dao;
	}

	public static JSONObject getCategoryDetails(String categoryId, String x_api_client)
			throws URISyntaxException, Exception {
		log.info(categoryId);
		String RequstUrlCategoryApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.CATEGORY_PATH, categoryId);
		

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		log.info("Get Category URL" + RequstUrlCategoryApi);
		HttpResponse httpResponseCategoryApi = RequestUtil.getRequest(RequstUrlCategoryApi, Header, null);
		JSONObject jsonResponse_category = RequestUtil.convertHttpResponseToJsonObject(httpResponseCategoryApi);
		Assert.assertEquals(httpResponseCategoryApi.getStatusLine().getStatusCode(), 200,
				GenericUtil.printAPICallDetails(RequstUrlCategoryApi, null, jsonResponse_category));
		Assert.assertNotNull(jsonResponse_category.getJSONObject("result").getString("category_name"),
				GenericUtil.printAPICallDetails(RequstUrlCategoryApi, null, jsonResponse_category));
		return jsonResponse_category;
	}

	public static JSONObject getCategoryDetails(String categoryId, String x_api_client, String x_country_code)

			throws URISyntaxException, Exception {
		String RequstUrlCategoryApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.CATEGORY_PATH, categoryId);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));
		Header.add(new BasicNameValuePair("X-Country-Code", x_country_code));

		log.debug("Get Category URL" + RequstUrlCategoryApi);
		HttpResponse httpResponseCategoryApi = RequestUtil.getRequest(RequstUrlCategoryApi, Header, null);
		JSONObject jsonResponse_category = RequestUtil.convertHttpResponseToJsonObject(httpResponseCategoryApi);
		Assert.assertEquals(httpResponseCategoryApi.getStatusLine().getStatusCode(), 200,
				GenericUtil.printAPICallDetails(RequstUrlCategoryApi, null, jsonResponse_category));
		Assert.assertNotNull(jsonResponse_category.getJSONObject("result").getString("category_name"),
				GenericUtil.printAPICallDetails(RequstUrlCategoryApi, null, jsonResponse_category));
		return jsonResponse_category;
	}

	public static HttpResponse getCategoryResponse(String category_id, String x_api_client)
			throws URISyntaxException, Exception {
		String RequstUrlCategoryApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.CATEGORY_PATH, category_id);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		log.debug("Get Category URL" + RequstUrlCategoryApi);
		HttpResponse httpResponseCategoryApi = RequestUtil.getRequest(RequstUrlCategoryApi, Header, null);
		/*
		 * JSONObject jsonResponse_category =
		 * RequestUtil.convertHttpResponseToJsonObject(httpResponseCategoryApi);
		 * Assert.assertEquals(httpResponseCategoryApi.getStatusLine(). getStatusCode(),
		 * 200, "\nCategory API (GET) :" + GenericUtil.printJSONDetails(null,
		 * jsonResponse_category));
		 * log.debug("------------Category API Response-------------");
		 * log.debug(jsonResponse_category);
		 * log.debug("-----------------------------------------------"); String
		 * product_id = jsonResponse_category.getJSONObject("result").getJSONArray(
		 * "product_list") .getJSONObject(0).getString("id");
		 * Assert.assertNotNull(product_id, "product_id not present for this category");
		 */
		return httpResponseCategoryApi;
	}

	public static HttpResponse getCategoryResponse(String category_id, String queryParam, String queryParamValue,
			String x_api_client) throws URISyntaxException, Exception {
		String RequstUrlCategoryApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.CATEGORY_PATH, category_id);
		List<NameValuePair> QueryParam = new ArrayList<NameValuePair>();
		QueryParam.add(new BasicNameValuePair(queryParam, queryParamValue));

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		log.debug("Get Category URL" + RequstUrlCategoryApi);
		log.debug("Get Header" + Header);
		HttpResponse httpResponseCategoryApi = RequestUtil.getRequest(RequstUrlCategoryApi, Header, QueryParam);
		return httpResponseCategoryApi;
	}

	public static JSONObject getFilterDetails(String category_id, String x_api_client)
			throws URISyntaxException, Exception {
		String RequstUrlFilterApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.FILTER_PATH, category_id);
		log.debug("Get Category URL" + RequstUrlFilterApi);
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));
		log.debug("Get Header" + Header);
		HttpResponse httpResponseFilterApi = RequestUtil.getRequest(RequstUrlFilterApi, Header, null);
		JSONObject jsonResponseFilter = RequestUtil.convertHttpResponseToJsonObject(httpResponseFilterApi);
		return jsonResponseFilter;
	}

	public static HttpResponse getFilterResponse(String category_id, String x_api_client)
			throws URISyntaxException, Exception {
		String RequstUrlFilterApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.FILTER_PATH, category_id);
		log.debug("Get Category URL" + RequstUrlFilterApi);
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));
		log.debug("Get Header" + Header);
		HttpResponse httpResponseFilterApi = RequestUtil.getRequest(RequstUrlFilterApi, Header, null);
		return httpResponseFilterApi;
	}

	public static JSONObject getProductDetails(String product_id, String x_api_client)
			throws URISyntaxException, Exception {
		String RequstUrlProductApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.PRODUCT_PATH, product_id);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		log.debug("Get Product URL" + RequstUrlProductApi);
		GenericUtil.curlBuilder("GET", null, RequstUrlProductApi, Header, null);
		HttpResponse httpResponseProductApi = RequestUtil.getRequest(RequstUrlProductApi, Header, null);
		JSONObject jsonResponse_product = RequestUtil.convertHttpResponseToJsonObject(httpResponseProductApi);
		Assert.assertEquals(httpResponseProductApi.getStatusLine().getStatusCode(), 200, "\nProduct API (GET) :"
				+ GenericUtil.printAPICallDetails(null, RequstUrlProductApi, null, jsonResponse_product));
		log.debug("------------Product API Response-------------");
		log.debug(jsonResponse_product);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(jsonResponse_product.getJSONObject("result").getString("id"), product_id,
				"Product API returns empty response body");
		return jsonResponse_product;
	}

	public static JSONObject getPackageIdFromBuyOptionApi(String product_id, String power_type, String frame_type,
			String x_api_client) throws URISyntaxException, Exception {
		String RequstUrlBuyOptionApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.GET_PACKAGE_ID_FROM_BUYOPTION_API, product_id);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("power_type", power_type));
		query.add(new BasicNameValuePair("frame_type", frame_type));

		log.debug("Get package_id URL" + RequstUrlBuyOptionApi);
		HttpResponse httpResponsePackageApi = RequestUtil.getRequest(RequstUrlBuyOptionApi, Header, query);
		JSONObject jsonResponse_Package = RequestUtil.convertHttpResponseToJsonObject(httpResponsePackageApi);

		if (httpResponsePackageApi.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(httpResponsePackageApi.getStatusLine().getStatusCode(), 200,
					"\nBuyOption API (GET) : " + RequstUrlBuyOptionApi
							+ GenericUtil.printAPICallDetails(RequstUrlBuyOptionApi, null, jsonResponse_Package));
			log.debug("------------Buy Option API Response-------------");
			log.debug(jsonResponse_Package);
			log.debug("-----------------------------------------------");
			Assert.assertEquals(jsonResponse_Package.getJSONObject("result").getString("id"), power_type,
					"Mismatch in power_type");
		} else if (httpResponsePackageApi.getStatusLine().getStatusCode() == 53) {
			jsonResponse_Package = null;

		}
		return jsonResponse_Package;
	}

	public static JSONObject getContactLensSolutions(String x_api_client) throws URISyntaxException, Exception {
		String RequstUrlContactLensSolutions = Environments.SERVICES_ENVIRONMENT
				+ JunoV1PathConstants.GET_CONTACT_LENS_SOLUTIONS;

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		log.debug("Get Contact Lens Solutions" + RequstUrlContactLensSolutions);
		HttpResponse httpResponseContactLensSolutions = RequestUtil.getRequest(RequstUrlContactLensSolutions, Header,
				null);
		JSONObject jsonResponse_ContactLensSolutions = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseContactLensSolutions);
		log.debug("------------Get Contact Lens Solutions API Response-------------");
		log.debug(jsonResponse_ContactLensSolutions);
		log.debug("-----------------------------------------------");
		return jsonResponse_ContactLensSolutions;
	}

	public static JSONObject saveWishlist(String x_session_Token, String pid) throws URISyntaxException, Exception {
		String RequestUrlSaveWishlist = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.SAVE_WISHLIST;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", x_session_Token));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject obj = new JSONObject();
		obj.put("productId", pid);
		HttpResponse httpResponse = RequestUtil.postRequest(RequestUrlSaveWishlist, headers, obj);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "invalid httpResponse");
		int pids = responseJson.getJSONObject("result").getJSONArray("productIds").length();

		Assert.assertEquals(responseJson.getJSONObject("result").getInt("numberOfProducts"), pids,
				"response is not correct");
		return responseJson;

	}

	public static void deleteWishlist(String x_session_Token) throws URISyntaxException, Exception {
		String RequestUrl_Delete_Wishlist = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.DELETE_WISHLIST;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-session-token", x_session_Token));
		HttpResponse httpResponse = RequestUtil.deleteRequest(RequestUrl_Delete_Wishlist, headers);
		RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "status code is incorrect");
	}

	public static JSONObject getWishlist(String x_session_Token) throws URISyntaxException, Exception {
		String requestUrlGetWishlist = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_WISHLIST;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-session-token", x_session_Token));
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("attribute", "true"));

		HttpResponse httpResponse = RequestUtil.getRequest(requestUrlGetWishlist, headers, queryParams);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		int pids = responseJson.getJSONObject("result").getJSONArray("productIds").length();
		Assert.assertEquals(responseJson.getJSONObject("result").getInt("numberOfProducts"), pids,
				"response is not correct");
		return responseJson;

	}

	public static ArrayList<String> findPackageIdfromDB(String xApiClient, String productId, String power_type,
			String frame_type, MongoConnectionUtility mongoConnectionObject) {
		/// Connecting Product Collection
		Map<String, Object> product = new HashMap<String, Object>();
		product.put("product_id", Integer.parseInt(productId));
		List<Document> product_details = mongoConnectionObject.executeQuery("products", product);
		Document product_db = product_details.get(0);
		String templateId = String.valueOf(product_db.get("template_id"));
		/// Connecting Template Collection
		ObjectId templateId_db = new ObjectId(templateId);
		Map<String, Object> template = new HashMap<String, Object>();
		template.put("_id", templateId_db);
		List<Document> template_details = mongoConnectionObject.executeQuery("templates", template);
		Document template_db = template_details.get(0);
		ArrayList<String> buyPackageId = new ArrayList<String>();
		@SuppressWarnings("unchecked")
		List<Document> package_details = (List<Document>) template_db.get("power_packages");
		for (int packages_count = 0; packages_count < package_details.size(); packages_count++) {
			Document packages_db = package_details.get(packages_count);
			if (packages_db.getString("power_type").equalsIgnoreCase(power_type)) {
				@SuppressWarnings("unchecked")
				List<Document> package_names_db = (List<Document>) packages_db.get("packages");
				for (int package_name_count = 0; package_name_count < package_names_db.size(); package_name_count++) {
					Document packages_name = package_names_db.get(package_name_count);
					@SuppressWarnings("unchecked")
					List<Document> platform_enabled_db = (List<Document>) packages_name.get("platform_enabled");
					if (packages_name.getBoolean("enabled") && platform_enabled_db.contains(xApiClient.toUpperCase())) {
						String package_sku = String.valueOf(packages_name.get("package_sku"));
						/// Connecting BuyPackage Collection
						Map<String, Object> buypackage = new HashMap<String, Object>();
						buypackage.put("sku", package_sku);
						buypackage.put("frame_type", frame_type);
						List<Document> buypackage_details = mongoConnectionObject.executeQuery("buy_packages",
								buypackage);
						Document buypackage_db = buypackage_details.get(0);
						String id = String.valueOf(buypackage_db.get("_id"));
						buyPackageId.add(id);
					}
				}
			}
		}
		return buyPackageId;
	}

	public static ArrayList<String> findCoatingFromDB(String packageId, MongoConnectionUtility mongoConnectionObject) {
		ArrayList<String> coating = new ArrayList<String>();
		ObjectId buyPackage_db = new ObjectId(packageId);

		Map<String, Object> buypackage = new HashMap<String, Object>();
		buypackage.put("_id", buyPackage_db);
		List<Document> buypackage_details = mongoConnectionObject.executeQuery("buy_packages", buypackage);
		Document buypackage_db = buypackage_details.get(0);
		@SuppressWarnings("unchecked")
		List<Document> coating_db = (List<Document>) buypackage_db.get("coatings");
		for (int i = 0; i < coating_db.size(); i++) {
			coating.add(String.valueOf(coating_db.get(i)));
		}
		return coating;
	}

	public static String getFirstProductIdFromCategory(String categoryId, String x_api_client) {
		JSONObject categoryResultObject;
		try {
			categoryResultObject = getCategoryDetails(categoryId, x_api_client).getJSONObject("result");
			if (categoryResultObject != null) {
				JSONArray productList = categoryResultObject.getJSONArray("product_list");
				if (productList != null && productList.length() > 0) {
					return productList.getJSONObject(0).getString("id");
				}
			}
		} catch (Exception e) {
			return null;
		}
		return null;
	}

	public static List<Document> getwishlistfromMongo(Map<String, Object> parms) throws Exception {

		List<Document> get_wishlist_details = mongo_dao.executeQuery(getCollectionName(), parms);
		return get_wishlist_details;
	}

	public static List<Document> getFilterDocumentfromDB(String filterId) throws Exception {
		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("filterId", filterId));
		return mongo_dao.executeQuery(mongoFilterCollectionName, queryParams);
	}

	public static List<Document> getFilterDocumentfromDB(Map<String, Object> queryParams) throws Exception {
		return mongo_dao.executeQuery(mongoFilterCollectionName, queryParams);
	}

	public static Document getFilterDocument(String filterId) throws Exception {
		List<Document> resultList = getFilterDocumentfromDB(filterId);
		if (resultList != null && !resultList.isEmpty()) {
			if (resultList.size() > 1) {
				throw new MongoException("Multiple records found");
			}
		} else {
			throw new MongoException("No result found..!");
		}
		return resultList.get(0);
	}

	public static JSONObject queryDBDocumentForFilter(String filterId) throws Exception {
		Document filterDoc = getFilterDocument(filterId);
		String jsonString = filterDoc.toJson();
		return new JSONObject(jsonString);
	}

	public static JSONObject createAndUpdateFilter(String filterId, String optionId)
			throws URISyntaxException, Exception {
		String requestUrlUpdateFilter = String
				.format(Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.UPDATE_FILTER_API, filterId);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject payloadJson = new JSONObject();
		payloadJson.put("filterName", "testFilter");
		payloadJson.put("catalogImageUrl", "catalogImageUrl.png");
		payloadJson.put("filterImageUrl", "filterImageUrl.png");
		payloadJson.put("enabled", "true");
		payloadJson.put("sortOrder", "4");
		JSONArray typeEnabledJsonArray = new JSONArray();
		typeEnabledJsonArray.put("Sunglasses");
		typeEnabledJsonArray.put("Eyeglasses");
		payloadJson.put("typeEnabled", typeEnabledJsonArray);
		JSONArray returnOptionsJsonArray = new JSONArray();
		JSONObject optionsJson = new JSONObject();
		optionsJson.put("magento_option_id", optionId);
		optionsJson.put("title", "testOption");
		optionsJson.put("enabled", "true");
		optionsJson.put("image_url", "imageUrl.png");
		optionsJson.put("hex_code", "FFFFF");
		returnOptionsJsonArray.put(optionsJson);
		payloadJson.put("options", returnOptionsJsonArray);
		HttpResponse httpUpdateFilterResponse = RequestUtil.postRequest(requestUrlUpdateFilter, headers, null,
				payloadJson);
		JSONObject jsonResponse = RequestUtil.convertHttpResponseToJsonObject(httpUpdateFilterResponse);
		log.debug("JSON Response object of update filter API" + jsonResponse);
		Assert.assertEquals(httpUpdateFilterResponse.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		return jsonResponse;
	}

	public static JSONObject getCountryState_V1() throws Exception {
		String requestURL_countryState = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_COUNTRY_STATE_PATH;
		/*
		 * List<NameValuePair> headers = new ArrayList<NameValuePair>(); headers.add(new
		 * BasicNameValuePair("Content-Type", "application/json"));
		 */
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_countryState);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject getCountryState_V1(String xApiClient) throws Exception {
		String requestURL_countryState = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_COUNTRY_STATE_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_countryState);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject getHomeService_v1(String audienceType, String xApiClient)
			throws URISyntaxException, IOException, ParseException, JSONException {
		String requestURL_HomeService = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_HOME_SERVICE_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("audienceType", audienceType));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_HomeService, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;

	}

	public static JSONObject getHomeService_v1_audienceType(String audienceType)
			throws URISyntaxException, IOException, ParseException, JSONException {
		String requestURL_HomeService = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_HOME_SERVICE_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("audienceType", audienceType));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_HomeService, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;

	}

	public static JSONObject getHomeService_v1(String xApiClient)
			throws URISyntaxException, IOException, ParseException, JSONException {
		String requestURL_HomeService = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_HOME_SERVICE_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_HomeService, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;

	}

	public static JSONObject campaignService(String campaignName, String xApiClient, JSONObject payload)
			throws Exception {
		String campaignURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.POST_CAMPAIGN_SERVICE_PATH, campaignName);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		HttpResponse httpResponse = RequestUtil.postRequest(campaignURL, headers, payload);
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJSON;
	}

	public static JSONObject offers_V1(String audienceType) throws ParseException, Exception {
		String requestURL_Offers = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_Offers_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("audienceType", audienceType));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_Offers, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject offers_V1(String xApiClient, String audienceType) throws ParseException, Exception {
		String requestURL_Offers = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_Offers_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("audienceType", audienceType));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_Offers, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject autoSuggest_V1(String searchText) throws Exception {
		String requestURL_autoSuggest = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.GET_AUTOSUGGEST_PATH, searchText);
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_autoSuggest);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject autoSuggest_V1(String searchText, String xApiClient) throws Exception {
		String requestURL_autoSuggest = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.GET_AUTOSUGGEST_PATH, searchText);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_autoSuggest, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject getRedis_V1(String redisKey) throws Exception {
		String requestURL_redis = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.GET_REDIS_PATH_V1;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("keys", redisKey));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_redis, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject getRedis_V1() throws Exception {
		String requestURL_redis = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.GET_REDIS_PATH_V1;
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_redis);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		return responseJson;
	}

	public static JSONObject queueService_V1(String productId) throws Exception {

		String requestURL_QueueApi = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_QUEUE_PATH_V1;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));

		JSONArray imageInfo = new JSONArray();
		JSONObject objMessage = new JSONObject();
		objMessage.put("productId", productId);
		objMessage.put("imageInfo", imageInfo);
		JSONObject obj = new JSONObject();
		obj.put("queueName", "IMG_RESYNC");
		obj.put("message", objMessage);
		log.debug("obj" + obj);

		HttpResponse httpResponse = RequestUtil.postRequest(requestURL_QueueApi, header, obj);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		responseJson.remove("error");
		return responseJson;
	}

	public static JSONObject queueServiceNegativecases_V1(String productId, String queueName) throws Exception {

		String requestURL_QueueApi = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_QUEUE_PATH_V1;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));

		JSONArray imageInfo = new JSONArray();
		JSONObject objMessage = new JSONObject();
		objMessage.put("productId", productId);
		objMessage.put("imageInfo", imageInfo);
		JSONObject obj = new JSONObject();
		obj.put("queueName", queueName);
		obj.put("message", objMessage);
		log.debug("obj" + obj);

		HttpResponse httpResponse = RequestUtil.postRequest(requestURL_QueueApi, header, obj);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);

		return responseJson;
	}

	public static JSONObject vaildQueNameWithoutMessage(String queuename) throws Exception {

		String requestURL_QueueApi = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_QUEUE_PATH_V1;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject obj = new JSONObject();
		obj.put("queueName", "IMG_RESYNC");
		log.debug("obj" + obj);

		HttpResponse httpResponse = RequestUtil.postRequest(requestURL_QueueApi, header, obj);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);

		return responseJson;
	}

	public static JSONObject getTemplate() throws Exception {
		String template_RequestUrl = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_TEMPLATE_V1;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient.ANDROID));
		header.add(new BasicNameValuePair("panelAuthToken", "NouqFyR8Bz2jk+BhAUdrIttcyApnkGmOCeVJwD890Ys="));
		HttpResponse httpResponse = RequestUtil.getRequest(template_RequestUrl, header, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);

		return responseJson;
	}

	public static JSONArray getPincode_v1() throws Exception {
		String requestURL_getPincode = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_PINCODE_PATH;
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_getPincode);
		JSONArray responseJson = RequestUtil.convertHttpResponseToJsonArray(httpResponse);
		return responseJson;
	}

	public static JSONArray getPincode_v1(String apiClient) throws Exception {
		String requestURL_getPincode = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_PINCODE_PATH;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", apiClient));
		HttpResponse httpResponse = RequestUtil.getRequest(requestURL_getPincode, header, null);
		JSONArray responseJson = RequestUtil.convertHttpResponseToJsonArray(httpResponse);
		return responseJson;

	}

	public static JSONObject updateBuyPackageApiResponse(JSONArray payladData) throws Exception {
		String UpdateBuyPackageRequestUrl = Environments.SERVICES_ENVIRONMENT
				+ JunoV1PathConstants.POST_UPDATE_BUY_PACKAGE;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		HttpResponse httpResponse = RequestUtil.postRequestFormData(UpdateBuyPackageRequestUrl, header, null,
				payladData);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("Response json for V1 API is *******************************" + responseJson);
		return responseJson;
	}

	public static JSONObject searchQuery(String catagoeryType) throws Exception {
		String searchQuery = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_SEARCH_QUERY_API;
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "android"));
		HttpResponse httpResponse = RequestUtil.getRequest(searchQuery, header);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.debug("Response json for V1 API is *******************************" + responseJson);
		return responseJson;
	}

	public static String getSubscriptionIdWithProductId(String productId, String isBothEye_value) throws Exception {
		JSONObject jsonObject_subscription = getSubscriptionDetailsWithProductId(productId, isBothEye_value);
		JSONObject result = jsonObject_subscription.getJSONObject("result");
		JSONArray subscriptions = result.getJSONArray("subscriptions");
		return subscriptions.getJSONObject(0).getString("subscriptionId");
	}

	public static JSONObject getSubscriptionDetailsWithProductId(String productId, String isBothEye_value)
			throws Exception {
		String RequestURL_subscription = Environments.SERVICES_ENVIRONMENT
				+ String.format(CLSubscriptionPathConstants.SUBSCRIPTION_API, productId);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Api-Client", "android"));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("isBothEye", isBothEye_value));

		HttpResponse HttpResponse_subscription = RequestUtil.getRequest(RequestURL_subscription, header, query);
		JSONObject jsonObject_subscription = RequestUtil.convertHttpResponseToJsonObject(HttpResponse_subscription);
		Assert.assertEquals(HttpResponse_subscription.getStatusLine().getStatusCode(), 200,
				"getSubscriptionIdWithProductId status code is not correct");
		return jsonObject_subscription;
	}

	public static JSONObject returnAllSubCategoryId(String gender, String categoryType) throws Exception {
		String url = Environments.SERVICES_ENVIRONMENT
				+ String.format("/v2/products/subcategory/gender/%s/catalog/%s", gender, categoryType);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Api-Client", "android"));
		HttpResponse httpResponseGetCategoryId = RequestUtil.getRequest(url,header,null);
		JSONObject getCategoryId = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCategoryId);
		Assert.assertEquals(httpResponseGetCategoryId.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject getCategoryIdResult = getCategoryId.getJSONObject("result");
		JSONArray subcategory = getCategoryIdResult.getJSONArray("subcategory");
		Assert.assertTrue(subcategory.length() > 0, "No category is present");
		return getCategoryId;
	}

}
