package org.lenskart.core.constant;

public class JunoV2ProductPathConstants {
//	public static final String CATEGORY_PATH_V2= "/v2/products/category/%s";
	public static final String CATEGORY_PATH_V2 = "/v2/products/category/%s";
	public static final String PRODUCT_PATH = "/v2/product/%s";
	public static final String WISHLIST_POST_V2 = "/v2/products/wishlist";
	public static final String SAVE_BULK_WISHLIST = "/v2/products/wishlist/bulk";
	public static final String WISHLIST_GET_V2 = "/v2/products/wishlist";
	public static final String DELETE_WISHLIST_V2 = "/v2/products/wishlist";
	public static final String DELETE_PRODUCT_FROM_WISHLIST_V2 = "/v2/products/wishlist/%s";
	public static final String UPDATE_WISHLIST_V2 = "/v2/products/wishlist";
	public static final String GET_HOME_SERVICE_PATH = "/v2/products/home";
	public static final String GET_AUTO_SUGGEST_PATH = "/v2/products/search/autosuggest/query/%s";
	public static final String GET_AUTO_SUGGEST_V2_PATH = "/v2/products/search/autosuggest/v2/query/%s";
	public static final String POST_POWER_TEMPLETE_PATH = "/v2/products/power-template";
	public static final String GET_PRODUCT_OFFER_V2 = "/v2/products/product/%s/offers";
	public static final String GET_SUBCATEGORY_PATH = "/v2/products/subcategory";
	public static final String GET_SUBCATEGORY_WITH_CATEGORYNAME_V2 = "/v2/products/subcategory/gender/%s/catalog/%s";

	public static final String GET_TEMPLATE_DETAILS = "/v2/products/template/{id}";
	public static final String GET_TEMPLATE_LIST = "/v2/products/template";
	public static final String UPDATE_TEMPLATE = "/v2/products/template";
	public static final String ADD_TEMPLATE = "/v2/products/template";
	public static final String GET_CATEGORY_BEST_SELLERS = "/v2/products/category/best-sellers";
	public static final String GET_PRODUCTS_AVAILABLE_FOR_LENS_SHADE = "/v2/products/category/contact-lens";
	public static final String GET_AVAILABLE_LENS_SHADE = "/v2/products/category/contact-lens/shade";
	public static final String GET_CATEGORY_NEW_ARRIVALS = "/v2/products/category/new-arrivals";
	public static final String GET_CATEGORY_API = "/v2/products/category/%s";
	public static final String GET_TEMPLATE_API_USING_TEMPLATE_ID = "/v2/products/template/%s";
	public static final String GET_TEMPLATE_API = "/v2/products/template";
	public static final String POST_UPDATE_BUY_PACKAGE = "/v2/products/update/buy-package";

	public static final String GET_BUYOPTION_BUYPACKAGE_V2 = "/v2/products/buyoption/buy-package";
	public static final String GET_BUYOPTION_COATING_V2 = "/v2/products/buyoption/coating";
	public static final String GET_SOLUTIONS_V2 = "/v2/products/product/solutions";
	public static final String GET_PRODUCT_V2 = "/v2/products/product/%s";
	public static final String GET_PRODUCT_DETAILSOF_MULTIPLE_PID_V2 = "/v2/products/product/";
	public static final String GET_PACKAGE_V2 = "/v2/products/product/%s/packages";
	public static final String GET_SUBSCRIPTION_V2 = "/v2/products/product/%s/subscriptions/";
	public static final String POST_SUBSCRIPTION_V2 = "/v2/products/product/oos-subscription/";
	public static final String GET_OFFERS_V2 = "/v2/products/product/%s/offers";
	public static final String GET_SEARCH_QUERY_V2 = "/v2/products/category/search/query/%s";
	public static final String GET_CarouselBanner_V2 = "/v2/products/carouselbanner";
	public static final String GET_PRODUCT_DETAILS_FOR_MULTIPLE_PID = "/v2/products/product/";

	public static final String GET_CONTACT_LENSES_POWERS = "/v2/products/product/%s/powers";
	public static final String GET_REVIEW_WITH_PRODUCTID = "/v2/products/product/%s/review";
	public static final String POST_CREATE_CUSTOMER_REVIEW = "/v2/products/product/%s/review";

	public static final String PRODUCT_PRICES = "/v2/products/product/%s/prices";
	public static final String GET_CUSTOMER_REVIEW = "/v2/products/product/review/customer";

	public static final String GET_PRODUCT_EXCHANGE = "/v2/products/product/";
	public static final String GET_TIER_DETAILS = "/v2/products/tiers";
	public static final String POST_UPDATE_PRODUCT_STATUS = "/v2/products/catalog/products/{pid}/status";
	public static final String POST_CATALOG_USER_LOGIN = "/v2/products/catalog/user/login";
	public static final String GET_METADATA = "/v2/products/catalog/metadata";
	public static final String FETCH_PRODUCTS_FILTERWISE = "/v2/products/catalog/products";
	public static final String EXPORT_PRODUCTS_DATA = "/v2/products/catalog/products/export";
	public static final String BULK_UPLOAD_TMEPLATE = "/v2/products/catalog/products/template";
	public static final String FETCH_PRODUCT = "/v2/products/catalog/products/{pid}";
	public static final String FETCH_CATALOG_USER = "/v2/products/catalog/user";
	public static final String GET_SIMILAR_PRODUCTS = "/v2/products/category/{id}/product/{productId}/similar";
	public static final String FHT_FILTERS = "/v2/products/fhtfilters/{categoryId}";
	public static final String FETCH_FHT_PACKAGE = "/v2/products/fht/package";
	public static final String FETCH_FHT_PACKAGE_BY_ID = "/v2/products/fht/package/{id}";
	public static final String GET_FILTERS = "/v2/products/filters/query/{query}";
	public static final String GET_REVIEW_FILTERS = "/v2/products/filters/review";
	public static final String GET_NEXT_FILTER = "/v2/products/filters/{category_id}/nextfilter";
	public static final String GET_FILTERS_WITH_ID = "/v2/products/filters/{id}";
	public static final String GET_QUICK_FILTER = "/v2/products/filters/{id}/quickfilter";
	public static final String UPDATE_QUICK_FILTER = "/v2/products/filters/{id}/quickfilter/update";
	public static final String DELETE_QUICK_FILTER = "/v2/products/filters/{id}/{title}/quickfilter/delete";
	public static final String VERIFY_QUICK_FILTER = "/v2/products/filters/{id}/{title}/quickfilter/verify";
	public static final String GET_GROUP_NAMES = "/v2/products/product/buyoptions/group";
	public static final String GET_GROUPS_NAMES = "/v2/products/product/buyoptions/groups";
	public static final String GET_DITTO_OPINION_PRODUCTS = "/v2/products/product/dittoOpinionProducts/{productType}/{qty}";
	public static final String GET_ALL_BUY_PACKAGES = "/v2/products/product/package";
	public static final String GET_PACKAGE_GROUP_NAMES = "/v2/products/product/packages/groups";
	public static final String GET_PACKAGE_GROUP_NAME_BY_ID = "/v2/products/product/packages/groups/{id}";
	public static final String CONVERT_EYE_TO_CL_POWER = "/v2/products/product/powers/convert";
	public static final String updatePowerTemplaeIDForGivenPorductId = "/v2/products/product/powertemplate/update";
	public static final String GET_PRODUCT_STATUS = "/v2/products/product/status";
	public static final String GET_ALL_BUY_OPTIONS_INFO_ON_GIVEN_PRODUCT = "/v2/products/product/v1/{id}/packages";
	public static final String GET_PRODUCT_DETAILS_BY_PARAM = "/v2/products/product/{id}/params";
	public static final String GET_GIVEN_PRODUCT_REVIEW_FOR_A_SPECIFIC_CUSTOMER = "/v2/products/product/{id}/review/order";
	public static final String GET_ALL_REVIEWS_OF_A_PRODUCT = "/v2/products/product/{id}/reviews";
	public static final String GET_TAT_RESPONSE = "/v2/products/product/{pid}/shipping/estimate";
	public static final String VALIDATE_POWER_DETAILS = "/v2/products/product/{id}/powers/validate";
	public static final String VALIDATE_PRODUCT_POWER_DETAILS = "/v2/products/product/powers/prices";
	public static final String ADD_TO_CART_VALIDATE = "/v2/products/product/addtocart/validate";
	public static final String ADD_POWER_TEMPLATE = "/v2/products/power-template";
	public static final String PATCH_PRODUCTS_DATA = "/v2/products/catalog/products";
	public static final String ADD_PRODUCT_TO_CATALOG = "/v2/products/catalog/products/{productId}";
	public static final String REGISTER_CATALOG_USER = "/v2/products/catalog/user/register";
	public static final String LOGIN_CATALOG_USER = "/v2/products/catalog/user/login";
	public static final String SEARCH_TRENDING = "/v2/products/search/trending";
	public static final String PRODUCT_SEARCH = "/v2/products/search";
	public static final String GET_GOLD_TIER_DETAILS_API = "/v2/products/tiers";
	public static final String GET_GOLD_TIER_BY_PRODUCTID_API = "/v2/products/tiers/id/%s";
	public static final String GET_GOLD_TIER_BY_NAME_API = "/v2/products/tiers/name/%s";
	public static final String ADD_LOCALE_FILTER_API = "/v2/products/filters/locale/filterName";
	public static final String PRODUCT_METADATA_API = "/v2/products/product/metadata";
	public static final String GET_CONTACT_LENS_DETAILS_FROM_POWERWISEID_API = "/v2/products/product/contact-lens/powerWise";
	public static final String GET_PRODUCT_DOCUMENT_BY_ID_ALL_IMAGES_INTERNAL_API = "/v2/products/product/internal/all-images/%s";
	public static final String GET_PRODUCT_DOCUMENT_BY_ID_INTERNAL_API = "/v2/products/product/internal/%s";
	public static final String GET_GROUP_NAME_FOR_GIVEN_OID_API = "/v2/products/product/packages/groups/name";
	public static final String GET_POWER_RESPONSE_WITH_RESTRICTIONS_API = "/v2/products/product/%s/power-restrictions";
	public static final String GET_PRODUCT_BOGO_WIDGET_API = "/v2/products/product/%s/bogo/widget";
	public static final String FETCH_INVENTORY_TEMPLATE_BY_PID_AND_CLIENT_API = "/v2/products/product/%s/templates/inventory";
	public static final String FETCH_PRODUCT_BY_FILTER_API = "/v2/products/products/filter";
	public static final String GET_SEARCH_RESULT_API = "/v2/products/search";
	public static final String GET_SEARCH_FILTER_API = "/v2/products/search/filters";
	public static final String GET_TRENDING_SEARCH_API = "/v2/products/search/trending";
	public static final String GET_SORTBY_OPTIONS_API = "/v2/products/sort";
	public static final String GET_BUYBACK_ELIGIBILITY_API = "/v2/products/product/%s/buyBack/eligibility";
	public static final String GET_ALL_SIZE_BUCKETING_CONFIG_API = "/v2/products/size/bucketing/config";
	public static final String APPLY_BUCKETING_CONFIG_API = "/v2/products/size/bucketing/config/apply";
	public static final String CHECK_STORE_AVAILABILITY_API = "/v2/products/product/check-store-availability";

	// V3 product URLs
	public static final String GET_CATEGORY_V3_API = "/v3/products/category/%s";
	public static final String GET_CATEGORY_FILTER_V3_API = "/v3/products/filters/%s";
	public static final String SEARCH_API_WITH_REVAMPED_FILTERS_V3_API = "/v3/products/search";
	public static final String SEARCH_REVAMPED_FILTERS_V3_API = "/v3/products/search/filters";
	public static final String GET_PRODUCT_DOCUMENT_BY_ID_API = "/v3/products/doc/product/%s";
	public static final String PRODUCT_DOCUMENT_SEARCH_API = "/v3/products/doc/products";
	public static final String GET_LIMITED_PRODUCT_INFO_POST_API = "/v3/products/doc/product/info";
	public static final String GET_CATEGORY_INLINE_FILTER_RESULT_V3_API = "/v3/products/filters/inline/%s";
	public static final String GET_AR_COLOR_OPTIONS_V3_API = "/v3/products/product/ar-color-options/%s";
	public static final String GET_CL_PRODUCT_V3_API = "/v3/products/product/%s";
	public static final String GET_BUYOPTIONS_DETAILS_V3_API = "/v3/products/product/%s/packages";

}
