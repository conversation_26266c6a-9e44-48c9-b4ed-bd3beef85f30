package org.lenskart.test.payment.v2;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.PaymentV2PathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.MoneyUtil;
import org.lenskart.core.util.OrderUtil;
import org.lenskart.core.util.PaymentUtil;
import org.lenskart.core.util.SessionUtil;
import org.lenskart.test.customer.mobilelogin.MobileAndEmailLogin;
import org.lenskart.test.order.OrderDatabaseValidation;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.v2.cart.CartItem;
import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.RequestUtil;

public class OrderPaymentUsecases {

	private static final Logger log = GenericUtil.InitLogger(OrderPaymentUsecases.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;
	private static String sessiontoken;
	private static String RequstUrlorderPayment = Environments.SERVICES_ENVIRONMENT
			+ PaymentV2PathConstants.GATEWAY_ORDER_PAYMENT_API;
	private MongoConnectionUtility mongoOrderConnectionObject;
	private MongoConnectionUtility mongoPaymentConnectionObject;
	private MySQLConnectionUtility mysqlConnectionObject = null;
	private static HashMap<String, String> product = new HashMap<>();
	private static String xApiClient = ApplicationConstants.XApiClient.DESKTOP;

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (db_Validation) {
			mongoOrderConnectionObject = OrderUtil.getOrderMongoConnectionObject();
			mongoPaymentConnectionObject = PaymentUtil.getPaymentMongoConnectionObject();
			mongoOrderConnectionObject.initMongoDBConnection();
			mongoPaymentConnectionObject.initMongoDBConnection();
			mysqlConnectionObject = JunoV1Util.getJunoV1MySQLConnectionObject();
			mysqlConnectionObject.initMySQLConnection();
		}
	}

	@AfterClass
	public void closeDBConection() throws IOException {
		if (db_Validation) {
			mongoOrderConnectionObject.closeMongoConnection();
			mongoPaymentConnectionObject.closeMongoConnection();
			mysqlConnectionObject.closeMySQLConnection();
		}
	}

	private void orderPaymentResponseValidation(JSONObject orderResponse, JSONObject orderPaymentRequest,
			boolean itemLevelShipToStoreRequired, boolean itemLevelIsLocalFittingRequired,
			boolean orderLevelShipToStoreRequired, boolean orderLevelIsLocalFittingRequired) throws JSONException {
		Assert.assertEquals(orderResponse.getString("storeType"), orderPaymentRequest.getString("storeType"),
				"storeType mismatch");
		Assert.assertEquals(orderResponse.getString("facilityCode"), orderPaymentRequest.getString("facilityCode"),
				"facilityCode mismatch");
		if (OrderDatabaseValidation.redisKey)
			Assert.assertTrue(orderResponse.getBoolean("isDualCompanyEnabled"), "isDualCompanyEnabled mismatch");
		else
			Assert.assertEquals(orderResponse.getBoolean("isDualCompanyEnabled"),
					orderPaymentRequest.getBoolean("isDualCompanyEnabled"), "isDualCompanyEnabled mismatch");
		Assert.assertEquals(orderResponse.getBoolean("isBulkOrder"), orderPaymentRequest.getBoolean("isBulkOrder"),
				"isBulkOrder mismatch");
		JSONArray orderItems = orderResponse.getJSONArray("items");
		for (int i = 0; i < orderItems.length(); i++) {
			JSONObject orderItemsObject = orderItems.getJSONObject(i);
			if (orderPaymentRequest.has("shipToStoreRequired")) {
				Assert.assertEquals(orderItemsObject.getBoolean("shipToStoreRequired"), itemLevelShipToStoreRequired,
						"shipToStoreRequired mismatch");
				Assert.assertEquals(orderItemsObject.getBoolean("isLocalFittingRequired"),
						itemLevelIsLocalFittingRequired, "isLocalFittingRequired mismatch");
				log.info("jshdghsdguyyu");
			}
		}
		Assert.assertTrue(orderResponse.has("shipToStoreRequired"), "shipToStoreRequired mismatch");
		Assert.assertTrue(orderResponse.has("isLocalFittingRequired"), "isLocalFittingRequired mismatch");
		Assert.assertEquals(orderResponse.getBoolean("shipToStoreRequired"), orderLevelShipToStoreRequired,
				"shipToStoreRequired mismatch");
		Assert.assertEquals(orderResponse.getBoolean("isLocalFittingRequired"), orderLevelIsLocalFittingRequired,
				"isLocalFittingRequired mismatch");
	}

	@DataProvider(name = "CreateOrderPayment")
	public static Iterator<String[]> supplyData() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "/csv_files/Payment_v2 API/CreateOrderPayment.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "IsPaymentCapture")
	public static Iterator<String[]> supplyData2() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "/csv_files/Payment_v2 API/IsPaymentCapture.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		int[] primeArray = new int[1000];
		for (int i = 0; i < primeArray.length; i++) {

		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "DualCompanyAdditionalParameter")
	public static Iterator<String[]> supplyData1() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "/csv_files/Payment_v2 API/dualComapnyAdditionalParameter.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@Test(enabled = true, dataProvider = "CreateOrderPayment")
	public void orderPayment_login_user_usecase(String usecase, String x_Api_Client, String bank, String payment,
			String gatewayId, String power_type, String category_name, String status_code, String error_status,
			String error_message, String errors) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");
		// session function call
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,x_Api_Client);
		// cart function call
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(category_name),
				x_Api_Client);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String package_id = null;
		if ((!power_type.equals(""))) {
			JSONObject buypackage = JunoV1Util.getPackageIdFromBuyOptionApi(product_Id, power_type, "", x_Api_Client);
			package_id = buypackage.getJSONObject("result").getJSONArray("packages").getJSONObject(0).getString("id");
		}
		CartItem reqObj = new CartItem();
		if (category_name.equals("category_contact_lens")) {
			Prescription presObj = ApplicationUtil.prescriptionContactLens();
			reqObj.setPrescription(presObj);
		}
		reqObj.setProductId(Long.parseLong(product_Id));
		reqObj.setPackageId(package_id);
		reqObj.setPowerType(power_type);
		JSONObject cart = CartUtil.createCart(sessiontoken, reqObj, x_Api_Client);
		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		int customerId = cart.getJSONObject("result").getJSONObject("customer").getInt("id");
		String customer_email = cart.getJSONObject("result").getJSONObject("customer").getString("email");

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", gatewayId);
		paymentInfo.put("paymentMethod", payment);
		if (!bank.equals("")) {
			JSONObject netbanking = new JSONObject();
			netbanking.put("bankCode", bank);
			paymentInfo.put("netbanking", netbanking);
		}
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), Integer.parseInt(status_code),
				"Status is not correct");

		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			int orderId = order_responseJson.getInt("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertNotNull(order_responseJson.getInt("customerId"), "customerId is null");
			Assert.assertNotNull(order_responseJson.getString("customerEmail"), " customerEmail is null");
			Assert.assertEquals(order_responseJson.getInt("customerId"), customerId, "Customer ID is not same");
			Assert.assertEquals(order_responseJson.getString("customerEmail"), customer_email,
					"Customer email is not same");
			if ((payment.equals("cod") && (!power_type.equals("")) && (!package_id.equals(""))) || payment.equals("sc")
					|| payment.equals("gv") || payment.equals("payzero") || payment.equals("offlineairtel")
					|| payment.equals("storeairtel")) {
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
						"Status is not same");
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
						"State is not same");
			} else if (payment.equals("nb") || payment.equals("cc") || payment.equals("dc") || payment.equals("wallet")
					|| payment.equals("paytm_cc")) {
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PENDING",
						"Status is not same");
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "NEW",
						"State is not same");
			} else {
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING_NEW",
						"Status is not same");
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "NEW",
						"State is not same");
			}
			if (!payment.equals("nb") && !bank.equals("")) {
				JSONObject payment_responseJson = result.getJSONObject("payment");
				String paymentid = payment_responseJson.getString("paymentId");
				JSONObject amount = payment_responseJson.getJSONObject("amount");
				Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
				Assert.assertNotNull(amount.getString("price"), "price is not same");
				JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
				if (payment.equals("sc") || payment.equals("gv") || payment.equals("cod")
						|| payment.equals("offlineairtel") || payment.equals("storeairtel")) {
					Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
				} else {
					if (gatewayId.equals("CT")) {
						Assert.assertEquals(actionInfo.getString("action"), "CITRUS_REDIRECT", "action is not correct");
					} else {
						Assert.assertEquals(actionInfo.getString("action"), "REDIRECT", "action is not correct");
					}
					Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
				}
				Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
						"orderID is not same");

				if (db_Validation) {
					Map<String, Object> get_order_details = new HashMap<String, Object>();
					get_order_details.put("_id", orderId);
					List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
					if (getOrderDetails.size() == 1) {
						Document order_detail = (Document) getOrderDetails.get(0);
						log.info("sdfdgdrfdgf " + order_detail);
						if (OrderDatabaseValidation.redisKey) {
							Assert.assertTrue(order_detail.getBoolean("isDualCompanyEnabled"),
									"isDualCompanyEnabled mismatch");
							log.info("isDualCompanyEnabled is True");
						}
						OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
					}

					Map<String, Object> payment_details = new HashMap<String, Object>();
					payment_details.put("_id", paymentid);

					List<Document> payment_status = PaymentUtil.getpayments(payment_details);
					for (int i = 0; i < payment_status.size(); i++) {
						Document payment_db = payment_status.get(i);
						log.info("asdfsfsd" + payment_db);
						PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
					}
				}
			}
		} else {
			Assert.assertEquals(Integer.parseInt(error_status), responseJson_Orderpayment.getInt("status"),
					"error_status is not correct");
			Assert.assertEquals(error_message, responseJson_Orderpayment.getString("message"),
					"error_message is not correct");
			Assert.assertEquals(errors, responseJson_Orderpayment.getString("error"), "errors is not correct");
		}

		CartUtil.clearCart(sessiontoken);
	}

	private void createCartForSessionToken(String sessionToken, String client) throws URISyntaxException, Exception {
		JSONObject categoryDetails = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), client);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id));
		CartUtil.createCart(sessionToken, reqObj, client);
		CartUtil.saveAddress(sessionToken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
	}

	@Test(enabled = true)
	public void orderPaymentWithPayuWallet() throws Exception {
		// sessiontoken =
		// CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
		// ApplicationConstants.userPassword);
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = MobileAndEmailLogin.emailLogin_MobileOnlyWithEmailNotVerified(sessiontoken, xApiClient,
				mobileNumber);
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id));
		JSONObject cart = CartUtil.createCart(sessiontoken, reqObj, xApiClient);
		int customerId = cart.getJSONObject("result").getJSONObject("customer").getInt("id");
		String customer_email = cart.getJSONObject("result").getJSONObject("customer").getString("email");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		JSONObject paymentMethod = PaymentUtil.getAllPaymentMethods(sessiontoken, xApiClient);
		CartUtil.getCartBasedOnApplyWallet(sessiontoken, xApiClient, "true");
		JSONObject paymentMethodResult = paymentMethod.getJSONObject("result");
		JSONArray paymentMethods = paymentMethodResult.getJSONArray("paymentMethods");
		Assert.assertTrue(paymentMethods.length() > 0, "No payment method is present");
		CartUtil.clearCart(sessiontoken);
		for (int i = 0; i < paymentMethods.length(); i++) {
			JSONObject paymentMethodJson = paymentMethods.getJSONObject(i);
			log.info("--------------------------" + paymentMethodJson.getString("code") + "-----------------------");
			if (paymentMethodJson.getString("code").equals("sc") || paymentMethodJson.getString("code").equals("gv")) {
				continue;
			} else {
				createCartForSessionToken(sessiontoken, xApiClient);
				List<NameValuePair> Header = new ArrayList<NameValuePair>();
				Header.add(new BasicNameValuePair("Content-Type", "application/json"));
				Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
				Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
				Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

				JSONObject Requestparams_createOrderPayment = new JSONObject();
				JSONObject paymentInfo = new JSONObject();
				if (paymentMethodJson.has("gatewayId")) {
					paymentInfo.put("gatewayId", paymentMethodJson.getString("gatewayId"));
				}
				paymentInfo.put("paymentMethod", paymentMethodJson.getString("code"));
				if (paymentMethodJson.getString("code").equals("nb")) {
					JSONObject netbanking = new JSONObject();
					netbanking.put("bankCode",
							paymentMethodJson.getJSONArray("banks").getJSONObject(1).getString("bankcode"));
					paymentInfo.put("netbanking", netbanking);
				}
				JSONObject card = new JSONObject();
				paymentInfo.put("card", card);
				Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

				log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
				HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
						Requestparams_createOrderPayment);
				JSONObject responseJson_Orderpayment = RequestUtil
						.convertHttpResponseToJsonObject(httpResponseorderpayment);
				Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200,
						"Status is not correct");

				if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
					Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
							"Order Payment is not giving correct response");
					Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getJSONObject("order"),
							"Order Payment order is not giving correct response");
					Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getJSONObject("payment"),
							"Order Payment payment is not giving correct response");
					JSONObject result = responseJson_Orderpayment.getJSONObject("result");
					JSONObject order_responseJson = result.getJSONObject("order");
					int orderId = order_responseJson.getInt("id");
					Assert.assertNotNull(order_responseJson.getInt("customerId"), "customerId is null");
					Assert.assertNotNull(order_responseJson.getString("customerEmail"), " customerEmail is null");
					Assert.assertEquals(order_responseJson.getInt("customerId"), customerId, "Customer ID is not same");
					Assert.assertEquals(order_responseJson.getString("customerEmail"), customer_email,
							"Customer email is not same");
					JSONObject payment_responseJson = result.getJSONObject("payment");
					String paymentid = payment_responseJson.getString("paymentId");
					JSONObject amount = payment_responseJson.getJSONObject("amount");
					Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
					Assert.assertNotNull(amount.getString("price"), "price is not same");
					JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
					if (paymentMethodJson.getString("code").equals("cod")
							|| paymentMethodJson.getString("code").equals("po")
							|| paymentMethodJson.getString("code").equals("offlineairtel")
							|| paymentMethodJson.getString("code").equals("storeairtel")
							|| paymentMethodJson.getString("code").equals("partialpayment")) {
						Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
					} else {
						if (Requestparams_createOrderPayment.getJSONObject("paymentInfo").has("gatewayId")) {
							if (paymentMethodJson.getString("gatewayId").equals("CT")) {
								Assert.assertEquals(actionInfo.getString("action"), "CITRUS_REDIRECT",
										"action is not correct");
							} else {
								Assert.assertEquals(actionInfo.getString("action"), "REDIRECT",
										"action is not correct");
							}
						}
						Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
						JSONObject requestParams = actionInfo.getJSONObject("requestParams");
						if (paymentMethodJson.getString("code").equals("cc")
								|| paymentMethodJson.getString("code").equals("dc")) {
							Assert.assertEquals(requestParams.getString("bankcode"), "CC", "bankcode mismatch");
							Assert.assertEquals(requestParams.getString("Pg").toLowerCase(),
									paymentMethodJson.getString("code"), "Pg mismatch");
						} else if (paymentMethodJson.getString("code").equals("nb")) {
							Assert.assertEquals(requestParams.getString("Bankcode"), paymentMethodJson
									.getJSONArray("banks").getJSONObject(1).getString("bankcode").split(":")[1],
									"bankcode mismatch");
							Assert.assertEquals(requestParams.getString("Pg").toLowerCase(),
									paymentMethodJson.getString("code"), "Pg mismatch");
						} else if (paymentMethodJson.getString("code").equals("paytm_cc")
								|| paymentMethodJson.getString("code").equals("wallet")) {
							// Assert.assertNotNull(requestParams.getString("email"),"email
							// not found");
						} else {
							Assert.assertEquals(requestParams.getString("bankcode"),
									paymentMethodJson.getString("gatewayId").split(":")[1], "bankcode mismatch");
							Assert.assertEquals(requestParams.getString("Pg"), "CASH", "Pg mismatch");
						}
					}
					Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
							"orderID is not same");

					if (db_Validation) {
						Map<String, Object> get_order_details = new HashMap<String, Object>();
						get_order_details.put("_id", orderId);
						List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
						Assert.assertEquals(getOrderDetails.size(), 1, "Multiple document present in db");
						Document order_detail = (Document) getOrderDetails.get(0);
						OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);

						Map<String, Object> payment_details = new HashMap<String, Object>();
						payment_details.put("_id", paymentid);
						List<Document> payment_status = PaymentUtil.getpayments(payment_details);
						Assert.assertEquals(payment_status.size(), 1, "Multiple document present in db");
						Document payment_db = payment_status.get(0);
						PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
						log.info("DB VALIDATION DONE");
					}
				}
			}
		}
	}

	@Test(enabled = true)
	public void orderPaymentLoggedInWithSCFullyApplied() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String package_id = null;
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id));
		reqObj.setPackageId(package_id);
		JSONObject cart = CartUtil.createCart(sessiontoken, reqObj, ApplicationConstants.XApiClient.ANDROID);
		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		int customerId = cart.getJSONObject("result").getJSONObject("customer").getInt("id");
		int itemTotal = cart.getJSONObject("result").getJSONObject("totals").getInt("total");
		String customer_email = cart.getJSONObject("result").getJSONObject("customer").getString("email");
		CartUtil.addStoreCredit(sessiontoken, ApplicationConstants.storeCreditCode, itemTotal);
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "sc");
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");

		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			int orderId = order_responseJson.getInt("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getBoolean("isPaymentCaptured"), true,
					"isPaymentCaptured is not same");
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertNotNull(order_responseJson.getInt("customerId"), "customerId is null");
			Assert.assertNotNull(order_responseJson.getString("customerEmail"), " customerEmail is null");
			Assert.assertEquals(order_responseJson.getInt("customerId"), customerId, "Customer ID is not same");
			Assert.assertEquals(order_responseJson.getString("customerEmail"), customer_email,
					"Customer email is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
					"State is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");

			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");

			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					Assert.assertEquals(order_detail.get("isPaymentCaptured"), true, "isPaymentCaptured is not same");
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}

				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void orderPaymentLoggedInWithSCNotFullyApplied() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String package_id = null;
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id));
		reqObj.setPackageId(package_id);
		CartUtil.createCart(sessiontoken, reqObj, ApplicationConstants.XApiClient.ANDROID);
		CartUtil.addStoreCredit(sessiontoken, ApplicationConstants.storeCreditCode, 10);
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "sc");
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 428, "Status is not correct");
		Assert.assertEquals(428, responseJson_Orderpayment.getInt("status"), "error_status is not correct");
		Assert.assertEquals("Order total amount should be zero if payment method provided is sc or gv",
				responseJson_Orderpayment.getString("message"), "error_message is not correct");
		Assert.assertEquals("Precondition Required", responseJson_Orderpayment.getString("error"),
				"errors is not correct");
	}

	@Test(enabled = true)
	public void orderPayment_login_user_usecase_for_sanity() throws Exception {
		log.info("------------------------usecase------------------------------------");
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		// cart function call
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject cart = CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);

		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		int customerId = cart.getJSONObject("result").getJSONObject("customer").getInt("id");
		String customer_email = cart.getJSONObject("result").getJSONObject("customer").getString("email");

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", "");
		paymentInfo.put("paymentMethod", "cod");

		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertNotNull(order_responseJson.getInt("customerId"), "customerId is null");
			Assert.assertNotNull(order_responseJson.getString("customerEmail"), " customerEmail is null");
			Assert.assertEquals(order_responseJson.getInt("customerId"), customerId, "Customer ID is not same");
			Assert.assertEquals(order_responseJson.getString("customerEmail"), customer_email,
					"Customer email is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
					"State is not same");

		}
	}

	@Test(enabled = false)
	public void orderPayment_login_user_usecase_B2B_Account() throws Exception {
		log.info("------------------------Order Payment For B2B account------------------------------------");
		// session function call
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		// cart function call
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject cart = CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);

		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		int customerId = cart.getJSONObject("result").getJSONObject("customer").getInt("id");
		String customer_email = cart.getJSONObject("result").getJSONObject("customer").getString("email");

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject billingAddress = ApplicationUtil.shippingAddress();
		JSONObject Requestparams_createOrderPayment = new JSONObject();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", "");
		paymentInfo.put("paymentMethod", "po");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");

		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			int orderId = order_responseJson.getInt("id");
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertNotNull(order_responseJson.getInt("customerId"), "customerId is null");
			Assert.assertNotNull(order_responseJson.getString("customerEmail"), " customerEmail is null");
			Assert.assertEquals(order_responseJson.getInt("customerId"), customerId, "Customer ID is not same");
			Assert.assertEquals(order_responseJson.getString("customerEmail"), customer_email,
					"Customer email is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING_NEW",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "NEW",
					"State is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void orderPayment_guest_user_usecase_mix_cart() throws Exception {
		log.info("-------------------Order Payment for Mix cart-------------------------------------");
		// session function call
		sessiontoken = SessionUtil.createNewSession();
		// cart function call
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		CartUtil.clearCart(sessiontoken);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);

		categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject cart2 = CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);

		int cartId = cart2.getJSONObject("result").getInt("cartId");
		int storeId = cart2.getJSONObject("result").getInt("storeId");
		log.info("Cart Response :" + cart2);
		boolean flag = false;
		for (int i = 0; i < cart2.getJSONObject("result").getJSONArray("items").length(); i++) {
			if (cart2.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId")
					.equals("128269")) {
				flag = true;
				break;
			}
		}
		if (flag) {
			Assert.assertEquals(cart2.getJSONObject("result").getInt("itemsQty"), 3,
					"Both the product didnt get added");
		} else {
			Assert.assertEquals(cart2.getJSONObject("result").getInt("itemsQty"), 2,
					"Both the product didnt get added");
		}
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", "");
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			int orderId = order_responseJson.getInt("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING_NEW",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "NEW",
					"State is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}
			Assert.assertEquals(responseJson_Orderpayment.getInt("status"), 200, "Response status is not correct");
		}
	}

	@Test(enabled = true, dataProvider = "CreateOrderPayment")
	public void orderPayment_guest_user_usecase(String usecase, String x_Api_Client, String bank, String payment,
			String gatewayId, String power_type, String category_name, String status_code, String error_status,
			String error_message, String errors) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");
		// session function call
		sessiontoken = SessionUtil.createNewSession();
		// cart function call
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(category_name),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String package_id = null;
		if ((!power_type.equals(""))) {
			JSONObject buypackage = JunoV1Util.getPackageIdFromBuyOptionApi(product_Id, power_type, "", xApiClient);
			package_id = buypackage.getJSONObject("result").getJSONArray("packages").getJSONObject(0).getString("id");
		}
		CartItem reqObj = new CartItem();
		if (category_name.equals("category_contact_lens")) {
			Prescription presObj = ApplicationUtil.prescriptionContactLens();
			reqObj.setPrescription(presObj);
		}
		reqObj.setProductId(Long.parseLong(product_Id));
		reqObj.setPackageId(package_id);
		reqObj.setPowerType(power_type);
		JSONObject cart = CartUtil.createCart(sessiontoken, reqObj, x_Api_Client);
		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		log.info("Cart Response :" + cart);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", gatewayId);
		paymentInfo.put("paymentMethod", payment);
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", bank);
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), Integer.parseInt(status_code),
				"Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			int orderId = order_responseJson.getInt("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");

			if ((payment.equals("cod") && (!power_type.equals("")) && (!package_id.equals(""))) || payment.equals("sc")
					|| payment.equals("gv") || payment.equals("payzero") || payment.equals("offlineairtel")
					|| payment.equals("storeairtel")) {
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
						"Status is not same");
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
						"State is not same");
			} else if (payment.equals("nb") || payment.equals("cc") || payment.equals("dc") || payment.equals("wallet")
					|| payment.equals("paytm_cc")) {
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PENDING",
						"Status is not same");
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "NEW",
						"State is not same");
			} else {
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING_NEW",
						"Status is not same");
				Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "NEW",
						"State is not same");
			}
			if (!payment.equals("nb") && !bank.equals("")) {
				JSONObject payment_responseJson = result.getJSONObject("payment");
				String paymentid = payment_responseJson.getString("paymentId");
				JSONObject amount = payment_responseJson.getJSONObject("amount");
				Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
				Assert.assertNotNull(amount.getString("price"), "price is not same");
				JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
				if (payment.equals("cod") || payment.equals("offlineairtel") || payment.equals("storeairtel")) {
					Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
				}
				Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
						"orderID is not same");
				if (db_Validation) {
					Map<String, Object> get_order_details = new HashMap<String, Object>();
					get_order_details.put("_id", orderId);
					List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
					if (getOrderDetails.size() == 1) {
						Document order_detail = (Document) getOrderDetails.get(0);
						log.info("sdfdgdrfdgf " + order_detail);
						OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
					}
					Map<String, Object> payment_details = new HashMap<String, Object>();
					payment_details.put("_id", paymentid);

					List<Document> payment_status = PaymentUtil.getpayments(payment_details);
					for (int i = 0; i < payment_status.size(); i++) {
						Document payment_db = payment_status.get(i);
						log.info("asdfsfsd" + payment_db);
						PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
					}
				}
			}
			Assert.assertEquals(responseJson_Orderpayment.getInt("status"), 200, "Response status is not correct");
		}
	}

	@Test(enabled = true)
	public void orderPayment_retry_order_usecase() throws Exception {
		log.info("------------------------Retry Order Payment-------------------------------------");
		sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject cart = CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);

		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		log.info("Cart Response :" + cart);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", "PU");
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");

			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
		}

		List<NameValuePair> Header1 = new ArrayList<NameValuePair>();
		Header1.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header1.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header1.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header1.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		log.info("session" + sessiontoken);
		JSONObject Requestparams_createOrderPayment_retry = new JSONObject();
		Requestparams_createOrderPayment_retry.put("orderId",
				responseJson_Orderpayment.getJSONObject("result").getJSONObject("order").getInt("id"));
		JSONObject billingAddress_retry = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress_retry);
		JSONObject paymentInfo_retry = new JSONObject();
		paymentInfo_retry.put("gatewayId", "PU");
		paymentInfo_retry.put("paymentMethod", "offlineairtel");
		JSONObject netbanking_retry = new JSONObject();
		netbanking_retry.put("bankCode", "");
		paymentInfo_retry.put("netbanking", netbanking_retry);
		JSONObject card_retry = new JSONObject();
		paymentInfo_retry.put("card", card_retry);
		Requestparams_createOrderPayment_retry.put("paymentInfo", paymentInfo_retry);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment_retry = RequestUtil.postRequest(RequstUrlorderPayment, Header1,
				Requestparams_createOrderPayment_retry);
		Assert.assertEquals(httpResponseorderpayment_retry.getStatusLine().getStatusCode(), 200,
				"Status is not correct");
		JSONObject responseJson_Orderpayment_retry = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseorderpayment_retry);
		if (httpResponseorderpayment_retry.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_Orderpayment_retry.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			int orderId = order_responseJson.getInt("id");
			JSONObject payment_responseJson = result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");

			// Assert.assertEquals(actionInfo.getString("action"), "REDIRECT",
			// "action is not correct");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			// Assert.assertNotNull(actionInfo.getString("redirectUrl"),
			// "redirectUrl is null");

			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void orderPayment_retry_order_usecase_blank_orderid() throws Exception {
		log.info("------------------------Retry Order Payment blank order id-------------------------------------");
		// session function call
		sessiontoken = SessionUtil.createNewSession();
		// cart function call
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject cart = CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);

		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		log.info("Cart Response :" + cart);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", "PU");
		paymentInfo.put("paymentMethod", "wallet");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "REDIRECT", "action is not correct");
			Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
		}

		List<NameValuePair> Header1 = new ArrayList<NameValuePair>();
		Header1.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header1.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header1.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header1.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		log.info("session" + sessiontoken);
		JSONObject Requestparams_createOrderPayment_retry = new JSONObject();
		Requestparams_createOrderPayment_retry.put("orderId", "");
		JSONObject billingAddress_retry = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress_retry);
		JSONObject paymentInfo_retry = new JSONObject();
		paymentInfo_retry.put("gatewayId", "PU");
		paymentInfo_retry.put("paymentMethod", "wallet");
		JSONObject netbanking_retry = new JSONObject();
		netbanking_retry.put("bankCode", "");
		paymentInfo_retry.put("netbanking", netbanking_retry);
		JSONObject card_retry = new JSONObject();
		paymentInfo_retry.put("card", card_retry);
		Requestparams_createOrderPayment_retry.put("paymentInfo", paymentInfo_retry);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment_retry = RequestUtil.postRequest(RequstUrlorderPayment, Header1,
				Requestparams_createOrderPayment_retry);
		Assert.assertEquals(httpResponseorderpayment_retry.getStatusLine().getStatusCode(), 428,
				"Status is not correct");
		RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment_retry);

	}

	@Test(enabled = true)
	public void orderPayment_retry_order_usecase_invaild_orderid() throws Exception {
		log.info("------------------------Retry Order Payment invalid order id-------------------------------------");
		// session function call
		sessiontoken = SessionUtil.createNewSession();
		// cart function call
		JSONObject cart = CartUtil.createCart(sessiontoken, "114313", "", "", xApiClient);
		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		log.info("Cart Response :" + cart);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("gatewayId", "PU");
		paymentInfo.put("paymentMethod", "wallet");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "REDIRECT", "action is not correct");
			Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
		}

		List<NameValuePair> Header1 = new ArrayList<NameValuePair>();
		Header1.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header1.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header1.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header1.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		log.info("session" + sessiontoken);
		JSONObject Requestparams_createOrderPayment_retry = new JSONObject();
		Requestparams_createOrderPayment_retry.put("orderId", "dwefsdfg");
		JSONObject billingAddress_retry = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress_retry);
		JSONObject paymentInfo_retry = new JSONObject();
		paymentInfo_retry.put("gatewayId", "PU");
		paymentInfo_retry.put("paymentMethod", "wallet");
		JSONObject netbanking_retry = new JSONObject();
		netbanking_retry.put("bankCode", "");
		paymentInfo_retry.put("netbanking", netbanking_retry);
		JSONObject card_retry = new JSONObject();
		paymentInfo_retry.put("card", card_retry);
		Requestparams_createOrderPayment_retry.put("paymentInfo", paymentInfo_retry);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment_retry = RequestUtil.postRequest(RequstUrlorderPayment, Header1,
				Requestparams_createOrderPayment_retry);
		JSONObject responseJson_Orderpayment_retry = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseorderpayment_retry);
		// Assert.assertEquals(
		// responseJson_Orderpayment_retry.getInt("status"),400, "error_status
		// is
		// not correct");
		Assert.assertEquals("Your order is not created because of a technical issue. Please try again!!!",
				responseJson_Orderpayment_retry.getString("message"), "error_message is not correct");
		Assert.assertEquals("Precondition Required", responseJson_Orderpayment_retry.getString("error"),
				"errors is not correct");
	}

	@Test(enabled = true)
	public void orderPayment_for_wallet_User() throws Exception {
		log.info("------------------------Wallet User-------------------------------------");
		// session function call
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();

		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationConstants.ProductCategories.SUNGLASSES, xApiClient);

		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));

		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, xApiClient);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		int cartId = cart_response.getJSONObject("result").getInt("cartId");
		int storeId = cart_response.getJSONObject("result").getInt("storeId");
		JSONObject walletResponse = CartUtil.getCartBasedOnApplyWallet(sessiontoken, xApiClient, "true");

		String customerId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("id");
		String walletId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("walletId");
		double beforeWalletBalance = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient, "lenskart");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "lenskartwallet");
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject payment_response_result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = payment_response_result.getJSONObject("order");
			String orderId = order_responseJson.getString("id");
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
					"State is not same");
			JSONObject payment_responseJson = payment_response_result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");

			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
			JSONArray discounts = payment_response_result.getJSONObject("order").getJSONObject("amount")
					.getJSONArray("discounts");
			double balance = 0;
			for (int i = 0; i < discounts.length(); i++) {
				JSONObject discountsJson = discounts.getJSONObject(i);
				balance = discountsJson.getDouble("amount");
				String txnNum = discountsJson.getString("txnNum");
				if (db_Validation) {
					String sql = "select * from order_wallet_history where orderId=" + orderId + " and txnNum='"
							+ txnNum + "'";
					log.info("nsabdhsjhc" + sql);
					String output[] = mysqlConnectionObject.executeSelectQuery(sql).split("\r\n");
					String db[] = output[0].split(",");
					MoneyUtil.orderWalletHistoryValidation(db, discountsJson, mobileNumber, orderId, customerId,
							walletId, "debit", "Debited for Order ID ");
				}

				JSONArray getAllTransactionResponse = MoneyUtil.getAllTransactions(sessiontoken, xApiClient)
						.getJSONArray("result");

				MoneyUtil.getAllTransactionValidation(getAllTransactionResponse.getJSONObject(0), discountsJson,
						String.valueOf(balance), mobileNumber, "dr", "Debited for Order ID " + orderId,
						discountsJson.getString("type"));
			}
			double afterWalletBalance = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient,
					"lenskart");
			Assert.assertEquals(beforeWalletBalance - balance, afterWalletBalance, "Amount didn't get updated");
			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}

			Assert.assertEquals(responseJson_Orderpayment.getInt("status"), 200, "Response status is not correct");
			if (OrderDatabaseValidation.redisKey) {

			}
		}
	}

	@Test(enabled = true)
	public void orderPayment_for_wallet_User_with_prepaidMethod() throws Exception {
		log.info("------------------------Wallet User-------------------------------------");
		// session function call
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();

		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationConstants.ProductCategories.EYEGLASSES_FFF, xApiClient);

		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, true);
		String productId = product.get("productId");
		String packageId = product.get("packageId");
		String coating_id = product.get("coating_id");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setPackageId(packageId);
		reqObj.setQuantity(Integer.parseInt("1"));
		reqObj.setAddOns(coating_id);

		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, xApiClient);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		int cartId = cart_response.getJSONObject("result").getInt("cartId");
		int storeId = cart_response.getJSONObject("result").getInt("storeId");
		JSONObject walletResponse = CartUtil.getCartBasedOnApplyWallet(sessiontoken, xApiClient, "true");

		String customerId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("id");
		String walletId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("walletId");

		double beforeWalletBalance = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient, "lenskart");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		paymentInfo.put("gatewayId", "PU");
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject payment_response_result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = payment_response_result.getJSONObject("order");
			String orderId = order_responseJson.getString("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PENDING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "NEW",
					"State is not same");
			JSONObject payment_responseJson = payment_response_result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "REDIRECT", "action is not correct");

			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
			JSONArray discounts = payment_response_result.getJSONObject("order").getJSONObject("amount")
					.getJSONArray("discounts");
			double balance = 0;
			for (int i = 0; i < discounts.length(); i++) {
				JSONObject discountsJson = discounts.getJSONObject(i);
				balance = discountsJson.getDouble("amount");
				if (discountsJson.getString("type").equals("lenskart")
						|| discountsJson.getString("type").equals("lenskartplus")) {
					if (db_Validation) {
						String sql = "select * from order_wallet_history where orderId=" + orderId + " and txnNum='"
								+ discountsJson.getString("txnNum") + "'";
						log.info("nsabdhsjhc" + sql);
						String output[] = mysqlConnectionObject.executeSelectQuery(sql).split("\r\n");
						String db[] = output[0].split(",");
						MoneyUtil.orderWalletHistoryValidation(db, discountsJson, mobileNumber, orderId, customerId,
								walletId, "debit", "Debited for Order ID ");
					}

					JSONArray getAllTransactionResponse = MoneyUtil.getAllTransactions(sessiontoken, xApiClient)
							.getJSONArray("result");

					MoneyUtil.getAllTransactionValidation(getAllTransactionResponse.getJSONObject(0), discountsJson,
							String.valueOf(balance), mobileNumber, "dr", "Debited for Order ID " + orderId,
							discountsJson.getString("type"));

					double afterWalletBalance = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient,
							"lenskart");
					Assert.assertEquals(beforeWalletBalance - balance, afterWalletBalance, "Amount didn't get updated");
				}
			}
			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}

			Assert.assertEquals(responseJson_Orderpayment.getInt("status"), 200, "Response status is not correct");
		}
	}

	@Test(enabled = true)
	public void orderPayment_for_mobileEmailMappedUser() throws Exception {
		log.info(
				"------------------------Wallet User- Mobile and email mapped user-------------------------------------");
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();

		sessiontoken = MobileAndEmailLogin.mobileLogin_EmailVerified(sessiontoken, xApiClient, mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessiontoken, xApiClient);
		MoneyUtil.bulkCredit(xApiClient, sessiontoken, authToken, mobileNumber, "50", "lenskartplus", null);
		double lenskartAmount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient, "lenskartplus");
		Assert.assertEquals(50.0, lenskartAmount, "Amount didnt get updated");
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationConstants.ProductCategories.SUNGLASSES, xApiClient);

		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));

		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, xApiClient);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		int cartId = cart_response.getJSONObject("result").getInt("cartId");
		int storeId = cart_response.getJSONObject("result").getInt("storeId");
		JSONObject walletResponse = CartUtil.getCartBasedOnApplyWallet(sessiontoken, xApiClient, "true");

		String customerId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("id");
		String walletId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("walletId");
		double beforeWalletBalance_lenskart = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient,
				"lenskart");
		double beforeWalletBalance_lenskartplus = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient,
				"lenskartplus");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "lenskartwallet");
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject payment_response_result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = payment_response_result.getJSONObject("order");
			String orderId = order_responseJson.getString("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
					"State is not same");
			JSONObject payment_responseJson = payment_response_result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");

			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
			JSONArray discounts = payment_response_result.getJSONObject("order").getJSONObject("amount")
					.getJSONArray("discounts");

			JSONArray getAllTransactionResponse = MoneyUtil.getAllTransactions(sessiontoken, xApiClient)

					.getJSONArray("result");
			double balance_lenskart = 0;
			double balance_lenskartplus = 0;
			for (int i = 0; i < discounts.length(); i++) {
				JSONObject discountsJson = discounts.getJSONObject(i);
				if (discountsJson.getString("type").equals("lenskart")) {
					balance_lenskart = discountsJson.getDouble("amount");
					String txnNum = discountsJson.getString("txnNum");
					if (db_Validation) {
						String sql = "select * from order_wallet_history where orderId=" + orderId + " and txnNum='"
								+ txnNum + "'";
						log.info("nsabdhsjhc" + sql);
						String output[] = mysqlConnectionObject.executeSelectQuery(sql).split("\r\n");
						String db[] = output[0].split(",");
						MoneyUtil.orderWalletHistoryValidation(db, discountsJson, mobileNumber, orderId, customerId,
								walletId, "debit", "Debited for Order ID ");

						MoneyUtil.getAllTransactionValidation(getAllTransactionResponse.getJSONObject(1), discountsJson,
								String.valueOf(balance_lenskart), mobileNumber, "dr", "Debited for Order ID " + orderId,
								discountsJson.getString("type"));
					}
				}
				if (discountsJson.getString("type").equals("lenskartplus")) {
					balance_lenskartplus = discountsJson.getDouble("amount");
					String txnNum = discountsJson.getString("txnNum");
					if (db_Validation) {
						String sql = "select * from order_wallet_history where orderId=" + orderId + " and txnNum='"
								+ txnNum + "'";
						log.info("nsabdhsjhc" + sql);
						String output[] = mysqlConnectionObject.executeSelectQuery(sql).split("\r\n");
						String db[] = output[0].split(",");
						MoneyUtil.orderWalletHistoryValidation(db, discountsJson, mobileNumber, orderId, customerId,
								walletId, "debit", "Debited for Order ID ");
						MoneyUtil.getAllTransactionValidation(getAllTransactionResponse.getJSONObject(0), discountsJson,
								String.valueOf(balance_lenskartplus), mobileNumber, "dr",
								"Debited for Order ID " + orderId, discountsJson.getString("type"));
					}
				}
			}
			double afterWalletBalance_lenskart = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient,
					"lenskart");
			Assert.assertEquals(beforeWalletBalance_lenskart - balance_lenskart, afterWalletBalance_lenskart,
					"Amount didn't get updated");
			double afterWalletBalance_lenskartplus = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
					xApiClient, "lenskartplus");
			Assert.assertEquals(beforeWalletBalance_lenskartplus - balance_lenskartplus,
					afterWalletBalance_lenskartplus, "Amount didn't get updated");
			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}

			Assert.assertEquals(responseJson_Orderpayment.getInt("status"), 200, "Response status is not correct");
		}
	}

	@Test(enabled = true)
	public void orderPayment_for_mobileEmailMappedUser_loginWithEmail() throws Exception {
		log.info(
				"------------------------Wallet User- Mobile and email mapped user login with email-------------------------------------");
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();

		sessiontoken = MobileAndEmailLogin.emailLogin_MobileVerified(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationConstants.ProductCategories.SUNGLASSES, xApiClient);

		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));

		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, xApiClient);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		int cartId = cart_response.getJSONObject("result").getInt("cartId");
		int storeId = cart_response.getJSONObject("result").getInt("storeId");
		JSONObject walletResponse = CartUtil.getCartBasedOnApplyWallet(sessiontoken, xApiClient, "true");

		String customerId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("id");
		String walletId = walletResponse.getJSONObject("result").getJSONObject("customer").getString("walletId");
		double beforeWalletBalance = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient, "lenskart");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject payment_response_result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = payment_response_result.getJSONObject("order");
			String orderId = order_responseJson.getString("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
					"State is not same");
			JSONObject payment_responseJson = payment_response_result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");

			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
			JSONArray discounts = payment_response_result.getJSONObject("order").getJSONObject("amount")
					.getJSONArray("discounts");
			double balance = 0;
			for (int i = 0; i < discounts.length(); i++) {
				JSONObject discountsJson = discounts.getJSONObject(i);
				balance = discountsJson.getDouble("amount");
				String txnNum = discountsJson.getString("txnNum");
				if (db_Validation) {
					String sql = "select * from order_wallet_history where orderId=" + orderId + " and txnNum='"
							+ txnNum + "'";
					log.info("nsabdhsjhc" + sql);
					String output[] = mysqlConnectionObject.executeSelectQuery(sql).split("\r\n");
					String db[] = output[0].split(",");
					MoneyUtil.orderWalletHistoryValidation(db, discountsJson, mobileNumber, orderId, customerId,
							walletId, "debit", "Debited for Order ID ");
				}

				JSONArray getAllTransactionResponse = MoneyUtil.getAllTransactions(sessiontoken, xApiClient)
						.getJSONArray("result");

				MoneyUtil.getAllTransactionValidation(getAllTransactionResponse.getJSONObject(0), discountsJson,
						String.valueOf(balance), mobileNumber, "dr", "Debited for Order ID " + orderId,
						discountsJson.getString("type"));
			}
			double afterWalletBalance = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, xApiClient,
					"lenskart");
			Assert.assertEquals(beforeWalletBalance - balance, afterWalletBalance, "Amount didn't get updated");
			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}

			Assert.assertEquals(responseJson_Orderpayment.getInt("status"), 200, "Response status is not correct");
		}
	}

	@Test(enabled = true)
	public void orderPayment_GuestUser_PaymentMethodLenskartWallet_usecase() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id));

		JSONObject cart = CartUtil.createCart(sessiontoken, reqObj, xApiClient);

		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");

		CartUtil.getCartBasedOnApplyWallet(sessiontoken, xApiClient, "true");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "lenskartwallet");
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");

		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
					"State is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");
		}
	}

	@Test(enabled = true)
	public void orderPayment_LoggedInUser_PaymentMethodLenskartWallet_usecase() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);

		sessiontoken = MobileAndEmailLogin.oldEmailLogin(sessiontoken, xApiClient, mobileNumber);

		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id));

		JSONObject cart = CartUtil.createCart(sessiontoken, reqObj, xApiClient);

		int cartId = cart.getJSONObject("result").getInt("cartId");
		int storeId = cart.getJSONObject("result").getInt("storeId");
		int customerId = cart.getJSONObject("result").getJSONObject("customer").getInt("id");
		String customer_email = cart.getJSONObject("result").getJSONObject("customer").getString("email");

		CartUtil.getCartBasedOnApplyWallet(sessiontoken, xApiClient, "true");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject billingAddress = ApplicationUtil.shippingAddress();

		Requestparams_createOrderPayment.put("billingAddress", billingAddress);
		JSONObject paymentInfo = new JSONObject();
		// paymentInfo.put("gatewayId", gatewayId);
		paymentInfo.put("paymentMethod", "lenskartwallet");
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);

		log.info("CreateOrderPayment URL of Post: " + RequstUrlorderPayment);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, Header,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");

		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Order Payment is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("order"),
					"Order Payment order is not giving correct response");
			Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getString("payment"),
					"Order Payment payment is not giving correct response");
			JSONObject result = responseJson_Orderpayment.getJSONObject("result");
			JSONObject order_responseJson = result.getJSONObject("order");
			int orderId = order_responseJson.getInt("id");
			if (OrderDatabaseValidation.redisKey) {
				Assert.assertTrue(order_responseJson.getBoolean("isDualCompanyEnabled"),
						"isDualCompanyEnabled mismatch");
				log.info("isDualCompanyEnabled is True");
			}
			Assert.assertEquals(order_responseJson.getInt("cartId"), cartId, "cartId is not same");
			Assert.assertEquals(order_responseJson.getInt("storeId"), storeId, "Store ID is not same");
			Assert.assertNotNull(order_responseJson.getInt("customerId"), "customerId is null");
			Assert.assertNotNull(order_responseJson.getString("customerEmail"), " customerEmail is null");
			Assert.assertEquals(order_responseJson.getInt("customerId"), customerId, "Customer ID is not same");
			Assert.assertEquals(order_responseJson.getString("customerEmail"), customer_email,
					"Customer email is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("status"), "PROCESSING",
					"Status is not same");
			Assert.assertEquals(order_responseJson.getJSONObject("status").getString("state"), "PROCESSING",
					"State is not same");

			JSONObject payment_responseJson = result.getJSONObject("payment");
			String paymentid = payment_responseJson.getString("paymentId");
			JSONObject amount = payment_responseJson.getJSONObject("amount");
			Assert.assertEquals(amount.getString("currencyCode"), "INR", "currencyCode is not same");
			Assert.assertNotNull(amount.getString("price"), "price is not same");
			JSONObject actionInfo = payment_responseJson.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			Assert.assertEquals(order_responseJson.getString("id"), payment_responseJson.getString("orderId"),
					"orderID is not same");

			if (db_Validation) {
				Map<String, Object> get_order_details = new HashMap<String, Object>();
				get_order_details.put("_id", orderId);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
				if (getOrderDetails.size() == 1) {
					Document order_detail = (Document) getOrderDetails.get(0);
					log.info("sdfdgdrfdgf " + order_detail);
					OrderDatabaseValidation.ordervalidation(order_responseJson, order_detail);
				}

				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(payment_responseJson, payment_db);
				}
			}

		}
	}

	@Test(enabled = true)
	public void scDiscount_validation_for_dualcompany() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		int storeCreditCodeAmount = 500;
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_JJ"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		CartUtil.clearCart(sessiontoken);
		CartItem reqObj = new CartItem();
		// reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqObj, ApplicationConstants.XApiClient.ANDROID);

		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SUNGLASSES,
				false);

		// reqObj.setPowerType(ApplicationConstants.PowerTypes.BIFOCAL);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqObj, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject cartResponseWithSC = CartUtil.addStoreCredit(sessiontoken, ApplicationConstants.storeCreditCode,
				storeCreditCodeAmount);
		boolean goldFlag = false;
		for (int i = 0; i < cartResponseWithSC.getJSONObject("result").getJSONArray("items").length(); i++) {
			if (cartResponseWithSC.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId")
					.equals("128269")) {
				goldFlag = true;
				break;
			}
		}

		// discountVerfication(cartResponseWithSC, "sc", goldFlag);

		JSONObject orderPayment_response = PaymentUtil.orderPayment(sessiontoken,
				ApplicationConstants.XApiClient.ANDROID, null, "cod", null);

		JSONArray itemArrayResponse = orderPayment_response.getJSONObject("result").getJSONObject("order")
				.getJSONArray("items");
		log.info("itemArrayResponse: " + itemArrayResponse);
		int totalamount = 0;
		for (int i = 0; i < itemArrayResponse.length(); i++) {
			int cartAmountOfOneitem = itemArrayResponse.getJSONObject(i).getJSONObject("amount").getInt("total");
			totalamount = cartAmountOfOneitem + totalamount;

		}
		log.info("totalamount: " + totalamount);
		for (int j = 0; j < itemArrayResponse.length(); j++) {
			int subTotal = itemArrayResponse.getJSONObject(j).getJSONObject("amount").getInt("total");

			int scAppliedOnEachItem = ApplicationUtil.discountCalculation(subTotal, totalamount, storeCreditCodeAmount);
			log.info("scAppliedOnEachItem: " + scAppliedOnEachItem + " , " + itemArrayResponse.getJSONObject(j)
					.getJSONObject("amount").getJSONArray("discounts").getJSONObject(0).getInt("amount"));
			Assert.assertEquals(itemArrayResponse.getJSONObject(j).getJSONObject("amount").getJSONArray("discounts")
					.getJSONObject(0).getInt("amount"), scAppliedOnEachItem, "sc amount mismatch");
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersInCartAndOrder() throws Exception {
		log.info("-----------------------Addition parameters of dual company-------------------------------------");
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		// reqJsonObject.put("powerType",
		// ApplicationConstants.PowerTypes.SINGLE_VISION);
		// reqJsonObject.put("packageId", product.get("packageId"));
		reqJsonObject.put("shipToStoreRequired", true);
		reqJsonObject.put("isLocalFittingRequired", true);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONObject saveAddressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray items = saveAddressResponse.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			JSONObject itemsObject = items.getJSONObject(i);
			Assert.assertTrue(itemsObject.getBoolean("shipToStoreRequired"), "shipToStoreRequired mismatch");
			Assert.assertTrue(itemsObject.getBoolean("isLocalFittingRequired"), "isLocalFittingRequired mismatch");
			log.info("jshdghsdguyyu");
		}

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "COD");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				true, false, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);

		}
	}

	@Test(enabled = true)
	public void gvDiscount_validation_for_dualcompanytask() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		int gvAmount = 10;
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);

		CartItem reqObj = new CartItem();
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		CartUtil.createCart(sessiontoken, reqObj, ApplicationConstants.XApiClient.ANDROID);

		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER,
				false);
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		CartUtil.createCart(sessiontoken, reqObj, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject cartResponseWithGV = CartUtil.addGiftVoucher(sessiontoken, ApplicationConstants.gvCode);

		boolean goldFlag = false;
		for (int i = 0; i < cartResponseWithGV.getJSONObject("result").getJSONArray("items").length(); i++) {
			if (cartResponseWithGV.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId")
					.equals("128269")) {
				goldFlag = true;
				break;
			}
		}

		// discountVerfication(cartResponseWithGV, "gv", goldFlag);

		JSONObject orderPayment_response = PaymentUtil.orderPayment(sessiontoken,
				ApplicationConstants.XApiClient.ANDROID, null, "cod", null);
		JSONArray itemArrayResponse = orderPayment_response.getJSONObject("result").getJSONObject("order")
				.getJSONArray("items");
		int totalamount = 0;
		for (int i = 0; i < itemArrayResponse.length(); i++) {
			int cartAmountOfOneitem = itemArrayResponse.getJSONObject(i).getJSONObject("amount").getInt("total");
			totalamount = cartAmountOfOneitem + totalamount;
		}
		for (int j = 0; j < itemArrayResponse.length(); j++) {
			int totalAmountOfeachItem = itemArrayResponse.getJSONObject(j).getJSONObject("amount").getInt("total");
			int gvAppliedOnEachItem = ApplicationUtil.discountCalculation(totalAmountOfeachItem, totalamount, gvAmount);
			log.info("gvAppliedOnEachItem: " + gvAppliedOnEachItem + " , " + itemArrayResponse.getJSONObject(j)
					.getJSONObject("amount").getJSONArray("discounts").getJSONObject(0).getInt("amount"));
			Assert.assertEquals(itemArrayResponse.getJSONObject(j).getJSONObject("amount").getJSONArray("discounts")
					.getJSONObject(0).getInt("amount"), gvAppliedOnEachItem, "gv amount mismatch");
		}

	}

	@Test(enabled = true)
	public void implicitDiscount_validation_for_dualcompanytask() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();

		String telephone = "12101" + GenericUtil.createRandomNumber(5);
		String mobileLoginSession = CustomerUtil
				.mobileAuthenticateWithoutReferCode(ApplicationConstants.XApiClient.ANDROID, sessiontoken,
						ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), telephone)
				.getJSONObject("result").getString("token");
		log.info("********" + mobileLoginSession);

		JSONObject signUpAmount = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone,
				ApplicationConstants.XApiClient.ANDROID);
		String authToken = MoneyUtil.generateAuthToken(mobileLoginSession, ApplicationConstants.XApiClient.ANDROID);
		JSONObject bulkCreditResponse = MoneyUtil.bulkCredit(ApplicationConstants.XApiClient.ANDROID,
				mobileLoginSession, authToken, telephone, "200", "lenskartplus", null);
		log.info("********" + bulkCreditResponse);
		JSONObject walletBalanceResponse = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("********" + walletBalanceResponse);
		double lenskartAmocunt = MoneyUtil.getWalletBalanceBasedOnWalletType(telephone,
				ApplicationConstants.XApiClient.ANDROID, "lenskart");
		double lenskartplusAmocunt = MoneyUtil.getWalletBalanceBasedOnWalletType(telephone,
				ApplicationConstants.XApiClient.ANDROID, "lenskartplus");

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		CartUtil.clearCart(mobileLoginSession);
		CartItem reqObj = new CartItem();
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		CartUtil.createCart(mobileLoginSession, reqObj, ApplicationConstants.XApiClient.ANDROID);
		CartUtil.createCart(mobileLoginSession, reqObj, ApplicationConstants.XApiClient.ANDROID);
		JSONObject cartResponse = CartUtil.createCart(mobileLoginSession, reqObj,
				ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(mobileLoginSession, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject cartResponseWithWallet = CartUtil.getCartBasedOnApplyWallet(mobileLoginSession,
				ApplicationConstants.XApiClient.ANDROID, "true");
		JSONArray itemArray = cartResponseWithWallet.getJSONObject("result").getJSONArray("items");

		JSONObject PaymentMethods = PaymentUtil.getAllPaymentMethods(mobileLoginSession,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("PaymentMethods**" + PaymentMethods);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", mobileLoginSession));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		log.info("paymentInfo: " + paymentInfo);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject orderPayment_response = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		log.info("orderPayment_response: " + orderPayment_response);

		int totalamount = 0;

		for (int i = 0; i < itemArray.length(); i++) {

			int cartAmountOfOneitem = itemArray.getJSONObject(i).getJSONObject("amount").getInt("total");
			log.info("cartAmountOfOneitem*" + cartAmountOfOneitem);
			totalamount = cartAmountOfOneitem + totalamount;

		}

		int TotalImplicitDiscount = 0;
		int TotalPrepaid = 0;
		for (int j = 0; j < itemArray.length(); j++) {

			int subTotal = itemArray.getJSONObject(j).getJSONObject("amount").getInt("total");
			int lenaksrtamount = ApplicationUtil.discountCalculation(subTotal, totalamount, (int) lenskartAmocunt);
			int lenskartplus = ApplicationUtil.discountCalculation(subTotal, totalamount, (int) lenskartplusAmocunt);
			log.info("********" + lenaksrtamount);
			log.info("********" + lenskartplus);
			JSONArray discountArray = itemArray.getJSONObject(j).getJSONObject("amount").getJSONArray("discounts");
			for (int k = 0; k < discountArray.length(); k++) {
				if (discountArray.getJSONObject(k).getString("type") == "lenskart") {
					Assert.assertEquals(
							itemArray.getJSONObject(j).getJSONObject("amount").getJSONArray("discounts")
									.getJSONObject(k).getInt("amount"),
							lenaksrtamount, "lenaksrtamount amount mismatch");

				} else if (discountArray.getJSONObject(k).getString("type") == "lenskartplus") {

					Assert.assertEquals(itemArray.getJSONObject(j).getJSONObject("amount").getJSONArray("discounts")
							.getJSONObject(k).getInt("amount"), lenskartplus, "lenaksrtamount amount mismatch");
				} else if (discountArray.getJSONObject(k).getString("type") == "prepaid") {

					TotalPrepaid = TotalPrepaid + itemArray.getJSONObject(j).getJSONObject("amount")
							.getJSONArray("discounts").getJSONObject(k).getInt("amount");
					log.info("TotalPrepaid" + TotalPrepaid);
				}

				else {
					TotalImplicitDiscount = TotalImplicitDiscount + TotalPrepaid + itemArray.getJSONObject(j)
							.getJSONObject("amount").getJSONArray("discounts").getJSONObject(k).getInt("amount");

				}
				// Assert.assertEquals(PaymentMethods.getJSONObject("result").getJSONArray("paymentMethods").getJSONObject(0).getString("prepaidDiscountAmount"),
				// orderPayment_response.getJSONObject("result").getJSONObject("order").getJSONObject("amount")
				// .getJSONArray("discounts").getJSONObject(0).getString("amount"),"prepaid
				// discount mismatch");
				// Assert.assertEquals(TotalImplicitDiscount,
				// orderPayment_response.getJSONObject("result").getJSONObject("order").getJSONObject("amount")
				// .getJSONArray("discounts").getJSONObject(1).getString("amount"),"implicit
				// discount mismatch");
			}
			String orderId = orderPayment_response.getJSONObject("result").getJSONObject("order").getString("id");
			if (db_Validation) {
				Map<String, Object> order_details = new HashMap<String, Object>();
				order_details.put("_id", Long.parseLong(orderId));
				List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
				Document orderObject = getOrderDetails.get(0);
				Document amountObject = (Document) orderObject.get("amount");
				@SuppressWarnings("unchecked")
				List<Document> discount = (List<Document>) amountObject.get("discounts");
				log.info("**discount**" + discount);

			}

		}

	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersInOrderWithoutShipToStoreRequired() throws Exception {
		log.info("-----------------------Addition parameters of dual company-------------------------------------");
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		// reqJsonObject.put("powerType",
		// ApplicationConstants.PowerTypes.SINGLE_VISION);
		// reqJsonObject.put("packageId", product.get("packageId"));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "COD");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				false, false, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true, dataProvider = "DualCompanyAdditionalParameter")
	public void orderPaymentAdditionalParametersOrderWithoutPackage(String storeType, String facilityCode,
			String isDualCompanyEnabled, String isBulkOrder, String shipToStoreRequired, String isLocalFittingRequired,
			String localFittingFacility, String statusCode) throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		JSONObject cartRespone = CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONArray item = cartRespone.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(item.getJSONObject(0).getString("productDeliveryType"), "B2B",
				"productDeliveryType mismatch");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", storeType);
		Requestparams_createOrderPayment.put("facilityCode", facilityCode);
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", Boolean.parseBoolean(isDualCompanyEnabled));
		Requestparams_createOrderPayment.put("isBulkOrder", Boolean.parseBoolean(isBulkOrder));
		Requestparams_createOrderPayment.put("shipToStoreRequired", Boolean.parseBoolean(shipToStoreRequired));
		Requestparams_createOrderPayment.put("isLocalFittingRequired", Boolean.parseBoolean(isLocalFittingRequired));
		Requestparams_createOrderPayment.put("localFittingFacility", localFittingFacility);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), Integer.parseInt(statusCode),
				"Status is not correct");
		if (httpResponseorderpayment.getStatusLine().getStatusCode() == 200) {
			Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
					"Payment jsonObject is missing");
			JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
					.getJSONObject("order");
			String orderId = responseJsonOrderPaymentResultOrder.getString("id");
			JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
			if (Boolean.parseBoolean(isLocalFittingRequired))
				Assert.assertEquals(responseJsonOrderPaymentResultOrder.getString("localFittingFacility"),
						localFittingFacility, "localFittingFacility mismatch");
			if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
				Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
						"productDeliveryType mismatch");
			else
				Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"),
						"productDeliveryType mismatch");
			orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment,
					Boolean.parseBoolean(shipToStoreRequired), Boolean.parseBoolean(isLocalFittingRequired),
					Boolean.parseBoolean(shipToStoreRequired), Boolean.parseBoolean(isLocalFittingRequired));
			if (db_Validation) {
				Map<String, Object> order_details = new HashMap<String, Object>();
				order_details.put("_id", Long.parseLong(orderId));
				List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
				Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
				Document order = (Document) getOrderDetails.get(0); 
				OrderDatabaseValidation
						.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
			}
		} else {
			Assert.assertEquals(responseJson_Orderpayment.getString("status"),"500","status mismatch");
			Assert.assertEquals(responseJson_Orderpayment.getString("message"),"org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.lenskart.juno.orderpayment.exception.InvalidParametersException: Local Fitting Facility cannot be null","message mismatch");
			Assert.assertEquals(responseJson_Orderpayment.getString("error"),"Internal Server Error","error mismatch");
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithPackage() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, true);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("powerType", ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqJsonObject.put("packageId", product.get("packageId"));
		reqJsonObject.put("addOns", product.get("coating_id"));
		JSONObject cartRespone = CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONArray item = cartRespone.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(item.getJSONObject(0).getString("productDeliveryType"), "DTC",
				"shipToStoreRequired mismatch");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "DTC",
					"shipToStoreRequired mismatch");
		else
			Assert.assertNull(orderItem.getJSONObject(0).getString("productDeliveryType"),
					"shipToStoreRequired mismatch");

		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, true, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForHTO() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject cartRespone = CartUtil.HTOCart(sessiontoken, xApiClient);
		JSONArray item = cartRespone.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(item.getJSONObject(0).getString("productDeliveryType"), "DTC",
				"productDeliveryType mismatch");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", false);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "DTC",
					"productDeliveryType mismatch");
		else
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");

		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, true, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForHTOWithStoreInventoryOTC() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject cartRespone = CartUtil.HTOCartWithStoreInventory(sessiontoken, xApiClient);
		JSONArray item = cartRespone.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(item.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"productDeliveryType mismatch");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", false);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
		else
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");

		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, true, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithPackageAndStoreInventoryOTC() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("powerType", ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqJsonObject.put("packageId", product.get("packageId"));
		reqJsonObject.put("storeInventory", 1);
		JSONObject cartRespone = CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONArray item = cartRespone.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(item.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"shipToStoreRequired mismatch");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		CartUtil.addGiftVoucher(sessiontoken, "LKAUTOMATION");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("transactionId", "skjsd12");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
					"shipToStoreRequired mismatch");
		else
			Assert.assertNull(orderItem.getJSONObject(0).getString("productDeliveryType"),
					"shipToStoreRequired mismatch");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			Assert.assertEquals(order.get("transactionId"), "skjsd12", "transactionId mismatch");
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithStoreInventory() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);

		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		JSONObject addressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray cartItem = addressResponse.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(cartItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"shipToStoreRequired mismatch");
		Assert.assertEquals(cartItem.getJSONObject(1).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		CartUtil.addStoreCredit(sessiontoken, ApplicationConstants.storeCreditCode, 200);
		CartUtil.addGiftVoucher(sessiontoken, ApplicationConstants.gvCode);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"), "PENDING",
				"status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
					"shipToStoreRequired mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "B2B",
					"shipToStoreRequired mismatch");
		} else {
			Assert.assertNull(orderItem.getJSONObject(0).getString("productDeliveryType"),
					"shipToStoreRequired mismatch");
			Assert.assertNull(orderItem.getJSONObject(1).getString("productDeliveryType"),
					"shipToStoreRequired mismatch");
		}

		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, true, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForMobileLogin() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "28004" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("shipToStoreRequired", true);
		reqJsonObject.put("isLocalFittingRequired", false);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		// JSONObject reqJsonObject1 = new JSONObject();
		// reqJsonObject1.put("productId", Long.parseLong("128269"));
		// reqJsonObject1.put("shipToStoreRequired", true);
		// reqJsonObject1.put("isLocalFittingRequired", false);
		// CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "COD");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		// if (OrderDatabaseValidation.redisKey ||
		// Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
		// {
		// Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"),
		// "B2B",
		// "shipToStoreRequired mismatch");
		//// Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"),
		// "DTC",
		//// "shipToStoreRequired mismatch");
		// } else {
		// Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"),
		// "shipToStoreRequired mismatch");
		// Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"),
		// "shipToStoreRequired mismatch");
		// }
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithStoreInventoryOTCForMobileLogin() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "28004" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("shipToStoreRequired", true);
		reqJsonObject.put("isLocalFittingRequired", false);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong("128269"));
		reqJsonObject1.put("shipToStoreRequired", true);
		reqJsonObject1.put("isLocalFittingRequired", false);
		reqJsonObject1.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "0");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
					"shipToStoreRequired mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "OTC",
					"shipToStoreRequired mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "shipToStoreRequired mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "shipToStoreRequired mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithStoreInventoryWithIsPaymentCapturedForPosValueFalse()
			throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);

		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		JSONObject addressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray cartItem = addressResponse.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(cartItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"shipToStoreRequired mismatch");
		Assert.assertEquals(cartItem.getJSONObject(1).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		CartUtil.addStoreCredit(sessiontoken, ApplicationConstants.storeCreditCode, 200);
		CartUtil.addGiftVoucher(sessiontoken, ApplicationConstants.gvCode);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		Requestparams_createOrderPayment.put("isPaymentCaptured", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getBoolean("isPaymentCaptured"), false,
				"isPaymentCaptured mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"shipToStoreRequired mismatch");
		Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, true, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			Assert.assertEquals(order.get("isPaymentCaptured"), false, "isPaymentCaptured mismatch");
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithStoreInventoryWithIsPaymentCapturedForPosValueTrue()
			throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);

		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		JSONObject addressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray cartItem = addressResponse.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(cartItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"shipToStoreRequired mismatch");
		Assert.assertEquals(cartItem.getJSONObject(1).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		CartUtil.addStoreCredit(sessiontoken, ApplicationConstants.storeCreditCode, 200);
		CartUtil.addGiftVoucher(sessiontoken, ApplicationConstants.gvCode);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		Requestparams_createOrderPayment.put("isPaymentCaptured", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"), "PENDING",
				"status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getBoolean("isPaymentCaptured"), true,
				"isPaymentCaptured mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"shipToStoreRequired mismatch");
		Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, true, false);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			Assert.assertEquals(order.get("isPaymentCaptured"), true, "isPaymentCaptured mismatch");
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true, dataProvider = "IsPaymentCapture")
	public void orderPaymentAdditionalParametersOrderWithStoreInventoryForMobileLoginWithIsPaymentCaptured(
			String paymentMethod) throws Exception {
		log.info("---------------Payment Method Name: " + paymentMethod + " ------------------------");
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "28001" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("storeInventory", 1);
		reqJsonObject.put("shipToStoreRequired", true);
		reqJsonObject.put("isLocalFittingRequired", false);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", paymentMethod);
		JSONObject netbanking = new JSONObject();
		if (paymentMethod.equals("nb"))
			netbanking.put("bankCode", "PU:AXIB");
		else
			netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getJSONObject("order"),
				"order jsonObject is missing");
		Assert.assertNotNull(responseJson_Orderpayment.getJSONObject("result").getJSONObject("payment"),
				"payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		if (paymentMethod.equals("cc") || paymentMethod.equals("dc") || paymentMethod.equals("nb")) {
			Assert.assertEquals(responseJsonOrderPaymentResultOrder.getBoolean("isPaymentCaptured"), false,
					"isPaymentCaptured mismatch");
		} else {
			Assert.assertEquals(responseJsonOrderPaymentResultOrder.getBoolean("isPaymentCaptured"), true,
					"isPaymentCaptured mismatch");
		}
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, true,
				false, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			if (paymentMethod.equals("cc") || paymentMethod.equals("dc") || paymentMethod.equals("nb")) {
				Assert.assertEquals(order.get("isPaymentCaptured"), false, "isPaymentCaptured mismatch");
			} else {
				Assert.assertEquals(order.get("isPaymentCaptured"), true, "isPaymentCaptured mismatch");
			}
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderInvalidStoreType() throws Exception {
		log.info("-----------------------Addition parameters of dual company-------------------------------------");
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFOWITHSC");
		Requestparams_createOrderPayment.put("facilityCode", "COD");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 400, "Status is not correct");
	}

	@Test(enabled = false)
	public void orderPaymentAdditionalParametersLoggedInWithRedisEmail() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication("<EMAIL>",
				"lenskart@123",ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONObject addressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray cartItem = addressResponse.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(cartItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"), "PENDING",
				"status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		Assert.assertTrue(responseJsonOrderPaymentResultOrder.getBoolean("isDualCompanyEnabled"),
				"isDualCompanyEnabled mismatch");
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			Assert.assertEquals(true, order.get("isDualCompanyEnabled"), "isDualCompanyEnabled mismatch");
			log.info("akjsjsh");
		}
	}

	@Test(enabled = false)
	public void orderPaymentAdditionalParametersGuestUserWithRedisEmail() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONObject addressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray cartItem = addressResponse.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(cartItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING_NEW", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		Assert.assertTrue(responseJsonOrderPaymentResultOrder.getBoolean("isDualCompanyEnabled"),
				"isDualCompanyEnabled mismatch");
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			Assert.assertEquals(true, order.get("isDualCompanyEnabled"), "isDualCompanyEnabled mismatch");
			log.info("akjsjsh");
		}
	}

	@Test(enabled = false)
	public void orderPaymentAdditionalParametersMobileLoginWithRedisEmail() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "1258747584";
		sessiontoken = CustomerUtil.mobileAuthenticate(xApiClient, sessiontoken,
				ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), mobileNumber, null).getJSONObject("result")
				.getString("token");
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		JSONObject addressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray cartItem = addressResponse.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(cartItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
				"shipToStoreRequired mismatch");
		Assert.assertTrue(responseJsonOrderPaymentResultOrder.getBoolean("isDualCompanyEnabled"),
				"isDualCompanyEnabled mismatch");
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			Assert.assertEquals(true, order.get("isDualCompanyEnabled"), "isDualCompanyEnabled mismatch");
			log.info("akjsjsh");
		}
	}

	public void calculateGoldDiscunt(double shippingAmount, double sum, double itemDiscountAmount) {
		Assert.assertEquals(0, (int) shippingAmount, "Shipping charges mismatch at item level");
		Assert.assertEquals((int) sum, (int) itemDiscountAmount, "item level discount mismatch ");
	}

	public void discountVerfication(JSONObject cartRespone, String type, boolean goldProduct) throws JSONException {
		JSONObject cartResult = cartRespone.getJSONObject("result");
		JSONObject totalOrder = cartResult.getJSONObject("totals");
		double orderAmount = totalOrder.getDouble("subTotal");
		double orderShipingAmount = totalOrder.getDouble("shipping");
		JSONArray discountOrder = totalOrder.getJSONArray("discounts");
		double orderDiscountAmount = 0.0;
		for (int i = 0; i < discountOrder.length(); i++) {
			JSONObject discountObject = discountOrder.getJSONObject(i);
			if (discountObject.getString("type").equals(type)) {
				orderDiscountAmount = discountObject.getDouble("amount");
				break;
			}
		}

		if (goldProduct && !type.equals("sc")) {
			log.info("Gold pid");
			JSONArray items = cartResult.getJSONArray("items");
			double sum = 0.0;
			double goldShippingAmount = 0.0;
			double goldItemDiscountAmount = 0.0;
			for (int i = 0; i < items.length(); i++) {
				JSONObject itemsObject = items.getJSONObject(i);
				if (itemsObject.getString("productId").equals("128269")) {
					JSONObject amount = itemsObject.getJSONObject("amount");
					goldShippingAmount = amount.getDouble("shipping");
					JSONArray discounts = amount.getJSONArray("discounts");
					for (int j = 0; j < discounts.length(); j++) {
						JSONObject discountsObject = discounts.getJSONObject(j);
						if (discountsObject.getString("type").equals(type)) {
							goldItemDiscountAmount = discountsObject.getDouble("amount");
						}
					}
				} else {
					JSONObject amount = itemsObject.getJSONObject("amount");
					double itemAmount = amount.getDouble("subTotal");
					double shippingAmount = amount.getDouble("shipping");
					JSONArray discounts = amount.getJSONArray("discounts");
					double itemDiscountAmount = 0.0;
					for (int j = 0; j < discounts.length(); j++) {
						JSONObject discountsObject = discounts.getJSONObject(j);
						if (discountsObject.getString("type").equals(type)) {
							itemDiscountAmount = discountsObject.getDouble("amount");
							double checkAmount = orderDiscountAmount * (itemAmount / (orderAmount - 500));
							if (checkAmount >= (amount.getDouble("subTotal") + shippingAmount
									+ amount.getDouble("totalTax"))) {
								sum = sum + (checkAmount - (amount.getDouble("subTotal") + shippingAmount
										+ amount.getDouble("totalTax")));
								checkAmount = amount.getDouble("subTotal") + shippingAmount
										+ amount.getDouble("totalTax");
							}
							Assert.assertEquals((int) checkAmount, (int) itemDiscountAmount,
									"item level discount mismatch " + itemsObject.getInt("id"));
							log.info("Check " + itemDiscountAmount);
							break;
						}
					}
					double checkShippingAmount = (amount.getDouble("subTotal") * orderShipingAmount)
							/ (totalOrder.getDouble("subTotal") - 500);
					Assert.assertEquals((int) checkShippingAmount, (int) shippingAmount,
							"Shipping charges mismatch at item level");
					System.out.println("check shipping ");
				}
			}
			calculateGoldDiscunt(goldShippingAmount, sum, goldItemDiscountAmount);
		} else {
			log.info("Without Gold Pid");
			JSONArray items = cartResult.getJSONArray("items");
			for (int i = 0; i < items.length(); i++) {
				JSONObject itemsObject = items.getJSONObject(i);
				JSONObject amount = itemsObject.getJSONObject("amount");
				orderAmount = totalOrder.getDouble("subTotal") + totalOrder.getDouble("shipping")
						+ totalOrder.getDouble("totalTax");
				double itemAmount = amount.getDouble("subTotal") + amount.getDouble("shipping")
						+ amount.getDouble("totalTax");
				double itemDiscountAmount = 0.0;
				JSONArray discounts = amount.getJSONArray("discounts");
				for (int j = 0; j < discounts.length(); j++) {
					JSONObject discountsObject = discounts.getJSONObject(j);
					if (discountsObject.getString("type").equals(type)) {
						itemDiscountAmount = discountsObject.getDouble("amount");
						double checkAmount = (itemAmount * orderDiscountAmount) / orderAmount;
						Assert.assertEquals((int) checkAmount, (int) itemDiscountAmount,
								"item level discount mismatch " + itemsObject.getInt("id"));
						log.info("Check " + itemDiscountAmount);
						break;
					}
				}
				double shippingAmount = amount.getDouble("shipping");
				double checkShippingAmount = 0.0;
				if (goldProduct && ((int) itemsObject.getInt("productId")) != 128269) {
					checkShippingAmount = (amount.getDouble("subTotal") * orderShipingAmount)
							/ (totalOrder.getDouble("subTotal") - 500);
				} else if (goldProduct && ((int) itemsObject.getInt("productId")) == 128269) {
					checkShippingAmount = 0.0;
				} else {
					checkShippingAmount = (amount.getDouble("subTotal") * orderShipingAmount)
							/ (totalOrder.getDouble("subTotal"));
				}
				Assert.assertEquals((int) checkShippingAmount, (int) shippingAmount,
						"Shipping charges mismatch at item level");

			}

		}

	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersWithGoldMoreThanOrderAmount() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "11231" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		MoneyUtil.bulkCredit(xApiClient, sessiontoken, MoneyUtil.generateAuthToken(sessiontoken, xApiClient),
				mobileNumber, "20000", "lenskartplus", null);
		CartUtil.createCart(sessiontoken, "121970", xApiClient);
		CartUtil.createCart(sessiontoken, "72869", xApiClient);
		CartUtil.createCart(sessiontoken, "12669", xApiClient);
		CartUtil.createCart(sessiontoken, "128269", xApiClient);
		CartUtil.createCart(sessiontoken, "8270", xApiClient);
		CartUtil.createCart(sessiontoken, "115802", xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		JSONObject cartResponse = CartUtil.getCartBasedOnApplyWallet(sessiontoken, mobileNumber, "true");
		boolean goldFlag = false;
		for (int i = 0; i < cartResponse.getJSONObject("result").getJSONArray("items").length(); i++) {
			if (cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId")
					.equals("128269")) {
				goldFlag = true;
				break;
			}
		}
		discountVerfication(cartResponse, "lenskartplus", goldFlag);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING_NEW", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersMoreThanOrderAmount() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "11231" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		MoneyUtil.bulkCredit(xApiClient, sessiontoken, MoneyUtil.generateAuthToken(sessiontoken, xApiClient),
				mobileNumber, "20000", "lenskartplus", null);
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);
		categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"),
				xApiClient);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		JSONObject cartResponse = CartUtil.getCartBasedOnApplyWallet(sessiontoken, mobileNumber, "true");
		boolean goldFlag = false;
		for (int i = 0; i < cartResponse.getJSONObject("result").getJSONArray("items").length(); i++) {
			if (cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId")
					.equals("128269")) {
				goldFlag = true;
				break;
			}
		}
		discountVerfication(cartResponse, "lenskartplus", goldFlag);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithGoldLessThanOrderAmount() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "1123199998";
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		CartUtil.clearCart(sessiontoken);
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);

		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);
		categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"),
				xApiClient);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartUtil.createCart(sessiontoken, product_Id, "", "", xApiClient);
		CartUtil.addToCart(sessiontoken, "128269", xApiClient);
		JSONObject addressResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONArray cartItem = addressResponse.getJSONObject("result").getJSONArray("items");
		Assert.assertEquals(cartItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
				"shipToStoreRequired mismatch");
		CartUtil.addGiftVoucher(sessiontoken, ApplicationConstants.gvCode);
		JSONObject scCartResponse = CartUtil.addStoreCredit(sessiontoken, "9377-LJ904-F679", 200);
		boolean goldFlag = false;
		for (int i = 0; i < scCartResponse.getJSONObject("result").getJSONArray("items").length(); i++) {
			if (scCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId")
					.equals("128269")) {
				goldFlag = true;
				break;
			}
		}
		discountVerfication(scCartResponse, "sc", goldFlag);
		discountVerfication(scCartResponse, "gv", goldFlag);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"), "PENDING",
				"status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderWithStoreInventoryAndGoldProductIDInBetweenOrderAmount()
			throws Exception {
		String email = "lens" + GenericUtil.createRandomNumber(3) + "@gmail.com";
		sessiontoken = CustomerUtil.registerNewCustomer("test", "test", email, "123456",
				"32134" + GenericUtil.createRandomNumber(5));
		CartUtil.clearCart(sessiontoken);
		String goldPid = "128269";
		List<String> categoryName = Arrays.asList("category_eyeglasses", "category_sunglasses",
				"category_reading_eyeglass");
		JSONObject customerDetails = CustomerUtil.getMeCustomerDetails(sessiontoken, xApiClient);
		int customerId = customerDetails.getInt("id");
		String scCode = GenericUtil.createRandomNumber(4) + "-LJ904-F" + GenericUtil.createRandomNumber(3);
		if (db_Validation) {
			String sql = "INSERT INTO storecredit (`storecredit_id`, `store_code`, `balance`, `currency`, `status`, `expired_at`, `customer_id`, `customer_name`, `customer_email`, `recipient_name`, `recipient_email`, `recipient_address`, `message`, `store_id`) VALUES("
					+ GenericUtil.createRandomNumber(9) + ", '" + scCode
					+ "', 99999.0000, 'INR', 2, '2020-02-04 07:38:02', " + customerId + ", 'test', '" + email
					+ "', 'Testetllenskart ', '" + email + "', NULL, NULL, 1)";

			mysqlConnectionObject.executeInsertQuery(sql);
		}

		CartUtil.addToCart(sessiontoken, goldPid, xApiClient);
		for (int i = 0; i < categoryName.size(); i++) {
			JSONObject jsonResponse_category = JunoV1Util
					.getCategoryDetails(ApplicationUtil.getcategoryId(categoryName.get(i)), xApiClient);
			product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
			CartUtil.createCart(sessiontoken, product.get("productId"), xApiClient);
		}
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		JSONObject scCartResponse = CartUtil.addStoreCredit(sessiontoken, scCode, 7700);
		boolean goldFlag = false;
		for (int i = 0; i < scCartResponse.getJSONObject("result").getJSONArray("items").length(); i++) {
			if (scCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId")
					.equals("128269")) {
				goldFlag = true;
				break;
			}
		}
		discountVerfication(scCartResponse, "sc", goldFlag);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");

	}

	private double returnDiscountAmount(JSONArray discountItemArray, String type) throws JSONException {
		double discountAmount = 0.0;
		for (int i = 0; i < discountItemArray.length(); i++) {
			if (discountItemArray.getJSONObject(i).getString("type").equals(type)) {
				discountAmount = discountItemArray.getJSONObject(i).getDouble("amount");
			}
		}
		return discountAmount;
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOnlyGoldLessThanOrderTotal() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		CartUtil.addToCart(sessiontoken, "128269", xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress("hagsd" + GenericUtil.createRandomNumber(5) + "@gmail.com",
						"12143" + GenericUtil.createRandomNumber(5)),
				null);
		JSONObject gvCartResponse = CartUtil.addGiftVoucher(sessiontoken, "LKAUTOMATION");
		JSONObject gvCartResponseResult = gvCartResponse.getJSONObject("result");
		Assert.assertEquals(gvCartResponseResult.getJSONArray("items").length(), 1, "More number of item is present");
		JSONArray discountItemArray = gvCartResponseResult.getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getJSONArray("discounts");
		double discountAmount = returnDiscountAmount(discountItemArray, "gv");
		Assert.assertEquals(10, (int) discountAmount, "Applied discount amount mismatch");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"), "PENDING",
				"status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONArray("items").length(), 1,
				"More number of item is present");
		JSONArray orderDiscountItemArray = gvCartResponseResult.getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getJSONArray("discounts");
		double oderDiscountAmount = returnDiscountAmount(orderDiscountItemArray, "gv");
		Assert.assertEquals(10, (int) oderDiscountAmount, "Applied discount amount mismatch");
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOnlyGoldLessWithoutDiscount() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		CartUtil.addToCart(sessiontoken, "128269", xApiClient);
		JSONObject gvCartResponse = CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress(), null);
		JSONObject gvCartResponseResult = gvCartResponse.getJSONObject("result");
		Assert.assertEquals(gvCartResponseResult.getJSONArray("items").length(), 1, "More number of item is present");
		JSONArray discountItemArray = gvCartResponseResult.getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getJSONArray("discounts");
		Assert.assertEquals(discountItemArray.length(), 0, "Discount array is not empty");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", true);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", false);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"), "PENDING",
				"status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONArray("items").length(), 1,
				"More number of item is present");
		JSONArray orderDiscountItemArray = gvCartResponseResult.getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getJSONArray("discounts");
		Assert.assertEquals(orderDiscountItemArray.length(), 0, "Applied discount amount mismatch");
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOnlyGoldMoreThanOrderTotal() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "12312" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		MoneyUtil.bulkCredit(xApiClient, sessiontoken, MoneyUtil.generateAuthToken(sessiontoken, xApiClient),
				mobileNumber, "1000", "lenskartplus", null);
		CartUtil.addToCart(sessiontoken, "128269", xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient,
				ApplicationUtil.saveShippingAddress("hagsd" + GenericUtil.createRandomNumber(5) + "@gmail.com",
						"12143" + GenericUtil.createRandomNumber(5)),
				null);
		JSONObject CartResponse = CartUtil.getCartBasedOnApplyWallet(sessiontoken, mobileNumber, "true");
		JSONObject CartResponseResult = CartResponse.getJSONObject("result");
		Assert.assertEquals(CartResponseResult.getJSONArray("items").length(), 1, "More number of item is present");
		JSONArray discountItemArray = CartResponseResult.getJSONArray("items").getJSONObject(0).getJSONObject("amount")
				.getJSONArray("discounts");
		double discountAmount = returnDiscountAmount(discountItemArray, "lenskartplus");
		Assert.assertEquals(500, (int) discountAmount, "Applied discount amount mismatch");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cc");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONArray("items").length(), 1,
				"More number of item is present");
		JSONArray orderDiscountItemArray = responseJsonOrderPaymentResultOrder.getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getJSONArray("discounts");
		double oderDiscountAmount = returnDiscountAmount(orderDiscountItemArray, "lenskartplus");
		Assert.assertEquals(500, (int) oderDiscountAmount, "Applied discount amount mismatch");
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesProductDeliveryType() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "28004" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesItselfOTC() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = "28004" + GenericUtil.createRandomNumber(5);
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, xApiClient, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertTrue(responseJsonOrderPaymentResultOrder.getBoolean("isDualCompanyEnabled"),
				"isDualCompanyEnabled mismatch");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesWithB2B() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesWithDTC() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "sunglasses", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject1.put("packageId", product.get("packageId"));
		reqJsonObject1.put("powerType", "sunglasses");
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "DTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "DTC",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesWithOTC() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "sunglasses", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject1.put("packageId", product.get("packageId"));
		reqJsonObject1.put("powerType", "sunglasses");
		reqJsonObject1.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesWithDTCAndB2B() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "sunglasses", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject1.put("packageId", product.get("packageId"));
		reqJsonObject1.put("powerType", "sunglasses");
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject2 = new JSONObject();
		reqJsonObject2.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject2, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "DTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(2).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(2).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesWithOTCAndDTC() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "sunglasses", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject1.put("packageId", product.get("packageId"));
		reqJsonObject1.put("powerType", "sunglasses");
		reqJsonObject1.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "single_vision", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject2 = new JSONObject();
		reqJsonObject2.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject2.put("packageId", product.get("packageId"));
		reqJsonObject2.put("powerType", "single_vision");
		CartUtil.createCart(sessiontoken, reqJsonObject2, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "DTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(2).getString("productDeliveryType"), "DTC",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(2).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesWithOTCAndB2B() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "sunglasses", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject1.put("packageId", product.get("packageId"));
		reqJsonObject1.put("powerType", "sunglasses");
		reqJsonObject1.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject2 = new JSONObject();
		reqJsonObject2.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject2, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(2).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(2).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesWithOTCAndB2BAndDTC() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "sunglasses", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject1.put("packageId", product.get("packageId"));
		reqJsonObject1.put("powerType", "sunglasses");
		reqJsonObject1.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject2 = new JSONObject();
		reqJsonObject2.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject2, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "single_vision", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject3 = new JSONObject();
		reqJsonObject3.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject3.put("packageId", product.get("packageId"));
		reqJsonObject3.put("powerType", "single_vision");
		CartUtil.createCart(sessiontoken, reqJsonObject3, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		int count = 0;
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(2).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(3).getString("productDeliveryType"), "DTC",
					"productDeliveryType mismatch");
			count = 1;
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(2).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(3).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(count, 1, "didnt not enter in if loop");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesOTCWithAnotherOTCAndB2B() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_accessories"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "sunglasses", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject1 = new JSONObject();
		reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject1.put("packageId", product.get("packageId"));
		reqJsonObject1.put("powerType", "sunglasses");
		reqJsonObject1.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject1, xApiClient);
		jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"),
				xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject2 = new JSONObject();
		reqJsonObject2.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject2, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		Requestparams_createOrderPayment.put("isDualCompanyEnabled", true);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		int count = 0;
		if (OrderDatabaseValidation.redisKey || Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled")) {
			Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(1).getString("productDeliveryType"), "OTC",
					"productDeliveryType mismatch");
			Assert.assertEquals(orderItem.getJSONObject(2).getString("productDeliveryType"), "B2B",
					"productDeliveryType mismatch");
			count = 1;
		} else {
			Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(1).has("productDeliveryType"), "productDeliveryType mismatch");
			Assert.assertFalse(orderItem.getJSONObject(2).has("productDeliveryType"), "productDeliveryType mismatch");
		}
		Assert.assertEquals(count, 1, "didnt enter in if loop");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesOTCWithAnotherOTCAndB2BPosWeb() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "pos_web");
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
		reqJsonObject.put("storeInventory", 1);
		CartUtil.createCart(sessiontoken, reqJsonObject, "pos_web");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "pos_web"));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		// Requestparams_createOrderPayment.put("isDualCompanyEnabled", false);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertTrue(responseJsonOrderPaymentResultOrder.getBoolean("isDualCompanyEnabled"),
				"isDualCompanyEnabled is not correct");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		int count = 0;
		// if (OrderDatabaseValidation.redisKey ||
		// Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
		// {
		// Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"),
		// "OTC",
		// "productDeliveryType mismatch");
		// count = 1;
		// } else {
		// Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"),
		// "productDeliveryType mismatch");
		//
		// }
		// Assert.assertEquals(count, 1, "didnt enter in if loop");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderForAccessoriesOTCWithAnotherOTCAndB2BPosIos() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,xApiClient);
		CartUtil.clearCart(sessiontoken);
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "pos_ios");
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject2 = new JSONObject();
		reqJsonObject2.put("productId", Long.parseLong(product.get("productId")));
		CartUtil.createCart(sessiontoken, reqJsonObject2, "pos_ios");
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "pos_ios"));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		Requestparams_createOrderPayment.put("storeType", "FOFO_WITH_SC");
		Requestparams_createOrderPayment.put("facilityCode", "DK02");
		// Requestparams_createOrderPayment.put("isDualCompanyEnabled", false);
		Requestparams_createOrderPayment.put("isBulkOrder", true);
		Requestparams_createOrderPayment.put("shipToStoreRequired", false);
		Requestparams_createOrderPayment.put("isLocalFittingRequired", true);
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		Assert.assertFalse(responseJsonOrderPaymentResultOrder.getBoolean("isDualCompanyEnabled"),
				"isDualCompanyEnabled is not correct");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");
		// int count = 0;
		// if (OrderDatabaseValidation.redisKey ||
		// Requestparams_createOrderPayment.getBoolean("isDualCompanyEnabled"))
		// {
		// Assert.assertEquals(orderItem.getJSONObject(0).getString("productDeliveryType"),
		// "B2B",
		// "productDeliveryType mismatch");
		// count = 1;
		// } else {
		// Assert.assertFalse(orderItem.getJSONObject(0).has("productDeliveryType"),
		// "productDeliveryType mismatch");
		// }
		// Assert.assertEquals(count, 1, "didnt enter in if loop");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"),
				"PROCESSING", "state mismatch");
		String orderId = responseJsonOrderPaymentResultOrder.getString("id");
		orderPaymentResponseValidation(responseJsonOrderPaymentResultOrder, Requestparams_createOrderPayment, false,
				true, false, true);
		if (db_Validation) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("_id", Long.parseLong(orderId));
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details);
			Assert.assertEquals(getOrderDetails.size(), 1, "Multiple record with same order Id");
			Document order = (Document) getOrderDetails.get(0);
			OrderDatabaseValidation
					.orderDbValidationForDualCompanyAdditionParameters(responseJsonOrderPaymentResultOrder, order);
		}
	}

	@Test(enabled = true)
	public void orderPaymentAdditionalParametersOrderBifocalTokai() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), xApiClient);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		Assert.assertNotNull(product.get("productId"), "Product id: " + product.get("productId"));
		JSONObject reqJsonObject = new JSONObject();
		reqJsonObject.put("productId", Long.parseLong(product.get("productId")));

		CartUtil.createCart(sessiontoken, reqJsonObject, xApiClient);
		CartUtil.saveAddress(sessiontoken, xApiClient, ApplicationUtil.saveShippingAddress(), null);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createOrderPayment = new JSONObject();
		JSONObject paymentInfo = new JSONObject();
		paymentInfo.put("paymentMethod", "cod");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "");
		paymentInfo.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInfo.put("card", card);
		Requestparams_createOrderPayment.put("paymentInfo", paymentInfo);
		HttpResponse httpResponseorderpayment = RequestUtil.postRequest(RequstUrlorderPayment, headers,
				Requestparams_createOrderPayment);
		JSONObject responseJson_Orderpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponseorderpayment);
		Assert.assertEquals(httpResponseorderpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		Assert.assertEquals(responseJson_Orderpayment.getJSONObject("result").length(), 2,
				"Payment jsonObject is missing");
		JSONObject responseJsonOrderPaymentResultOrder = responseJson_Orderpayment.getJSONObject("result")
				.getJSONObject("order");
		JSONArray orderItem = responseJsonOrderPaymentResultOrder.getJSONArray("items");

		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("status"),
				"PROCESSING_NEW", "status mismatch");
		Assert.assertEquals(responseJsonOrderPaymentResultOrder.getJSONObject("status").getString("state"), "NEW",
				"state mismatch");
	}
}
