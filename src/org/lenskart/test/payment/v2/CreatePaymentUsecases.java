package org.lenskart.test.payment.v2;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.PaymentV2PathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.OrderUtil;
import org.lenskart.core.util.PaymentUtil;
import org.lenskart.core.util.SessionUtil;
import org.lenskart.test.customer.mobilelogin.MobileAndEmailLogin;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.v2.cart.CartItem;
import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class CreatePaymentUsecases {
	private static boolean db_Validation = Environments.dbConnectionFlag;
	private static String sessiontoken;
	private static final Logger log = GenericUtil.InitLogger(CreatePaymentUsecases.class);
	private static String RequstUrlcreatePayment = Environments.SERVICES_ENVIRONMENT
			+ PaymentV2PathConstants.GATEWAY_CREATE_PAYMENT_API;
	private MongoConnectionUtility mongoOrderConnectionObject;
	private MongoConnectionUtility mongoPaymentConnectionObject;
	private static String categoryId;
	private final String x_Api_Client = ApplicationConstants.XApiClient.DESKTOP;

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (db_Validation) {
			mongoOrderConnectionObject = OrderUtil.getOrderMongoConnectionObject();
			mongoPaymentConnectionObject = PaymentUtil.getPaymentMongoConnectionObject();
			mongoOrderConnectionObject.initMongoDBConnection();
			mongoPaymentConnectionObject.initMongoDBConnection();
		}
	}

	@AfterClass
	public void closeDBConection() throws IOException {
		if (db_Validation) {
			mongoOrderConnectionObject.closeMongoConnection();
			mongoPaymentConnectionObject.closeMongoConnection();
		}
	}

	@DataProvider(name = "CreatePayment")
	public static Iterator<String[]> supplyData() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "/csv_files/Payment_v2 API/CreatePayment.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@Test(enabled = true, dataProvider = "CreatePayment")
	public void createPaymentForGuestUserUsecase(String usecase, String bankCode, String method, String gatewayId,
			String status_code, String error_status, String error_message, String errors) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");
		sessiontoken = SessionUtil.createNewSession();
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject cart = CartUtil.createCart(sessiontoken, product_Id, x_Api_Client);
		log.info("Cart Response :" + cart);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("gatewayId", gatewayId);
		paymentInstrument.put("method", method);
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", bankCode);
		paymentInstrument.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInstrument.put("card", card);
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), Integer.parseInt(status_code),
				"Status is not correct");
		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			if (method.equals("sc") || method.equals("gv") || method.equals("cod") || method.equals("storeairtel")
					|| method.equals("offlineairtel")) {
				Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			} else {
				Assert.assertEquals(actionInfo.getString("action"), "REDIRECT", "action is not correct");
				Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
			}
			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		} else {
			Assert.assertEquals(Integer.parseInt(error_status), responseJson_createpayment.getInt("status"),
					"error_status is not correct");
			Assert.assertEquals(error_message, responseJson_createpayment.getString("message"),
					"error_message is not correct");
			Assert.assertEquals(errors, responseJson_createpayment.getString("error"), "errors is not correct");
		}

	}

	@Test(enabled = true)
	public static void createPaymentForGuestUserUsecaseForSanity() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.CATEGORY_EYEGLASSES_JJ;
			log.info("Hard coded Category id");
		}
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(categoryId, XApiClient.DESKTOP);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject cart = CartUtil.createCart(sessiontoken, product_Id, XApiClient.DESKTOP);
		log.info("Cart Response :" + cart);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", XApiClient.DESKTOP));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("gatewayId", "PU");
		paymentInstrument.put("method", "nb");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "PU:ICIB");
		paymentInstrument.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInstrument.put("card", card);
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		int httpStatusCode = httpResponse_createpayment.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Response code received : " + httpStatusCode);
		RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
	}

	@Test(enabled = true)
	public void createPaymentForLoginUserUsecaseForSanity() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartUtil.createCart(sessiontoken, product_Id, x_Api_Client);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("gatewayId", "PU");
		paymentInstrument.put("method", "nb");
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", "PU:ICIB");
		paymentInstrument.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInstrument.put("card", card);
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		int httpStatusCode = httpResponse_createpayment.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, 200, "Response code received : " + httpStatusCode);
		RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
	}

	@Test(enabled = true, dataProvider = "CreatePayment")
	public void createPaymentForLoginUserUsecase(String usecase, String bankCode, String method, String gatewayId,
			String status_code, String error_status, String error_message, String errors) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		CartUtil.clearCart(sessiontoken);
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartUtil.createCart(sessiontoken, product_Id, x_Api_Client);

		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("gatewayId", gatewayId);
		paymentInstrument.put("method", method);
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", bankCode);
		paymentInstrument.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInstrument.put("card", card);
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), Integer.parseInt(status_code),
				"Status is not correct");
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);

		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			if (method.equals("sc") || method.equals("gv") || method.equals("cod") || method.equals("storeairtel")
					|| method.equals("offlineairtel")) {
				Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			} else {
				Assert.assertEquals(actionInfo.getString("action"), "REDIRECT", "action is not correct");
				Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
			}

			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		} else {
			Assert.assertEquals(Integer.parseInt(error_status), responseJson_createpayment.getInt("status"),
					"error_status is not correct");
			Assert.assertEquals(error_message, responseJson_createpayment.getString("message"),
					"error_message is not correct");
			Assert.assertEquals(errors, responseJson_createpayment.getString("error"), "errors is not correct");
		}

	}
	@Test(enabled = true, dataProvider = "CreatePayment")
	public void createPaymentCartLoginUserUsecase(String usecase, String bankCode, String method, String gatewayId,
			String status_code, String error_status, String error_message, String errors) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");
		sessiontoken = SessionUtil.createNewSession();
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject cart = CartUtil.createCart(sessiontoken, product_Id, x_Api_Client);
		log.info("Cart Response :" + cart);
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(sessiontoken,
				ApplicationConstants.userEmail, ApplicationConstants.userPassword,
				ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
	//	Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("gatewayId", gatewayId);
		paymentInstrument.put("method", method);
		JSONObject netbanking = new JSONObject();
		netbanking.put("bankCode", bankCode);
		paymentInstrument.put("netbanking", netbanking);
		JSONObject card = new JSONObject();
		paymentInstrument.put("card", card);
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
log.info(httpResponse_createpayment.getStatusLine().getStatusCode());
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), Integer.parseInt(status_code),
				"Status is not correct");

		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			if (method.equals("sc") || method.equals("gv") || method.equals("cod") || method.equals("storeairtel")
					|| method.equals("offlineairtel")) {
				Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			} else {
				Assert.assertEquals(actionInfo.getString("action"), "REDIRECT", "action is not correct");
				Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
			}

			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		} else {
			Assert.assertEquals(Integer.parseInt(error_status), responseJson_createpayment.getInt("status"),
					"error_status is not correct");
			Assert.assertEquals(error_message, responseJson_createpayment.getString("message"),
					"error_message is not correct");
			Assert.assertEquals(errors, responseJson_createpayment.getString("error"), "errors is not correct");
		}
	}

	@Test(enabled = true)
	public void createPaymentForWalletUser() throws Exception {
		log.info("------------------------Wallet User-------------------------------------");
		// session function call
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, x_Api_Client, mobileNumber);
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));
		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, x_Api_Client);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		CartUtil.getCartBasedOnApplyWallet(sessiontoken, x_Api_Client, "true");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("method", "lenskartwallet");
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void createPaymentForWalletUserForWhichWalletIsNotApplied() throws Exception {
		log.info("------------------------Wallet User- wallet not applied-------------------------------------");
		// session function call
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessiontoken, x_Api_Client, mobileNumber);
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));
		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, x_Api_Client);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		CartUtil.getCartBasedOnApplyWallet(sessiontoken, x_Api_Client, "false");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("method", "lenskartwallet");
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void createPaymentForMobileEmailMappedUser() throws Exception {
		log.info(
				"------------------------Wallet User- Mobile and email mapped user-------------------------------------");
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = SessionUtil.createNewSession();
		sessiontoken = MobileAndEmailLogin.mobileLogin_EmailVerified(sessiontoken, x_Api_Client, mobileNumber);
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));
		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, x_Api_Client);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		CartUtil.getCartBasedOnApplyWallet(sessiontoken, x_Api_Client, "true");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("method", "Lenskartwallet");
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void createPaymentForMobileEmailMappedUserLoginWithEmail() throws Exception {
		log.info(
				"------------------------Wallet User- Mobile and email mapped user login with email-------------------------------------");
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		String sessionToken = SessionUtil.createNewSession();
		sessionToken = MobileAndEmailLogin.emailLogin_MobileVerified(sessionToken, x_Api_Client, mobileNumber);
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));
		JSONObject cart_response = CartUtil.createCart(sessionToken, reqObj, x_Api_Client);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		CartUtil.getCartBasedOnApplyWallet(sessionToken, x_Api_Client, "true");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("method", "Lenskartwallet");
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void createPaymentForGuestUser() throws Exception {
		log.info("------------------------Guest User------------------------------------");
		// session function call
		String sessiontoken = SessionUtil.createNewSession();
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));
		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, x_Api_Client);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		CartUtil.getCartBasedOnApplyWallet(sessiontoken, x_Api_Client, "false");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("method", "lenskartwallet");
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		}
	}

	@Test(enabled = true)
	public void createPaymentForLoggedInUser() throws Exception {
		log.info("------------------------LoggedIn User------------------------------------");
		String sessiontoken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessiontoken = MobileAndEmailLogin.oldEmailLogin(sessiontoken, x_Api_Client, mobileNumber);
		categoryId = ApplicationUtil.returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = ApplicationConstants.ProductCategories.EYEGLASSES;
			log.info("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_Api_Client);
		String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_id));
		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, x_Api_Client);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		CartUtil.getCartBasedOnApplyWallet(sessiontoken, x_Api_Client, "false");
		List<NameValuePair> Header = new ArrayList<NameValuePair>();
		Header.add(new BasicNameValuePair("Content-Type", "application/json"));
		Header.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		Header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		Header.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		JSONObject Requestparams_createPayment = new JSONObject();
		JSONObject amount = new JSONObject();
		amount.put("currencyCode", "INR");
		amount.put("price", "100");
		Requestparams_createPayment.put("amount", amount);
		JSONObject billingInfo = ApplicationUtil.createPaymentBillingAddress();

		Requestparams_createPayment.put("billingInfo", billingInfo);
		JSONObject paymentInstrument = new JSONObject();
		paymentInstrument.put("method", "lenskartwallet");
		Requestparams_createPayment.put("paymentInstrument", paymentInstrument);
		JSONObject order = new JSONObject();
		order.put("orderId", GenericUtil.createRandomNumber(7));
		order.put("storeId", "1");
		Requestparams_createPayment.put("order", order);

		log.info("CreatePayment URL of Post: " + RequstUrlcreatePayment);
		HttpResponse httpResponse_createpayment = RequestUtil.postRequest(RequstUrlcreatePayment, Header,
				Requestparams_createPayment);
		JSONObject responseJson_createpayment = RequestUtil.convertHttpResponseToJsonObject(httpResponse_createpayment);
		Assert.assertEquals(httpResponse_createpayment.getStatusLine().getStatusCode(), 200, "Status is not correct");
		if (httpResponse_createpayment.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJson_createpayment.getJSONObject("result");
			String paymentid = result.getString("paymentId");
			JSONObject actionInfo = result.getJSONObject("actionInfo");
			Assert.assertEquals(actionInfo.getString("action"), "DONE", "action is not correct");
			if (db_Validation) {
				Map<String, Object> payment_details = new HashMap<String, Object>();
				payment_details.put("_id", paymentid);

				List<Document> payment_status = PaymentUtil.getpayments(payment_details);
				for (int i = 0; i < payment_status.size(); i++) {
					Document payment_db = payment_status.get(i);
					log.info("asdfsfsd" + payment_db);
					PaymentDatabaseValidation.createPaymentValidation(result, payment_db);
				}
			}
		}
	}
}