package org.lenskart.test.payment.v1;

import java.util.ArrayList;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.PaymentV1PathConstants;
import org.testng.Assert;
import org.testng.annotations.Test;

import com.utilities.JsonUtil;
import com.utilities.RequestUtil;

public class HappyFlow_without_login {
  private static JSONObject reqJsonObject = null;
  private static String sessiontoken = null;
  public static String product_Id;

  @Test(enabled = true)
  public void Payment_Successful_usecase() throws Exception {
    Payment_utilities.sessiontoken_without_login();
    // String RequstUrlGetPayment= EnvironmentConstants.PAYMENT_TEST_ENVIRONMENT +
    // PaymentPathConstants.GET_PAYMENT_METHODS_PATH;
    // System.out.println("--------------------------GET PAYMENT METHODS
    // DESKTOP----------------------------------");
    // System.out.println("RequstUrlGetPayment: "+RequstUrlGetPayment);
    // // Post request to the URL with the param data*/
    // reqJsonObject = JsonUtil.convert_json_textfile_to_object(
    // System.getProperty("user.dir")+
    // "/API_Payload/GET_PAYMENT/getPaymentMethods_desktopff.json");
    //
    // List<NameValuePair> Header= new ArrayList<NameValuePair>();
    // Header.add(new BasicNameValuePair("Content-Type", "application/json"));
    // Header.add(new BasicNameValuePair("Accept", "application/json"));
    // Header.add(new BasicNameValuePair("X-Api-Client", "DESKTOP"));
    //
    // HttpResponse httpResponsegetpayment =
    // RequestUtil.postJSON_Request(RequstUrlGetPayment,Header,reqJsonObject);
    // Assert.assertEquals(httpResponsegetpayment.getStatusLine().getStatusCode(),200,"Response
    // is
    // not proper");
    // JSONObject
    // responseJson_Getpayment=RequestUtil.convert_HttpResponse_to_JsonObject(httpResponsegetpayment);
    //
    // JSONArray payMethodsArray = (JSONArray)
    // responseJson_Getpayment.get("paymentMethods");
    // Assert.assertNotNull(responseJson_Getpayment.get("paymentMethods"),"No
    // Payment Method is
    // available");
    // System.out.println("Different Payment Methods");
    // for(int n = 0; n < payMethodsArray.length(); n++)
    // {
    // JSONObject objec = payMethodsArray.getJSONObject(n);
    // System.out.println("Method cod: "+objec.getString("code")+" enabled:
    // "+objec.getBoolean("enabled"));
    // if(objec.getString("code").equals("cod"))
    // {
    // Assert.assertEquals(objec.getBoolean("enabled"),true, "cod is not true");
    // }
    // if(objec.getString("code").equals("po"))
    // {
    // Assert.assertEquals(objec.getBoolean("enabled"),false, "Po is not false");
    // }
    // }
    //
    // Assert.assertEquals(responseJson_Getpayment.getString("savedCards"),"[]","savedCards
    // is
    // null");
    // Assert.assertEquals(responseJson_Getpayment.getString("defaultMethodCode"),"cod","defaultMethodCode
    // is not correct ");
    //

    // System.out.println("--------------------------MAKE PAYMENT
    // API--------------------------------------");
    // String RequstUrlmakePayment= EnvironmentConstants.PAYMENT_TEST_ENVIRONMENT +
    // PaymentPathConstants.MAKE_PAYMENT_PATH;
    // reqJsonObject = JsonUtil.convert_json_textfile_to_object(
    // System.getProperty("user.dir")+
    // "/API_Payload/CREATE_ORDER/makePaymentmethod.json");
    // List<NameValuePair> Header= new ArrayList<NameValuePair>();
    // Header.add(new BasicNameValuePair("Content-Type", "application/json"));
    // Header.add(new BasicNameValuePair("Accept", "application/json"));
    // Header.add(new BasicNameValuePair("X-Api-Client", "DESKTOP"));
    // Header.add(new BasicNameValuePair("sessiontoken",sessiontoken));
    //
    // HttpResponse httpResponsemakepayment =
    // RequestUtil.postJSON_Request(RequstUrlmakePayment,Header,reqJsonObject);
    // Assert.assertEquals(httpResponsemakepayment.getStatusLine().getStatusCode(),200,"Response
    // is
    // not proper");
    // JSONObject
    // responseJson_Makepayment=RequestUtil.convert_HttpResponse_to_JsonObject(httpResponsemakepayment);
    // Assert.assertNotNull(responseJson_Makepayment.getString("orderId"),"OrderId
    // is null");
    // String OrderId=responseJson_Makepayment.getString("orderId");
    // Assert.assertEquals(responseJson_Makepayment.getString("success"),"true","success
    // is not
    // correct");
    // Assert.assertEquals(responseJson_Makepayment.getString("error"),"null","error
    // is not null");
    // Assert.assertNotNull(responseJson_Makepayment.getString("paymentData"),"paymentData
    // is
    // null");
    // Assert.assertNotNull(responseJson_Makepayment.getString("powerRequired"),"powerRequired
    // is
    // null");
    // Assert.assertEquals(responseJson_Makepayment.getString("childOrderIds"),"null","childOrderIds
    // is not null");
    // Assert.assertNotNull(responseJson_Makepayment.getString("otpSent"),"otpSent
    // is null");

    System.out.println(
        "--------------------------CREATE PAYMENT API--------------------------------------");
    String restURL =
        Environments.SERVICES_ENVIRONMENT + PaymentV1PathConstants.CREATE_PAYMENT_PATH;
    reqJsonObject = JsonUtil.convert_json_textfile_to_object(
        System.getProperty("user.dir") + "/API_Payload/CREATE_PAYMENT/createPayment.json");
    List<NameValuePair> headers = new ArrayList<NameValuePair>();
    headers.add(new BasicNameValuePair("sessiontoken", sessiontoken));
    headers.add(new BasicNameValuePair("Content-Type", "application/json"));

    HttpResponse httpresponse = RequestUtil.postRequest(restURL, headers, reqJsonObject);
    Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
        "Response is not proper");
    JSONObject respJsonObjectcreatepayment =
        RequestUtil.convertHttpResponseToJsonObject(httpresponse);

    JSONObject res = (JSONObject) respJsonObjectcreatepayment.get("result");
    Assert.assertNotNull(res, "Result list is NULL");
    Assert.assertNotNull(res.getString("paymentId"), "Payment Id is blank");
    System.out.println("paymentId: " + res.getString("paymentId"));
    String paymentId = res.getString("paymentId");
    Assert.assertNotNull(res.getString("orderId"), "Order Id is blank");
    // String orderId= res.getString("orderId");
    System.out.println("OrderId: " + res.getString("orderId"));
    Assert.assertNotNull(res.getString("actionInfo"), "actionInfo is blank");
    JSONObject actionInfo = (JSONObject) res.get("actionInfo");
    Assert.assertEquals(actionInfo.getString("action"), "REDIRECT" + "", "Action is not correct");
    // Assert.assertEquals(actionInfo.getString("action"),"DONE"
    // + "", "Action is not correct");
    Assert.assertNotNull(actionInfo.getString("redirectUrl"), "redirectUrl is null");
    Assert.assertNotNull(actionInfo.getString("requestParams"), "requestParams is null");

    System.out.println(
        "--------------------------VERIFY PAYMENT API- after creating order---------------------------------");
    String RequstUrlVerify = Environments.SERVICES_ENVIRONMENT + "/juno-payment/v1/payments/"
        + paymentId + "/status";
    HttpResponse httpResponseverify = RequestUtil.getRequest(RequstUrlVerify);
    Assert.assertEquals(httpResponseverify.getStatusLine().getStatusCode(), 200,
        "Response is not proper");
    JSONObject responseJson_Verify =
        RequestUtil.convertHttpResponseToJsonObject(httpResponseverify);

    System.out.println("Response: " + responseJson_Verify);
    JSONObject payMethodsa = (JSONObject) responseJson_Verify.get("paymentStatus");
    Assert.assertNotNull(payMethodsa.getString("status"), "Payment is not done");

    System.out
        .println("--------------------------PROCESS PG RESPONSE----------------------------------");
    String RequstUrlProcessPayment = Environments.SERVICES_ENVIRONMENT
        + "/juno-payment/v1/payments/" + paymentId + "/paymentgateway-response/";
    JSONObject reqJsonObject = new JSONObject();
    reqJsonObject.put("paymentGateway", "payu");
    reqJsonObject.put("refererUrl", actionInfo.getString("redirectUrl"));
    reqJsonObject.put("httpParams", actionInfo.getString("requestParams"));

    HttpResponse httpResponseprocesspayment =
        RequestUtil.postRequest(RequstUrlProcessPayment, reqJsonObject);
    Assert.assertEquals(httpResponseprocesspayment.getStatusLine().getStatusCode(), 200,
        "Response is not proper");
    JSONObject responseJson_Processpayment =
        RequestUtil.convertHttpResponseToJsonObject(httpResponseprocesspayment);
    JSONObject payMethodsArr = (JSONObject) responseJson_Processpayment.get("paymentStatus");
    Assert.assertEquals(payMethodsArr.getString("status"), "FAILURE", "status is null");
    Assert.assertNotNull(payMethodsArr.getString("message"), "transaction was not completed");
    Assert.assertNotNull(payMethodsArr.getString("amount"), "amount is null");

  }
}
