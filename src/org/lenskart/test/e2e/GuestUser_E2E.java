package org.lenskart.test.e2e;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.OrderUtil;
import org.lenskart.core.util.PaymentUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.GiftMessage;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.lenskart.juno.schema.v2.common.PrescriptionType;
import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;

public class GuestUser_E2E {

  private static final Logger log = GenericUtil.InitLogger(GuestUser_E2E.class);
  private static boolean db_update = Environments.dbConnectionFlag;
  private static String sessiontoken;

  // private String gvCode1 = "LENS-JIT-ZMFY0-WTGR";
  private String gvCode1 = "LKAUTOMATION";
  private static HashMap<String, String> product = new HashMap<>();
  // unconditional GV
  // private String gvCode1 = "LENS-TEST-CONTACT-NYFO";

  // unconditional GV
  // private String gvCode1 = "LENS-TEST-CONTACT-NYFO";

  private static int update_qty(String table_name, String particluar_column_name, String value,
      String corresponding_column_name, String column_value) throws SQLException {

    String sql = "update 3Sep." + table_name + " set " + particluar_column_name + "=" + value
        + " where " + corresponding_column_name + "=" + column_value;
    int output = mysqlConnectionObject.executeUpdateQuery(sql);
    return output;
  }

  private static boolean db_validation = Environments.dbConnectionFlag;

  private static MongoConnectionUtility mongoConnectionObject = null;
  private static MySQLConnectionUtility mysqlConnectionObject = null;

  @BeforeClass
  public void setup() throws Exception {
    if (db_validation) {
      PropertyFactory pf = new PropertyFactory("E2EService");
      mongoConnectionObject = new MongoConnectionUtility(pf);
      mongoConnectionObject.initMongoDBConnection();

      mysqlConnectionObject = new MySQLConnectionUtility(pf);
      mysqlConnectionObject.initMySQLConnection();
    }
  }

  @AfterClass
  public void destroy() throws Exception {
    if (db_validation) {
      mongoConnectionObject.closeMongoConnection();
      mysqlConnectionObject.closeMySQLConnection();
    }
  }

  @DataProvider(name = "Gueste2e")
  public static Iterator<String[]> supplyData() throws IOException {
    CSVReadUtil csv =
        new CSVReadUtil(System.getProperty("user.dir") + "/csv_files/E2E Flow/GuestUser_e2e.csv");
    List<String[]> list = csv.getEntriesAsList();
    for (int i = 0; i < list.size(); i++) {
      for (int j = 0; j < list.get(i).length; j++) {
        if (list.get(i)[j].equals("null"))
          list.get(i)[j] = null;
      }
    }
    Iterator<String[]> itr = list.iterator();
    return itr;
  }

  @DataProvider(name = "Gueste2e_POS")
  public static Iterator<String[]> supplyData_pos() throws IOException {
    CSVReadUtil csv = new CSVReadUtil(
        System.getProperty("user.dir") + "/csv_files/E2E Flow/GuestUser_e2e_pos.csv");
    List<String[]> list = csv.getEntriesAsList();
    for (int i = 0; i < list.size(); i++) {
      for (int j = 0; j < list.get(i).length; j++) {
        if (list.get(i)[j].equals("null"))
          list.get(i)[j] = null;
      }
    }
    Iterator<String[]> itr = list.iterator();
    return itr;
  }

  @DataProvider(name = "Gueste2e_mix_cart")
  public static Iterator<String[]> supplyDataFor_mic_cart() throws IOException {
    CSVReadUtil csv = new CSVReadUtil(
        System.getProperty("user.dir") + "/csv_files/E2E Flow/GuestUser_e2e_mix_cart.csv");
    List<String[]> list = csv.getEntriesAsList();
    for (int i = 0; i < list.size(); i++) {
      for (int j = 0; j < list.get(i).length; j++) {
        if (list.get(i)[j].equals("null"))
          list.get(i)[j] = null;
      }
    }
    Iterator<String[]> itr = list.iterator();
    return itr;
  }

  @DataProvider(name = "addPrescription")
  public static Iterator<String[]> supplyData1() throws IOException, JSONException {
    char seperator = ',';
    CSVReadUtil csv = new CSVReadUtil(
        System.getProperty("user.dir") + "//csv_files//cart//Prescription.csv", seperator);
    List<String[]> list = csv.getEntriesAsList();

    Iterator<String[]> itr = list.iterator();
    return itr;

  }

  private Address shippingAddress() {
    Address address = new Address();
    address.setFirstName("Test");
    address.setLastName("Test");
    address.setAddressline1("Test order please don't proceed");
    address.setCity("Test order please don't proceed");
    address.setCountry("IN");
    address.setGender("Female");
    address.setEmail("<EMAIL>");
    address.setPhone("**********");
    address.setState("test");
    address.setPostcode("560038");
    return address;
  }

  @Test(enabled = true, dataProvider = "Gueste2e", priority = 6)
  public void guest_user_flow_without_gv_usecases(String usecase, String x_Api_Client,
      String categoryId, String powerType, String bank, String payment_method, String gatewayId)
      throws Exception {
    log.info("------------------------" + usecase + "-------------------------------------");
    sessiontoken = SessionUtil.createNewSession();
    JSONObject jsonResponse_category =
        JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId), x_Api_Client);

    product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType, false);
    String product_Id = product.get("productId");
    String packageId = product.get("packageId");
    JSONObject cart_response =
        CartUtil.createCart(sessiontoken, product_Id, powerType, packageId, x_Api_Client);

    log.info("-----------Create Cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, bank, payment_method, gatewayId);
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
    JSONObject payment_response_result = payment_response.getJSONObject("result");
    String OrderId = payment_response_result.getJSONObject("order").getString("id");
    String ItemId = payment_response_result.getJSONObject("order").getJSONArray("items")
        .getJSONObject(0).getString("id");
    if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
        .getString("powerRequired").equals("POWER_REQUIRED")) {
      String powerType2 = payment_response_result.getJSONObject("order").getJSONArray("items")
          .getJSONObject(0).getJSONObject("prescription").getString("powerType");
      OrderUtil.updateOrderItemPrescription_usecase(sessiontoken, OrderId, ItemId, powerType2);
    }

  }

  @Test(enabled = true, priority = 7)
  public void guest_user_flow_for_HTO_cod() throws Exception {
    log.info("------------------------HTO CASES------------------------------------");
    sessiontoken = SessionUtil.createNewSession();
    JSONObject cart_response =
        CartUtil.HTOCart(sessiontoken, ApplicationConstants.XApiClient.ANDROID);
    log.info("-----------Create Cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, "", "cod", "");
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, priority = 8)
  public void guest_user_flow_for_HTO_cc() throws Exception {
    log.info("------------------------HTO CASES------------------------------------");
    sessiontoken = SessionUtil.createNewSession();
    JSONObject cart_response =
        CartUtil.HTOCart(sessiontoken, ApplicationConstants.XApiClient.ANDROID);
    log.info("-----------Create Cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, "", "cc", "");
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, priority = 5)
  public void guest_user_flow_for_HTO_with_gv() throws Exception {
    log.info(
        "------------------------HTO CASES after applying gv------------------------------------");
    if (db_update) {
      int bal = update_qty("giftvoucher", "balance", "500", "gift_code", "'" + gvCode1 + "'");
      Assert.assertEquals(bal, 1, "DB didnt get updated");
    }
    sessiontoken = SessionUtil.createNewSession();
    JSONObject cart_response =
        CartUtil.HTOCart(sessiontoken, ApplicationConstants.XApiClient.ANDROID);
    log.info("-----------Create Cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    CartUtil.addGiftVoucher(sessiontoken, gvCode1);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, "", "gv", "");
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, priority = 9)
  public void guest_user_flow_for_contact_lens_with_power() throws Exception {
    log.info("------------------------Contact lens with power------------------------------------");
    sessiontoken = SessionUtil.createNewSession();
    Prescription presObj = new Prescription();
    presObj.setPrescriptionType(PrescriptionType.valueOf("CONTACT_LENS"));
    presObj.setDob("12-Aug-92");
    presObj.setGender("Female");
    // Left Power
    Power leftPower = new Power();
    leftPower.setSph("-0.75");
    leftPower.setAxis("1");
    leftPower.setBoxes(Integer.valueOf("2"));
    leftPower.setCyl("-0.75");

    presObj.setLeft(leftPower);
    // Right Power
    Power rightPower = new Power();
    rightPower.setSph("-0.75");
    rightPower.setAxis("1");
    rightPower.setBoxes(Integer.valueOf("2"));
    rightPower.setCyl("-0.75");

    presObj.setRight(rightPower);
    presObj.setPowerType(PowerType.valueOf("CONTACT_LENS"));
    presObj.setUserName("user1");

    JSONObject cart_response = CartUtil.createCart(sessiontoken,
        JunoV1Util.getFirstProductIdFromCategory(
            ApplicationConstants.ProductCategories.CONTACT_LENS,
            ApplicationConstants.XApiClient.ANDROID),
        "", "1", presObj, ApplicationConstants.XApiClient.ANDROID);
    log.info("-----------Create Cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, "", "cc", "");
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, priority = 4)
  public void guest_user_flow_for_contact_lens_with_power_with_gv() throws Exception {
    log.info(
        "------------------------Contact lens with power GV is applied------------------------------------");
    if (db_update) {
      int bal = update_qty("giftvoucher", "balance", "500", "gift_code", "'" + gvCode1 + "'");
      Assert.assertEquals(bal, 1, "DB didnt get updated");
    }
    sessiontoken = SessionUtil.createNewSession();
    Prescription presObj = new Prescription();
    presObj.setPrescriptionType(PrescriptionType.valueOf("CONTACT_LENS"));
    presObj.setDob("12-Aug-92");
    presObj.setGender("Female");
    // Left Power
    Power leftPower = new Power();
    leftPower.setSph("-0.75");
    leftPower.setAxis("1");
    leftPower.setBoxes(Integer.valueOf("2"));
    leftPower.setCyl("-0.75");

    presObj.setLeft(leftPower);
    // Right Power
    Power rightPower = new Power();
    rightPower.setSph("-0.75");
    rightPower.setAxis("1");
    rightPower.setBoxes(Integer.valueOf("2"));
    rightPower.setCyl("-0.75");

    presObj.setRight(rightPower);
    presObj.setPowerType(PowerType.valueOf("CONTACT_LENS"));
    presObj.setUserName("user1");

    JSONObject cart_response = CartUtil.createCart(sessiontoken,
        JunoV1Util.getFirstProductIdFromCategory(
            ApplicationConstants.ProductCategories.CONTACT_LENS,
            ApplicationConstants.XApiClient.ANDROID),
        "", "1", presObj, ApplicationConstants.XApiClient.ANDROID);
    log.info("-----------Create Cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    CartUtil.addGiftVoucher(sessiontoken, gvCode1);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, "PU:ICIB", "nb", "");
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, dataProvider = "Gueste2e", priority = 2)
  public void guest_user_flow_for_GV_usecases(String usecase, String x_Api_Client,
      String categoryId, String powerType, String bank, String payment_method, String gatewayId)
      throws Exception {
    log.info("------------------------" + usecase + "-------------------------------------");
    if (db_update) {
      int bal = update_qty("giftvoucher", "balance", "500", "gift_code", "'" + gvCode1 + "'");
      Assert.assertEquals(bal, 1, "DB didnt get updated");
    }
    sessiontoken = SessionUtil.createNewSession();
    JSONObject jsonResponse_category =
        JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId), x_Api_Client);

    product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType, false);
    String product_Id = product.get("productId");
    String packageId = product.get("packageId");
    JSONObject cart_response =
        CartUtil.createCart(sessiontoken, product_Id, powerType, packageId, x_Api_Client);

    log.info("-----------create cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    CartUtil.addGiftVoucher(sessiontoken, gvCode1);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, bank, payment_method, gatewayId);
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    if (db_update) {
      int balance = update_qty("giftvoucher", "balance", "500", "gift_code", "'" + gvCode1 + "'");
      Assert.assertEquals(balance, 1, "DB didnt get updated");
    }
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, dataProvider = "Gueste2e_mix_cart", priority = 10)

  public void guest_user_flow_for_mix_cart(String usecase, String x_Api_Client, String categoryId1,
      String powerType1, String powerType2, String categoryId2, String bank, String payment_method,
      String gatewayId) throws Exception {
    log.info("------------------------" + usecase + "-------------------------------------");
    sessiontoken = SessionUtil.createNewSession();
    JSONObject jsonResponse_category1 =
        JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId1), x_Api_Client);
    product = ApplicationUtil.getProductDetails(jsonResponse_category1, powerType1, false);
    log.info("---------" + product);
    String productId1 = product.get("productId");
    String packageId1 = product.get("packageId");
    JSONObject cart_response1 =
        CartUtil.createCart(sessiontoken, productId1, powerType1, packageId1, x_Api_Client);

    JSONObject jsonResponse_category2 =
        JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId2), x_Api_Client);
    product = ApplicationUtil.getProductDetails(jsonResponse_category2, powerType2, false);
    log.info("---------" + product);
    String productId2 = product.get("productId");
    String packageId2 = product.get("packageId");
    JSONObject cart_response2 =
        CartUtil.createCart(sessiontoken, productId2, powerType2, packageId2, x_Api_Client);

    Assert.assertEquals(cart_response1.getJSONObject("result").getString("cartId"),
        cart_response2.getJSONObject("result").getString("cartId"),
        "Item is not getting added to same cart");

    log.info("-----------create cart response-----------");
    log.info(cart_response2);
    log.info("------------------------------------------");
    if (productId1.equals(productId2) && powerType1.equals("") && powerType2.equals("")
        && packageId1.equals("") && packageId2.equals(""))
      Assert.assertEquals(cart_response2.getJSONObject("result").getInt("itemsQty"), 1,
          "Both the product didnt get added");
    else
      Assert.assertEquals(cart_response2.getJSONObject("result").getInt("itemsQty"), 2,
          "Both the product didnt get added");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, bank, payment_method, gatewayId);
    log.info(payment_response);
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, dataProvider = "Gueste2e_mix_cart", priority = 3)

  public void guest_user_flow_for_mix_cart_with_gv(String usecase, String x_Api_Client,
      String categoryId1, String powerType1, String powerType2, String categoryId2, String bank,
      String payment_method, String gatewayId) throws Exception {

    log.info("------------------------" + usecase + "-------------------------------------");

    if (db_update) {
      int bal = update_qty("giftvoucher", "balance", "500", "gift_code", "'" + gvCode1 + "'");
      Assert.assertEquals(bal, 1, "DB didnt get updated");
    }

    sessiontoken = SessionUtil.createNewSession();
    log.info(sessiontoken);

    JSONObject jsonResponse_category1 =
        JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId1), x_Api_Client);
    product = ApplicationUtil.getProductDetails(jsonResponse_category1, powerType1, false);
    String productId1 = product.get("productId");
    String packageId1 = product.get("packageId");
    JSONObject cart_response_item1 =
        CartUtil.createCart(sessiontoken, productId1, powerType1, packageId1, x_Api_Client);
    JSONObject jsonResponse_category2 =
        JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId2), x_Api_Client);
    product = ApplicationUtil.getProductDetails(jsonResponse_category2, powerType2, false);
    String productId2 = product.get("productId");
    String packageId2 = product.get("packageId");
    JSONObject cart_response_item2 =
        CartUtil.createCart(sessiontoken, productId2, powerType2, packageId2, x_Api_Client);

    Assert.assertEquals(cart_response_item1.getJSONObject("result").getString("cartId"),
        cart_response_item2.getJSONObject("result").getString("cartId"),
        "Item is not getting added to same cart");
    log.info("-----------create cart response-----------");
    log.info(cart_response_item2);
    log.info("------------------------------------------");
    if (productId1.equals(productId2) && powerType1.equals("") && powerType2.equals("")
        && packageId1.equals("") && packageId2.equals(""))
      Assert.assertEquals(cart_response_item2.getJSONObject("result").getInt("itemsQty"), 1,
          "Both the product didnt get added");
    else
      Assert.assertEquals(cart_response_item2.getJSONObject("result").getInt("itemsQty"), 2,
          "Both the product didnt get added");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    // CartUtil.addGiftVoucher(sessiontoken, gvCode1);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, bank, payment_method, gatewayId);
    log.info(payment_response);

    if (db_update) {
      int balance = update_qty("giftvoucher", "balance", "500", "gift_code", "'" + gvCode1 + "'");
      Assert.assertEquals(balance, 1, "DB didnt get updated");
    }

    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
    JSONObject payment_response_result = payment_response.getJSONObject("result");
    for (int j = 0; j < payment_response_result.getJSONObject("order").getJSONArray("items")
        .length(); j++) {
      String OrderId = payment_response_result.getJSONObject("order").getString("id");
      String ItemId = payment_response_result.getJSONObject("order").getJSONArray("items")
          .getJSONObject(j).getString("id");
      if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(j)
          .getString("powerRequired").equals("POWER_REQUIRED")) {
        String powerType3 = payment_response_result.getJSONObject("order").getJSONArray("items")
            .getJSONObject(j).getJSONObject("prescription").getString("powerType");
        OrderUtil.updateOrderItemPrescription_usecase(sessiontoken, OrderId, ItemId, powerType3);
      }
    }

  }

  @Test(enabled = true, priority = 1)
  public void guest_user_flow_for_gv_fully_applied() throws Exception {
    log.info("------------------------GV applied completly-------------------------------------");

    if (db_update) {
      int balance = update_qty("giftvoucher", "balance", "5000", "gift_code", "'" + gvCode1 + "'");
      log.info("bal" + balance);
      Assert.assertEquals(balance, 1, "DB didnt get updated");
    }

    sessiontoken = SessionUtil.createNewSession();
    JSONObject cart_response = CartUtil.createCart(sessiontoken,
        JunoV1Util.getFirstProductIdFromCategory(ApplicationConstants.ProductCategories.ACCESSORIES,
            ApplicationConstants.XApiClient.ANDROID),
        ApplicationConstants.XApiClient.ANDROID);
    log.info("-----------create cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    CartUtil.addGiftVoucher(sessiontoken, gvCode1);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, "", "gv", "");
    log.info(payment_response);
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }

  @Test(enabled = true, dataProvider = "Gueste2e_POS", priority = 11)
  public void guest_user_flow_for_POS_payment_method(String usecase, String x_Api_Client,
      String powerType, String bank, String payment_method, String gatewayId) throws Exception {
    log.info("------------------------" + usecase + "-------------------------------------");
    sessiontoken = SessionUtil.createNewSession();
    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_contact_lens"), x_Api_Client);
    product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType, false);
    String productId = product.get("productId");
    JSONObject cart_response = CartUtil.createCart(sessiontoken, productId, x_Api_Client);

    log.info("-----------create cart response-----------");
    log.info(cart_response);
    log.info("------------------------------------------");
    Address address = shippingAddress();
    GiftMessage giftMsgObj = new GiftMessage();
    giftMsgObj = null;
    CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address,
        giftMsgObj);
    JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
        ApplicationConstants.XApiClient.ANDROID, bank, payment_method, gatewayId);
    log.info("------------OrderPayment Response-------------");
    log.info(payment_response);
    log.info("-----------------------------------------------");
    Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
        "Order Payment is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
        "Order Payment order is not giving correct response");
    Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
        "Order Payment payment is not giving correct response");
  }
}
