package org.lenskart.test.e2e;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.PaymentUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.GiftMessage;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.lenskart.juno.schema.v2.common.PrescriptionType;
import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;

public class LoggedInUser_E2E {

	private static final Logger log = GenericUtil.InitLogger(LoggedInUser_E2E.class);
	private static boolean db_update = Environments.dbConnectionFlag;
	private static String loggedInSessiontoken;
	private static HashMap<String, String> product = new HashMap<>();
	
	private String storeCreditCode1 = "2541-LJ905-EEWF";
	private String storeCreditCode2 = "4054-VKGU8-YWXK";

	private static MongoConnectionUtility mongoConnectionObject = null;
	private static MySQLConnectionUtility mysqlConnectionObject = null;
	 private static boolean db_validation = Environments.dbConnectionFlag;
	private static int update_qty(String table_name, String particluar_column_name, String value,
			String corresponding_column_name, String column_value) throws SQLException {

		String sql = "update 3Sep." + table_name + " set " + particluar_column_name + "=" + value + " where "
				+ corresponding_column_name + "=" + column_value;
		int output = mysqlConnectionObject.executeUpdateQuery(sql);
		return output;
	}

	@BeforeClass
	  public void setup() throws Exception {
	    if (db_validation) {
	      PropertyFactory pf = new PropertyFactory("DittoDataShare");
	      mongoConnectionObject = new MongoConnectionUtility(pf);
	      mongoConnectionObject.initMongoDBConnection();

	      mysqlConnectionObject = new MySQLConnectionUtility(pf);
	      mysqlConnectionObject.initMySQLConnection();
	    }
	  }

	  @AfterClass
	  public void destroy() throws Exception {
	    if (db_validation) {
	      mongoConnectionObject.closeMongoConnection();
	      mysqlConnectionObject.closeMySQLConnection();
	    }
	  }

	@DataProvider(name = "LoggedIne2e_without_GV_SC")
	public static Iterator<String[]> supplyData() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "/csv_files/E2E Flow/LoggedInUser_e2e.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "LoggedIne2e_with_GV")
	public static Iterator<String[]> supplyDataFor_GV() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "/csv_files/E2E Flow/LoggedInUser_e2e.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "LoggedIne2e_with_SC")
	public static Iterator<String[]> supplyDataFor_SC() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "/csv_files/E2E Flow/LoggedInUser_e2e.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "LoggedIne2e_with_GV_SC")
	public static Iterator<String[]> supplyDataFor_GV_SC() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "/csv_files/E2E Flow/LoggedInUser_e2e.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "LoggedIne2e_with_GV_SC_for_mix_cart")
	public static Iterator<String[]> supplyDataFor_GV_SC_mix_cart() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "/csv_files/E2E Flow/LoggedInUser_e2e_for_mix_cart.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	private Address shippingAddress() {
		Address address = new Address();
		address.setFirstName("Test");
		address.setLastName("Test");
		address.setAddressline1("Test order please don't proceed");
		address.setCity("Test order please don't proceed");
		address.setCountry("IN");
		address.setGender("Female");
		address.setEmail("<EMAIL>");
		address.setPhone("**********");
		address.setState("test");
		address.setPostcode("560038");
		return address;
	}

	@Test(enabled = true, dataProvider = "LoggedIne2e_without_GV_SC", priority = 16)
	public void loggedInUser_flow_without_sc_gv_usecases(String usecase, String x_Api_Client, String categoryId,
			String powerType, String bank, String payment_method, String gatewayId) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId),
				x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType, false);

		String product_Id = product.get("productId");
		String packageId = product.get("packageId");
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, product_Id, powerType, packageId,
				x_Api_Client);
		log.info("-----------cart_response-------------");
		log.info(cart_response);
		log.info("--------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_Api_Client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_Api_Client, bank, payment_method,
				gatewayId);
		log.info("------------OrderPayment Response-------------");
		log.info(payment_response);
		log.info("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 15)
	public void loggedInUser_flow_for_HTO_cod() throws Exception {
		log.info("------------------------HTO CASES------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject cart_response = CartUtil.HTOCart(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------Create Cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		GiftMessage giftMsgObj = new GiftMessage();
		giftMsgObj = null;
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, giftMsgObj);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "cod", "");
		log.info("------------OrderPayment Response-------------");
		log.info(payment_response);
		log.info("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 14)
	public void loggedInUser_flow_HTO_cc() throws Exception {
		log.info("------------------------HTO CASES------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject cart_response = CartUtil.HTOCart(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------Create Cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		GiftMessage giftMsgObj = new GiftMessage();
		giftMsgObj = null;
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, giftMsgObj);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "cc", "");
		log.info("------------OrderPayment Response-------------");
		log.info(payment_response);
		log.info("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 13)
	public void loggedInUser_flow_HTO_with_gv() throws Exception {
		log.info("------------------------HTO CASES after applying gv------------------------------------");
		if (db_update) {
			int bal = update_qty("giftvoucher", "balance", "500", "gift_code", "'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(bal, 1, "DB didnt get updated");
		}
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject cart_response = CartUtil.HTOCart(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------Create Cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		GiftMessage giftMsgObj = new GiftMessage();
		giftMsgObj = null;
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, giftMsgObj);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "gv", "");
		log.info("------------OrderPayment Response-------------");
		log.info(payment_response);
		log.info("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 12)
	public void loggedInUser_flow_contact_lens_with_power() throws Exception {
		log.info("------------------------Contact lens with power------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();

		presObj.setPrescriptionType(PrescriptionType.valueOf("CONTACT_LENS"));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.50");
		leftPower.setBoxes(Integer.valueOf("2"));


		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.50");
		rightPower.setBoxes(Integer.valueOf("2"));
	

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf("CONTACT_LENS"));
		presObj.setUserName("user1");

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken,
				JunoV1Util.getFirstProductIdFromCategory(ApplicationConstants.ProductCategories.CONTACT_LENS,
						ApplicationConstants.XApiClient.ANDROID),
				"", "1", presObj, ApplicationConstants.XApiClient.ANDROID);

		log.info("-----------Create Cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response =  PaymentUtil.orderPayment(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, "", "cc",
				"PU");
		log.info("------------OrderPayment Response-------------");
		log.info(payment_response);
		log.info("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 11)
	public void loggedInUser_flow_contact_lens_with_power_with_gv() throws Exception {
		log.info("------------------------Contact lens with power------------------------------------");
		if (db_update) {
			int bal = update_qty("giftvoucher", "balance", "50000", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(bal, 1, "DB didnt get updated");
		}
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf("CONTACT_LENS"));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));
		leftPower.setCyl("-0.75");

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));
		rightPower.setCyl("-0.75");

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf("CONTACT_LENS"));
		presObj.setUserName("user1");

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken,
				JunoV1Util.getFirstProductIdFromCategory(ApplicationConstants.ProductCategories.CONTACT_LENS,
						ApplicationConstants.XApiClient.ANDROID),
				"", "1", presObj, ApplicationConstants.XApiClient.ANDROID);

		log.info("-----------Create Cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "gv", "");
		log.info("------------OrderPayment Response-------------");
		log.info(payment_response);
		log.info("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, dataProvider = "LoggedIne2e_with_GV", priority = 10)
	public void loggedInUser_flow_with_GV_usecases(String usecase, String x_Api_Client, String categoryId,
			String powerType, String bank, String payment_method, String gatewayId) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");
		if (db_update) {
			int bal = update_qty("giftvoucher", "balance", "200", "gift_code", "'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(bal, 1, "GV DB didnt get updated");
		}
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId),
				x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType, false);

		String productId = product.get("productId");
		String packageId = product.get("packageId");

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, productId, powerType, packageId,
				x_Api_Client);
		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_Api_Client, address, null);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_Api_Client, bank, payment_method,
				gatewayId);
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
		if (db_update) {
			int balance = update_qty("giftvoucher", "balance", "200", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(balance, 1, "GV DB didnt get updated");
		}
	}

	@Test(enabled = true, priority = 9)
	public void loggedInUser_flow_with_fully_applied_GV_usecases() throws Exception {
		log.info("------------------------Fully applied GV-------------------------------------");

		if (db_update) {
			int bal = update_qty("giftvoucher", "balance", "20000", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(bal, 1, "GV DB didnt get updated");
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		String productId = product.get("productId");

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, productId,
				ApplicationConstants.XApiClient.ANDROID);

		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "gv", "");
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, dataProvider = "LoggedIne2e_with_SC", priority = 8)
	public void loggedInUser_flow_with_SC_usecases(String usecase, String x_Api_Client, String categoryId,
			String powerType, String bank, String payment_method, String gatewayId) throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");

		// if (db_update) {
		// int balance = update_qty("storecredit", "balance", "50",
		// "store_code", "'" + storeCreditCode1 + "'");
		// Assert.assertEquals(balance, 1, "SC DB didnt get updated");
		// }

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId),
				x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType, false);

		String productId = product.get("productId");
		String packageId = product.get("packageId");

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, productId, powerType, packageId,
				x_Api_Client);
		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_Api_Client, address, null);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode2, 11);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_Api_Client, bank, payment_method,
				gatewayId);
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");

		// if (db_update) {
		// int bal = update_qty("storecredit", "balance", "50", "store_code",
		// "'" + storeCreditCode1 + "'");
		// Assert.assertEquals(bal, 1, "SC DB didnt get updated");
		// }
	}

	@Test(enabled = true, priority = 7)
	public void loggedInUser_flow_with_fully_applied_SC_usecases() throws Exception {
		log.info("----------------------FULLY APPLIED SC------------------------------------");

		if (db_update) {
			int balance = update_qty("storecredit", "balance", "20000", "store_code", "'" + storeCreditCode1 + "'");
			Assert.assertEquals(balance, 1, "SC DB didnt get updated");
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		String productId = product.get("productId");

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, productId,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode2,
				cart_response.getJSONObject("result").getJSONObject("totals").getInt("total"));
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "sc", "");
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, dataProvider = "LoggedIne2e_with_GV_SC", priority = 6)
	public void loggedInUser_flow_with_GV_SC_usecases_with_different_payment_method(String usecase, String x_Api_Client,
			String categoryId, String powerType, String bank, String payment_method, String gatewayId)
			throws Exception {
		log.info("------------------------" + usecase + "-------------------------------------");

		if (db_update) {
			// int sc_balance = update_qty("storecredit", "balance", "50",
			// "store_code", "'" + storeCreditCode1 + "'");
			// Assert.assertEquals(sc_balance, 1, "SC DB didnt get updated");
			int gv_balance = update_qty("giftvoucher", "balance", "50", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(gv_balance, 1, "GV DB didnt get updated");
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId),
				x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, powerType, false);

		String productId = product.get("productId");
		String packageId = product.get("packageId");

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, productId, powerType, packageId,
				x_Api_Client);
		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_Api_Client, address, null);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode1, 10);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_Api_Client, bank, payment_method,
				gatewayId);
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 5)
	public void loggedInUser_flow_with_GV_SC_usecases() throws Exception {
		log.info("------------------------FULLY APPLIED GV AND SC------------------------------------");

		if (db_update) {

			int gv_balance = update_qty("giftvoucher", "balance", "50", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(gv_balance, 1, "GV DB didnt get updated");
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);

		String product_Id = product.get("productId");
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, product_Id,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode1,
				cart_response.getJSONObject("result").getJSONObject("totals").getInt("total"));
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "sc", "");
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 4)
	public void loggedInUser_flow_with_SC_GV_usecases() throws Exception {
		log.info("------------------------FULLY APPLIED GV AND One SC------------------------------------");

		if (db_update) {

			int gv_balance = update_qty("giftvoucher", "balance", "50000", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(gv_balance, 1, "GV DB didnt get updated");
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);

		String product_Id = product.get("productId");
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, product_Id,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode1, 10);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "gv", "");
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 3)
	public void loggedInUser_flow_with_multiple_SC_and_GV_usecases() throws Exception {
		log.info("------------------------FULLY APPLIED GV AND MULTIPLE SC------------------------------------");

		if (db_update) {

			int gv_balance = update_qty("giftvoucher", "balance", "50000", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(gv_balance, 1, "GV DB didnt get updated");
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);

		String product_Id = product.get("productId");
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, product_Id,
				ApplicationConstants.XApiClient.ANDROID);

		log.info("-----------create cart response-----------");
		log.info(cart_response);
		log.info("------------------------------------------");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode1, 11);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode2, 10);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "cc", "");
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, priority = 2)
	public void loggedInUser_flow_for_mix_cart_with_multiple_SC_and_full_GV_usecases() throws Exception {
		log.info("------------------------FULLY APPLIED GV AND MULTIPLE SC------------------------------------");
		if (db_update) {
			int gv_balance = update_qty("giftvoucher", "balance", "50000", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(gv_balance, 1, "GV DB didnt get updated");
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category1 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_contact_lens"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category1, "", false);

		String product_Id1 = product.get("productId");
		JSONObject cart_response_item1 = CartUtil.createCart(loggedInSessiontoken, product_Id1, "", "",
				ApplicationConstants.XApiClient.ANDROID);

		JSONObject jsonResponse_category2 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_sunglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category2, "", false);
		String product_Id2 = product.get("productId");

		JSONObject cart_response_item2 = CartUtil.createCart(loggedInSessiontoken, product_Id2, "", "",
				ApplicationConstants.XApiClient.ANDROID);
		Assert.assertEquals(cart_response_item2.getJSONObject("result").getString("cartId"),
				cart_response_item2.getJSONObject("result").getString("cartId"),
				"Item is not getting added to same cart");
		log.info("-----------create cart response-----------");
		log.info(cart_response_item2);
		log.info("------------------------------------------");
		Assert.assertEquals(cart_response_item2.getJSONObject("result").getInt("itemsQty"), 2,
				"Both the product didnt get added");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode1, 10);
		CartUtil.addStoreCredit(loggedInSessiontoken, storeCreditCode2, 11);
		CartUtil.addGiftVoucher(loggedInSessiontoken, ApplicationConstants.gvCode);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken,
				ApplicationConstants.XApiClient.ANDROID, "", "gv", "");
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = true, dataProvider = "LoggedIne2e_with_GV_SC_for_mix_cart", priority = 1)
	public void loggedInUser_flow_for_mix_cart_with_multiple_SC_and_GV_usecases(String usecase, String x_Api_Client,
			String categoryId1, String powerType1, String powerType2, String categoryId2, String bank,
			String payment_method, String gatewayId) throws Exception {
		log.info("------------------------" + usecase + "------------------------------------");
		if (db_update) {
			int gv_balance = update_qty("giftvoucher", "balance", "50", "gift_code",
					"'" + ApplicationConstants.gvCode + "'");
			Assert.assertEquals(gv_balance, 1, "GV DB didnt get updated");
		}
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);

		JSONObject jsonResponse_category1 = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId1),
				x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category1, powerType1, false);

		String productId1 = product.get("productId");
		String packageId1 = product.get("packageId");
		JSONObject cart_response_item1 = CartUtil.createCart(loggedInSessiontoken, productId1, powerType1, packageId1,
				x_Api_Client);
		JSONObject jsonResponse_category2 = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId2),
				x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category2, powerType2, false);

		String productId2 = product.get("productId");
		String packageId2 = product.get("packageId");
		JSONObject cart_response_item2 = CartUtil.createCart(loggedInSessiontoken, productId2, powerType2, packageId2,
				x_Api_Client);
		Assert.assertEquals(cart_response_item1.getJSONObject("result").getString("cartId"),
				cart_response_item2.getJSONObject("result").getString("cartId"),
				"Item is not getting added to same cart");
		log.info("-----------create cart response-----------");
		log.info(cart_response_item2);
		log.info("------------------------------------------");

		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_Api_Client, address, null);

		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_Api_Client, bank, payment_method,
				gatewayId);
		log.info(payment_response);
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
	}

	@Test(enabled = false)
	public void test_loggedInUser_qty1_out_of_stock() throws Exception {
		log.info("-----------------------Out Of Stock-------------------------------------");
		String loggedInSessiontoken1 = CustomerUtil.get_sessionId_after_user_authentication(
				ApplicationConstants.userEmail, ApplicationConstants.userPassword,
				ApplicationConstants.XApiClient.ANDROID);
		JSONObject jsonResponse_category1 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category1, "", false);

		String productId = product.get("productId");

		JSONObject cart_response1 = CartUtil.createCart(loggedInSessiontoken1, productId,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------cart_response-------------");
		log.info(cart_response1);
		log.info("--------------------------------------");

		String loggedInSessiontoken2 = CustomerUtil.get_sessionId_after_user_authentication(
				ApplicationConstants.userEmail, ApplicationConstants.userPassword,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------------------Out Of Stock-------------------------------------");

		JSONObject cart_response2 = CartUtil.createCart(loggedInSessiontoken2, productId,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------cart_response-------------");
		log.info(cart_response2);
		log.info("--------------------------------------");
		JSONObject payment_response2 = PaymentUtil.orderPayment(loggedInSessiontoken2,
				ApplicationConstants.XApiClient.ANDROID, "PU:ICIB", "nb", "PU");
		log.info("------------OrderPayment Response-------------");
		log.info(payment_response2);
		log.info("-----------------------------------------------");

		String loggedInSessiontoken3 = CustomerUtil.get_sessionId_after_user_authentication(
				ApplicationConstants.userEmail, ApplicationConstants.userPassword,
				ApplicationConstants.XApiClient.ANDROID);
		JSONObject cart_response3 = CartUtil.createCart(loggedInSessiontoken3, productId,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("-----------cart_response-------------");
		log.info(cart_response3);
		log.info("--------------------------------------");
	}

}
