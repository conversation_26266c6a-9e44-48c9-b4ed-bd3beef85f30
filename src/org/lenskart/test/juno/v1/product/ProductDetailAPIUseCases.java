package org.lenskart.test.juno.v1.product;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.ProductCategories;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.JunoV1PathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.utilities.CSVReadUtil;
import com.utilities.JsonUtil;
import com.utilities.RequestUtil;
import com.utilities.SolrConnectionUtility;

public class ProductDetailAPIUseCases {
	private static final Logger log = Logger.getLogger(ProductDetailAPIUseCases.class);
	private String restURL = "";
	private static HashMap<String, String> product = new HashMap<>();
	private static final boolean solrValidationFlag = Environments.dbConnectionFlag;
	private SolrConnectionUtility solrCategoryConnectionObject = null;

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (solrValidationFlag) {
			solrCategoryConnectionObject = ProductUtil.getProductSolrConnectionObject();
			solrCategoryConnectionObject.initSolrDBConnection("juno_products_common");
		}
	}

	@AfterClass
	public void closeConnection() {
		if (solrValidationFlag) {
			solrCategoryConnectionObject.closeSolrDBConnection();
		}
	}

	// @DataProvider(name= "combValues")
	public static Iterator<String[]> supplyData() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "//csv_files//ProductGetDetailsTestData.csv");
		List<String[]> list = csv.getEntriesAsList();
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "EyeglassValidate")
	public static Iterator<String[]> supplyData_of_eyeglass() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "//csv_files//Product//Eyeglass.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "SunglassValidate")
	public static Iterator<String[]> supplyData_of_sunglass() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "//csv_files//Product//sunglass.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "ContactLensValidate")
	public static Iterator<String[]> supplyData_of_contactlens() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "//csv_files//Product//contactlens.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "Buypackagefordifferentcategory")
	public static Iterator<String[]> minlensPriceForDifferentCategory() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "//csv_files//Product//GetPcakagesForCategory.csv");

		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "AllPlatformsWithCategory")
	public static Iterator<String[]> supplyData_of_allPlatforms() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "//csv_files//Product//AllPlatfomswithDifferentCategory.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	public String[] GetCSVData(String data) {
		String[] prodID = null;
		try {
			Iterator<String[]> itr = supplyData();
			while (itr.hasNext()) {
				String[] prod = itr.next();
				for (int i = 0; i < prod.length; i++) {
					if (prod[i].equals(data))
						for (int j = i + 1; j < prod.length; j++) {
							prodID = prod;
						}
				}
			}
		} catch (IOException e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}

		return prodID;
	}

	@Test
	public void getRequestWithOutQueryParameter() {

		// API returns all parameters including relateditems and coloroptions.
		log.info("*********************************Get Request WithOut Query Parameter*****************************");
		try {
			JSONObject jsonResponse_category = JunoV1Util
					.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "desktop");
			product = ProductUtil.getProductDetails(jsonResponse_category, "single_vision",
					Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
			restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
					+ product.get("productId");
			log.info("API path is :: " + restURL);
			List<NameValuePair> header = new ArrayList<NameValuePair>();
			header.add(new BasicNameValuePair("Content-Type", "application/json"));
			header.add(new BasicNameValuePair("X-Api-Client", "android"));
			HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
					"Response is not proper for" + restURL);
			JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			JSONObject result1 = (JSONObject) respJsonObjectproduct.get("result");
			Assert.assertTrue(result1.has("colorOptions"), "Color Options are NOT present");
			Assert.assertTrue(result1.has("relatedItems"), "Related Items are NOT present");
		} catch (Exception e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}

	}

	@Test(dataProvider = "EyeglassValidate")
	@SuppressWarnings("unchecked")
	public void getRequestVerifyFieldsEyeglassesWithOutQueryParameter(String testcase, String category,
			String powerType, String XApiClient) {
		// Verifying all parameters without view summary from SOLR DB.
		// for Eyeglasses and Sunglasses only.
		log.info("*********************************Eyeglasses*****************************");
		if (solrValidationFlag) {

			ArrayList<String> list = null;
			try {
				JSONObject jsonResponse_category = JunoV1Util
						.getCategoryDetails(ApplicationUtil.getcategoryId(category), XApiClient);
				product = ProductUtil.getProductDetails(jsonResponse_category, powerType, Boolean.parseBoolean("FALSE"),
						Boolean.parseBoolean("FALSE"));
				log.info(product.get("productId"));
				restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
						+ product.get("productId");

				log.info("API path is :: " + restURL);
				List<NameValuePair> header = new ArrayList<NameValuePair>();
				header.add(new BasicNameValuePair("Content-Type", "application/json"));
				header.add(new BasicNameValuePair("X-Api-Client", XApiClient));
				HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
				Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
						"Response is not proper for" + restURL);
				JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
				JSONObject result1 = (JSONObject) respJsonObjectproduct.get("result");
				JSONArray SolrResultArr = solrCategoryConnectionObject.querySolrDB("product_id",
						product.get("productId").toString());
				JSONObject docResult = SolrResultArr.getJSONObject(0);
				log.info("SOLR docResult" + docResult);
				Assert.assertEquals(result1.getString("url"), docResult.get("product_url"),
						"product_url mismatch for " + product.get("productId"));
				JSONArray arr = JsonUtil.jsonObjectToJsonArray(result1, "prices");
				Assert.assertEquals(Float.valueOf(JsonUtil.getJsonArrayData(arr, "Lenskart Price", "name", "price")),
						docResult.get("lenskart_price"), "lenskart_price mismatch for " + product.get("productId"));
				Assert.assertEquals(Float.valueOf(JsonUtil.getJsonArrayData(arr, "Market Price", "name", "price")),
						docResult.get("market_price"), "market_price mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("brandName"), docResult.get("brandname"),
						"brandname mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("sku"), docResult.get("sku"),
						"sku mismatch for " + product.get("productId"));
				arr = JsonUtil.jsonObjectToJsonArray(result1, "specifications");
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Model No.", "name", "value"),
						docResult.get("model_no"), "model_no mismatch for " + product.get("productId"));

				if (docResult.has("frame_shape")) {
					Assert.assertEquals(
							JsonUtil.getJsonArrayDataArray(arr, "items", "technical", "Frame Shape", "name", "value"),
							docResult.get("frame_shape"), "frame_shape mismatch for " + product.get("productId"));
				}
				Assert.assertEquals(result1.getString("seoMetaDescription"), docResult.get("meta_description"),
						"meta_description mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("seoTitle"), docResult.get("meta_title"),
						"meta_title  mismatch for " + product.get("productId"));
				Assert.assertEquals(Long.parseLong(result1.getString("id")), docResult.getLong("product_id"),
						"Product ID  mismatch for " + product.get("productId"));
				if (result1.has("offerImage"))
					Assert.assertEquals(result1.getString("offerImage"), docResult.get("offer_image"),
							"offer_image mismatch for " + product.get("productId"));
				else
					Assert.assertTrue(true, "offer image is not present for" + product.get("productId"));
				if (docResult.has("frame_material"))
					Assert.assertEquals(
							JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Frame Material", "name", "value"),
							docResult.get("frame_material"), "frame_material mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Frame Size", "name", "value"),
						docResult.get("frame_size"), "frame_size mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "technical", "Product Type", "name", "value"),
						docResult.get("product_type"), "product_type mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "technical", "Frame Type", "name", "value"),
						docResult.get("frametype"), "frametype mismatch for " + product.get("productId"));
				Assert.assertEquals(JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Gender", "name", "value"),
						docResult.get("gender"), "gender mismatch for " + product.get("productId"));
				if (result1.has("tryOnImage"))
					Assert.assertEquals(result1.getString("isTryNowAvailable"), "true",
							"isTryNowAvailable mismatch for " + product.get("productId"));
				else
					Assert.assertEquals(result1.getString("isTryNowAvailable"), "false",
							"isTryNowAvailable mismatch for " + product.get("productId"));
				if (docResult.has("number_of_reviews"))
					Assert.assertEquals(Integer.valueOf(result1.getString("numberOfReviews")),
							docResult.get("number_of_reviews"),
							"number_of_reviews  mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getInt("qty"), (docResult.getInt("qty")),
						"qty  mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("fullName"), docResult.get("product_name"),
						"product_name mismatch for " + product.get("productId"));
				// Assert.assertEquals(result1.get("classification").toString().toLowerCase(),
				// docResult.get("c2c_classification_value").toString().toLowerCase(),
				// "classification mismatch for "+product.get("productId"));
				if (docResult.has("color_options")) {
					list = (ArrayList<String>) docResult.get("color_options");
					if (!list.isEmpty()) {
						ArrayList<String> templist = new ArrayList<String>();
						for (int i = 0; i < list.size(); i++) {
							JSONObject temp = new JSONObject(list.get(i));
							if (!temp.get("color").toString().isEmpty())
								templist.add(temp.get("id").toString());

						}

						list.clear();
						list.addAll(templist);
						log.info("list of colorOptions before checking Availability of PID's:" + list);
						System.out.println("list of colorOptions before checking Availability of PID's:" + list);

						list = ProductUtil.checkProductAvailability(list);

						log.info("list of colorOptions after checking Availability of PID's:" + list);
						System.out.println("list of colorOptions after checking Availability of PID's:" + list);

						Assert.assertEquals(result1.getJSONArray("colorOptions").length(), list.size(),
								"colorOptions size mismatch for " + product.get("productId"));
					} else
						Assert.assertEquals(result1.getJSONArray("colorOptions").length(), 0,
								"colorOptions size mismatch for " + product.get("productId"));

				}
				if (docResult.has("headturn_male_composite"))
					Assert.assertEquals(result1.getString("headturnMale"), docResult.get("headturn_male_composite"),
							"headturnMale mismatch for " + product.get("productId"));
				if (docResult.has("headturn_female_composite"))
					Assert.assertEquals(result1.getString("headturnFemale"), docResult.get("headturn_female_composite"),
							"headturnFemale mismatch for " + product.get("productId"));
				if (docResult.has("product_offer_image"))
					Assert.assertEquals(result1.getString("offerBanner"), docResult.get("product_offer_image"),
							"product_offer_image mismatch for " + product.get("productId"));
				if (docResult.has("avg_rating"))
					Assert.assertEquals(Float.valueOf(result1.getString("avgRating")), docResult.get("avg_rating"),
							"avg_rating mismatch for " + product.get("productId"));
				if (docResult.has("total_ratings"))
					Assert.assertEquals(Integer.valueOf(result1.getString("totalNoOfRatings")),
							docResult.get("total_ratings"), "total_ratings mismatch for " + product.get("productId"));
				Assert.assertTrue(
						result1.getString("thumbnailImage").contains((CharSequence) docResult.get("thumbnail_image")),
						"thumbnail_image mismatch for " + product.get("productId"));
				String img_url = null;
				if (XApiClient.equalsIgnoreCase("Desktop"))
					img_url = "desktop_image_urls";
				else
					img_url = "mobile_image_urls";
				list = (ArrayList<String>) docResult.get(img_url);
				Set<String> hs = new HashSet<>();
				hs.addAll(list);
				list.clear();
				list.addAll(hs);
				if (result1.has("imageUrlsDetail"))
					Assert.assertEquals(result1.getJSONArray("imageUrlsDetail").length(), list.size(),
							"imgUrl size mismatch for " + product.get("productId"));
				list = (ArrayList<String>) docResult.get("related_products");
				log.info("list of related_products before checking Availability of PID's:" + list);
				System.out.println("list of related_products before checking Availability of PID's:" + list);

				list = ProductUtil.checkProductAvailability(list);

				log.info("list of related_products after checking Availability of PID's:" + list);
				System.out.println("list of related_products after checking Availability of PID's:" + list);
				Assert.assertEquals(result1.getJSONArray("relatedItems").length(), list.size(), "ProductID"
						+ product.get("productId") + "relatedItems size mismatch for " + product.get("productId"));
				// Integer i1= (Integer) docResult.get("is_ditto");
				if (docResult.has("cross_sells")) {
					list = (ArrayList<String>) docResult.get("cross_sells");
					Assert.assertEquals(result1.getJSONArray("crossSells").length(), list.size(),
							"imgUrl size mismatch for " + product.get("productId"));
				}

			} catch (Exception e) {
				Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
				e.printStackTrace();
			}

		}
	}

	@Test(dataProvider = "SunglassValidate")
	@SuppressWarnings("unchecked")
	public void getRequestVerifyFieldsSunglassesWithOutQueryParameter(String testcase, String category,
			String powerType, String XApiClient) {
		// PropertyFactory pf = new PropertyFactory("Database");
		// Verifying all parameters without view summary from SOLR DB.
		// for Eyeglasses and Sunglasses only.
		log.info("*********************************Sunglasses*****************************");
		if (solrValidationFlag) {
			ArrayList<String> list = null;
			String flag = null;

			try {

				JSONObject jsonResponse_category = JunoV1Util
						.getCategoryDetails(ApplicationUtil.getcategoryId(category), XApiClient);
				product = ProductUtil.getProductDetails(jsonResponse_category, powerType, Boolean.parseBoolean("FALSE"),
						Boolean.parseBoolean("FALSE"));
				log.info(product.get("productId"));
				restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
						+ product.get("productId");
				log.info("API path is :: " + restURL);
				List<NameValuePair> header = new ArrayList<NameValuePair>();
				header.add(new BasicNameValuePair("Content-Type", "application/json"));
				header.add(new BasicNameValuePair("X-Api-Client", XApiClient));
				HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
				Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
						"Response is not proper for" + restURL);
				JSONObject respJsonObjectProduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
				JSONObject result1 = (JSONObject) respJsonObjectProduct.get("result");

				JSONArray SolrResultArr = solrCategoryConnectionObject.querySolrDB("product_id",
						product.get("productId").toString());
				JSONObject docResult = SolrResultArr.getJSONObject(0);
				log.info("SOLR docResult:" + docResult);
				Assert.assertEquals(result1.getString("url"), docResult.get("product_url"),
						"product_url mismatch for " + product.get("productId"));
				JSONArray arr = JsonUtil.jsonObjectToJsonArray(result1, "prices");
				Assert.assertEquals(Float.valueOf(JsonUtil.getJsonArrayData(arr, "Lenskart Price", "name", "price")),
						docResult.get("lenskart_price"), "lenskart_price mismatch for " + product.get("productId"));
				Assert.assertEquals(Float.valueOf(JsonUtil.getJsonArrayData(arr, "Market Price", "name", "price")),
						docResult.get("market_price"), "market_price mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("brandName"), docResult.get("brandname"),
						"brandname mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("sku"), docResult.get("sku"),
						"sku mismatch for " + product.get("productId"));
				arr = JsonUtil.jsonObjectToJsonArray(result1, "specifications");
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Model No.", "name", "value"),
						docResult.get("model_no"), "model_no mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "technical", "Frame Shape", "name", "value"),
						docResult.get("frame_shape"), "frame_shape mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("seoMetaDescription"), docResult.get("meta_description"),
						"meta_description mismatch for " + product.get("productId"));
				Assert.assertEquals(Long.parseLong(result1.getString("id")), docResult.getLong("product_id"),
						"product_id  mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("offerImage"), docResult.get("offer_image"),
						"offer_image mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Frame Material", "name", "value"),
						docResult.get("frame_material"), "frame_material mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Frame Size", "name", "value"),
						docResult.get("frame_size"), "frame_size mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "technical", "Product Type", "name", "value"),
						docResult.get("product_type"), "product_type mismatch for " + product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "technical", "Frame Type", "name", "value"),
						docResult.get("frametype"), "frametype mismatch for " + product.get("productId"));
				// Assert.assertEquals(JsonUtil.getJsonArrayDataArray(arr,"items","technical","Gender","name","value"),
				// docResult.get("gender"), "gender mismatch for
				// "+product.get("productId"));
				Assert.assertEquals(result1.get("classification"), docResult.get("c2c_classification_value"),
						"classification mismatch for " + product.get("productId"));
				if (result1.has("tryOnImage"))
					Assert.assertEquals(result1.getString("isTryNowAvailable"), "true",
							"isTryNowAvailable mismatch for " + product.get("productId"));
				else
					Assert.assertEquals(result1.getString("isTryNowAvailable"), "false",
							"isTryNowAvailable mismatch for " + product.get("productId"));

				if (docResult.has("color_options")) {
					list = (ArrayList<String>) docResult.get("color_options");
					Assert.assertEquals(result1.getJSONArray("colorOptions").length(), list.size(),
							"colorOptions size mismatch for " + product.get("productId"));
				} else
					Assert.assertEquals(result1.getJSONArray("colorOptions").length(), 0,
							"relatedItems size mismatch for " + product.get("productId"));

				if (docResult.has("number_of_reviews"))
					Assert.assertEquals(Integer.valueOf(result1.getString("numberOfReviews")),
							docResult.get("number_of_reviews"),
							"number_of_reviews  mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("seoTitle"), docResult.get("meta_title"),
						"meta_title  mismatch for " + product.get("productId"));
				Assert.assertEquals(Float.valueOf(result1.getString("qty")), docResult.get("qty"),
						"qty  mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("fullName"), docResult.get("product_name"),
						"product_name mismatch for " + product.get("productId"));
				if (docResult.has("headturn_male_composite"))
					Assert.assertEquals(result1.getString("headturnMale"), docResult.get("headturn_male_composite"),
							"headturnMale mismatch for " + product.get("productId"));
				if (docResult.has("headturn_female_composite"))
					Assert.assertEquals(result1.getString("headturnFemale"), docResult.get("headturn_female_composite"),
							"headturnFemale mismatch for " + product.get("productId"));
				if (docResult.has("product_offer_image"))
					Assert.assertEquals(result1.getString("offerBanner"), docResult.get("product_offer_image"),
							"product_offer_image mismatch for " + product.get("productId"));
				if (docResult.has("avg_rating"))
					Assert.assertEquals(Float.valueOf(result1.getString("avgRating")), docResult.get("avg_rating"),
							"avg_rating mismatch for " + product.get("productId"));

				if (docResult.has("total_ratings"))
					Assert.assertEquals(Integer.valueOf(result1.getString("totalNoOfRatings")),
							docResult.get("total_ratings"), "total_ratings mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("seoMetaDescription"), docResult.get("meta_description"),
						"Product ID mismatch for " + product.get("productId"));
				Assert.assertTrue(
						result1.getString("thumbnailImage").contains((CharSequence) docResult.get("thumbnail_image")),
						"thumbnail_image mismatch for " + product.get("productId"));
				String img_url = null;
				if (XApiClient.equalsIgnoreCase("Desktop"))
					img_url = "desktop_image_urls";
				else
					img_url = "mobile_image_urls";
				list = (ArrayList<String>) docResult.get(img_url);
				Assert.assertEquals(result1.getJSONArray("imageUrlsDetail").length(), list.size(),
						"imgUrl size mismatch for " + product.get("productId"));
				if (docResult.has("related_products")) {
					list = (ArrayList<String>) docResult.get("related_products");
					log.info("list of related_products before checking Availability of PID's:" + list);
					System.out.println("list of related_products before checking Availability of PID's:" + list);

					list = ProductUtil.checkProductAvailability(list);

					log.info("list of related_products after checking Availability of PID's:" + list);
					System.out.println("list of related_products after checking Availability of PID's:" + list);

					if (result1.has("relatedItems"))
						Assert.assertEquals(result1.getJSONArray("relatedItems").length(), list.size(),
								"imgUrl size mismatch for " + product.get("productId"));
				}
				// redis Validation
				Integer i1 = (Integer) docResult.get("is_ditto");
				/*
				 * if(i1==0) { flag="false";
				 * 
				 * //Assert.assertEquals(result1.getString("isDittoEnabled"),
				 * flag, "DittoEnabled mismatch for "+product.get("productId"));
				 * 
				 * } if(i1==1) { flag="true";
				 * Assert.assertEquals(result1.getString("isDittoEnabled"),
				 * flag, "DittoEnabled mismatch for "+product.get("productId"));
				 * }
				 */
				if (docResult.has("cross_sells")) {
					list = (ArrayList<String>) docResult.get("cross_sells");
					Assert.assertEquals(result1.getJSONArray("crossSells").length(), list.size(),
							"imgUrl size mismatch for " + product.get("productId"));
				}
				if (docResult.has("is_virtual_try_on")) {
					i1 = (Integer) docResult.get("is_virtual_try_on");
					if (i1 == 0) {
						flag = "false";
						Assert.assertEquals(result1.getString("isTryNowAvailable"), flag, "TryNowAvailable mismatch");
					}
					if (i1 == 1) {
						flag = "true";
						Assert.assertEquals(result1.getString("isTryNowAvailable"), flag, "TryNowAvailable mismatch");
					}
				}
			} catch (Exception e) {
				Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
				e.printStackTrace();
			}

		}

	}

	@SuppressWarnings("unchecked")
	@Test(dataProvider = "ContactLensValidate")
	public void getRequestVerifyFieldsContactLensWithOutQueryParameter(String testcase, String category,
			String XApiClient) {
		// Verifying all parameters without view summary from SOLR DB.
		// for Eyeglasses and Sunglasses only.
		log.info("*********************************Contact Lens*****************************");
		if (solrValidationFlag) {
			ArrayList<String> list = null;
			String flag = null;

			try {
				JSONObject jsonResponse_category = JunoV1Util
						.getCategoryDetails(ApplicationUtil.getcategoryId(category), XApiClient);
				product = ProductUtil.getProductDetails(jsonResponse_category, "CONTACT_LENS",
						Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
				log.info(product.get("productId"));
				restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
						+ product.get("productId");
				log.info("API path is :: " + restURL);
				List<NameValuePair> header = new ArrayList<NameValuePair>();
				header.add(new BasicNameValuePair("Content-Type", "application/json"));
				header.add(new BasicNameValuePair("X-Api-Client", "android"));
				HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);

				Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
						"Response is not proper for" + restURL);
				JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
				JSONObject result1 = (JSONObject) respJsonObjectproduct.get("result");

				JSONArray SolrResultArr = solrCategoryConnectionObject.querySolrDB("product_id",
						product.get("productId").toString());
				JSONObject docResult = SolrResultArr.getJSONObject(0);
				log.info("SOLR docResult:" + docResult);
				Assert.assertEquals(result1.getString("url"), docResult.get("product_url"),
						"product_url mismatch for " + product.get("productId"));
				JSONArray arr = JsonUtil.jsonObjectToJsonArray(result1, "prices");
				Assert.assertEquals(Float.valueOf(JsonUtil.getJsonArrayData(arr, "Lenskart Price", "name", "price")),
						docResult.get("lenskart_price"), "lenskart_price mismatch for " + product.get("productId"));
				Assert.assertEquals(Float.valueOf(JsonUtil.getJsonArrayData(arr, "Market Price", "name", "price")),
						docResult.get("market_price"), "market_price mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("brandName"), docResult.get("brandname"),
						"brandname mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("sku"), docResult.get("sku"), "sku mismatch.");
				Assert.assertEquals(Long.parseLong(result1.getString("id")), docResult.getLong("product_id"),
						"Product ID mismatch for " + product.get("productId"));
				if (result1.has("seoTitle"))
					Assert.assertEquals(result1.getString("seoTitle"), docResult.get("meta_title"),
							"meta_title mismatch for " + product.get("productId"));
				arr = JsonUtil.jsonObjectToJsonArray(result1, "specifications");
				// Assert.assertEquals(JsonUtil.getJsonArrayDataArray(arr,"items","technical","Gender","name","value"),
				// docResult.get("gender"), "Gender mismatch for
				// "+product.get("productId"));
				Assert.assertEquals(
						JsonUtil.getJsonArrayDataArray(arr, "items", "technical", "Product Type", "name", "value"),
						docResult.get("product_type"), "product_type mismatch for " + product.get("productId"));
				Assert.assertEquals(Float.valueOf(result1.getString("qty")), docResult.get("qty"),
						"qty  mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("fullName"), docResult.get("product_name"),
						"product_name mismatch for " + product.get("productId"));
				if (docResult.has("product_offer_image"))
					Assert.assertEquals(result1.getString("offerBanner"), docResult.get("product_offer_image"),
							"product_offer_image mismatch for " + product.get("productId"));
				Assert.assertEquals(result1.getString("seoMetaDescription"), docResult.get("meta_description"),
						"Product ID mismatch for " + product.get("productId"));
				if (docResult.has("contact_lens_material"))
					Assert.assertEquals(
							JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Lens Material", "name", "value"),
							docResult.get("contact_lens_material"),
							"contact_lens_material mismatch for " + product.get("productId"));

				if (docResult.has("contact_packaging"))
					Assert.assertEquals(
							JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Packaging", "name", "value"),
							docResult.get("contact_packaging"),
							"contact_packaging mismatch for " + product.get("productId"));

				if (docResult.has("contact_usage_duration"))
					Assert.assertEquals(
							JsonUtil.getJsonArrayDataArray(arr, "items", "general", "Usage Duration", "name", "value"),
							docResult.get("contact_usage_duration"),
							"contact_usage_duration mismatch for " + product.get("productId"));

				if (docResult.has("c2c_classification_value"))
					Assert.assertEquals(result1.getString("classification"), docResult.get("c2c_classification_value"),
							"classification mismatch for " + product.get("productId"));
				String img_url = null;
				if (XApiClient.equalsIgnoreCase("Desktop"))
					img_url = "desktop_image_urls";
				else
					img_url = "mobile_image_urls";
				list = (ArrayList<String>) docResult.get(img_url);
				Assert.assertEquals(result1.getJSONArray("imageUrls").length(), list.size(),
						"imgUrl size mismatch for " + product.get("productId"));
				list = (ArrayList<String>) docResult.get("related_products");
				log.info("list of related_products before checking Availability of PID's:" + list);
				System.out.println("list of related_products before checking Availability of PID's:" + list);

				list = ProductUtil.checkProductAvailability(list);

				log.info("list of related_products after checking Availability of PID's:" + list);
				System.out.println("list of related_products after checking Availability of PID's:" + list);
				Assert.assertEquals(result1.getJSONArray("relatedItems").length(), list.size(),
						"PID" + product.get("productId") + "relatedItems size mismatch for " + list);
				if (docResult.has("cross_sells")) {
					list = (ArrayList<String>) docResult.get("cross_sells");
					Assert.assertEquals(result1.getJSONArray("crossSells").length(), list.size(),
							"imgUrl size mismatch for " + product.get("productId"));
				}
				if (result1.has("tryOnImage"))
					Assert.assertEquals(result1.getString("isTryNowAvailable"), "true",
							"isTryNowAvailable mismatch for " + product.get("productId"));
				else
					Assert.assertEquals(result1.getString("isTryNowAvailable"), "false",
							"isTryNowAvailable mismatch for " + product.get("productId"));
				Assert.assertTrue(
						result1.getString("thumbnailImage").contains((CharSequence) docResult.get("thumbnail_image")),
						"thumbnail_image mismatch for " + product.get("productId"));
				// Redis Validation
				/*
				 * Integer i1= (Integer) docResult.get("is_ditto"); if(i1==0) {
				 * flag="false";
				 * Assert.assertEquals(result1.getString("isDittoEnabled"),
				 * flag, "DittoEnabled mismatch for "+product.get("productId"));
				 * } if(i1==1) { flag="true";
				 * Assert.assertEquals(result1.getString("isDittoEnabled"),
				 * flag, "DittoEnabled mismatch for "+product.get("productId"));
				 * }
				 * 
				 * i1= (Integer) docResult.get("is_virtual_try_on"); if(i1==0) {
				 * flag="false";
				 * Assert.assertEquals(result1.getString("isTryNowAvailable"),
				 * flag, "TryNowAvailable mismatch for "
				 * +product.get("productId")); } if(i1==1) { flag="true";
				 * Assert.assertEquals(result1.getString("isTryNowAvailable"),
				 * flag, "TryNowAvailable mismatch for "
				 * +product.get("productId")); }
				 */

			} catch (Exception e) {
				// TODO Auto-generated catch block
				Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
				e.printStackTrace();

			}

		}
	}

	private ArrayList<String> checkProductAvailability(ArrayList<String> list) {
		// TODO Auto-generated method stub

		try {

			for (int i = 0; i < list.size(); i++) {
				restURL = (Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
						+ String.valueOf(list.get(i)));
				List<NameValuePair> params = new ArrayList<NameValuePair>();
				params.add(new BasicNameValuePair("view", "summary"));
				List<NameValuePair> header = new ArrayList<NameValuePair>();
				header.add(new BasicNameValuePair("Content-Type", "application/json"));
				header.add(new BasicNameValuePair("X-Api-Client", "android"));
				HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, params);
				if (httpresponse.getStatusLine().getStatusCode() != 200) {
					list.remove(list.get(i));
					i--;
				}
			}
		} catch (Exception e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}
		return list;
	}

	@Test
	public void getRequestWithQueryParameter() {
		// In summary view API returns all parameters except relateditems and
		// coloroptions.
		System.out
				.println("*********************************Get Request With Query Parameter**************************");
		try {
			JSONObject jsonResponse_category = JunoV1Util
					.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "desktop");
			product = ProductUtil.getProductDetails(jsonResponse_category, "single_vision",
					Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
			restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
					+ product.get("productId");
			log.info("APIwee path is :: " + restURL);
			List<NameValuePair> params = new ArrayList<NameValuePair>();
			params.add(new BasicNameValuePair("view", "summary"));
			List<NameValuePair> header = new ArrayList<NameValuePair>();
			header.add(new BasicNameValuePair("Content-Type", "application/json"));
			header.add(new BasicNameValuePair("X-Api-Client", "android"));
			HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, params);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
					"Response is not proper for" + restURL);
			JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			JSONObject result1 = (JSONObject) respJsonObjectproduct.get("result");
			Assert.assertFalse(result1.has("colorOptions"), "Color Options are present");
			Assert.assertFalse(result1.has("relatedItems"), "Related Items are present");
		} catch (Exception e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}

	}

	@Test
	public void getRequestWithValidProductID() {
		// With Valid Product ID.
		log.info("*********************************Get Request With Valid Product ID**************************");
		try {
			JSONObject jsonResponse_category = JunoV1Util
					.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "desktop");
			product = ProductUtil.getProductDetails(jsonResponse_category, "single_vision",
					Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
			restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
					+ product.get("productId");
			log.info("API path is :: " + restURL);
			List<NameValuePair> header = new ArrayList<NameValuePair>();
			header.add(new BasicNameValuePair("Content-Type", "application/json"));
			header.add(new BasicNameValuePair("X-Api-Client", "android"));
			log.info(header);
			HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);

			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response is not proper" + restURL);

		} catch (Exception e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}

	}

	@Test
	public void getRequestWithInValidProductID() {
		// With Invalid Product ID..
		log.info("*********************************Get Request With InValid Product ID**************************");
		try {
			JSONObject jsonResponse_category = JunoV1Util
					.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "desktop");
			product = ProductUtil.getProductDetails(jsonResponse_category, "single_vision",
					Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
			restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
					+ product.get("productId") + "@1";
			log.info("API path is :: " + restURL);
			List<NameValuePair> header = new ArrayList<NameValuePair>();
			header.add(new BasicNameValuePair("Content-Type", "application/json"));
			header.add(new BasicNameValuePair("X-Api-Client", "android"));
			HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 404,
					"Response is not proper for" + restURL);
		} catch (Exception e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}

	}

	@Test
	public void getRequestWithNOProductID() {
		// With No Product ID.
		log.info("*********************************Get Request With No Product ID**************************");
		try {

			JSONObject jsonResponse_category = JunoV1Util
					.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "desktop");
			product = ProductUtil.getProductDetails(jsonResponse_category, "single_vision",
					Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
			restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH;
			log.info("API path is :: " + restURL);
			List<NameValuePair> header = new ArrayList<NameValuePair>();
			header.add(new BasicNameValuePair("Content-Type", "application/json"));
			header.add(new BasicNameValuePair("X-Api-Client", "android"));
			HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 500,
					"Response is not proper for" + restURL);

		} catch (Exception e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}

	}

	@Test
	public void getRequestWithInValidHeaderData() {
		// With No Header parameters.
		log.info("*********************************Get Request With InValid Header Data**************************");
		try {
			JSONObject jsonResponse_category = JunoV1Util
					.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), "desktop");
			product = ProductUtil.getProductDetails(jsonResponse_category, "single_vision",
					Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
			restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
					+ product.get("productId");
			log.info("API path is :: " + restURL);
			List<NameValuePair> header = new ArrayList<NameValuePair>();
			header.add(new BasicNameValuePair("Content-Type", "xxxx"));
			header.add(new BasicNameValuePair("X-Api-Client", "xx"));
			HttpResponse httpresponse = RequestUtil.getRequest(restURL, header);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 400,
					"Response is not proper for" + restURL);
			// Assert.a
		} catch (Exception e) {
			Assert.assertFalse(true, "TestCase Failed" + e.getMessage());
			e.printStackTrace();
		}

	}

	@Test(enabled = true, dataProvider = "Buypackagefordifferentcategory")
	public void testcase_for_minLensPrice(String categoryId, String apiClient) throws Exception {
		String sessionToken = SessionUtil.createNewSession();

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryId),
				ApplicationConstants.XApiClient.ANDROID);
		product = ProductUtil.getProductDetails(jsonResponse_category, "single_vision", Boolean.parseBoolean("FALSE"),
				Boolean.parseBoolean("FALSE"));
		restURL = String.format(Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.GET_BUY_PACKAGE_API,
				product.get("productId"));

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", apiClient));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
		if (httpresponse.getStatusLine().getStatusCode() == 200) {
			JSONObject responseOfBuyPackage = RequestUtil.convertHttpResponseToJsonObject(httpresponse);

			log.info("responseOfBuyPackage: " + responseOfBuyPackage);
			JSONArray packagesInBuypackageAPI;

			double minvalue = responseOfBuyPackage.getJSONArray("results").getJSONObject(0).getJSONArray("prices")
					.getJSONObject(1).getDouble("price");
			for (int j = 0; j < responseOfBuyPackage.getJSONArray("results").length(); j++) {
				packagesInBuypackageAPI = responseOfBuyPackage.getJSONArray("results").getJSONObject(j)
						.getJSONArray("packages");
				for (int i = 0; i < packagesInBuypackageAPI.length(); i++) {
					 double arrayValues = packagesInBuypackageAPI.getJSONObject(i).getJSONArray("prices")
							.getJSONObject(1).getDouble("price");
					if (arrayValues <= minvalue) {
						minvalue = arrayValues;
						log.info("***********:"+minvalue);
					}

				}
			}

			log.info(minvalue);


			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
			restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH
					+ product.get("productId");
			httpresponse = RequestUtil.getRequest(restURL, header, null);
			JSONObject responseOfProduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			log.info("responseOfProduct: " + responseOfProduct);
			log.info("************:"+ responseOfProduct.getJSONObject("result").getString("minLensPrice"));
			Assert.assertEquals(responseOfProduct.getJSONObject("result").getString("minLensPrice"), minvalue,
					"minimun value mismatch");
		} else {
			log.info("print http response:" + httpresponse.getStatusLine().getStatusCode());

			
		}
	}

	@Test(dataProvider = "AllPlatformsWithCategory")
	public void checkFramecolorInSpecifications_and_checkColourInFrameDetails(String categoryid, String XApiClient)
			throws Exception {
		String queryResult = null;
		String queryResult_c2c_frame_colour = null;
		String specifications_frame_color_value = null;
		JSONArray SolrResultArr = null;

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryid, XApiClient);
		log.info("!!!!!!!" + jsonResponse_category);
		String product = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH + product;
		log.info("API path is :: " + restURL);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", XApiClient));
		HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response is not proper for" + restURL);
		JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		if (categoryid.equals("3194") || categoryid.equals("2852") || categoryid.equals("7163")) {
			String frameDetails_colour = respJsonObjectproduct.getJSONObject("result").getJSONArray("frameDetails")
					.getJSONObject(2).getString("value");

			specifications_frame_color_value = respJsonObjectproduct.getJSONObject("result")
					.getJSONArray("specifications").getJSONObject(0).getJSONArray("items").getJSONObject(3)
					.getString("value");

			if (solrValidationFlag) {
				SolrResultArr = solrCategoryConnectionObject.querySolrDB("product_id", product);

				queryResult = SolrResultArr.getJSONObject(0).getString("frame_color");
				queryResult_c2c_frame_colour = SolrResultArr.getJSONObject(0).getString("c2c_frame_colour");

				Assert.assertEquals(frameDetails_colour, queryResult);
				Assert.assertEquals(specifications_frame_color_value, queryResult_c2c_frame_colour);
			} else {
				Assert.assertNotEquals(frameDetails_colour, "");
				Assert.assertNotNull(frameDetails_colour);
				Assert.assertNotEquals(specifications_frame_color_value, "");
				Assert.assertNotNull(specifications_frame_color_value);
			}

		}
	}

	@Test
	public void check_HTTPS_urls_are_coming_for_colorOptions() throws URISyntaxException, Exception {
		String specification_urlDetails_url = null;
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ProductCategories.SUNGLASSES,
				XApiClient.ANDROID);
		String product = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH + product;
		log.info("API path is :: " + restURL);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", "desktop"));
		HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
		Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200, "Response is not proper for" + restURL);
		JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		JSONArray allimageURL = respJsonObjectproduct.getJSONObject("result").getJSONArray("colorOptions");
		String seoMetaCanonical = respJsonObjectproduct.getJSONObject("result").getString("seoMetaCanonical");
		String seoMetaAlternate = respJsonObjectproduct.getJSONObject("result").getString("seoMetaAlternate");
		JSONArray specifications_array = respJsonObjectproduct.getJSONObject("result").getJSONArray("specifications");
		for (int i = 0; i < specifications_array.length(); i++) {
			JSONArray specifications_items = specifications_array.getJSONObject(i).getJSONArray("items");
			for (int j = 0; j < specifications_items.length(); j++) {
				JSONObject specification_urlDetails = specifications_items.getJSONObject(j);
				if (specification_urlDetails.has("urlDetails")) {
					specification_urlDetails_url = specification_urlDetails.getJSONObject("urlDetails")
							.getString("url");
					Assert.assertEquals(specification_urlDetails_url.substring(0, 5), "https");
				}
			}
		}
		for (int i = 0; i < allimageURL.length(); i++) {
			String imageURL_to_String = allimageURL.getJSONObject(i).getJSONArray("imageUrls").toString().substring(2,
					7);
			Assert.assertEquals(imageURL_to_String, "https");
		}
		Assert.assertEquals(seoMetaCanonical.substring(0, 5), "https");
		Assert.assertEquals(seoMetaAlternate.substring(0, 5), "https");
	}

	
	
	/**
	 * In the below test case visibility 1 = Not visible Individually
	 * 2 = Catalog
	 * 3 = Search
	 * 4 = Catalog, Search
	 * 5 = Visible for POS
	 * 6 = Defective Products
	 * @throws Exception
	 * @throws IOException
	 */
	@Test
	public void test_all_visibility_cases() throws Exception, IOException {
		JSONArray SolrResultArr = null;
		List<String> X_Api_Client = Arrays.asList("Desktop", "android", "ios", "mobilesite", "pos");
		if (solrValidationFlag) {
			for (int i = 1; i < 7; i++) {
				SolrResultArr = solrCategoryConnectionObject.querySolrDB("visibility", Integer.toString(i));
				int product_id = SolrResultArr.getJSONObject(0).getInt("product_id");
				restURL = Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH + product_id;
				log.info("API path is :: " + restURL);
				for (int j = 0; j < X_Api_Client.size(); j++) {
					List<NameValuePair> header = new ArrayList<NameValuePair>();
					header.add(new BasicNameValuePair("Content-Type", "application/json"));
					header.add(new BasicNameValuePair("X-Api-Client", X_Api_Client.get(j)));
					HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
					JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
					if (Integer.toString(i).equals("4") || Integer.toString(i).equals("3")
							|| Integer.toString(i).equals("2")) {
						Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200);
						Assert.assertEquals(respJsonObjectproduct.getJSONObject("result").getString("id"),
								Integer.toString(product_id));
					} else if ((X_Api_Client.get(j).equals("pos") && Integer.toString(i).equals("1"))
							|| (X_Api_Client.get(j).equals("pos") && Integer.toString(i).equals("5"))
							|| (X_Api_Client.get(j).equals("pos") && Integer.toString(i).equals("6"))) {
						Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200);
						Assert.assertEquals(respJsonObjectproduct.getJSONObject("result").getString("id"),
								Integer.toString(product_id));
					} else {
						Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 404);
						Assert.assertEquals(respJsonObjectproduct.getString("error"), "Product does not exist");
					}
				}
			}
		}
	}
}
