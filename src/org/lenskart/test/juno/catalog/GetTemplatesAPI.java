package org.lenskart.test.juno.catalog;

import java.util.HashMap;

import org.apache.log4j.Logger;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.CatalogServicePathConstants;
import org.lenskart.core.util.CatalogUtil;
import org.lenskart.core.util.HeadersUtil;
import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.RestAssuredUtils;

import io.restassured.response.Response;

public class GetTemplatesAPI {
	private static final Logger log = GenericUtil.InitLogger(GetTemplatesAPI.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;
	public static String XCatalogServiceId1 = "junocatalo";
	private static String XCatalogServiceKey1 = "cWne1BWN6";
	private static String uri = Environments.CATALOG_SERVICE;

	@DataProvider(name = "TemplateTypes")
	public static Object[][] templateTypes() throws Exception {
		return new Object[][] { { "POWER" }, { "PACKAGE" }, { "INVENTORY" }, { "LINKED_PRODUCT" } };
	}

	@Test(priority = 1, dataProvider = "TemplateTypes")
	public void getAllTemplates(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);

		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		log.info("Result = " + response.jsonPath().get("result"));
		log.info("Templates list = " + response.jsonPath().getList("result.templates"));
		log.info("Total Elements = " + response.jsonPath().get("result.totalElements"));
		log.info("Number of Elements Fetched = " + response.jsonPath().get("result.numberOfElements"));
		log.info("Total Pages = " + response.jsonPath().get("result.totalPages"));
		if (response.jsonPath().get("result.numberOfElements").equals(0)) {
			log.info("No Templates found");
		} else {
			Assert.assertTrue(response.jsonPath().getList("result.templates").size() >= 1);
		}
	}

	@Test(priority = 2, dataProvider = "TemplateTypes")
	public void getAllTemplatesWithoutPagination(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);

		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		log.info("Result = " + response.jsonPath().get("result"));
		log.info("Templates list = " + response.jsonPath().getList("result.coatings"));
		log.info("Total Elements = " + response.jsonPath().get("result.totalElements"));
		log.info("Number of Elements Fetched = " + response.jsonPath().get("result.numberOfElements"));
		log.info("Total Pages = " + response.jsonPath().get("result.totalPages"));
		if (response.jsonPath().get("result.numberOfElements").equals(0)) {
			log.info("No Templates found");
		} else {
			Assert.assertTrue(response.jsonPath().getList("result.templates").size() >= 1);
		}
	}

	@Test(priority = 3, dataProvider = "TemplateTypes")
	public void getTemplatesWithWrongServiceId(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), XCatalogServiceId1,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));

	}

	@Test(priority = 4, dataProvider = "TemplateTypes")
	public void getTemplatesWithoutServiceId(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), "",
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));

	}

	@Test(priority = 5, dataProvider = "TemplateTypes")
	public void getTemplatesWithWrongServiceKey(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId, XCatalogServiceKey1),
				queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));

	}

	@Test(priority = 6, dataProvider = "TemplateTypes")
	public void getTemplatesWithoutServiceKey(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId, ""),
				queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));

	}

	@Test(priority = 7, dataProvider = "TemplateTypes")
	public void getTemplatesWithoutServiceKeyAndId(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), "", ""), queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 8, dataProvider = "TemplateTypes")
	public void getTemplatesWithWrongAuthToken(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken() + "1",
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);

		Assert.assertEquals(response.statusCode(), 401, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 9, dataProvider = "TemplateTypes")
	public void getTemplatesWithoutAuthToken(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService("", ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);

		Assert.assertEquals(response.statusCode(), 401, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 10, dataProvider = "TemplateTypes")
	public void getTemplatesWithoutAuthTokenServiceIdAndKey(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService("", "", ""), queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 11, dataProvider = "TemplateTypes")
	public void getTemplatesWithWrongAuthTokenServiceIdAndKey(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken() + "2", XCatalogServiceId1, XCatalogServiceKey1),
				queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 12, dataProvider = "TemplateTypes")
	public void getTemplatesWithWrongServiceIdAndKey(String TemplateTypes) throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", TemplateTypes);

		Response response = RestAssuredUtils.GET(uri,CatalogServicePathConstants.GET_ALL_TEMPLATES_API, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), XCatalogServiceId1, XCatalogServiceKey1),
				queryParams);

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

}
