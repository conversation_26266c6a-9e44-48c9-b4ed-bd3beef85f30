package org.lenskart.test.juno.catalog;

import org.apache.log4j.Logger;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.CatalogServicePathConstants;
import org.lenskart.core.util.CatalogUtil;
import org.lenskart.core.util.HeadersUtil;
import org.testng.Assert;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.RestAssuredUtils;

import io.restassured.response.Response;

public class GetAllUnbxdFiledsAPI {
	private static final Logger log = GenericUtil.InitLogger(GetAllUnbxdFiledsAPI.class);
	public static String XCatalogServiceId1 = "junocatalo";
	private static String XCatalogServiceKey1 = "cWne1BWN4";
	private static String country = "IN";
	private static String uri = Environments.CATALOG_SERVICE;

	@Test(priority = 1)
	public void getAllUnbxdFiledsAPI() throws Exception {
		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);
		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		log.info("Result = " + response.jsonPath().getString("result"));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");

	}

	@Test(priority = 2)
	public void getAllUnbxdFiledsAPIWithWrongServiceId() throws Exception {
		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), XCatalogServiceId1,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 3)
	public void getAllUnbxdFiledsAPIWithWrongServiceKey() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId, XCatalogServiceKey1));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 4)
	public void getAllUnbxdFiledsAPIWithWrongServiceKeyAndId() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), XCatalogServiceId1, XCatalogServiceKey1));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 5)
	public void getAllUnbxdFiledsAPIWithoutServiceId() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL, HeadersUtil.headers_CatalogService(
				CatalogUtil.CatalogLoginToken(), "", ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 6)
	public void getAllUnbxdFiledsAPIWithoutServiceKey() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL, HeadersUtil.headers_CatalogService(
				CatalogUtil.CatalogLoginToken(), ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId, ""));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 7)
	public void getAllUnbxdFiledsAPIWithoutServiceKeyAndId() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), "", ""));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 8)
	public void getAllUnbxdFiledsAPIWithWrongAuthToken() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken() + "2",
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));

		Assert.assertEquals(response.statusCode(), 401, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 9)
	public void getAllUnbxdFiledsAPIWithOutAuthToken() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService("", ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));

		Assert.assertEquals(response.statusCode(), 401, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 10)
	public void getAllUnbxdFiledsAPIWithOutAuthTokenServiceKeYAndId() throws Exception {

		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);

		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService("", "", ""));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

	@Test(priority = 11)
	public void getAllUnbxdFiledsAPIWithWrongServiceIdKeyAndAuthToken() throws Exception {
		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, country);
		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL, HeadersUtil.headers_CatalogService(
				CatalogUtil.CatalogLoginToken() + "1", XCatalogServiceId1, XCatalogServiceKey1));

		Assert.assertEquals(response.statusCode(), 403, "Status code is not correct");
		log.info("Error Message = " + response.jsonPath().getString("message"));
	}

}
