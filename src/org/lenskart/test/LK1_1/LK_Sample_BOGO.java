package org.lenskart.test.LK1_1;

/*
 * import java.io.File; import java.io.FileReader; import java.io.FileWriter;
 * import java.io.IOException; import java.util.ArrayList; import
 * java.util.HashMap; import java.util.Hashtable; import java.util.List; import
 * java.util.Map; import java.util.Vector;
 * 
 * import org.apache.http.NameValuePair; import
 * org.apache.http.message.BasicNameValuePair; import org.apache.log4j.Logger;
 * import org.bson.Document; import org.json.JSONArray; import
 * org.json.JSONObject; import org.lenskart.core.Environments; import
 * org.lenskart.core.constant.ApplicationConstants; import
 * org.lenskart.core.util.ApplicationUtil; import
 * org.lenskart.core.util.CartUtil; import org.lenskart.core.util.CustomerUtil;
 * import org.lenskart.core.util.JunoV1Util; import
 * org.lenskart.core.util.JunoV2Util; import org.lenskart.core.util.OrderUtil;
 * import org.lenskart.core.util.SessionUtil; import org.testng.Assert; import
 * org.testng.annotations.AfterClass; import org.testng.annotations.BeforeClass;
 * import org.testng.annotations.Test;
 * 
 * import com.lenskart.juno.schema.v2.common.Address; import
 * com.opencsv.CSVReader; import com.opencsv.CSVWriter; import
 * com.utilities.GenericUtil; import com.utilities.MongoConnectionUtility;
 * import com.utilities.MySQLConnectionUtility; import
 * com.utilities.PropertyFactory;
 * 
 * import javafx.util.Pair;
 * 
 * public class LK_Sample_BOGO { private static final Logger log =
 * Logger.getLogger(LK_Sample_BOGO.class); private static String x_api_client =
 * ApplicationConstants.XApiClient.ANDROID; private static final boolean
 * mongoValidationFlag = Environments.dbConnectionFlag; private static
 * MongoConnectionUtility mongoConnectionObject; private static
 * MongoConnectionUtility mongoCartConnectionObject = null; private static
 * String created_sessionID = null; private static String created_token = null;
 * private static Hashtable<Integer, String> telephones_map = new
 * Hashtable<Integer, String>(); private static Hashtable<Integer,
 * Pair<String,String>> orderId_map = new Hashtable<Integer,
 * Pair<String,String>>(); private static int map_count_telephone = 0; private
 * static int map_count_orderId = 0; private static MySQLConnectionUtility
 * mysqlConnectionObject = null; private static Hashtable<Integer, String>
 * AllTestCasesMapping = new Hashtable<Integer, String>(); private static
 * JSONObject Final_response_cart = new JSONObject(); private static JSONObject
 * Final_response_order = new JSONObject(); private static String telephone =
 * ""; private static List<NameValuePair> CategoryParam = new
 * ArrayList<NameValuePair>(); private final static int
 * MaxMonthlyLoyalityOrderCount=5; private static String CSV_URL=null; private
 * static String CSV_Name=null; private static String Final_CSV_URL=null;
 * private static int SR_NO=1; private static String GoldPId="128269"; private
 * static String MasterOtp="7352"; private static String
 * OfferName="GOLD,JJEYE,BOGO,ECOBOGO,BLUBOGO";
 * 
 * @BeforeClass public void initDBConnection() throws IOException { if
 * (mongoValidationFlag) { mongoConnectionObject =
 * OrderUtil.getOrderMongoConnectionObject(); mongoCartConnectionObject =
 * CartUtil.getCartMongoConnectionObject();
 * mongoCartConnectionObject.initMongoDBConnection();
 * mongoConnectionObject.initMongoDBConnection(); PropertyFactory pf = new
 * PropertyFactory("MoneyService"); mysqlConnectionObject = new
 * MySQLConnectionUtility(pf); mysqlConnectionObject.initMySQLConnection();
 * 
 * } }
 * 
 * @AfterClass public void closeDBConection() { if (mongoValidationFlag) {
 * mongoConnectionObject.closeMongoConnection();
 * mongoCartConnectionObject.closeMongoConnection();
 * mysqlConnectionObject.closeMySQLConnection(); } }
 * 
 * @BeforeClass public static void CreateFileWithHeaders()throws Exception{
 * CSV_URL="/Users/<USER>/Documents/api-auto-juno/csv_files/GOLD_TEST_CASES/";
 * CSV_Name="test5.csv"; Final_CSV_URL=CSV_URL+CSV_Name;
 * GenericUtil.createNewCSVFile(Final_CSV_URL);
 * GenericUtil.addHeadersCSVFile(Final_CSV_URL,
 * "TEST CASE","PID1","BRAND1","PID2","BRAND2",
 * "SUBTOTAL","DISCOUNT","TAX","TOTAL","APPLIED RULES","ITEM ID1","ITEM ID2"
 * ,"ORDER ID","TELEPHONE","STATE");
 * 
 * 
 * }
 * 
 * @AfterClass public void printTelephones() throws Exception { String
 * state="CANCELED"; String status="CANCELED"; boolean cancellable=false;
 * boolean returnable=false; Vector <String>allOrders=new Vector<String>();
 * Vector<String> allTelephone=new Vector<String>();
 * allOrders=GenericUtil.ReadCSVFile(Final_CSV_URL,12);
 * allTelephone=GenericUtil.ReadCSVFile(Final_CSV_URL,13);
 * log.info("all orders"+allOrders); log.info("all telephone"+allTelephone); int
 * length=allOrders.size(); for(int i=1;i<length;i++) { JSONObject
 * requestBody=SyncStatus.getRequestBodyWithAllItems(allOrders.get(i),
 * allTelephone.get(i),state,status,cancellable,returnable);
 * log.info("requestBody :"+requestBody); JSONObject
 * response=SyncStatus.changeStateAndStatusWithRequestBody(requestBody,allOrders
 * .get(i)); log.info(response); } for(int i=1;i<length;i++) { JSONObject
 * order_details = OrderUtil.getOrder(created_sessionID, x_api_client,
 * allOrders.get(i)); String
 * replace=order_details.getJSONObject("result").getJSONObject("status").
 * getString("state"); String fileToUpdate=Final_CSV_URL; int row, col=14; File
 * inputFile = new File(fileToUpdate); // Read existing file CSVReader reader =
 * new CSVReader(new FileReader(inputFile), ','); List<String[]> csvBody =
 * reader.readAll(); for(int j=1;j<length;j++) { // get CSV row column and
 * replace with by using row and column row=j; csvBody.get(row)[col] = replace;
 * reader.close(); // Write to CSV file which is open CSVWriter writer = new
 * CSVWriter(new FileWriter(inputFile), ','); writer.writeAll(csvBody);
 * writer.flush(); writer.close(); } } //log.info("map of telephone=" +
 * telephones_map); //log.info("map of order id=" + orderId_map);
 * //log.info("final cart response =" + Final_response_cart);
 * //log.info("final order response=" + Final_response_order);
 * 
 * } public static String Create_BOGO_Order(JSONObject
 * add_to_cart_request_body1, JSONObject add_to_cart_request_body2, String
 * telephone) throws Exception { created_token = SessionUtil.createNewSession();
 * JSONObject mobAuth =
 * CustomerUtil.mobileAuthenticateWithoutReferCode(x_api_client, created_token,
 * MasterOtp, telephone); log.info("Mob Auth=" + mobAuth); created_sessionID =
 * mobAuth.getJSONObject("result").getString("token");
 * Assert.assertNotNull(created_sessionID,
 * "Can't find authentication token in result"); JSONObject cart_response1 =
 * CartUtil.createCart(created_sessionID, add_to_cart_request_body1,
 * x_api_client); JSONObject cart_response2 = null; if
 * (add_to_cart_request_body2.length() > 0)// for single gold membership order
 * add_to_cart_request_body2 is empty cart_response2 =
 * CartUtil.createCart(created_sessionID, add_to_cart_request_body2,
 * x_api_client); log.info("cart res1=" + cart_response1); if
 * (add_to_cart_request_body2.length() > 0) {
 * Final_response_cart.put("cart_response",cart_response2);
 * printCSVFileBOGO(cart_response2); boolean flag=false; log.info("cart res2=" +
 * cart_response2); JSONArray appliedRules =
 * cart_response2.getJSONObject("result").getJSONArray("appliedRuleIds"); for
 * (int i = 0; i < appliedRules.length(); i++) { log.info("apllied rules" +
 * appliedRules.getString(i)); // if applied rules is 61003,61004 or 61005 then
 * order is bogo applied if (appliedRules.getString(i).trim().equals("610003")
 * || appliedRules.getString(i).trim().equals("610004") ||
 * appliedRules.getString(i).trim().equals("610005")) flag=true; }
 * 
 * //Assert.assertTrue(flag,"BOGO not apllied at cart level"); } Address address
 * = ApplicationUtil.saveShippingAddress(); log.info("address :" + address);
 * String orderID = OrderUtil.createOrder(x_api_client, created_sessionID,
 * "cod", telephone); mapping_orderId(orderID,telephone); log.info("order id=" +
 * orderID + "for telephone number" + telephone); JSONObject order_details =
 * OrderUtil.getOrder(created_sessionID, x_api_client, orderID); if
 * (add_to_cart_request_body2.length() > 0)
 * printCSVFileBOGO_order(order_details,orderID,telephone);
 * Final_response_order=order_details; log.info("order details = " +
 * order_details); return orderID; } public static boolean IsBogoApplied(String
 * orderID) throws Exception { JSONObject order_details =
 * OrderUtil.getOrder(created_sessionID, x_api_client, orderID);
 * log.info("order details : " + order_details); JSONArray appliedRules =
 * order_details.getJSONObject("result").getJSONArray("appliedRules");
 * 
 * for (int i = 0; i < appliedRules.length(); i++) { log.info("apllied rules" +
 * appliedRules.getString(i)); // if applied rules is 61003,61004 or 61005 then
 * order is bogo applied if (appliedRules.getString(i).trim().equals("610003")
 * || appliedRules.getString(i).trim().equals("610004") ||
 * appliedRules.getString(i).trim().equals("610005")) return true; } // else
 * order is not BOGO applied return false; } public static void
 * mapping_telephone(String num) { telephones_map.put(map_count_telephone++,
 * num); } public static void mapping_orderId(String orderId,String telephone) {
 * orderId_map.put(map_count_orderId++,new Pair <String,String> (telephone,
 * orderId)); }
 * 
 * public static String placeGoldOrder(String telephone) throws Exception {
 * JSONObject add_to_cart_request_body1 = new JSONObject(); JSONObject
 * add_to_cart_request_body2 = new JSONObject();
 * add_to_cart_request_body1.put("productId", GoldPId); String orderID =
 * Create_BOGO_Order(add_to_cart_request_body1, add_to_cart_request_body2,
 * telephone); GenericUtil.changeLineInCSVFile(Final_CSV_URL);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL, String.valueOf(SR_NO++));
 * PrintGoldCSV(orderID,telephone);
 * 
 * return orderID; } public static void printCSVFileBOGO_order(JSONObject
 * order_details,String orderID,String telephone) throws Exception{ String
 * itemid1=order_details.getJSONObject("result").getJSONArray("items").
 * getJSONObject(0).getString("id"); String
 * itemid2=order_details.getJSONObject("result").getJSONArray("items").
 * getJSONObject(1).getString("id"); String
 * state=order_details.getJSONObject("result").getJSONObject("status").getString
 * ("state"); GenericUtil.addElementInCSVFile(Final_CSV_URL,itemid1);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,itemid2);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,orderID);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,telephone);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,state);
 * 
 * }
 * 
 * public static void printCSVFileBOGO(JSONObject cart_response)throws
 * Exception{ String itemsQty=
 * cart_response.getJSONObject("result").getString("itemsQty");
 * if(Integer.parseInt(itemsQty)==1) { String
 * pid1=cart_response.getJSONObject("result").getJSONArray("items").
 * getJSONObject(0).getString("productId"); String
 * brand1=cart_response.getJSONObject("result").getJSONArray("items").
 * getJSONObject(0).getString("brandName"); String
 * pid2=cart_response.getJSONObject("result").getJSONArray("items").
 * getJSONObject(1).getString("productId"); String
 * brand2=cart_response.getJSONObject("result").getJSONArray("items").
 * getJSONObject(1).getString("brandName");
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,pid1);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,brand1);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,pid2);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,brand2); }
 * 
 * if(Integer.parseInt(itemsQty)==2) { String
 * pid1=cart_response.getJSONObject("result").getJSONArray("items").
 * getJSONObject(0).getString("productId"); String
 * brand1=cart_response.getJSONObject("result").getJSONArray("items").
 * getJSONObject(0).getString("brandName");
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,pid1);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,brand1);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,pid1);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,brand1); } String
 * appliedrulesfinal="  "; String totalTax=
 * cart_response.getJSONObject("result").getJSONObject("totals").getString(
 * "totalTax"); String
 * subTotal=cart_response.getJSONObject("result").getJSONObject("totals").
 * getString("subTotal"); String
 * total=cart_response.getJSONObject("result").getJSONObject("totals").getString
 * ("total"); String
 * totalDiscount=cart_response.getJSONObject("result").getJSONObject("totals").
 * getString("totalDiscount"); JSONArray
 * appliedRules=cart_response.getJSONObject("result").getJSONArray(
 * "appliedRuleIds"); if(appliedRules.length()==0) appliedrulesfinal="NULL";
 * else { for (int i = 0; i < appliedRules.length(); i++) {
 * appliedrulesfinal=appliedrulesfinal+" "+appliedRules.getString(i); } }
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,subTotal);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,totalDiscount);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,totalTax);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,total);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,appliedrulesfinal); }
 * 
 * public static void PrintGoldCSV(String orderID,String telephone) throws
 * Exception { JSONObject order_details = OrderUtil.getOrder(created_sessionID,
 * x_api_client, orderID);
 * 
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,GoldPId);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL," Gold  ");
 * GenericUtil.addElementInCSVFile(Final_CSV_URL," null ");
 * GenericUtil.addElementInCSVFile(Final_CSV_URL," null ");
 * 
 * String SubTotal=order_details.getJSONObject("result").getJSONObject("amount")
 * .getString("subTotal"); String
 * Discount=order_details.getJSONObject("result").getJSONObject("amount").
 * getString("totalDiscount"); String
 * Tax=order_details.getJSONObject("result").getJSONObject("amount").
 * getString("totalTax"); String
 * Total=order_details.getJSONObject("result").getJSONObject("amount").
 * getString("total"); String AppliedRules="null"; String
 * ItemId1=order_details.getJSONObject("result").
 * getJSONArray("items").getJSONObject(0).getString("id"); String
 * ItemId2="null"; String
 * state=order_details.getJSONObject("result").getJSONObject("status").getString
 * ("state"); GenericUtil.addElementInCSVFile(Final_CSV_URL,SubTotal);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,Discount);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,Tax);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,Total);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,AppliedRules);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,ItemId1);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,ItemId2);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,orderID);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,telephone);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL,state);
 * 
 * //GenericUtil.changeLineInCSVFile(Final_CSV_URL);
 * //GenericUtil.addElementInCSVFile(Final_CSV_URL,String.valueOf(SR_NO++));
 * 
 * }
 * 
 * public static void FinalTestCase(String category_price_range, int ith_order)
 * throws Exception { // every 6th time new mobile number has to be generated to
 * place order // because monthly limit for Bogo order is 5
 * log.info("ith order :"+ith_order); if (ith_order %
 * MaxMonthlyLoyalityOrderCount == 0) { telephone = GetTelephoneNumber(); //
 * storing that mobile in a map mapping_telephone(telephone); // buying one gold
 * membership String orderid = placeGoldOrder(telephone);
 * log.info("order id for gold :" + orderid); } // splitting a particular test
 * into 1.category id 2.starting price 3.end price // range , similarly for 2nd
 * eye/sunglass String[] category_price_split = category_price_range.split(" ",
 * 6); log.info(category_price_split[0] + " " + category_price_split[1] + " " +
 * category_price_split[2] + " " + category_price_split[3] + " " +
 * category_price_split[4] + " " + category_price_split[5]); JSONObject
 * add_to_cart_request_body1 = getproduct(category_price_split[0],
 * category_price_split[1], category_price_split[2],
 * getPowerType(category_price_split[0])); JSONObject add_to_cart_request_body2
 * = getproduct(category_price_split[3], category_price_split[4],
 * category_price_split[5], getPowerType(category_price_split[3]));
 * 
 * Assert.assertNotNull(add_to_cart_request_body1,
 * "Add to cart request body1 is null");
 * Assert.assertNotNull(add_to_cart_request_body2,
 * "Add to cart request body2 is null"); log.info("add to cart request body1 :"
 * + add_to_cart_request_body1); log.info("add to cart request body2 :" +
 * add_to_cart_request_body2); if (add_to_cart_request_body1.length() > 0 &&
 * add_to_cart_request_body2.length() > 0) {
 * GenericUtil.changeLineInCSVFile(Final_CSV_URL);
 * GenericUtil.addElementInCSVFile(Final_CSV_URL, String.valueOf(SR_NO++));
 * String orderID = Create_BOGO_Order(add_to_cart_request_body1,
 * add_to_cart_request_body2, telephone); log.info("order created and order id:"
 * + orderID); Assert.assertTrue(IsBogoApplied(orderID),
 * "BOGO has not been applied for this order");
 * log.info("bogo applied for order id " + orderID); } else {
 * log.info("add to cart request body is empty"); } }
 * 
 * public static JSONObject getproduct(String categoryId, String price_start,
 * String price_end, String power_type) throws Exception {
 * 
 * JSONObject add_to_cart_request_body = new JSONObject(); // applying price
 * filter according to start and end price String filter_type =
 * getFilterType(price_start, price_end); log.info("filter type :" +
 * filter_type); String[] filter_type_split = filter_type.split(" ", 2); int
 * filter_start = Integer.parseInt(filter_type_split[0]); int filter_length =
 * Integer.parseInt(filter_type_split[1]); CategoryParam.add(new
 * BasicNameValuePair("page-size", "400")); CategoryParam.add(new
 * BasicNameValuePair("page", "0")); while (filter_length >= 0) {
 * CategoryParam.add(new BasicNameValuePair("filter_lenskart_price",
 * String.valueOf(filter_start))); filter_start++; filter_length--; } JSONObject
 * categoryDetails = JunoV2Util.getCategoryDetails(categoryId, x_api_client,
 * CategoryParam); String no_of_products =
 * categoryDetails.getJSONObject("result").getString("num_of_products");
 * log.info("no of products=" + no_of_products); int no_of_Products =
 * Integer.parseInt(no_of_products); Assert.assertTrue(no_of_Products > 0,
 * "No Products found  in getCategory API"); log.info("categoryDetails :" +
 * categoryDetails); // Finding product id of that category and price range
 * String ProductId = getProductIdFromCategoryDetails(categoryDetails,
 * price_start, price_end); Assert.assertNotNull(ProductId,
 * "Product id not found"); if (!ProductId.equals("")) { JSONObject
 * PackageResponse = JunoV1Util.getPackageIdFromBuyOptionApi(ProductId,
 * power_type, "", x_api_client); log.info("package response" +
 * PackageResponse); int i = getRandomIntegerBetweenRange(0,
 * PackageResponse.getJSONObject("result").getJSONArray("packages").length() -
 * 1); String PackageId =
 * PackageResponse.getJSONObject("result").getJSONArray("packages").
 * getJSONObject(i) .getString("id"); log.info("Productid :" + ProductId);
 * log.info("package :" + PackageId); log.info("power type :" + power_type);
 * add_to_cart_request_body.put("packageId", PackageId);
 * add_to_cart_request_body.put("powerType", power_type);
 * add_to_cart_request_body.put("productId", ProductId); return
 * add_to_cart_request_body; } else { JSONObject null_object = new JSONObject();
 * return null_object; } }
 * 
 * public static String getProductIdFromCategoryDetails(JSONObject
 * categoryDetails, String price_start, String price_end) throws Exception {
 * JSONObject getCategoryResult = categoryDetails.getJSONObject("result");
 * JSONArray Products = getCategoryResult.getJSONArray("product_list");
 * Assert.assertTrue(Products.length() > 0,
 * "No products found in product list of that particular category"); int num =
 * Products.length() - 1; int iterator = num; // starting search from a random
 * number and iterating through whole list int Product_list_num =
 * getRandomIntegerBetweenRange(0, num); String PId = ""; while (iterator > 0) {
 * PId = Products.getJSONObject(Product_list_num).getString("id"); if
 * (IsBogoProductWithinGivenPrice(price_start, price_end, PId)) {
 * log.info("product :" + PId + " found and is bogo enabled"); iterator = -1; }
 * else { iterator--; Product_list_num++; Product_list_num = Product_list_num %
 * num;// because we started from random number if Product_list_num // exceeds
 * num // we have to take mod of num }
 * 
 * } log.info("iterator :" + iterator);
 * 
 * Assert.assertFalse(iterator == 0,
 * "No BOGO enabled products is found in getCategory API after iteration"); if
 * (iterator == 0) return ""; else return PId; } //selecting filter type query
 * param for category api public static String getFilterType(String price_start,
 * String price_end) throws Exception { double price_s =
 * Double.parseDouble(price_start); double price_e =
 * Double.parseDouble(price_end); int filterTypeS = 0; int filterTypeE = 0; if
 * (price_s > 500 && price_s < 1000) filterTypeS = 0; else if (price_s >= 1000
 * && price_s <= 1999) filterTypeS = 1; else if (price_s >= 2000 && price_s <=
 * 2999) filterTypeS = 2; else if (price_s >= 3000 && price_s <= 3999)
 * filterTypeS = 3; else if (price_s >= 4000 && price_s <= 4999) filterTypeS =
 * 4; else if (price_s >= 5000 && price_s <= 5999) filterTypeS = 5; else if
 * (price_s >= 6000) filterTypeS = 6;
 * 
 * if (price_e > 500 && price_e < 1000) filterTypeE = 0; else if (price_e >=
 * 1000 && price_e <= 1999) filterTypeE = 1; else if (price_e >= 2000 && price_e
 * <= 2999) filterTypeE = 2; else if (price_e >= 3000 && price_e <= 3999)
 * filterTypeE = 3; else if (price_e >= 4000 && price_e <= 4999) filterTypeE =
 * 4; else if (price_e >= 5000 && price_e <= 5999) filterTypeE = 5; else if
 * (price_e > 6000) filterTypeE = 6; String result = ""; result =
 * String.valueOf(filterTypeS) + " " + String.valueOf(filterTypeE -
 * filterTypeS); return result;
 * 
 * }
 * 
 * // checking from SQL database that whether quantity is >0 private static
 * boolean sql_query_for_product(String ProductID) throws Exception { int
 * productId = Integer.parseInt(ProductID); String sql =
 * "select qty from cataloginventory_stock_item where product_id=" + productId +
 * " "; log.info(sql); String s = mysqlConnectionObject.executeSelectQuery(sql);
 * log.info("quantity_of_product=" + s); double quantity_of_product =
 * Double.parseDouble(s); if (quantity_of_product > 0) {
 * log.info("quantity_of_product is more than 0 (sql) "); return true; } else {
 * log.info("quantity_of_product is less than 0 (sql) "); return false; }
 * 
 * }
 * 
 * public static boolean IsBogoProductWithinGivenPrice(String price_start,
 * String price_end, String productId) throws Exception { JSONObject
 * product_response = JunoV2Util.getProductDetails(productId, x_api_client);
 * String temp = product_response.getJSONObject("result").getString("qty");
 * double qty = Double.parseDouble(temp); // checking from product_response that
 * qty>0 if (qty <= 0) { log.info("qty is less than 0(mgdb) "); return false; }
 * if (!sql_query_for_product(productId)) {
 * log.info("qty is less than 0(sql) "); return false; } String[] offers =
 * OfferName.split(",", 5); JSONArray price_list =
 * product_response.getJSONObject("result").getJSONArray("prices"); String
 * IsBogoEnabled =
 * product_response.getJSONObject("result").getString("bogoEnabled"); String
 * ProductOffer=product_response.getJSONObject("result").getString("offerName");
 * Assert.assertNotNull(price_list, "price  array is empty");
 * log.info(offers[0]); log.info(offers[1]); log.info(offers[2]);
 * log.info(offers[3]); log.info(offers[4]);
 * 
 * 
 * if (IsBogoEnabled.equals("true") || offers[0].equals(ProductOffer) ||
 * offers[1].equals(ProductOffer) || offers[2].equals(ProductOffer)||
 * offers[3].equals(ProductOffer) || offers[4].equals(ProductOffer) ){ for (int
 * i = 0; i < price_list.length(); i++) { if
 * (price_list.getJSONObject(i).getString("name").equals("Lenskart Price")) {
 * String s = price_list.getJSONObject(i).getString("price"); double
 * product_price = Double.parseDouble(s); double price_s =
 * Double.parseDouble(price_start); double price_e =
 * Double.parseDouble(price_end); log.info("price is :" +
 * price_list.getJSONObject(i).getString("price") + "and given price range is "
 * + price_start + " & " + price_end); log.info("pid is :" + productId); if
 * (product_price > price_s && product_price < price_e) { log.info("product :" +
 * productId + " found in price range" + price_s + "&" + price_e); return true;
 * } else { log.info("product :" + productId + " of price " + product_price +
 * "is not in price range" + price_s + "&" + price_e); } } }
 * Assert.assertNotNull(IsBogoEnabled, "Bogo Enabled  String is empty"); } else
 * log.info("product :" + productId + " is not bogo enabled");
 * 
 * return false; }
 * 
 * public static String GetTelephoneNumber() throws Exception { boolean flag =
 * true; String telephone = "112"; while (flag) { telephone +=
 * GenericUtil.createRandomNumber(7); log.info("telephone"+telephone); if
 * (Isregistered(telephone)) flag = true; else flag = false; } return telephone;
 * }
 * 
 * // checking from mongoDb wheather mobile number is registered or not public
 * static boolean Isregistered(String telephone) throws Exception { Map<String,
 * Object> object = new HashMap<String, Object>(); object.put("telephone",
 * telephone); List<Document> list2 =
 * mongoConnectionObject.executeQuery("customer_v2", object);
 * Thread.sleep(2000); if (list2.size() > 0) return true; return false; }
 * 
 * // randomly selecting power type according to the category public static
 * String getPowerType(String categoryId) throws Exception { String S[] = {
 * "single_vision", "bifocal", "zero_power" }; if
 * (categoryId.equals(ApplicationUtil.getcategoryId("category_eyeglasses_VC"))
 * ||
 * categoryId.equals(ApplicationUtil.getcategoryId("category_eyeglasses_JJ"))) {
 * return S[getRandomIntegerBetweenRange(0.0, 2.0)]; } else if
 * (categoryId.equals(ApplicationUtil.getcategoryId("category_sunglasses_VC"))
 * ||
 * categoryId.equals(ApplicationUtil.getcategoryId("category_sunglasses_JJ"))) {
 * return "sunglasses"; } return "invalid"; }
 * 
 * public static Integer getRandomIntegerBetweenRange(double min, double max) {
 * log.info("random int=" + Math.random()); int x = (int) ((int) (Math.random()
 * * ((max - min) + 1)) + min); Integer num = x; log.info("random number=" +
 * num); return num; }
 * 
 * // mapping all test cases in one map
 * 
 * @Test(enabled = false) public static void GetAllTestCases() throws Exception
 * { JSONObject item = new JSONObject(); JSONArray arr = new JSONArray(); //
 * FOUR CATEGORIES
 * arr.put(ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
 * arr.put(ApplicationUtil.getcategoryId("category_sunglasses_VC"));
 * arr.put(ApplicationUtil.getcategoryId("category_eyeglasses_JJ"));
 * arr.put(ApplicationUtil.getcategoryId("category_sunglasses_JJ"));
 * item.put("Result", arr); // their price range JSONArray price0 = new
 * JSONArray(); price0.put("500.0"); price0.put("1000.0"); price0.put("2000.0");
 * price0.put("3000.0"); price0.put("4000.0");
 * 
 * JSONArray price1 = new JSONArray(); price1.put("500.0");
 * price1.put("1000.0"); price1.put("2000.0"); price1.put("3000.0");
 * price1.put("4000.0");
 * 
 * JSONArray price2 = new JSONArray(); price2.put("3000.0");
 * price2.put("4000.0"); price2.put("5000.0"); price2.put("6000.0");
 * 
 * JSONArray price3 = new JSONArray(); price3.put("3000.0");
 * price3.put("4000.0"); price3.put("5000.0"); price3.put("6000.0");
 * 
 * JSONObject Categ1 = new JSONObject(); Categ1.put("id", "2473");
 * Categ1.put("name", "category_eyeglasses_VC"); Categ1.put("price", price0);
 * JSONArray temp1 = new JSONArray(); temp1.put(Categ1);
 * 
 * JSONObject Categ2 = new JSONObject(); Categ2.put("id", "2587");
 * Categ2.put("name", "category_sunglasses_VC"); Categ2.put("price", price1);
 * temp1.put(Categ2);
 * 
 * JSONObject Categ3 = new JSONObject(); Categ3.put("id", "4704");
 * Categ3.put("name", "category_eyeglasses_JJ"); Categ3.put("price", price2);
 * temp1.put(Categ3);
 * 
 * JSONObject Categ4 = new JSONObject(); Categ4.put("id", "2480");
 * Categ4.put("name", "category_sunglasses_JJ"); Categ4.put("price", price3);
 * temp1.put(Categ4);
 * 
 * log.info("temp " + temp1); JSONObject Category_list = new JSONObject();
 * Category_list.put("Category_list", temp1);
 * 
 * log.info("temp is " + Category_list); log.info("length" +
 * Category_list.getJSONArray("Category_list").length()); int count = 0; String
 * Category1, Category2, price_s1, price_e1, price_s2, price_e2; for (int i = 0;
 * i < Category_list.getJSONArray("Category_list").length(); i++) { for (int j =
 * i; j < Category_list.getJSONArray("Category_list").length(); j++) { Category1
 * =
 * Category_list.getJSONArray("Category_list").getJSONObject(i).getString("id");
 * Category2 =
 * Category_list.getJSONArray("Category_list").getJSONObject(j).getString("id");
 * for (int k = 0; k <
 * Category_list.getJSONArray("Category_list").getJSONObject(i).getJSONArray(
 * "price") .length() - 1; k++) { for (int l = 0; l <
 * Category_list.getJSONArray("Category_list").getJSONObject(j)
 * .getJSONArray("price").length() - 1; l++) { price_s1 =
 * Category_list.getJSONArray("Category_list").getJSONObject(i).getJSONArray(
 * "price") .getString(k); price_s2 =
 * Category_list.getJSONArray("Category_list").getJSONObject(j).getJSONArray(
 * "price") .getString(l); price_e1 =
 * Category_list.getJSONArray("Category_list").getJSONObject(i).getJSONArray(
 * "price") .getString(k + 1); price_e2 =
 * Category_list.getJSONArray("Category_list").getJSONObject(j).getJSONArray(
 * "price") .getString(l + 1); String category_price = Category1 + " " +
 * price_s1 + " " + price_e1 + " " + Category2 + " " + price_s2 + " " +
 * price_e2; //log.info(count); AllTestCasesMapping.put(count++,
 * category_price); } }
 * 
 * } } }
 * 
 * int itr = 0;
 * 
 * 
 * @Test(enabled = true) public void testCase1() throws Exception { String
 * category1 = "2473"; String price_start1 = "1200"; String price_end1 = "5200";
 * String category2 = "2473"; String price_start2 = "500"; String price_end2 =
 * "5200"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * FinalTestCase(category_price, itr++);
 * 
 * 
 * }
 * 
 * @Test(enabled = true) public void testCase2() throws Exception { String
 * category1 = "2473"; String price_start1 = "1000"; String price_end1 = "5000";
 * String category2 = "2473"; String price_start2 = "1000"; String price_end2 =
 * "5000"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * 
 * FinalTestCase(category_price, itr++);
 * 
 * 
 * }
 * 
 * @Test(enabled = true) public void testCase3() throws Exception { String
 * category1 = "2473"; String price_start1 = "3000"; String price_end1 = "4000";
 * String category2 = "2473"; String price_start2 = "3000"; String price_end2 =
 * "4000"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * 
 * FinalTestCase(category_price, itr++); }
 * 
 * @Test(enabled = true) public void testCase4() throws Exception { String
 * category1 = "2473"; String price_start1 = "500"; String price_end1 = "2000";
 * String category2 = "2473"; String price_start2 = "500"; String price_end2 =
 * "3200"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * 
 * FinalTestCase(category_price, itr++); }
 * 
 * @Test(enabled = false) public void testCase5() throws Exception { String
 * category1 = "2473"; String price_start1 = "800"; String price_end1 = "4200";
 * String category2 = "2473"; String price_start2 = "500"; String price_end2 =
 * "4200"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * 
 * FinalTestCase(category_price, itr++);
 * 
 * 
 * }
 * 
 * @Test(enabled = false) public void testCase6() throws Exception { String
 * category1 = "2473"; String price_start1 = "800"; String price_end1 = "4200";
 * String category2 = "2473"; String price_start2 = "500"; String price_end2 =
 * "4200"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * 
 * FinalTestCase(category_price, itr++);
 * 
 * 
 * }
 * 
 * @Test(enabled = false) public void testCase7() throws Exception { String
 * category1 = "2473"; String price_start1 = "800"; String price_end1 = "4200";
 * String category2 = "2473"; String price_start2 = "500"; String price_end2 =
 * "4200"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * 
 * FinalTestCase(category_price, itr++);
 * 
 * 
 * }
 * 
 * @Test(enabled = false) public void testCase8() throws Exception { String
 * category1 = "2473"; String price_start1 = "800"; String price_end1 = "4200";
 * String category2 = "2473"; String price_start2 = "500"; String price_end2 =
 * "4200"; String category_price = category1 + " " + price_start1 + " " +
 * price_end1 + " " + category2 + " " + price_start2 + " " + price_end2;
 * 
 * FinalTestCase(category_price, itr++);
 * 
 * 
 * }
 * 
 * 
 * 
 * @Test(enabled = true) public void SampletestCase1() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++); }
 * 
 * @Test(enabled = true) public void SampletestCase2() throws Exception { String
 * category_price= AllTestCasesMapping.get(itr);
 * FinalTestCase(category_price,itr++); }
 * 
 * @Test(enabled = true) public void SampletestCase3() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase4() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase5() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * 
 * @Test(enabled = true) public void SampletestCase6() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase7() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase8() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase9() throws Exception { String
 * s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase10() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase11() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase12() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase13() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase14() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase15() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase16() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase17() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase18() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase19() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase20() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase21() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase22() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase23() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase24() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase25() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase26() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase27() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase28() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase29() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase30() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase31() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase32() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase33() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase34() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase35() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase36() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase37() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase38() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase39() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase40() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase41() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase42() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase43() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase44() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase45() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase46() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase47() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase48() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase49() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase50() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase51() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase52() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase53() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase54() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase55() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase56() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase57() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase58() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase59() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase60() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase61() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase62() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase63() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase64() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase65() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase66() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase67() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase68() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase69() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase70() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase71() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase72() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase73() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase74() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase75() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase76() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase77() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase78() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase79() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase80() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase81() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase82() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase83() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase84() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase85() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase86() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase87() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase88() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase89() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * 
 * }
 * 
 * @Test(enabled = true) public void SampletestCase90() throws Exception {
 * String s= AllTestCasesMapping.get(itr); FinalTestCase(s,itr++);
 * log.info("map="+telephones_map); }
 * 
 * 
 * }
 */