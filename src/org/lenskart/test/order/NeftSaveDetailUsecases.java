package org.lenskart.test.order;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.OrderPathConstants;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.OrderUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class NeftSaveDetailUsecases {
	private static final Logger log = GenericUtil.InitLogger(NeftSaveDetailUsecases.class);
	private static final boolean mongoValidationFlag = Environments.dbConnectionFlag;

	private MongoConnectionUtility mongoConnectionObject = null;
	private static final String RequestUrlNeftSave = Environments.SERVICES_ENVIRONMENT
			+ OrderPathConstants.GATEWAY_POST_NEFT_SAVE_DETAILS;

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (mongoValidationFlag) {
			mongoConnectionObject = OrderUtil.getOrderMongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();

		}
	}

	@AfterClass
	public void closeDBConection() {
		if (mongoValidationFlag) {
			mongoConnectionObject.closeMongoConnection();
		}
	}

	@DataProvider(name = "NeftSaveDetails")
	public static Iterator<String[]> supplyData() throws IOException {
		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "/csv_files/Order API/NeftSaveDetails.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@Test(enabled = true, dataProvider = "NeftSaveDetails")
	public void neftSaveDetailsUsecases(String usecase, String orderId, String returnId, String accountNumber,
			String accountName, String accountType, String bankCode, String statusCode, String error, String message)
			throws Exception {
		log.info("------------------------" + usecase + "----------------------------");

		String sessiontoken = CustomerUtil.get_sessionId_after_user_authentication("<EMAIL>",
				"123456", ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		// headers.add(new BasicNameValuePair("x-customer-id", "1703329"));
		if (accountNumber.equals("ACC")) {
			accountNumber = "12" + GenericUtil.createRandomNumber(6);
		}
		JSONObject Requestparams = new JSONObject();
		Requestparams.put("orderId", orderId);
		Requestparams.put("returnId", returnId);
		Requestparams.put("accountNumber", accountNumber);
		Requestparams.put("accountName", accountName);
		Requestparams.put("accountType", accountType);
		Requestparams.put("bankCode", bankCode);
		HttpResponse httpResponseNeft = RequestUtil.postRequest(RequestUrlNeftSave, headers, Requestparams);
		JSONObject responseJSONNeft = RequestUtil.convertHttpResponseToJsonObject(httpResponseNeft);
		Assert.assertEquals(httpResponseNeft.getStatusLine().getStatusCode(), Integer.parseInt(statusCode),
				"Status code is not correct");
		if (httpResponseNeft.getStatusLine().getStatusCode() == 200) {
			JSONObject result = responseJSONNeft.getJSONObject("result");
			Assert.assertEquals(result.getString("status"), error, "status mismatch");
			Assert.assertEquals(result.getString("message"), message, "message mismatch");
			if (mongoValidationFlag) {
				Map<String, Object> order_details = new HashMap<String, Object>();
				order_details.put("orderId", orderId);
				order_details.put("accountNumber", accountNumber);
				List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details, "neft_details");
				Assert.assertEquals(getOrderDetails.size(), 1, "Number of document is more than 1");
				Document neftDetails = getOrderDetails.get(0);
				Assert.assertEquals(neftDetails.get("returnId"), returnId, "returnId mismatch");
				Assert.assertEquals(neftDetails.get("accountName"), accountName, "accountName mismatch");
				Assert.assertEquals(neftDetails.get("accountType"), accountType, "accountType mismatch");
				Assert.assertEquals(neftDetails.get("bankCode"), bankCode, "bankCode mismatch");
				Assert.assertEquals(neftDetails.get("status"), error, "status mismatch");
				Assert.assertEquals(neftDetails.get("message"), message, "message mismatch");
				log.info("Database is verified");
			}
		} else {

			Assert.assertEquals(responseJSONNeft.getString("error"), error, "Error mismatch");
			if (message == null)
				Assert.assertEquals(responseJSONNeft.getString("message"), "null", "Message mismatch");
			else
				Assert.assertEquals(responseJSONNeft.getString("message"), message, "Message mismatch");
		}
	}

	@Test(enabled = true)
	public void neftSaveDetailsMismatchCustomerId() throws Exception {
		String sessiontoken = CustomerUtil.get_sessionId_after_user_authentication("<EMAIL>",
				"123456", ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		// headers.add(new BasicNameValuePair("x-customer-id", "1703329"));

		JSONObject Requestparams = new JSONObject();
		Requestparams.put("orderId", "**********");
		Requestparams.put("returnId", "2033374");
		Requestparams.put("accountNumber", "423434");
		Requestparams.put("accountName", "accountName");
		Requestparams.put("accountType", "saving");
		Requestparams.put("bankCode", "ICIC0001666");
		HttpResponse httpResponseNeft = RequestUtil.postRequest(RequestUrlNeftSave, headers, Requestparams);
		JSONObject responseJSONNeft = RequestUtil.convertHttpResponseToJsonObject(httpResponseNeft);
		Assert.assertEquals(httpResponseNeft.getStatusLine().getStatusCode(), 500, "Status code is not correct");

		Assert.assertEquals(responseJSONNeft.getString("error"), "Internal Server Error", "Error mismatch");
		Assert.assertEquals(responseJSONNeft.getString("message"),
				"User is not authorized to get the refund for this order.", "Message mismatch");
	}

	@Test(enabled = false)
	public void neftSaveDetailsShippingEmailCustomerId() throws Exception {
		// String sessiontoken =
		// CustomerUtil.get_sessionId_after_user_authentication("<EMAIL>",
		// "India@1234",
		// ApplicationConstants.XApiClient.ANDROID);
		String sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));

		String acc = "13" + GenericUtil.createRandomNumber(6);
		JSONObject Requestparams = new JSONObject();
		Requestparams.put("orderId", "**********");
		Requestparams.put("returnId", "2033374");
		Requestparams.put("accountNumber", acc);
		Requestparams.put("accountName", "accountName");
		Requestparams.put("accountType", "current");
		Requestparams.put("bankCode", "ICIC0001666");
		HttpResponse httpResponseNeft = RequestUtil.postRequest(RequestUrlNeftSave, headers, Requestparams);
		JSONObject responseJSONNeft = RequestUtil.convertHttpResponseToJsonObject(httpResponseNeft);
		Assert.assertEquals(httpResponseNeft.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		JSONObject result = responseJSONNeft.getJSONObject("result");
		Assert.assertEquals(result.getString("status"), "Success", "status mismatch");
		Assert.assertEquals(result.getString("message"), "Successfully saved details", "message mismatch");
		if (mongoValidationFlag) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("orderId", "**********");
			order_details.put("accountNumber", acc);
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details, "neft_details");
			Assert.assertEquals(getOrderDetails.size(), 1, "Number of document is more than 1");
			Document neftDetails = getOrderDetails.get(0);
			Assert.assertEquals(neftDetails.get("returnId"), "2033374", "returnId mismatch");
			Assert.assertEquals(neftDetails.get("accountName"), "accountName", "accountName mismatch");
			Assert.assertEquals(neftDetails.get("accountType"), "current", "accountType mismatch");
			Assert.assertEquals(neftDetails.get("bankCode"), "ICIC0001666", "bankCode mismatch");
			Assert.assertEquals(neftDetails.get("status"), "Success", "status mismatch");
			Assert.assertEquals(neftDetails.get("message"), "Successfully saved details", "message mismatch");
			log.info("Database is verified");
		}
	}

	@Test(enabled = true)
	public void neftSaveDetailsOrderPhoneCustomerId() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.mobileAuthenticateSession(ApplicationConstants.XApiClient.ANDROID,
				ApplicationConstants.userMobileNumber);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		JSONObject list = OrderUtil.getAllOrdersFromAPI(sessiontoken, XApiClient.ANDROID);
		log.info(list);
		String acc = "13" + GenericUtil.createRandomNumber(6);
		JSONObject Requestparams = new JSONObject();
		Requestparams.put("orderId", list.getJSONArray("result").getJSONObject(0).getString("id"));
		Requestparams.put("returnId", "2033374");
		Requestparams.put("accountNumber", acc);
		Requestparams.put("accountName", "accountName");
		Requestparams.put("accountType", "current");
		Requestparams.put("bankCode", "ICIC0001666");
		HttpResponse httpResponseNeft = RequestUtil.postRequest(RequestUrlNeftSave, headers, Requestparams);
		JSONObject responseJSONNeft = RequestUtil.convertHttpResponseToJsonObject(httpResponseNeft);
		Assert.assertEquals(httpResponseNeft.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		JSONObject result = responseJSONNeft.getJSONObject("result");
		Assert.assertEquals(result.getString("status"), "Success", "status mismatch");
		Assert.assertEquals(result.getString("message"), "Successfully saved details", "message mismatch");
		if (mongoValidationFlag) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("orderId", "**********");
			order_details.put("accountNumber", acc);
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details, "neft_details");
			Assert.assertEquals(getOrderDetails.size(), 1, "Number of document is more than 1");
			Document neftDetails = getOrderDetails.get(0);
			Assert.assertEquals(neftDetails.get("returnId"), "2033374", "returnId mismatch");
			Assert.assertEquals(neftDetails.get("accountName"), "accountName", "accountName mismatch");
			Assert.assertEquals(neftDetails.get("accountType"), "current", "accountType mismatch");
			Assert.assertEquals(neftDetails.get("bankCode"), "ICIC0001666", "bankCode mismatch");
			Assert.assertEquals(neftDetails.get("status"), "Success", "status mismatch");
			Assert.assertEquals(neftDetails.get("message"), "Successfully saved details", "message mismatch");
			log.info("Database is verified");
		}
	}

	@Test(enabled = true)
	public void neftSaveDetailsOrderEmailCustomerId() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication("<EMAIL>", "123456",
				ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		String acc = "13" + GenericUtil.createRandomNumber(6);
		JSONObject Requestparams = new JSONObject();
		Requestparams.put("orderId", "**********");
		Requestparams.put("returnId", "2030548");
		Requestparams.put("accountNumber", acc);
		Requestparams.put("accountName", "accountName");
		Requestparams.put("accountType", "current");
		Requestparams.put("bankCode", "ICIC0001666");
		HttpResponse httpResponseNeft = RequestUtil.postRequest(RequestUrlNeftSave, headers, Requestparams);
		JSONObject responseJSONNeft = RequestUtil.convertHttpResponseToJsonObject(httpResponseNeft);
		Assert.assertEquals(httpResponseNeft.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		JSONObject result = responseJSONNeft.getJSONObject("result");
		Assert.assertEquals(result.getString("status"), "Success", "status mismatch");
		Assert.assertEquals(result.getString("message"), "Successfully saved details", "message mismatch");
		if (mongoValidationFlag) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("orderId", "**********");
			order_details.put("accountNumber", acc);
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details, "neft_details");
			Assert.assertEquals(getOrderDetails.size(), 1, "Number of document is more than 1");
			Document neftDetails = getOrderDetails.get(0);
			Assert.assertEquals(neftDetails.get("returnId"), "2033374", "returnId mismatch");
			Assert.assertEquals(neftDetails.get("accountName"), "accountName", "accountName mismatch");
			Assert.assertEquals(neftDetails.get("accountType"), "current", "accountType mismatch");
			Assert.assertEquals(neftDetails.get("bankCode"), "ICIC0001666", "bankCode mismatch");
			Assert.assertEquals(neftDetails.get("status"), "Success", "status mismatch");
			Assert.assertEquals(neftDetails.get("message"), "Successfully saved details", "message mismatch");
			log.info("Database is verified");
		}
	}

	@Test(enabled = true)
	public void neftSaveDetailsOrderStatusIsRefundComplete() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();
		sessiontoken = CustomerUtil.mobileAuthenticateSession(ApplicationConstants.XApiClient.ANDROID, "**********");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		String acc = "13" + GenericUtil.createRandomNumber(6);
		JSONObject Requestparams = new JSONObject();
		Requestparams.put("orderId", "**********");
		Requestparams.put("returnId", "2030552");
		Requestparams.put("accountNumber", acc);
		Requestparams.put("accountName", "accountName");
		Requestparams.put("accountType", "current");
		Requestparams.put("bankCode", "ICIC0001666");
		HttpResponse httpResponseNeft = RequestUtil.postRequest(RequestUrlNeftSave, headers, Requestparams);
		JSONObject responseJSONNeft = RequestUtil.convertHttpResponseToJsonObject(httpResponseNeft);
		Assert.assertEquals(httpResponseNeft.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		JSONObject result = responseJSONNeft.getJSONObject("result");
		Assert.assertEquals(result.getString("status"), "Failure", "status mismatch");
		Assert.assertEquals(result.getString("message"),
				"Refund is already completed for order id ********** and returnId 2030552", "message mismatch");
		if (mongoValidationFlag) {
			Map<String, Object> order_details = new HashMap<String, Object>();
			order_details.put("orderId", "**********");
			order_details.put("accountNumber", acc);
			List<Document> getOrderDetails = OrderUtil.getAllOrders(order_details, "neft_details");
			Assert.assertEquals(getOrderDetails.size(), 1, "Number of document is more than 1");
			Document neftDetails = getOrderDetails.get(0);
			Assert.assertEquals(neftDetails.get("returnId"), "2030552", "returnId mismatch");
			Assert.assertEquals(neftDetails.get("accountName"), "accountName", "accountName mismatch");
			Assert.assertEquals(neftDetails.get("accountType"), "current", "accountType mismatch");
			Assert.assertEquals(neftDetails.get("bankCode"), "ICIC0001666", "bankCode mismatch");
			Assert.assertEquals(neftDetails.get("status"), "Failure", "status mismatch");
			Assert.assertEquals(neftDetails.get("message"),
					"Refund is already completed for order id ********** and returnId 2030552", "message mismatch");
			log.info("Database is verified");
		}
	}
}
