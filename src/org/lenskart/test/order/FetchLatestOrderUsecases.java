package org.lenskart.test.order;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.OrderPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.OrderUtil;
import org.lenskart.core.util.PaymentUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.v2.cart.CartItem;
import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.lenskart.juno.schema.v2.common.PrescriptionType;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class FetchLatestOrderUsecases {

	private boolean mongoValidationFlag = Environments.dbConnectionFlag;
	private static final Logger log = GenericUtil.InitLogger(FetchLatestOrderUsecases.class);
	private static String sessiontoken;
	private static String product_Id, product_Id_1, product_Id_2, product_Id_3;

	private MongoConnectionUtility mongoConnectionObject = null;
	private static String email = "<EMAIL>";
	private static String mobileNumber = "1262183385";
//	private static String email = "test" + GenericUtil.createRandomNumber(5) + "<EMAIL>";
//	private static String mobileNumber = "1810" + GenericUtil.createRandomNumber(6);

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (mongoValidationFlag) {
			mongoConnectionObject = OrderUtil.getOrderMongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();
		}
	}

	@AfterClass
	public void closeDBConection() {
		if (mongoValidationFlag) {
			mongoConnectionObject.closeMongoConnection();
		}
	}

	@BeforeMethod
	public void wishislist() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_JJ"), ApplicationConstants.XApiClient.ANDROID);
		JSONObject categoryDetails_1 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_sunglasses"), ApplicationConstants.XApiClient.ANDROID);
		JSONObject categoryDetails_2 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_contact_lens"), ApplicationConstants.XApiClient.ANDROID);
		JSONObject categoryDetails_3 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		log.info("***********" + categoryDetails);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		product_Id_1 = categoryDetails_1.getJSONObject("result").getJSONArray("product_list").getJSONObject(1)
				.getString("id");
		product_Id_2 = categoryDetails_2.getJSONObject("result").getJSONArray("product_list").getJSONObject(2)
				.getString("id");
		product_Id_3 = categoryDetails_3.getJSONObject("result").getJSONArray("product_list").getJSONObject(3)
				.getString("id");

	}


	@Test(enabled = true)
	public void get_latest_order_details_for_logged_customer_Sucessfull() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_JJ"), ApplicationConstants.XApiClient.ANDROID);
		JSONObject categoryDetails_1 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_sunglasses"), ApplicationConstants.XApiClient.ANDROID);
		JSONObject categoryDetails_2 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_contact_lens"), ApplicationConstants.XApiClient.ANDROID);
		JSONObject categoryDetails_3 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		log.info("***********" + categoryDetails);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		product_Id_1 = categoryDetails_1.getJSONObject("result").getJSONArray("product_list").getJSONObject(1)
				.getString("id");
		product_Id_2 = categoryDetails_2.getJSONObject("result").getJSONArray("product_list").getJSONObject(2)
				.getString("id");
		product_Id_3 = categoryDetails_3.getJSONObject("result").getJSONArray("product_list").getJSONObject(3)
				.getString("id");
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase());
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);
		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);

		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(responseJson.getJSONObject("result").getInt("itemCount"),
				payment_response.getJSONObject("result").getJSONObject("order").getInt("itemCount"),
				"Number of items mismatch");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerEmail"),
				ApplicationConstants.userEmail, "customerEmail mismatch");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerId"),
				payment_response.getJSONObject("result").getJSONObject("order").getString("customerId"),
				"customerId mismatch");
	}

	@Test(enabled = true)
	public void get_latest_order_details_for_logged_customer_specified_category() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);
		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("productTypeValue", "Eyeglasses"));
		query.add(new BasicNameValuePair("email", ApplicationConstants.userEmail));
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(
				responseJson.getJSONObject("result").getJSONArray("items").getJSONObject(0).getString("productType"),
				"Eyeglasses", "productTypeValue is not matching");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerEmail"),
				ApplicationConstants.userEmail, "customerEmail mismatch");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerId"),
				payment_response.getJSONObject("result").getJSONObject("order").getString("customerId"),
				"customerId mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_logged_customer_specified_categories() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);
		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("productTypeValue", "Eyeglasses"));
		query.add(new BasicNameValuePair("productTypeValue", "Contact_Lens"));
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 404, "status is not correct");
		Assert.assertEquals(responseJson.getString("error"), "Not Found", "error statement is not correct");
		Assert.assertEquals(responseJson.getString("message"), "Order not found.", "message is not correct");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_logged_customer_without_adding_particularCategory() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);
		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("productTypeValue", "Eyeglasses"));

		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, header, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 400, "status is not correct");
		Assert.assertEquals(responseJson.getString("error"), "Bad Request", "error statement is not correct");
	}

	@Test(enabled = true)
	public void get_latest_order_details_for_logged_customer_with_shippingAddressMailId() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		log.info("createCart" + createCart);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("email",email));

		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerId"),
				payment_response.getJSONObject("result").getJSONObject("order").getString("customerId"),
				"customerId mismatch");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerEmail"),
				ApplicationConstants.userEmail, "customerEmail mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_logged_customer_with_different_UsermailId() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		log.info("createCart" + createCart);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("email", "<EMAIL>"));

		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerId"),
				payment_response.getJSONObject("result").getJSONObject("order").getString("customerId"),
				"customerId mismatch");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerEmail"),
				ApplicationConstants.userEmail, "customerEmail mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_logged_customer_with_differentUser_phonenumber() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		log.info("createCart" + createCart);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String OrderId = payment_response.getJSONObject("result").getJSONObject("order").getString("id");
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("phone", "1234567890"));

		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerId"),
				payment_response.getJSONObject("result").getJSONObject("order").getString("customerId"),
				"customerId mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guestuser() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String OrderId = payment_response.getJSONObject("result").getJSONObject("order").getString("id");
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("phone",mobileNumber));
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);

		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(responseJson.getJSONObject("result").getInt("itemCount"),
				payment_response.getJSONObject("result").getJSONObject("order").getInt("itemCount"),
				"Number of items mismatch");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerEmail"),
				payment_response.getJSONObject("result").getJSONObject("order").getString("customerEmail"),
				"customerEmail mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guest_customer_with_Mailid() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String OrderId = payment_response.getJSONObject("result").getJSONObject("order").getString("id");
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("email", email));
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);

		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(responseJson.getJSONObject("result").getInt("itemCount"),
				payment_response.getJSONObject("result").getJSONObject("order").getInt("itemCount"),
				"Number of items mismatch");
		// Assert.assertEquals(responseJson.getJSONObject("result").getString("customerEmail"),payment_response.getJSONObject("result").getJSONObject("order").getString("customerEmail"),"customerEmail
		// mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guest_customer_with_ValidMailid_InvalidPhoneNo() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String OrderId = payment_response.getJSONObject("result").getJSONObject("order").getString("id");
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("email",email));
		query.add(new BasicNameValuePair("phone", "1856758760"));
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);

		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(responseJson.getJSONObject("result").getInt("itemCount"),
				payment_response.getJSONObject("result").getJSONObject("order").getInt("itemCount"),
				"Number of items mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guest_customer_with_InValidMailid_ValidPhoneNo() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String OrderId = payment_response.getJSONObject("result").getJSONObject("order").getString("id");
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("email", "<EMAIL>"));
		query.add(new BasicNameValuePair("phone",mobileNumber));
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);

		Assert.assertEquals(responseJson.getInt("status"), 404, "status is not correct");
		Assert.assertEquals(responseJson.getString("error"), "Not Found", "error is not correct");
		Assert.assertEquals(responseJson.getString("message"), "Order not found.", "message is not correct");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guestUser_with_specified_category() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		log.info("createCart" + createCart);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("productTypeValue", "Sunglasses"));
		query.add(new BasicNameValuePair("email",email));
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 200, "status is not correct");
		Assert.assertEquals(
				responseJson.getJSONObject("result").getJSONArray("items").getJSONObject(0).getString("productType"),
				"Sunglasses", "productTypeValue is not matching");
		Assert.assertEquals(responseJson.getJSONObject("result").getString("customerEmail"),
				email, "customerEmail mismatch");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guestUser_with_specified_categories() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		log.info("createCart" + createCart);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("productTypeValue", "Sunglasses"));
		query.add(new BasicNameValuePair("productTypeValue", "Eyeglasses"));

		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 400, "status is not correct");
		Assert.assertEquals(responseJson.getString("error"), "Bad Request", "error is not correct");

	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guestUser_without_adding_particularItem() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("productTypeValue", "Eyeglasses"));

		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, header, query);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 400, "status is not correct");
		Assert.assertEquals(responseJson.getString("error"), "Bad Request", "error statement is not correct");
	}

	@Test(enabled = true)
	public void get_latest_order_details_for_guestUser_withoutParam() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		log.info("xjbcjsdfds" + sessiontoken);
		JSONObject createCart = CartUtil.createCart(sessiontoken, product_Id, ApplicationConstants.XApiClient.ANDROID);
		log.info("createCart" + createCart);
		createCart = CartUtil.createCart(sessiontoken, product_Id_1, ApplicationConstants.XApiClient.ANDROID);
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product_Id_2));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		createCart = CartUtil.createCart(sessiontoken, reqObj, Environments.X_API_CLIENT);
		createCart = CartUtil.createCart(sessiontoken, product_Id_3, ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress(email, mobileNumber);

		CartUtil.saveAddress(sessiontoken, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken, ApplicationConstants.XApiClient.ANDROID,
				null, "cod", "PU");
		log.info("payment_response" + payment_response);

		String RequestUrl = Environments.SERVICES_ENVIRONMENT + OrderPathConstants.GET_LATEST_ORDER_PATH;
		HttpResponse httpresponse = RequestUtil.getRequest(RequestUrl, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
		Assert.assertEquals(responseJson.getInt("status"), 400, "status is not correct");
		Assert.assertEquals(responseJson.getString("error"), "Bad Request", "error statement is not correct");

	}
}
