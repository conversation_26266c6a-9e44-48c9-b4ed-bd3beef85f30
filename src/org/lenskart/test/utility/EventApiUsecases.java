package org.lenskart.test.utility;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.UtilityPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.JsonUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class EventApiUsecases {

	private static final Logger log = GenericUtil.InitLogger(EventApiUsecases.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;
	private static MongoConnectionUtility mongoConnectionObject = null;
	List<String> x_Api_Client = Arrays.asList(XApiClient.ANDROID, XApiClient.IOS, XApiClient.DESKTOP,
			XApiClient.MOBILESITE, XApiClient.POS_IOS, XApiClient.POS_WEB);
	String eventRequestUrl = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V2;
	String eventRequestUrlForV1 = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V1;

	@BeforeClass
	public void setup() throws Exception {
		if (db_Validation) {
			mongoConnectionObject = JunoV1Util.getJunoV1MongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();

		}
	}

	@AfterClass
	public void destroy() throws Exception {
		if (db_Validation) {
			mongoConnectionObject.closeMongoConnection();
		}
	}

	@Test(enabled = true)
	public void eventService_validation() throws Exception {
		for (int i = 0; i < x_Api_Client.size(); i++) {
			String loggedInSessionId = CustomerUtil.get_sessionId_after_user_authentication(
					ApplicationConstants.userEmail, ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		//	ProductUtil.deleteWishlist(loggedInSessionId);
			JSONObject customerDetails = SessionUtil.getMeSessionDetails(loggedInSessionId);
			String customerId = customerDetails.getJSONObject("result").getJSONObject("attrs").getString("customerId");

			String sessiontoken = SessionUtil.createNewSession();
			JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
					ApplicationUtil.getcategoryId("category_reading_eyeglass"),
					ApplicationConstants.XApiClient.ANDROID);
			String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(i)
					.getString("id");
			ProductUtil.saveWishlist(sessiontoken, product_Id);
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
			headers.add(new BasicNameValuePair("customerId", customerId));
			headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(i)));

			List<NameValuePair> param = new ArrayList<NameValuePair>();
			param.add(new BasicNameValuePair("event-type", "login"));

			HttpResponse httpResponse = RequestUtil.postRequest(eventRequestUrl, headers, param);
			JSONObject jsonResponseOfevent = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
			log.info("jsonResponseOfevent:" + jsonResponseOfevent);
			HttpResponse httpResponseofV1 = RequestUtil.postRequest(eventRequestUrlForV1, headers, param);

			// V1 and V2 validation
			Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(),
					httpResponseofV1.getStatusLine().getStatusCode(), "incorrect status code");

			loggedInSessionId = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
					ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
			JSONObject cxDetailsAfterES = SessionUtil.getMeSessionDetails(loggedInSessionId);
			log.info("cxDetailsAfterES" + cxDetailsAfterES);

			 if(db_Validation) {
			 Map<String, Object> object = new HashMap<String, Object>();
			 object.put("customerId", customerId);
			 List<Document> wishlistDetails = mongoConnectionObject.executeQuery("wishlist", object);
			 String productId = wishlistDetails.get(0).getString("productId");
			 Assert.assertEquals(product_Id, productId, "productId mismatch");
			
			 }
		//	ProductUtil.deleteWishlist(loggedInSessionId);
		}

	}

	@Test(enabled = true)
	public void eventService_withOut_EventType() throws Exception {

		String loggedInSessionId = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		JSONObject customerDetails = SessionUtil.getMeSessionDetails(loggedInSessionId);
		String customerId = customerDetails.getJSONObject("result").getJSONObject("attrs").getString("customerId");

		String sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		ProductUtil.saveWishlist(sessiontoken, product_Id);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("customerId", customerId));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(0)));

		String eventRequestUrl = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V2;
		HttpResponse httpResponse = RequestUtil.postRequest(eventRequestUrl, headers);
		JSONObject jsonResponseOfevent = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("jsonResponseOfevent:" + jsonResponseOfevent);

		String eventRequestUrlForV1 = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V1;
		HttpResponse httpResponseofV1 = RequestUtil.postRequest(eventRequestUrlForV1, headers);

		JSONObject jsonResponseOfeventV1 = RequestUtil.convertHttpResponseToJsonObject(httpResponseofV1);
		log.info("jsonResponseOfevent:" + jsonResponseOfeventV1);

		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfevent, jsonResponseOfeventV1),
					"JSON mismatch");
		}

	}

	@Test(enabled = true)
	public void eventService_Empty_EventType() throws Exception {

		String loggedInSessionId = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		JSONObject customerDetails = SessionUtil.getMeSessionDetails(loggedInSessionId);
		String customerId = customerDetails.getJSONObject("result").getJSONObject("attrs").getString("customerId");

		String sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		ProductUtil.saveWishlist(sessiontoken, product_Id);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("customerId", customerId));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(1)));

		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("event-type", " "));
		String eventRequestUrl = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V2;
		HttpResponse httpResponse = RequestUtil.postRequest(eventRequestUrl, headers, param);
		JSONObject jsonResponseOfevent = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("jsonResponseOfevent:" + jsonResponseOfevent);

		String eventRequestUrlForV1 = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V1;
		HttpResponse httpResponseofV1 = RequestUtil.postRequest(eventRequestUrlForV1, headers, param);

		JSONObject jsonResponseOfeventV1 = RequestUtil.convertHttpResponseToJsonObject(httpResponseofV1);
		log.info("jsonResponseOfevent:" + jsonResponseOfeventV1);

		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfevent, jsonResponseOfeventV1),
					"JSON mismatch");
		}
	}

	@Test(enabled = true)
	public void eventService_withOutCustomerId() throws Exception {

		String loggedInSessionId = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		JSONObject customerDetails = SessionUtil.getMeSessionDetails(loggedInSessionId);
		String customerId = customerDetails.getJSONObject("result").getJSONObject("attrs").getString("customerId");

		String sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		ProductUtil.saveWishlist(sessiontoken, product_Id);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client.get(1)));

		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("event-type", " "));
		String eventRequestUrl = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V2;
		HttpResponse httpResponse = RequestUtil.postRequest(eventRequestUrl, headers, param);
		JSONObject jsonResponseOfevent = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("jsonResponseOfevent:" + jsonResponseOfevent);

		String eventRequestUrlForV1 = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_EVENT_UTILITY_V1;
		HttpResponse httpResponseofV1 = RequestUtil.postRequest(eventRequestUrlForV1, headers, param);

		JSONObject jsonResponseOfeventV1 = RequestUtil.convertHttpResponseToJsonObject(httpResponseofV1);
		log.info("jsonResponseOfevent:" + jsonResponseOfeventV1);

		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfevent, jsonResponseOfeventV1),
					"JSON mismatch");
		}
	}

}
