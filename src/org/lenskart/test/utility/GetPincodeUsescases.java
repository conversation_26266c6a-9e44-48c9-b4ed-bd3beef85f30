package org.lenskart.test.utility;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.UtilityPathConstants;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.JsonUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class GetPincodeUsescases {

	private static final Logger log = GenericUtil.InitLogger(GetPincodeUsescases.class);
	private MongoConnectionUtility mongoConnectionObject = null;
	private static boolean db_Validation = Environments.dbConnectionFlag;

	List<String> X_Api_Client = Arrays.asList(XApiClient.DESKTOP, XApiClient.ANDROID, XApiClient.IOS,
			XApiClient.MOBILESITE, XApiClient.POS_IOS, XApiClient.POS_WEB);
	String getPincodeURL = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.GET_PINCODE_PATH;

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (db_Validation) {
			mongoConnectionObject = JunoV1Util.getJunoV1MongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();
		}
	}

	@AfterClass
	public void closeDBConection() {
		if (db_Validation) {
			mongoConnectionObject.closeMongoConnection();
		}
	}

	private List<NameValuePair> getHeader(String X_Api_Client, String sessionToken) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session_token", sessionToken));
		headers.add(new BasicNameValuePair("X-api-Client", X_Api_Client));
		return headers;
	}

	@Test
	public void getPincode_Success_usecase() throws Exception, IOException {
		int count = 0;
		JSONObject storeDetail = null;
		HttpResponse httpResponse = RequestUtil.getRequest(getPincodeURL,
				getHeader(XApiClient.ANDROID, SessionUtil.createNewSession()), null);
		JSONArray responseJSON = RequestUtil.convertHttpResponseToJsonArray(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_arrays(JunoV1Util.getPincode_v1(), responseJSON));
		}
		if (db_Validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			List<Document> list = mongoConnectionObject.executeQuery("pincode_store_mapping", object);
			Assert.assertEquals(responseJSON.length(), list.size());
			JSONObject document = new JSONObject(list.get(0).toJson());
			log.info(document);
			log.info(responseJSON.getJSONObject(0));

			for (int i = 0, j = 0; i < list.size() && j < responseJSON.length(); i++, j++) {
				JSONArray storeDetail_array = responseJSON.getJSONObject(j).getJSONArray("storeDetail");
				for (int a = 0; a < storeDetail_array.length(); a++) {
					storeDetail = storeDetail_array.getJSONObject(a);
					String storeName = storeDetail.getString("storeName");
					if (storeName.equals("null")) {
						storeDetail.remove("storeName");
					}
				}
				JSONObject document11 = new JSONObject(list.get(i).toJson());
				JSONObject response = responseJSON.getJSONObject(j);
				// Assert.assertEquals(document11.getJSONObject("_id").getString("$oid"),
				// response.getString("id"));
				document11.remove("_id");
				if (document11.has("_class")) {
					document11.remove("_class");
				}
				response.remove("id");
				log.info(document11);
				log.info(response);
				Assert.assertTrue(JsonUtil.compare_json_objects(document11, response));
			}

			/*
			 * for (int i = 0; i < list.size(); i++) { JSONObject pincode_db = new
			 * JSONObject(list.get(i).toJson()); for (int j = 0; j < responseJSON.length();
			 * j++) { JSONObject pincode = responseJSON.getJSONObject(j); if
			 * (pincode_db.getString("pincode").equals(pincode.getString("pincode"))) {
			 * storeDetail = pincode.getJSONArray("storeDetail").getJSONObject(0); String
			 * storeName = storeDetail.getString("storeName"); if (storeName.equals("null"))
			 * { storeDetail.remove("storeName"); } log.info(storeDetail);
			 * log.info(pincode_db.getJSONArray("storeDetail").getJSONObject(0));
			 * Assert.assertTrue(JsonUtil.compare_json_objects(storeDetail,
			 * pincode_db.getJSONArray("storeDetail").getJSONObject(0))); log.info(count++);
			 * } } }
			 */
		}
	}

	@Test
	public void getPincode_Success_usecase_withClient() throws Exception, IOException {
		for (int a = 0; a < X_Api_Client.size(); a++) {
			JSONObject storeDetail = null;
			HttpResponse httpResponse = RequestUtil.getRequest(getPincodeURL, getHeader(X_Api_Client.get(a),SessionUtil.createNewSession()), null);
			JSONArray responseJSON = RequestUtil.convertHttpResponseToJsonArray(httpResponse);
			Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
			if (ApplicationConstants.v1_validationFlag) {
				Assert.assertTrue(
						JsonUtil.compare_json_arrays(JunoV1Util.getPincode_v1(X_Api_Client.get(a)), responseJSON));
			}
			if (db_Validation) {
				Map<String, Object> object = new HashMap<String, Object>();
				List<Document> list = mongoConnectionObject.executeQuery("pincode_store_mapping", object);
				Assert.assertEquals(responseJSON.length(), list.size());
				JSONObject document = new JSONObject(list.get(0).toJson());
				log.info(document);

				for (int i = 0; i < list.size(); i++) {
					JSONObject pincode_db = new JSONObject(list.get(i).toJson());
					for (int j = 0; j < responseJSON.length(); j++) {
						JSONObject pincode = responseJSON.getJSONObject(j);
						if (pincode_db.getString("pincode").equals(pincode.getString("pincode"))) {
							storeDetail = pincode.getJSONArray("storeDetail").getJSONObject(0);
							String storeName = storeDetail.getString("storeName");
							if (storeName.equals("null")) {
								storeDetail.remove("storeName");
							}
							log.info(storeDetail);
							log.info(pincode_db.getJSONArray("storeDetail").getJSONObject(0));
							Assert.assertTrue(JsonUtil.compare_json_objects(storeDetail,
									pincode_db.getJSONArray("storeDetail").getJSONObject(0)));
						}
					}
				}
			}
		}
	}
}
