package org.lenskart.test.utility;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.UtilityPathConstants;
import org.lenskart.core.util.JunoV1Util;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.JsonUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class CampaignUsecases {
	private static final Logger log = GenericUtil.InitLogger(CampaignUsecases.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;
	private static MongoConnectionUtility mongoConnectionObject = null;

	static String udid = GenericUtil.createRandomNumber(8);
	static String phone = "121" + GenericUtil.createRandomNumber(7);
	static String email = phone + "@gmail.com";
	static String pincode = "5700" + GenericUtil.createRandomNumber(2);
	List<String> collectionName = Arrays.asList("camp_AndroidData", "MobileData");
	List<String> x_Api_Client = Arrays.asList(XApiClient.ANDROID, XApiClient.IOS);

	@BeforeClass
	public void setup() throws Exception {
		if (db_Validation) {
			mongoConnectionObject = JunoV1Util.getJunoV1MongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();

		}
	}

	@AfterClass
	public void destroy() throws Exception {
		if (db_Validation) {
			mongoConnectionObject.closeMongoConnection();
		}
	}

	private List<NameValuePair> getHeader(String xApiClient) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("udid", udid));
		headers.add(new BasicNameValuePair("phone", phone));
		headers.add(new BasicNameValuePair("email", email));
		return headers;
	}

	private JSONObject requestJSON(String phone, String version, String pincode) throws Exception {
		JSONObject requestObject = new JSONObject();
		JSONObject collectionData = new JSONObject();
		JSONArray collectionArray = new JSONArray();
		collectionData.put("phone", phone);
		collectionData.put("version", version);
		collectionData.put("pincode", pincode);
		collectionArray.put(collectionData);
		requestObject.put("collectionData", collectionArray);
		return requestObject;

	}

	// @DataProvider(name = "session")
	// public static Object[][] session() throws Exception {
	// return new Object[][] { { SessionUtil.createNewSession() },
	// {
	// CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
	// ApplicationConstants.userPassword) } };
	// }

	@Test(priority = 1)
	public void campaign_Success_Usecase() throws Exception {
		for (int i = 0; i < collectionName.size(); i++) {
			String getCampaignURL = Environments.UTILITY_SERVICE
					+ String.format(UtilityPathConstants.POST_CAMPAIGN_PATH, collectionName.get(i));
			HttpResponse httpResponse;
			JSONObject responseJSON;
			if (collectionName.get(i).equals("MobileData")) {
				httpResponse = RequestUtil.postRequest(getCampaignURL,
						getHeader(ApplicationConstants.XApiClient.MOBILESITE), requestJSON(phone, "2.1.1", pincode));
				responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
				log.info("responseJSON: " + responseJSON);

			} else {
				httpResponse = RequestUtil.postRequest(getCampaignURL, getHeader(x_Api_Client.get(i)),
						requestJSON(phone, "2.1.1", pincode));
				responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
				log.info("responseJSON: " + responseJSON);
				if (db_Validation) {
					Map<String, Object> object = new HashMap<String, Object>();

					object.put("last_store_id", phone);
					List<Document> listOfdeviceData = mongoConnectionObject.executeQuery("device_data", object);
					JSONObject listOfdeviceDataInJson = new JSONObject(listOfdeviceData.get(0).toJson());
					log.info("listOfdeviceDataInJson" + listOfdeviceDataInJson);
					Assert.assertEquals(listOfdeviceDataInJson.getString("device_id"), udid, "");
					Assert.assertEquals(listOfdeviceDataInJson.getString("platform"), x_Api_Client.get(i),
							"platform mismatch");
					Assert.assertEquals(listOfdeviceDataInJson.get("last_store_id"), phone, "phone mismatch");
					Assert.assertEquals(listOfdeviceDataInJson.get("app_version"), "2.1.1", "app version incorrect");

					object.put("phone", phone);
					List<Document> listOfdeviceD = mongoConnectionObject.executeQuery("last_store_id", object);

					// log.info(document);
					// Assert.assertEquals(document.getString("pincode"),
					// pincode);
					// Assert.assertEquals(document.getString("phone"), phone);
					// Assert.assertEquals(document.getString("version"),
					// "2.1.1");

				}
			}
			Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
			Assert.assertEquals(responseJSON.getString("message"), "Successfully data saved");
			Assert.assertEquals(responseJSON.getBoolean("success"), true);
			String collectiondb = collectionName.get(i);

			if (db_Validation) {
				Map<String, Object> object = new HashMap<String, Object>();
				object.put("phone", phone);
				List<Document> list = mongoConnectionObject.executeQuery(collectionName.get(i), object);
				JSONObject document = new JSONObject(list.get(list.size() - 1).toJson());
				log.info(document);
				Assert.assertEquals(document.getString("pincode"), pincode);
				Assert.assertEquals(document.getString("phone"), phone);
				Assert.assertEquals(document.getString("version"), "2.1.1");

				// Assert.assertEquals(document.getString("platform"),
				// X_Api_Client.get(i))


			}
		}
	}

	@Test(priority = 2)
	public void invalidCampaignName_Usecase() throws JSONException, Exception {

		String collectionName = "camp";
		String getCampaignURL = Environments.UTILITY_SERVICE
				+ String.format(UtilityPathConstants.POST_CAMPAIGN_PATH, collectionName);
		HttpResponse httpResponse = RequestUtil.postRequest(getCampaignURL, getHeader(x_Api_Client.get(0)),
				requestJSON(phone, "2.1.1", pincode));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		Assert.assertEquals(responseJSON.getString("message"), "Campaign name must start with camp_");
		Assert.assertEquals(responseJSON.getBoolean("success"), false);
		if (ApplicationConstants.v1_validationFlag) {
			JSONObject response_v1 = JunoV1Util.campaignService(collectionName, x_Api_Client.get(0),
					requestJSON(phone, "2.1.1", pincode));
			Assert.assertTrue(JsonUtil.compare_json_objects(responseJSON, response_v1));

		}
	}

	@Test(priority = 3)
	public void emptyClient_Usecase() throws JSONException, Exception {

		String getCampaignURL = Environments.UTILITY_SERVICE
				+ String.format(UtilityPathConstants.POST_CAMPAIGN_PATH, collectionName.get(0));
		String apiclient = "";
		HttpResponse httpResponse = RequestUtil.postRequest(getCampaignURL, getHeader(apiclient),
				requestJSON(phone, "2.1.1", pincode));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct");
		Assert.assertEquals(responseJSON.getString("error"), "Invalid header data");
		if (ApplicationConstants.v1_validationFlag) {
			JSONObject response_v1 = JunoV1Util.campaignService(collectionName.get(0), "",
					requestJSON(phone, "2.1.1", pincode));
			Assert.assertTrue(JsonUtil.compare_json_objects(responseJSON, response_v1));
		}
	}

	@Test(enabled = true)
	public void emptySession_Usecase() throws JSONException, Exception {
		String sessionToken = "";
		String collectionName = "camp_1";
		String getCampaignURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(UtilityPathConstants.POST_CAMPAIGN_PATH, collectionName);
		HttpResponse httpResponse = RequestUtil.postRequest(getCampaignURL, getHeader(x_Api_Client.get(0)),
				requestJSON("9945214634", "2.1.1", "560038"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(responseJSON);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 404, "Status code is not correct");
		if (ApplicationConstants.v1_validationFlag) {
			JSONObject response_v1 = JunoV1Util.campaignService(collectionName, x_Api_Client.get(2),
					requestJSON("9945214634", "2.1.1", "560038"));
			Assert.assertTrue(JsonUtil.compare_json_objects(responseJSON, response_v1));
		}
	}

	@Test(enabled = false)
	public void withoutSession_Usecase() throws JSONException, Exception {
		String sessionToken = "";
		String collectionName = "camp_1";
		String getCampaignURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(UtilityPathConstants.POST_CAMPAIGN_PATH, collectionName);
		HttpResponse httpResponse = RequestUtil.postRequest(getCampaignURL, getHeader(x_Api_Client.get(0)),
				requestJSON("9945214634", "2.1.1", "560038"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(responseJSON);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 401, "Status code is not correct");
	}

	@Test(priority = 4)
	public void invalidPayload_usecase() throws Exception {
		String collectionName = "camp_A";
		String getCampaignURL = Environments.UTILITY_SERVICE
				+ String.format(UtilityPathConstants.POST_CAMPAIGN_PATH, collectionName);
		HttpResponse httpResponse = RequestUtil.postRequest(getCampaignURL, getHeader(x_Api_Client.get(0)),
				requestJSON("", "", ""));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		Assert.assertEquals(responseJSON.getString("message"),
				"Exception occurred while inserting data into mongo: For input string: \"\"");
		Assert.assertEquals(responseJSON.getBoolean("success"), false);
		if (ApplicationConstants.v1_validationFlag) {
			JSONObject response_v1 = JunoV1Util.campaignService(collectionName, x_Api_Client.get(0),
					requestJSON("", ",", ""));
			Assert.assertTrue(JsonUtil.compare_json_objects(responseJSON, response_v1));
		}
	}

	@Test(priority = 5, enabled=false)
	public void newCampaign_Usecase() throws Exception {
		String collectionName = "camp_".concat(GenericUtil.genterateRandomString());
		String getCampaignURL = Environments.UTILITY_SERVICE
				+ String.format(UtilityPathConstants.POST_CAMPAIGN_PATH, collectionName);

		HttpResponse httpResponse = RequestUtil.postRequest(getCampaignURL, getHeader(x_Api_Client.get(1)),
				requestJSON(phone, "2.1.1", pincode));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		Assert.assertEquals(responseJSON.getString("message"), "Successfully data saved");
		Assert.assertEquals(responseJSON.getBoolean("success"), true);
		if (db_Validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("phone", phone);
			List<Document> list = mongoConnectionObject.executeQuery(collectionName, object);
			JSONObject document = new JSONObject(list.get(list.size() - 1).toJson());
			log.info(document);
			document.remove("created_at");
			document.remove("_id");
			document.remove("_class");
			Assert.assertEquals(document.getString("pincode"), pincode);
			Assert.assertEquals(document.getString("phone"), phone);
			Assert.assertEquals(document.getString("version"), "2.1.1");
			Assert.assertEquals(document.getString("platform"), x_Api_Client.get(1));
		}

		if (ApplicationConstants.v1_validationFlag) {
			JSONObject response_v1 = JunoV1Util.campaignService(collectionName, x_Api_Client.get(1),
					requestJSON(phone, "2.1.1", pincode));
			Assert.assertTrue(JsonUtil.compare_json_objects(responseJSON, response_v1));
			if (db_Validation) {
				Map<String, Object> object = new HashMap<String, Object>();
				object.put("phone", phone);
				List<Document> list = mongoConnectionObject.executeQuery(collectionName, object);
				JSONObject document = new JSONObject(list.get(list.size() - 1).toJson());
				log.info(document);
				document.remove("created_at");
				document.remove("_id");
				document.remove("_class");
				Assert.assertEquals(document.getString("pincode"), pincode);
				Assert.assertEquals(document.getString("phone"), phone);
				Assert.assertEquals(document.getString("version"), "2.1.1");
				Assert.assertEquals(document.getString("platform"), x_Api_Client.get(1));
			}
		}
	}
}

// gv , gv_history(valid mobile, valid platform)
// mobileData - logs - kafaka v1 and utility
// redis
// new mobile number only gv should be sent
