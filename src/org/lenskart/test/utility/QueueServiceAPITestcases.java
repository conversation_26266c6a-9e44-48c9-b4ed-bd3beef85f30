package org.lenskart.test.utility;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.UtilityPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.JsonUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;
import com.utilities.SolrConnectionUtility;

public class QueueServiceAPITestcases {
	private static final Logger log = GenericUtil.InitLogger(QueueServiceAPITestcases.class);

	private static final String queueRequestUrl_v1 = Environments.SERVICES_ENVIRONMENT
			+ UtilityPathConstants.POST_QUEUE_PATH_V1;
	private static final String queueRequestUrl_v2 = Environments.SERVICES_ENVIRONMENT
			+ UtilityPathConstants.POST_QUEUE_PATH_V2;
	private static final boolean db_validation = Environments.dbConnectionFlag;
	private SolrConnectionUtility solrCategoryConnectionObject = null;
	private static MongoConnectionUtility mongoConnectionObject = null;

	PropertyFactory testProperty;

	@BeforeClass
	public void dataSetup() throws URISyntaxException, Exception {
		if (db_validation) {
			testProperty = new PropertyFactory("JunoV1Service");
			solrCategoryConnectionObject = new SolrConnectionUtility(testProperty);
			solrCategoryConnectionObject.initSolrDBConnection();

			mongoConnectionObject = JunoV1Util.getJunoV1MongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();
			JunoV1Util.setCollectionName("solr_document_request_log");
		}
	}

	@AfterClass
	public void destroy() throws Exception {
		if (db_validation) {
			mongoConnectionObject.closeMongoConnection();
			solrCategoryConnectionObject.closeSolrDBConnection();
		}
	}

	@Test(enabled = true)
	public void queueAPi_Successful() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("")).get("productId");
		List<NameValuePair> header = new ArrayList<NameValuePair>();
	header.add(new BasicNameValuePair("Content-Type", "application/json"));
	header.add(new BasicNameValuePair("X-session-token", SessionUtil.createNewSession()));

		JSONArray imageInfo = new JSONArray();
		JSONObject objMessage = new JSONObject();
		objMessage.put("productId", product);
		objMessage.put("imageInfo", imageInfo);
		JSONObject obj = new JSONObject();
		obj.put("queueName", "IMG_RESYNC");
		obj.put("message", objMessage);
		log.info("obj" + obj);

		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("jsonResponseOfQueue: " + jsonResponseOfQueue);
		Assert.assertEquals(jsonResponseOfQueue.getString("code"), 200);
		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfQueue, JunoV1Util.queueService_V1(product)),
					"JSON mismatch");
		}
	}

	@Test(enabled = true)
	public void inVlaidQueueName_InqueueApi() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("")).get("productId");
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));

		JSONArray imageInfo = new JSONArray();
		JSONObject objMessage = new JSONObject();
		objMessage.put("productId", product);
		objMessage.put("imageInfo", imageInfo);
		String queueName = "gfhfjgfhj";
		JSONObject obj = new JSONObject();
		obj.put("queueName", queueName);
		obj.put("message", objMessage);
		log.info("obj" + obj);

		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(httpResponse.getStatusLine().getStatusCode());
		log.info("jsonResponseOfQueue: " + jsonResponseOfQueue);
		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfQueue,
					JunoV1Util.queueServiceNegativecases_V1(product, queueName)), "JSON mismatch");
		}

	}

	@Test(enabled = true)
	public void emptyQueueName_InqueueApi() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("")).get("productId");
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));

		JSONArray imageInfo = new JSONArray();
		JSONObject objMessage = new JSONObject();
		objMessage.put("productId", product);
		objMessage.put("imageInfo", imageInfo);
		String queueName = "";
		JSONObject obj = new JSONObject();
		obj.put("queueName", queueName);
		obj.put("message", objMessage);
		log.info("obj" + obj);

		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("jsonResponseOfQueue: " + jsonResponseOfQueue);
		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfQueue,
					JunoV1Util.queueServiceNegativecases_V1(product, queueName)), "JSON mismatch");
		}

	}

	@Test(enabled = true)
	public void validQueueNameInvalidMessage_InqueueApi() throws Exception {

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		String product = "tryturyru";
		JSONArray imageInfo = new JSONArray();
		JSONObject objMessage = new JSONObject();
		objMessage.put("productId", product);
		objMessage.put("imageInfo", imageInfo);
		String queueName = "IMG_RESYNC";
		JSONObject obj = new JSONObject();
		obj.put("queueName", queueName);
		obj.put("message", objMessage);
		log.info("obj" + obj);

		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("jsonResponseOfQueue: " + jsonResponseOfQueue);
		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfQueue, JunoV1Util.queueService_V1(product)),
					"JSON mismatch");
		}

	}

	@Test(enabled = true)
	public void validQueueNameWithoutMessage_InqueueApi() throws Exception {
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		String queueName = "IMG_RESYNC";
		JSONObject obj = new JSONObject();
		obj.put("queueName", queueName);
		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("jsonResponseOfQueue: " + jsonResponseOfQueue);
		if (ApplicationConstants.v1_validationFlag) {
			Assert.assertTrue(JsonUtil.compare_json_objects(jsonResponseOfQueue,
					JunoV1Util.vaildQueNameWithoutMessage(queueName)), "JSON mismatch");
		}
	}

	private JSONObject requestJSON(int id, String core, String action, String topic, String queueName, JSONObject data)
			throws Exception {
		JSONObject json_request = new JSONObject();
		JSONObject message = new JSONObject();
		message.put("documentId", id);
		message.put("core", core);
		message.put("action", action);
		message.put("topic", topic);
		message.put("data", data);
		json_request.put("queueName", queueName);
		json_request.put("message", message);
		return json_request;

	}

	@Test(enabled = false)
	public void solr_updateDocument_for_product() throws Exception {
		solrCategoryConnectionObject.switchSolrCore("juno_products_common");
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("")).get("productId");
		JSONObject solrJsonObjectForProduct = solrCategoryConnectionObject.querySolrByURL("product_id", product);
		int originalCount = solrJsonObjectForProduct.optJSONObject("response").getJSONArray("docs").getJSONObject(0)
				.getInt("wishlist_count");

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject data1 = new JSONObject();
		data1.put("wishlist_count", originalCount + 1);
		JSONObject obj = requestJSON(Integer.parseInt(product), "juno_products_common", "UPDATE_DOCUMENT",
				"solrservices", "solrservices", data1);
		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_ACCEPTED);
		Thread.sleep(10000);
		if (db_validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("documentId", Integer.parseInt(product));
			object.put("data.wishlist_count", originalCount + 1);
			object.put("status", "SUCCESSFUL");
			log.info(object);
			List<Document> resultList = mongoConnectionObject.executeQuery("solr_document_request_log", object, "_id");
			log.info(resultList);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document);
			Assert.assertEquals(document.getString("status"), "SUCCESSFUL");
			Assert.assertEquals(document.getJSONObject("data").getInt("wishlist_count"), originalCount + 1);

			JSONObject solrJsonObjectForProduct1 = solrCategoryConnectionObject.querySolrByURL("product_id", product);
			int changedCount = solrJsonObjectForProduct1.optJSONObject("response").getJSONArray("docs").getJSONObject(0)
					.getInt("wishlist_count");
			Assert.assertEquals(changedCount, originalCount + 1);

			// JSONObject product_Response =
			// ProductUtil.getProductDetails(product);
			// Assert.assertEquals(product_Response.getJSONObject("result").getInt("wishlistCount"),
			// originalCount + 1);
		}
	}

	@Test(enabled = false)
	public void solr_updateDocument_for_category() throws Exception {
		solrCategoryConnectionObject.switchSolrCore("juno_category");
		String categoryId = "2468";
		JSONObject solrJsonObjectForProduct = solrCategoryConnectionObject.querySolrByURL("id", categoryId);
		String orginal_type = solrJsonObjectForProduct.optJSONObject("response").getJSONArray("docs").getJSONObject(0)
				.getString("alternate_name");
		String changed_type = orginal_type.concat("es");
		log.info(changed_type);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject data1 = new JSONObject();
		data1.put("alternate_name", changed_type);
		JSONObject obj = requestJSON(Integer.parseInt(categoryId), "juno_category", "UPDATE_DOCUMENT", "solrservices",
				"solrservices", data1);
		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_ACCEPTED);
		Thread.sleep(10000);
		if (db_validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("documentId", Integer.parseInt(categoryId));
			object.put("data.alternate_name", changed_type);
			// object.put("status", "SUCCESSFUL");
			log.info(object);
			List<Document> resultList = mongoConnectionObject.executeQuery("solr_document_request_log", object, "_id");
			log.info(resultList);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document);
			// Assert.assertEquals(document.getString("status"), "SUCCESSFUL");
			Assert.assertEquals(document.getJSONObject("data").getString("alternate_name"), changed_type);

			Thread.sleep(5000);
			JSONObject solrJsonObjectForProduct1 = solrCategoryConnectionObject.querySolrByURL("id", categoryId);
			String new_type = solrJsonObjectForProduct1.optJSONObject("response").getJSONArray("docs").getJSONObject(0)
					.getString("alternate_name");
			Assert.assertEquals(new_type, changed_type);

			// JSONObject category_Response =
			// JunoV1Util.getCategoryDetails(categoryId, XApiClient.ANDROID);
			// Assert.assertEquals(category_Response.getJSONObject("result").getString("category_name"),
			// changed_type);
		}
		JSONObject data2 = new JSONObject();
		data2.put("alternate_name", orginal_type);
		JSONObject obj1 = requestJSON(Integer.parseInt(categoryId), "juno_category", "UPDATE_DOCUMENT", "solrservices",
				"solrservices", data2);
		HttpResponse httpResponse1 = RequestUtil.postRequest(queueRequestUrl_v2, header, obj1);
		Assert.assertEquals(httpResponse1.getStatusLine().getStatusCode(), HttpStatus.SC_ACCEPTED);

		Thread.sleep(5000);
		Map<String, Object> object1 = new HashMap<String, Object>();
		object1.put("documentId", Integer.parseInt(categoryId));
		object1.put("data.alternate_name", orginal_type);
		// object1.put("status", "SUCCESSFUL");
		log.info(object1);
		List<Document> resultList = mongoConnectionObject.executeQuery("solr_document_request_log", object1, "_id");
		log.info(resultList);
		JSONObject document1 = new JSONObject(resultList.get(0).toJson());
		log.info(document1);
		// Assert.assertEquals(document1.getString("status"), "SUCCESSFUL");
		Assert.assertEquals(document1.getJSONObject("data").getString("alternate_name"), orginal_type);

		JSONObject solrJsonObjectForProduct1 = solrCategoryConnectionObject.querySolrByURL("id", categoryId);
		String new_type = solrJsonObjectForProduct1.optJSONObject("response").getJSONArray("docs").getJSONObject(0)
				.getString("alternate_name");
		Assert.assertEquals(new_type, orginal_type);
	}

	@Test
	public void solr_invalid_queueName() throws Exception {
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject data1 = new JSONObject();
		data1.put("alternate_name", "njnjn");
		JSONObject obj = requestJSON(2468, "juno_category", "UPDATE_DOCUMENT", "solrservices", "solsssssrservices",
				data1);
		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(jsonResponseOfQueue.getInt("code"), 406);
		Assert.assertEquals(jsonResponseOfQueue.getString("message"), "Could not push to kafka queue");
	}

	@Test
	public void solr_empty_queueName() throws Exception {
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject data1 = new JSONObject();
		data1.put("alternate_name", "njnjn");
		JSONObject obj = requestJSON(2468, "juno_category", "UPDATE_DOCUMENT", "solrservices", "", data1);
		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(jsonResponseOfQueue.getInt("code"), 51);
		Assert.assertEquals(jsonResponseOfQueue.getString("error"), "PushToQueueRequest is invalid!");
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_BAD_REQUEST);
	}

	@Test(enabled = false)
	public void solr_deleteDocument_for_product() throws Exception {
		solrCategoryConnectionObject.switchSolrCore("juno_products_common");
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("")).get("productId");

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		JSONObject data1 = new JSONObject();
		data1.put("wishlist_count", 123);
		JSONObject obj = requestJSON(Integer.parseInt(product), "juno_products_common", "DELETE_DOCUMENT",
				"solrservices", "solrservices", data1);
		HttpResponse httpResponse = RequestUtil.postRequest(queueRequestUrl_v2, header, obj);
		JSONObject jsonResponseOfQueue = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_ACCEPTED);
		Thread.sleep(10000);
		if (db_validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("documentId", Integer.parseInt(product));
			object.put("status", "SUCCESSFUL");
			log.info(object);
			List<Document> resultList = mongoConnectionObject.executeQuery("solr_document_request_log", object, "_id");
			log.info(resultList);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document);
			Assert.assertEquals(document.getString("status"), "SUCCESSFUL");
			JSONObject solrJsonObjectForProduct1 = solrCategoryConnectionObject.querySolrByURL("product_id", product);
			log.info(solrJsonObjectForProduct1);
			Assert.assertEquals(solrJsonObjectForProduct1.optJSONObject("response").getJSONArray("docs").length(), 0);
		}
	}
}
