
package org.lenskart.test.utility;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.ProductCategories;
import org.lenskart.core.constant.ApplicationConstants.ReturnMasterOtp;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.UtilityPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.MoneyUtil;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.lenskart.core.util.UtilityUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.util.Strings;

import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;

public class BuyOnCallUsecases {

	private final String buyOnCallURL = Environments.SERVICES_ENVIRONMENT + UtilityPathConstants.POST_BUY_ON_CALL_PATH;
	private static final Logger log = GenericUtil.InitLogger(BuyOnCallUsecases.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;
	private static MongoConnectionUtility mongoConnectionObject = null;
	private static MongoConnectionUtility mongoConnectionObject1 = null;
	private static MongoConnectionUtility mongoConnectionObject2 = null;
	private MySQLConnectionUtility mysqlConnectionObject = null;


	List<String> x_Api_Client = Arrays.asList(XApiClient.DESKTOP, XApiClient.ANDROID, XApiClient.IOS,
			XApiClient.MOBILESITE, XApiClient.POS_IOS, XApiClient.POS_WEB);
	public static List<String> pids = new ArrayList<String>();
	
	private String getStoreCreditDetailBasedOnEmail(String email) throws Exception {
		String sql = "select balance from storecredit where customer_email='" + email + "' AND expired_at > SYSDATE() group by balance DESC LIMIT 1";
		return mysqlConnectionObject.executeSelectQuery(sql);
	}

	private List<Document> queryMongoWithWaitForResults(MongoConnectionUtility mongoConnectionObject, String mongoCollectionName, Map<String, Object> queryObject) throws InterruptedException{
		int timeoutInSeconds = 20;
		int timer = 1;
		List<Document> resultList = null;
		do{
			resultList = mongoConnectionObject.executeQuery(mongoCollectionName, queryObject);
			Thread.sleep(1000);
			timer++;
		}while(resultList.size() == 0 && timer <= timeoutInSeconds);
		return resultList;
	}

	private List<NameValuePair> getHeader(String x_Api_Client, String x_session_token, String udid) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		if(udid !=null){
		headers.add(new BasicNameValuePair("udid", String.valueOf(udid)));
		}
		headers.add(new BasicNameValuePair("X-Session-Token", x_session_token));
		return headers;
	}

	private JSONObject requestJSON(String leadStep, String id, String telephone, String type) throws Exception {
		JSONObject requestObject = new JSONObject();
		JSONObject dataObject = new JSONObject();
		dataObject.put("leadStep", leadStep);
		dataObject.put("id", id);
		dataObject.put("telephone", telephone);
		requestObject.put("data", dataObject);
		requestObject.put("type", type);
		return requestObject;
	}
	//		blockUnblockVPAParams.put("customerId", customerId==null?JSONObject.NULL:customerId);

	
	private JSONObject requestJSON1(String leadStep, String id, String telephone, String type) throws Exception {
		JSONObject requestObject = new JSONObject();
		JSONObject dataObject = new JSONObject();
		
		if(Strings.isNotNullAndNotEmpty(leadStep)){
			dataObject.put("leadStep", leadStep);
		}
		if(Strings.isNotNullAndNotEmpty(id)){
			dataObject.put("id", id);
		}
		if(Strings.isNotNullAndNotEmpty(telephone)){
			dataObject.put("telephone", telephone);
		}
		if(Strings.isNotNullAndNotEmpty(type)){
			requestObject.put("type", type);
		}	
		requestObject.put("data", dataObject);
		return requestObject;
	}
	
	

	@DataProvider(name = "GuestAndLoggedInSession")
	public static Object[][] session() throws Exception {
		return new Object[][] { { SessionUtil.createNewSession() },
				{ CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
						ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID) } };
	}
	
	@Test(dataProvider = "GuestAndLoggedInSession")
	public void gusetUser_buyOnCall_Success_Usecaseeeee(String session){
		String a = session;
		log.info(a);
	}
	
	

	@BeforeClass
	public void id() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
			ApplicationUtil.getcategoryId("category_sunglasses"), ApplicationConstants.XApiClient.ANDROID);
	JSONArray listOfPids = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		for (int i = 0; i < listOfPids.length(); i++) {
		String id = listOfPids.getJSONObject(i).getString("id");
		log.info(id);
	pids.add(id);
	}
		if (db_Validation) {
			mongoConnectionObject = UtilityUtil.getMongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();
			UtilityUtil.setCollectionName("buyoncall");
			
			mongoConnectionObject1 = JunoV1Util.getJunoV1MongoConnectionObject();
			mongoConnectionObject1.initMongoDBConnection();
			JunoV1Util.setCollectionName("camp_InviteReferral_ContactSync");
			
			mongoConnectionObject2 = CustomerUtil.getCustomerMongoConnectionObject();
			mongoConnectionObject2.initMongoDBConnection();
			JunoV1Util.setCollectionName("customer_v2");
			
			PropertyFactory pf = new PropertyFactory("MoneyService");
			mysqlConnectionObject = new MySQLConnectionUtility(pf);
			mysqlConnectionObject.initMySQLConnection();
		}
	}
	
	@AfterClass
	public void destroy() throws Exception {
		if (db_Validation) {
			mongoConnectionObject.closeMongoConnection();
			mongoConnectionObject1.closeMongoConnection();
			mongoConnectionObject2.closeMongoConnection();
			mysqlConnectionObject.closeMySQLConnection();
		}
	}

	@DataProvider(name = "Buy_On_call_useCases")
	public static Object[][] useCases() throws Exception {
		return new Object[][] { 
			
{"Sucessful Usecase for leadStep-Product for android",XApiClient.ANDROID, "PRODUCT", pids.get(0),GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for ios",XApiClient.IOS, "PRODUCT", pids.get(1),GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for desktop",XApiClient.DESKTOP, "PRODUCT", pids.get(2),GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for msite",XApiClient.MOBILESITE, "PRODUCT", pids.get(3),GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for android",XApiClient.ANDROID, "HOME", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for ios",XApiClient.IOS, "HOME", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for desktop",XApiClient.DESKTOP, "HOME", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for msite",XApiClient.MOBILESITE, "HOME", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for android",XApiClient.ANDROID, "COLLECTION", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for ios",XApiClient.IOS, "COLLECTION", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for desktop",XApiClient.DESKTOP, "COLLECTION", "premium_eyeglasses",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for msite",XApiClient.MOBILESITE, "COLLECTION", "sunglasses",GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for android",XApiClient.ANDROID, "CATEGORY", ProductCategories.EYEGLASSES,GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for ios",XApiClient.IOS, "CATEGORY", ProductCategories.SUNGLASSES,GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for desktop",XApiClient.DESKTOP, "CATEGORY",ProductCategories.CONTACT_LENS ,GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for msite",XApiClient.MOBILESITE, "CATEGORY", ProductCategories.ACCESSORIES,GenericUtil.createRandomNumber(9), "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"invalid mobile no. - less than 7 digits",XApiClient.ANDROID, "CATEGORY", ProductCategories.ACCESSORIES,"123456", "BUYONCALL", 400, "Phone should be numeric with length 7-13; Phone: 123456" },
{"invalid mobile no. - more than 14 digits",XApiClient.ANDROID, "CATEGORY", ProductCategories.ACCESSORIES,"12345678901234", "BUYONCALL", 400, "Phone should be numeric with length 7-13; Phone: 12345678901234" },
{"invalid mobile no. spcl cha.",XApiClient.ANDROID, "CATEGORY", ProductCategories.ACCESSORIES,"12345678a@", "BUYONCALL", 400, "Phone should be numeric with length 7-13; Phone: 12345678a@" },

{"empty telephone",XApiClient.ANDROID, "CATEGORY", ProductCategories.ACCESSORIES,"", "BUYONCALL", 400, "Phone should not be blank" },
{"invalid lead step",XApiClient.MOBILESITE, "hom", ProductCategories.CATEGORY_EYEGLASSES_JJ,GenericUtil.createRandomNumber(9), "BUYONCALL", 400, "Invalid Lead Step"},
{"empty lead step",XApiClient.MOBILESITE, "", ProductCategories.CATEGORY_EYEGLASSES_JJ,GenericUtil.createRandomNumber(9), "BUYONCALL", 400, "Invalid Lead Step"},
{"Empty id for leadStep-Product",XApiClient.DESKTOP, "product", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 400, "Id can not be blank."},
{"Empty id for leadStep-category",XApiClient.IOS, "category", "",GenericUtil.createRandomNumber(9), "BUYONCALL", 400, "Id can not be blank."},
{"Empty x_api_client","", "category", "",GenericUtil.createRandomNumber(7), "BUYONCALL", 400, "Missing Header: API Client"}, 
{"Contact_sync and gold member no case",XApiClient.ANDROID, "PRODUCT", "122078","**********", "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"gold member Yes use case",XApiClient.ANDROID, "PRODUCT", "38723","1240937384", "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Contact_Sync Yes use case",XApiClient.ANDROID, "PRODUCT", "38723","**********", "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
{"Store credit",XApiClient.ANDROID, "PRODUCT", "38723","**********", "BUYONCALL", 200, "The request has been completed successfully and pushed for processing." },
};
		//122078 eyeglass -- done
		//38723 contact lens -- done
		//contact sync yes and gold member yes case -- done
	}
	
	@Test(dataProvider = "Buy_On_call_useCases")
	public void gusetUser_buyOnCall_Success_Usecase(String testCaseName, String x_Api_Client,
			String leadStep, String id, String telephone, String type, int statusCode, String message)
			throws Exception {
		String session = SessionUtil.createNewSession();
		String contact_sync = null;
		String udid = null;
		String lastname = null;
		String firstname = null;
		String email = null;
		Boolean isLoyal;
		double lkCash = 0;
		double lkCashPlus = 0;
		String is_gold_member = "No";
		if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			udid = String.valueOf(UUID.randomUUID());
		}
	
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client, session, udid),
				requestJSON(leadStep, id, telephone, type));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, statusCode, "Status code is not correct.");

		JSONObject resultObject = responseJSON;
		if (statusCode == HttpStatus.SC_OK) {
			resultObject = responseJSON.getJSONObject("result");
			Assert.assertEquals(responseJSON.getJSONObject("result").getBoolean("success"), true, "mismatch in result");
			Assert.assertEquals("The request has been completed successfully and pushed for processing.", message, "mismatch in result");
			
			if (db_Validation) {
				Map<String, Object> object = new HashMap<String, Object>();
				object.put("session_id",session);
				List<Document> resultList = queryMongoWithWaitForResults(mongoConnectionObject, "buyoncall", object);
				if(resultList == null){
					Assert.assertFalse(true, "No documents fetched from mongo after timeout");
				}
				JSONObject document = new JSONObject(resultList.get(0).toJson());
				log.info(document);
				if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
				Assert.assertEquals(document.getString("device_id"), udid);
				}
				Assert.assertEquals(document.getString("session_id"), session);
				Assert.assertEquals(document.getString("lead_source"), x_Api_Client.toUpperCase());
				Assert.assertEquals(document.getString("telephone"), telephone);
				Assert.assertEquals(document.getString("lead_step"), leadStep);
				Assert.assertEquals(document.getString("lead_type"), "Buy_On_Call");
				
				if(leadStep.equalsIgnoreCase("PRODUCT")){
				JSONObject product_response = ProductUtil.getProductDetails(id);
				Assert.assertEquals(document.getString("boc_product_id"), id);
				Assert.assertEquals(document.getString("category"), product_response.getJSONObject("result").getString("type"));
				Assert.assertEquals(document.getString("amount"), product_response.getJSONObject("result").getJSONArray("prices").getJSONObject(1).getString("price"));
				}
				/*if(document.has("lk_balance")||document.has("lkplus_balance")){
					JSONObject getWalletBalanceDetails = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone, "android");
					lkCash = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(0).getInt("balance");
					lkCashPlus = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(1).getInt("balance");
				}
				if(lkCash>0){
					Assert.assertEquals(document.getDouble("lk_balance"), lkCash);
				}
				if(lkCashPlus>0){
					Assert.assertEquals(document.getDouble("lkplus_balance"), lkCashPlus);
				}*/
				/*Map<String, Object> queryMap = new HashMap<>();
				BasicDBList dbl = new BasicDBList();
				dbl.add(new BasicDBObject("mobile",telephone));
				dbl.add(new BasicDBObject("Referrer", telephone));
				queryMap.put("$or",dbl);
				List<Document> list1 = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", queryMap);*/
	
				Map<String, Object> object1 = new HashMap<String, Object>();
				object1.put("Referrer",telephone);
				List<Document> list1 = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", object);
				Thread.sleep(4000);
				if(list1.size() > 0){
				contact_sync = "Yes";
				}else{
					contact_sync = "No";
				}
				log.info(contact_sync);
				Assert.assertEquals(document.getString("contact_sync"), contact_sync);
				
				Map<String, Object> object2 = new HashMap<String, Object>();
				object2.put("telephone",telephone);
				List<Document> list2 = mongoConnectionObject2.executeQuery("customer_v2", object2);
				Thread.sleep(2000);

				if(list2.size() > 0){
				JSONObject document2 = new JSONObject(list2.get(0).toJson());
				isLoyal = document2.getBoolean("isLoyalty");
				try{
					lastname = document2.getString("lastName");
					}catch(Exception e){
						e.printStackTrace();
					}
				
				firstname = document2.getString("firstName");
				email = document2.getString("email");
				log.info(isLoyal);
				log.info(lastname);
				log.info(firstname);
				log.info(email);
				 if(isLoyal){
					  is_gold_member  ="Yes";
				 }
				 log.info(is_gold_member);
				 
				Assert.assertEquals(document.getString("first_name"), firstname);
				if(lastname !=null){
				Assert.assertEquals(document.getString("last_name"), lastname);
				}
				Assert.assertEquals(document.getString("email"), email);
				Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
				if(document2.has("isWalletVerified")&&document2.has("walletId")&&document2.getBoolean("isWalletVerified")){
					JSONObject getWalletBalanceDetails = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone, "android");
					lkCash = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(0).getInt("balance");
					lkCashPlus = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(1).getInt("balance");
					Assert.assertEquals(document.getDouble("lk_balance"), lkCash);
					
					Assert.assertEquals(document.getDouble("lkplus_balance"), lkCashPlus);
				}
				String sc =	getStoreCreditDetailBasedOnEmail(email);
				if(!sc.isEmpty()){
				Assert.assertEquals(document.getDouble("store_credit"),Double.parseDouble(sc));
				}
			// doubts - gold member, email, lk+ and lk cash, SC, FN and LN for guest users
			}
				else{
					Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
				}
		} }
		else {
			resultObject = responseJSON;
		}

		Assert.assertEquals(resultObject.getString("message"), message, "mismatch in result");
		}
	
	
	@Test
	public void invalidType_useCase() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client.get(0), SessionUtil.createNewSession(), "hv66t6t6"),
				requestJSON("HOME", "", "**********", "buyoncall"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
	}
	
	
	@Test
	public void emptyType_useCase() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client.get(0), SessionUtil.createNewSession(), "hv66t6t6"),
				requestJSON("HOME", "", "**********", ""));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
	}
	
	@Test
	public void emptySession_useCase() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client.get(0), "", "1123456789"),
				requestJSON("HOME", "", "**********", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 401, "Status code is not correct.");
	}
	
	@Test
	public void invalidSession_useCase() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client.get(0), "12345", "1123456789"),
				requestJSON("HOME", "", "**********", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 401, "Status code is not correct.");
	}
	
	@Test
	public void without_X_Session_Token_Header_Usecase() throws Exception{
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("udid", "123456"));
		
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, headers,
				requestJSON("home", "", "8096767", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 401, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Missing X-Session-Token in Header");
	}
	
	@Test
	public void without_X_api_Client_Header_Usecase() throws Exception{
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID)));
		headers.add(new BasicNameValuePair("udid", "123456"));
		
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, headers,
				requestJSON("home", "", "8096767", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Missing Header: API Client");
	}
	
	@Test
	public void empty_UDID_for_Android() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(XApiClient.ANDROID, CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID), ""),
				requestJSON("HOME", "", "**********", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Device Id can't be empty in the case of ios or android");
	}
	
	@Test
	public void empty_UDID_for_IOS() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(XApiClient.IOS, CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID), ""),
				requestJSON("HOME", "", "**********", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Device Id can't be empty in the case of ios or android");
	}
	
	@Test
	public void empty_UDID_for_mobilesite() throws Exception{
		String contact_sync = null;
		String udid = null;
		String lastname = null;
		String firstname = null;
		String email = null;
		Boolean isLoyal;
		String is_gold_member = "No";
		String	x_Api_Client = XApiClient.MOBILESITE;
		String leadStep = "HOME";
		double lkCash =0;
		double lkCashPlus =0;
		String telephone = GenericUtil.createRandomNumber(9);
		String session = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			udid = String.valueOf(UUID.randomUUID());
		}
		
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client,session , ""),
				requestJSON("HOME", "", telephone, "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getJSONObject("result").getString("message"),"The request has been completed successfully and pushed for processing.","mismatch in result");

			if (db_Validation) {
				Map<String, Object> object = new HashMap<String, Object>();
				object.put("session_id",session);
				List<Document> resultList = queryMongoWithWaitForResults(mongoConnectionObject, "buyoncall", object);
				if(resultList == null){
					Assert.assertFalse(true, "No documents fetched from mongo after timeout");
				}
				JSONObject document = new JSONObject(resultList.get(0).toJson());
				log.info(document);
				if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
				Assert.assertEquals(document.getString("device_id"), udid);
				}
				Assert.assertEquals(document.getString("session_id"), session);
				Assert.assertEquals(document.getString("lead_source"), x_Api_Client.toUpperCase());
				Assert.assertEquals(document.getString("telephone"), telephone);
				Assert.assertEquals(document.getString("lead_step"), leadStep);
				Assert.assertEquals(document.getString("lead_type"), "Buy_On_Call");
				
				/*if(leadStep.equalsIgnoreCase("PRODUCT")){
				JSONObject product_response = ProductUtil.getProductDetails(id);
				Assert.assertEquals(document.getString("boc_product_id"), id);
				Assert.assertEquals(document.getString("category"), product_response.getJSONObject("result").getString("type"));
				Assert.assertEquals(document.getString("amount"), product_response.getJSONObject("result").getJSONArray("prices").getJSONObject(1).getString("price"));
				}*/
				
				if(document.has("lk_balance")||document.has("lkplus_balance")){
					JSONObject getWalletBalanceDetails = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone, "android");
					lkCash = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(0).getInt("balance");
					lkCashPlus = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(1).getInt("balance");
				}
				if(lkCash>0){
					Assert.assertEquals(document.getDouble("lk_balance"), lkCash);
				}
				if(lkCashPlus>0){
					Assert.assertEquals(document.getDouble("lkplus_balance"), lkCashPlus);
				}
	
				Map<String, Object> object1 = new HashMap<String, Object>();
				object1.put("Referrer",telephone);
				List<Document> list1 = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", object);
				Thread.sleep(4000);
				if(list1.size() > 0){
				contact_sync = "Yes";
				}else{
					contact_sync = "No";
				}
				log.info(contact_sync);
				Assert.assertEquals(document.getString("contact_sync"), contact_sync);
				
				Map<String, Object> object2 = new HashMap<String, Object>();
				object2.put("telephone",telephone);
				List<Document> list2 = mongoConnectionObject2.executeQuery("customer_v2", object2);
				Thread.sleep(2000);

				if(list2.size() > 0){
				JSONObject document2 = new JSONObject(list2.get(0).toJson());
				isLoyal = document2.getBoolean("isLoyalty");
				try{
					lastname = document2.getString("lastName");
					}catch(Exception e){
						e.printStackTrace();
					}
				
				firstname = document2.getString("firstName");
				email = document2.getString("email");
				log.info(isLoyal);
				log.info(lastname);
				log.info(firstname);
				log.info(email);
				 if(isLoyal){
					  is_gold_member  ="Yes";
				 }
				 log.info(is_gold_member);
				 
				Assert.assertEquals(document.getString("first_name"), firstname);
				if(lastname !=null){
				Assert.assertEquals(document.getString("last_name"), lastname);
				}
				Assert.assertEquals(document.getString("email"), email);
				Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
				String sc =	getStoreCreditDetailBasedOnEmail(email);
				if(!sc.isEmpty()){
				Assert.assertEquals(document.getDouble("store_credit"),Double.parseDouble(sc));
				}
			}
				else{
					Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
				}
		} 
	}
	
	@Test
	public void empty_UDID_for_desktop() throws Exception{
		String contact_sync = null;
		String udid = null;
		String lastname = null;
		String firstname = null;
		String email = null;
		Boolean isLoyal;
		String is_gold_member = "No";
		String	x_Api_Client = XApiClient.MOBILESITE;
		String leadStep = "HOME";
		String telephone = "**********";
		String session = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			udid = String.valueOf(UUID.randomUUID());
		}
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client, session, ""),
				requestJSON("HOME", "", telephone, "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getJSONObject("result").getString("message"), "The request has been completed successfully and pushed for processing.");
		
		if (db_Validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("session_id",session);
			List<Document> resultList = queryMongoWithWaitForResults(mongoConnectionObject, "buyoncall", object);
			if(resultList == null){
				Assert.assertFalse(true, "No documents fetched from mongo after timeout");
			}
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document);
			if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			Assert.assertEquals(document.getString("device_id"), udid);
			}
			Assert.assertEquals(document.getString("session_id"), session);
			Assert.assertEquals(document.getString("lead_source"), x_Api_Client.toUpperCase());
			Assert.assertEquals(document.getString("telephone"), telephone);
			Assert.assertEquals(document.getString("lead_step"), leadStep);
			Assert.assertEquals(document.getString("lead_type"), "Buy_On_Call");

			Map<String, Object> object1 = new HashMap<String, Object>();
			object1.put("Referrer",telephone);
			List<Document> list1 = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", object);
			Thread.sleep(4000);
			if(list1.size() > 0){
			contact_sync = "Yes";
			}else{
				contact_sync = "No";
			}
			log.info(contact_sync);
			Assert.assertEquals(document.getString("contact_sync"), contact_sync);
			
			Map<String, Object> object2 = new HashMap<String, Object>();
			object2.put("telephone",telephone);
			List<Document> list2 = mongoConnectionObject2.executeQuery("customer_v2", object2);
			Thread.sleep(2000);

			if(list2.size() > 0){
			JSONObject document2 = new JSONObject(list2.get(0).toJson());
			isLoyal = document2.getBoolean("isLoyalty");
			try{
				lastname = document2.getString("lastName");
				}catch(Exception e){
					e.printStackTrace();
				}
			
			firstname = document2.getString("firstName");
			email = document2.getString("email");
			log.info(isLoyal);
			log.info(lastname);
			log.info(firstname);
			log.info(email);
			 if(isLoyal){
				  is_gold_member  ="Yes";
			 }
			 log.info(is_gold_member);
			 
			Assert.assertEquals(document.getString("first_name"), firstname);
			if(lastname !=null){
			Assert.assertEquals(document.getString("last_name"), lastname);
			}
			Assert.assertEquals(document.getString("email"), email);
			Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
			String sc =	getStoreCreditDetailBasedOnEmail(email);
			if(!sc.isEmpty()){
			Assert.assertEquals(document.getDouble("store_credit"),Double.parseDouble(sc));
			}
		}
			else{
				Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
			}
	} 
	}
	
	@Test
	public void testbyRemoving_telephone_Param() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(XApiClient.DESKTOP, CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,XApiClient.DESKTOP), "!@QWE#1456"),
				requestJSON1("Product", "12345", null, "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Invalid request data");
	}
	
	@Test
	public void testbyRemoving_leadStep_Param() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(XApiClient.DESKTOP, CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,XApiClient.DESKTOP), "!@QWE#1456"),
				requestJSON1(null, "12345", "**********", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Invalid request data");
	}
	
	@Test
	public void testbyRemoving_id_Param() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(XApiClient.DESKTOP, CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,XApiClient.DESKTOP), "!@QWE#1456"),
				requestJSON1("home", "", "**********", "BUYONCALL"));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Invalid request data");
	}
	
	@Test
	public void testbyRemoving_type_Param() throws Exception{
		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(XApiClient.DESKTOP, CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,XApiClient.DESKTOP), "!@QWE#1456"),
				requestJSON1("home", "123456", "**********", ""));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 400, "Status code is not correct.");
		Assert.assertEquals(responseJSON.getString("message"), "Invalid event type");
	}
	
	@Test
	public void testby_updating_the_api_for_same_session(){
		
	}
	
	@DataProvider(name = "Buy_On_call_useCases_loggedInUser")
	public static Object[][] useCases_loggedinUSer() throws Exception {
		return new Object[][] { 
			
{"Sucessful Usecase for leadStep-Product for android",XApiClient.ANDROID, "PRODUCT", pids.get(0), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for ios",XApiClient.IOS, "PRODUCT", pids.get(1), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for desktop",XApiClient.DESKTOP, "PRODUCT", pids.get(2), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for msite",XApiClient.MOBILESITE, "PRODUCT", pids.get(3), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for android",XApiClient.ANDROID, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for ios",XApiClient.IOS, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for desktop",XApiClient.DESKTOP, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for msite",XApiClient.MOBILESITE, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for android",XApiClient.ANDROID, "COLLECTION", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for ios",XApiClient.IOS, "COLLECTION", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for desktop",XApiClient.DESKTOP, "COLLECTION", "premium_eyeglasses", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for msite",XApiClient.MOBILESITE, "COLLECTION", "sunglasses", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for android",XApiClient.ANDROID, "CATEGORY", ProductCategories.EYEGLASSES, 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for ios",XApiClient.IOS, "CATEGORY", ProductCategories.SUNGLASSES, 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for desktop",XApiClient.DESKTOP, "CATEGORY",ProductCategories.CONTACT_LENS, 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for msite",XApiClient.MOBILESITE, "CATEGORY", ProductCategories.ACCESSORIES, 200, "The request has been completed successfully and pushed for processing." },
{"invalid lead step",XApiClient.MOBILESITE, "hom", ProductCategories.CATEGORY_EYEGLASSES_JJ, 400, "Invalid Lead Step"},
{"empty lead step",XApiClient.MOBILESITE, "", ProductCategories.CATEGORY_EYEGLASSES_JJ, 400, "Invalid Lead Step"},
{"Empty id for leadStep-Product",XApiClient.DESKTOP, "product", "", 400, "Id can not be blank."},
{"Empty id for leadStep-category",XApiClient.IOS, "category", "", 400, "Id can not be blank."},
{"Empty x_api_client","", "category", "", 400, "Missing Header: API Client"},

		};
	}

	
	@Test(dataProvider="Buy_On_call_useCases_loggedInUser",enabled=false)
	public void loggedInUser_buyOnCall_Usecases(String testCaseName, String x_Api_Client,String leadStep, String id, int statusCode, String message) throws Exception {
		String session = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword,XApiClient.ANDROID);
		String type = "BUYONCALL";
		String contact_sync = null;
		String udid = null;
		String lastname = null;
		String firstname = null;
		String email = null;
		Boolean isLoyal;
		double lkCash = 0;
		double lkCashPlus = 0;
		String is_gold_member = "No";
		if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			udid = String.valueOf(UUID.randomUUID());
		}
		JSONObject getCustomerDetails = CustomerUtil.getMeCustomerDetails(session, XApiClient.IOS);
		String telephone = getCustomerDetails.getString("telephone");
		if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
				udid = String.valueOf(UUID.randomUUID());
			}

		HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client, session, udid),
				requestJSON(leadStep, id, telephone, type));
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		Assert.assertEquals(httpStatusCode, statusCode, "Status code is not correct.");

		JSONObject resultObject = responseJSON;
		if (statusCode == HttpStatus.SC_OK) {
			resultObject = responseJSON.getJSONObject("result");
			Assert.assertEquals(responseJSON.getJSONObject("result").getBoolean("success"), true, "mismatch in result");
			
			if (db_Validation) {
				
				Map<String, Object> object = new HashMap<String, Object>();
				object.put("session_id",session);
				List<Document> resultList = queryMongoWithWaitForResults(mongoConnectionObject, "buyoncall", object);
				if(resultList == null){
					Assert.assertFalse(true, "No documents fetched from mongo after timeout");
				}
				JSONObject document = new JSONObject(resultList.get(0).toJson());
				log.info(document);
				if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
				Assert.assertEquals(document.getString("device_id"), udid);
				}
				Assert.assertEquals(document.getString("session_id"), session);
				Assert.assertEquals(document.getString("lead_source"), x_Api_Client.toUpperCase());
				Assert.assertEquals(document.getString("telephone"), telephone);
				Assert.assertEquals(document.getString("lead_step"), leadStep);
				Assert.assertEquals(document.getString("lead_type"), "Buy_On_Call");
				
				if(leadStep.equalsIgnoreCase("PRODUCT")){
				JSONObject product_response = ProductUtil.getProductDetails(id);
				Assert.assertEquals(document.getString("boc_product_id"), id);
				Assert.assertEquals(document.getString("category"), product_response.getJSONObject("result").getString("type"));
				Assert.assertEquals(document.getString("amount"), product_response.getJSONObject("result").getJSONArray("prices").getJSONObject(1).getString("price"));
				}
				if(document.has("lk_balance")||document.has("lkplus_balance")){
					JSONObject getWalletBalanceDetails = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone, "android");
					lkCash = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(0).getInt("balance");
					lkCashPlus = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(1).getInt("balance");
				}
				if(lkCash>0){
					Assert.assertEquals(document.getDouble("lk_balance"), lkCash);
				}
				if(lkCashPlus>0){
					Assert.assertEquals(document.getDouble("lkplus_balance"), lkCashPlus);
				}
	
				Map<String, Object> object1 = new HashMap<String, Object>();
				object1.put("Referrer",telephone);
				List<Document> list1 = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", object);
				Thread.sleep(4000);
				if(list1.size() > 0){
				contact_sync = "Yes";
				}else{
					contact_sync = "No";
				}
				log.info(contact_sync);
				Assert.assertEquals(document.getString("contact_sync"), contact_sync);
				
				Map<String, Object> object2 = new HashMap<String, Object>();
				object2.put("telephone",telephone);
				List<Document> list2 = mongoConnectionObject2.executeQuery("customer_v2", object2);
				Thread.sleep(2000);

				if(list2.size() > 0){
				JSONObject document2 = new JSONObject(list2.get(0).toJson());
				isLoyal = document2.getBoolean("isLoyalty");
				try{
					lastname = document2.getString("lastName");
					}catch(Exception e){
						e.printStackTrace();
					}
				
				firstname = document2.getString("firstName");
				email = document2.getString("email");
				log.info(isLoyal);
				log.info(lastname);
				log.info(firstname);
				log.info(email);
				 if(isLoyal){
					  is_gold_member  ="Yes";
				 }
				 log.info(is_gold_member);
				 
				Assert.assertEquals(document.getString("first_name"), firstname);
				if(lastname !=null){
				Assert.assertEquals(document.getString("last_name"), lastname);
				}
				Assert.assertEquals(document.getString("email"), email);
				Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
				String sc =	getStoreCreditDetailBasedOnEmail(email);
				if(!sc.isEmpty()){
				Assert.assertEquals(document.getDouble("store_credit"),Double.parseDouble(sc));
				}
			}
				else{
					Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
				}
			}
		} else {
			resultObject = responseJSON;
		} 

		Assert.assertEquals(resultObject.getString("message"), message, "mismatch in result");
	} 



@DataProvider(name = "Buy_On_call_useCases_mobileUser")
public static Object[][] useCases_mobileUser() throws Exception {
	return new Object[][] { 
		
{"Sucessful Usecase for leadStep-Product for android",XApiClient.ANDROID, "PRODUCT", pids.get(0), 200, "The request has been completed successfully and pushed for processing." },

{"Sucessful Usecase for leadStep-Product for ios",XApiClient.IOS, "PRODUCT", pids.get(1), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for desktop",XApiClient.DESKTOP, "PRODUCT", pids.get(2), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Product for msite",XApiClient.MOBILESITE, "PRODUCT", pids.get(3), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for android",XApiClient.ANDROID, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for ios",XApiClient.IOS, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for desktop",XApiClient.DESKTOP, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-Home for msite",XApiClient.MOBILESITE, "HOME", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for android",XApiClient.ANDROID, "COLLECTION", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for ios",XApiClient.IOS, "COLLECTION", "", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for desktop",XApiClient.DESKTOP, "COLLECTION", "premium_eyeglasses", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-collection for msite",XApiClient.MOBILESITE, "COLLECTION", "sunglasses", 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for android",XApiClient.ANDROID, "CATEGORY", ProductCategories.EYEGLASSES, 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for ios",XApiClient.IOS, "CATEGORY", ProductCategories.SUNGLASSES, 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for desktop",XApiClient.DESKTOP, "CATEGORY",ProductCategories.CONTACT_LENS, 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-CATEGORY for msite",XApiClient.MOBILESITE, "CATEGORY", ProductCategories.ACCESSORIES, 200, "The request has been completed successfully and pushed for processing." },
{"invalid lead step",XApiClient.MOBILESITE, "hom", ProductCategories.CATEGORY_EYEGLASSES_JJ, 400, "Invalid Lead Step"},
{"empty lead step",XApiClient.MOBILESITE, "", ProductCategories.CATEGORY_EYEGLASSES_JJ, 400, "Invalid Lead Step"},
{"Empty id for leadStep-Product",XApiClient.DESKTOP, "PRODUCT", "", 400, "Id can not be blank."},
{"Empty id for leadStep-category",XApiClient.IOS, "CATEGORY", "", 400, "Id can not be blank."},
};
}


@Test(dataProvider="Buy_On_call_useCases_mobileUser")
public void mobileUser_buyOnCall_Usecases(String testCaseName, String x_Api_Client,String leadStep, String id, int statusCode, String message) throws Exception {
	String create_session = SessionUtil.createNewSession();
	JSONObject responseJSON_mobileAuthenticate = CustomerUtil.mobileAuthenticateWithoutReferCode(x_Api_Client, create_session, ReturnMasterOtp.returnMasterOtp(), "**********"); 
	String session = responseJSON_mobileAuthenticate.getJSONObject("result").getString("token");
	
	String type = "BUYONCALL";
	String contact_sync = null;
	String udid = null;	
	String lastname = null;
	String firstname = null;
	String email = null;
	Boolean isLoyal;
	double lkCash = 0;
	double lkCashPlus = 0;
	String is_gold_member = "No";
	if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
		udid = String.valueOf(UUID.randomUUID());
	}
	JSONObject getCustomerDetails = CustomerUtil.getMeCustomerDetails(session, XApiClient.IOS);
	String telephone = getCustomerDetails.getString("telephone");
	if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			udid = String.valueOf(UUID.randomUUID());
		}

	HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client, session, udid),
			requestJSON(leadStep, id, telephone, type));
	JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
	int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
	Assert.assertEquals(httpStatusCode, statusCode, "Status code is not correct.");

	JSONObject resultObject = responseJSON;
	if (statusCode == HttpStatus.SC_OK) {
		resultObject = responseJSON.getJSONObject("result");
		Assert.assertEquals(responseJSON.getJSONObject("result").getBoolean("success"), true, "mismatch in result");
		
		if (db_Validation) {
			
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("session_id",session);
			List<Document> resultList = queryMongoWithWaitForResults(mongoConnectionObject, "buyoncall", object);
			if(resultList == null){
				Assert.assertFalse(true, "No documents fetched from mongo after timeout");
			}
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document);
			if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			Assert.assertEquals(document.getString("device_id"), udid);
			}
			Assert.assertEquals(document.getString("session_id"), session);
			Assert.assertEquals(document.getString("lead_source"), x_Api_Client.toUpperCase());
			Assert.assertEquals(document.getString("telephone"), telephone);
			Assert.assertEquals(document.getString("lead_step"), leadStep);
			Assert.assertEquals(document.getString("lead_type"), "Buy_On_Call");
			
			if(leadStep.equalsIgnoreCase("PRODUCT")){
			JSONObject product_response = ProductUtil.getProductDetails(id);
			Assert.assertEquals(document.getString("boc_product_id"), id);
			Assert.assertEquals(document.getString("category"), product_response.getJSONObject("result").getString("type"));
			Assert.assertEquals(document.getString("amount"), product_response.getJSONObject("result").getJSONArray("prices").getJSONObject(1).getString("price"));
			}
			if(document.has("lk_balance")||document.has("lkplus_balance")){
				JSONObject getWalletBalanceDetails = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone, "android");
				lkCash = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(0).getInt("balance");
				log.info(lkCash);
				lkCashPlus = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(1).getInt("balance");
			}
			if(lkCash>0){
				Assert.assertEquals(document.getDouble("lk_balance"), lkCash);
			}
			if(lkCashPlus>0){
				Assert.assertEquals(document.getDouble("lkplus_balance"), lkCashPlus);
			}

			Map<String, Object> object1 = new HashMap<String, Object>();
			object1.put("Referrer",telephone);
			List<Document> list1 = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", object);
			Thread.sleep(4000);
			if(list1.size() > 0){
			contact_sync = "Yes";
			}else{
				contact_sync = "No";
			}
			log.info(contact_sync);
			Assert.assertEquals(document.getString("contact_sync"), contact_sync);
			
			Map<String, Object> object2 = new HashMap<String, Object>();
			object2.put("telephone",telephone);
			List<Document> list2 = mongoConnectionObject2.executeQuery("customer_v2", object2);
			Thread.sleep(2000);

			if(list2.size() > 0){
			JSONObject document2 = new JSONObject(list2.get(0).toJson());
			isLoyal = document2.getBoolean("isLoyalty");
			try{
				lastname = document2.getString("lastName");
				}catch(Exception e){
					e.printStackTrace();
				}
			
			firstname = document2.getString("firstName");
			email = document2.getString("email");
			log.info(isLoyal);
			log.info(lastname);
			log.info(firstname);
			log.info(email);
			 if(isLoyal){
				  is_gold_member  ="Yes";
			 }
			 log.info(is_gold_member);
			 
			Assert.assertEquals(document.getString("first_name"), firstname);
			if(lastname !=null){
			Assert.assertEquals(document.getString("last_name"), lastname);
			}
			Assert.assertEquals(document.getString("email"), email);
			Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
			String sc =	getStoreCreditDetailBasedOnEmail(email);
			if(!sc.isEmpty()){
			Assert.assertEquals(document.getDouble("store_credit"),Double.parseDouble(sc));
			}
		}
			else{
				Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
			}
		}
	} else {
		resultObject = responseJSON;
	} 

	Assert.assertEquals(resultObject.getString("message"), message, "mismatch in result");
} 

@Test
public void update_buy_on_call_lead_byChangingPID() throws Exception{
	String telephone = GenericUtil.createRandomNumber(10);
	String session = CustomerUtil.mobileAuthenticateSession(XApiClient.ANDROID, telephone);
	String x_Api_Client = XApiClient.ANDROID;
	String leadStep = "PRODUCT";
	String id = pids.get(0);
	String contact_sync = null;
	String udid = null;
	String lastname = null;
	String firstname = null;
	String email = null;
	Boolean isLoyal;
	double lkCash = 0;
	double lkCashPlus = 0;
	String is_gold_member = "No";
	JSONObject getCustomerDetails = CustomerUtil.getMeCustomerDetails(session, XApiClient.IOS);
	if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
			udid = String.valueOf(UUID.randomUUID());
		}
	
	
	
	HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(XApiClient.ANDROID, session, udid),
			requestJSON(leadStep, id, telephone, "BUYONCALL"));
	JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
	int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
	Assert.assertEquals(httpStatusCode, 200, "Status code is not correct.");
	Assert.assertEquals(responseJSON.getJSONObject("result").getBoolean("success"), true, "mismatch in result");
	
	if(db_Validation){
		Map<String, Object> object = new HashMap<String, Object>();
		object.put("session_id",session);
		List<Document> resultList = queryMongoWithWaitForResults(mongoConnectionObject, "buyoncall", object);
		if(resultList == null){
			Assert.assertFalse(true, "No documents fetched from mongo after timeout");
		}
		JSONObject document = new JSONObject(resultList.get(0).toJson());
		log.info(document);
		if(x_Api_Client.equalsIgnoreCase("android") || x_Api_Client.equalsIgnoreCase("ios")){
		Assert.assertEquals(document.getString("device_id"), udid);
		}
		Assert.assertEquals(document.getString("session_id"), session);
		Assert.assertEquals(document.getString("lead_source"), x_Api_Client.toUpperCase());
		Assert.assertEquals(document.getString("telephone"), telephone);
		Assert.assertEquals(document.getString("lead_step"), leadStep);
		Assert.assertEquals(document.getString("lead_type"), "Buy_On_Call");
		
		if(leadStep.equalsIgnoreCase("PRODUCT")){
		JSONObject product_response = ProductUtil.getProductDetails(id);
		Assert.assertEquals(document.getString("boc_product_id"), id);
		Assert.assertEquals(document.getString("category"), product_response.getJSONObject("result").getString("type"));
		Assert.assertEquals(document.getString("amount"), product_response.getJSONObject("result").getJSONArray("prices").getJSONObject(1).getString("price"));
		}
		if(document.has("lk_balance")||document.has("lkplus_balance")){
			JSONObject getWalletBalanceDetails = MoneyUtil.getWalletBalanceOnlyWithMobileNumber(telephone, "android");
			lkCash = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(0).getInt("balance");
			log.info(lkCash);
			lkCashPlus = getWalletBalanceDetails.getJSONObject("result").getJSONArray("wallets").getJSONObject(1).getInt("balance");
		}
		if(lkCash>0){
			Assert.assertEquals(document.getDouble("lk_balance"), lkCash);
		}
		if(lkCashPlus>0){
			Assert.assertEquals(document.getDouble("lkplus_balance"), lkCashPlus);
		}

		Map<String, Object> object1 = new HashMap<String, Object>();
		object1.put("Referrer",telephone);
		List<Document> list1 = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", object);
		Thread.sleep(4000);
		if(list1.size() > 0){
		contact_sync = "Yes";
		}else{
			contact_sync = "No";
		}
		log.info(contact_sync);
		Assert.assertEquals(document.getString("contact_sync"), contact_sync);
		
		Map<String, Object> object2 = new HashMap<String, Object>();
		object2.put("telephone",telephone);
		List<Document> list2 = mongoConnectionObject2.executeQuery("customer_v2", object2);
		Thread.sleep(2000);

		if(list2.size() > 0){
		JSONObject document2 = new JSONObject(list2.get(0).toJson());
		isLoyal = document2.getBoolean("isLoyalty");
		try{
			lastname = document2.getString("lastName");
			}catch(Exception e){
				e.printStackTrace();
			}
		
		firstname = document2.getString("firstName");
		email = document2.getString("email");
		log.info(isLoyal);
		log.info(lastname);
		log.info(firstname);
		log.info(email);
		 if(isLoyal){
			  is_gold_member  ="Yes";
		 }
		 log.info(is_gold_member);
		 
		Assert.assertEquals(document.getString("first_name"), firstname);
		if(lastname !=null){
		Assert.assertEquals(document.getString("last_name"), lastname);
		}
		Assert.assertEquals(document.getString("email"), email);
		Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
		String sc =	getStoreCreditDetailBasedOnEmail(email);
		if(!sc.isEmpty()){
		Assert.assertEquals(document.getDouble("store_credit"),Double.parseDouble(sc));
		}
	}
		else{
			Assert.assertEquals(document.getString("is_gold_member"), is_gold_member);
		}
	
		
		
	}
	
	
}



/*@DataProvider(name = "leadStepParamsInLowerCase")
public static Object[][] leadStepParamInlowerCase() throws Exception {
	return new Object[][] { 
		
{"Sucessful Usecase for leadStep-Product in lowercase for android",SessionUtil.createNewSession(),XApiClient.ANDROID, "product", pids.get(2), 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-home in lowercase for ios",CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail, ApplicationConstants.userPassword),XApiClient.IOS, "home", "", 200, "The request has been completed successfully and pushed for processing." },	
{"Sucessful Usecase for leadStep-category in lowercase for msite",SessionUtil.createNewSession(),XApiClient.MOBILESITE, "category", ProductCategories.SUNGLASSES, 200, "The request has been completed successfully and pushed for processing." },
{"Sucessful Usecase for leadStep-category in lowercase for msite",SessionUtil.createNewSession(),XApiClient.MOBILESITE, "collection", "", 200, "The request has been completed successfully and pushed for processing." },};
	}
@Test(enabled = false)
public void allLeadStepParamInlowerCase(String testCaseName, String session, String x_Api_Client,
		String leadStep, String id, String telephone, String type, int statusCode, String message) throws Exception{
	
	String udid = String.valueOf(UUID.randomUUID());
	HttpResponse httpResponse = RequestUtil.postRequest(buyOnCallURL, getHeader(x_Api_Client, session, udid),
			requestJSON(leadStep, id, telephone, type));
	JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
	int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
	Assert.assertEquals(httpStatusCode, statusCode, "Status code is not correct.");

	JSONObject resultObject = responseJSON;
	if (statusCode == HttpStatus.SC_OK) {
		resultObject = responseJSON.getJSONObject("result");
		Assert.assertEquals(responseJSON.getJSONObject("result").getBoolean("success"), true, "mismatch in result");
		
		if (db_Validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("session_id",session);
			Thread.sleep(3000);
			List<Document> list = mongoConnectionObject.executeQuery("buyoncall", object);
			Thread.sleep(3000);

			JSONObject document = new JSONObject(list.get(0).toJson());
			log.info(document);
			Assert.assertEquals(document.getString("device_id"), udid);
			Assert.assertEquals(document.getString("session_id"), session);
			Assert.assertEquals(document.getString("lead_source"), x_Api_Client.toUpperCase());
			Assert.assertEquals(document.getString("telephone"), telephone);
			Assert.assertEquals(document.getString("lead_step"), leadStep);
			Assert.assertEquals(document.getString("lead_type"), "Buy_On_Call");
			
			if(leadStep.equalsIgnoreCase("PRODUCT")){
			JSONObject product_response = ProductUtil.getProductDetails(id);
			Assert.assertEquals(document.getString("boc_product_id"), id);
			Assert.assertEquals(document.getString("category"), product_response.getJSONObject("result").getString("brandName"));
			Assert.assertEquals(document.getString("amount"), product_response.getJSONObject("result").getJSONArray("prices").getJSONObject(1).getString("price"));
			}
		//	Assert.assertEquals(document.getString("first_name"), telephone);
		//	Assert.assertEquals(document.getString("last_name"), telephone);
			
		//	Assert.assertEquals(document.getString("is_gold_member"), telephone);
		//	Assert.assertEquals(document.getString("contact_sync"), telephone);
		// doubts - gold member, email, lk+ and lk cash, SC, FN and LN for guest users
		}
	} else {
		resultObject = responseJSON;
	}

	Assert.assertEquals(resultObject.getString("message"), message, "mismatch in result");

}*/
@Test(enabled=false)
public void test() throws Exception{
	String contact_sync = null;
	String str = "100.0000";
	String lastname = null;
	String firstname = null;
	String email = null;
	Boolean isLoyal = false;
	log.info(str.replaceAll("\\.0*$", ""));
	log.info(str.replaceAll("(?<=^\\d+)\\.0*$", ""));
	double lkCash = 0.1;
	double lkCashPlus = 0.1;

	if(lkCash>0){
log.info("pass");	}
	if(lkCashPlus>0){
		log.info("pass");	}
	log.info(getStoreCreditDetailBasedOnEmail("<EMAIL>"));
	String text = "3178.0000"; // example String
	double value = Double.parseDouble(text);
	log.info(value);
	log.info(getStoreCreditDetailBasedOnEmail("<EMAIL>"));
	String sc =	getStoreCreditDetailBasedOnEmail("<EMAIL>");
	if(!sc.isEmpty()){
	Assert.assertEquals(3718.0,Double.parseDouble(sc));
	}else{
		log.info("gjhjhbcjh8979878978798ghvdnbvcbvbcvchcvhcvhcvhcvhcvhg");
	}
	//double storeCredit = Double.parseDouble(getStoreCreditDetailBasedOnEmail("<EMAIL>"));
	//log.info(storeCredit);

//log.info(CustomerUtil.mobileAuthenticateSession("android", "2800765432"));

if (db_Validation) {

/*	Map<String, Object> object = new HashMap<String, Object>();
	object.put("mobile","8095726310");
	List<Document> list = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", object);
	try{
	JSONObject document = new JSONObject(list.get(0).toJson());
	contact_sync = "yes";
	}catch(IndexOutOfBoundsException e){
		contact_sync = "no";
	}
	log.info(contact_sync);

	Map<String, Object> object2 = new HashMap<String, Object>();
	object2.put("telephone","9876543210");
	List<Document> list2 = mongoConnectionObject2.executeQuery("customer_v2", object2);
	if(list2.size() > 0){
	JSONObject document2 = new JSONObject(list2.get(0).toJson());
	isLoyal = document2.getBoolean("isLoyalty");
	lastname = document2.getString("lastName");
	firstname = document2.getString("firstName");
	email = document2.getString("email");

	log.info(isLoyal);
	log.info(lastname);
	log.info(firstname);
	log.info(email);
	}else{
		
		
	}
	//log.info(getStoreCreditDetailBasedOnEmail("<EMAIL>"));
	String sc_db = getStoreCreditDetailBasedOnEmail("<EMAIL>");
	String[] namesList = sc_db.split(",");
	log.info(namesList[2]);
	String multiSC[]=sc_db.split("\r\n");
	//String sc[] = multiSC[].split(",");*/
	
}

}

@Test(enabled=true)
public void check() throws Exception{
	/*Map<String, Object> queryMap = new HashMap<>();
	BasicDBList dbl = new BasicDBList();
	dbl.add(new BasicDBObject("mobile","**********"));
	dbl.add(new BasicDBObject("Referrer", "**********"));
	//queryMap.put("$or", "[{\"mobile\": \"**********\"},{\"Referrer\" : \"**********\"}]");
	queryMap.put("$or",dbl);
	List<Document> executeQuery = mongoConnectionObject1.executeQuery("camp_InviteReferral_ContactSync", queryMap);
	
	for(Document doc :executeQuery ){
		System.out.println(doc);
	}*/
	
	String sc =	"312";
	
	if(!sc.isEmpty()){
		log.info("ggggg");
	}
	
}
	
}



	// Store credit case
	// lower case for all lead step -- done
	//invalid type - buy on call -- done
	// empty udid for msite and desktop, android, ios -- done
	//remove client, headers, payload  -- done 
	// update check 
	//authenticate session and mobile authentication -- done
	//guest session with registered user/no. - done
	//automate bugs
	//automate vikas scenarios
	//one mobile with multiple email
	//mobile and auth session... with different no.

