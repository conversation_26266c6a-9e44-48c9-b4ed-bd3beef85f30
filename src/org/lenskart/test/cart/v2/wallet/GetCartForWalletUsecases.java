package org.lenskart.test.cart.v2.wallet;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.CartPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.MoneyUtil;
import org.lenskart.core.util.SessionUtil;
import org.lenskart.test.customer.mobilelogin.MobileAndEmailLogin;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.v2.cart.CartItem;
import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.lenskart.juno.schema.v2.common.PrescriptionType;
import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.JsonUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;

public class GetCartForWalletUsecases {
	private static final Logger log = GenericUtil.InitLogger(GetCartForWalletUsecases.class);
	PropertyFactory pf = new PropertyFactory();
	boolean mongoValidationFlag = Environments.dbConnectionFlag;
	private MongoConnectionUtility mongoConnectionObject = null;
	private static HashMap<String, String> product = new HashMap<>();
	private static JSONObject getCartResponse = null;
	private static HashMap<String, Double> codeAmount = new HashMap<String, Double>();
	private static String gvCode = "LKAUTOMATION";
	private static DecimalFormat df = new DecimalFormat(".##");

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (mongoValidationFlag) {
			mongoConnectionObject = CartUtil.getCartMongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();
		}
	}

	@AfterClass
	public void closeDBConection() {
		if (mongoValidationFlag) {
			mongoConnectionObject.closeMongoConnection();
		}
	}

	public List<NameValuePair> getHeader(String sessionToken, String xApiClient) throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		return headers;
	}

	@DataProvider(name = "getCartForPreniumProduct")
	public static Iterator<String[]> supplyData() throws IOException {
		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "/csv_files/Wallet/getCartForPreniumProduct.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "getCartForNonPreniumProduct")
	public static Iterator<String[]> supplyData1() throws IOException {
		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "/csv_files/Wallet/getCartForNonPreniumProduct.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	private void walletValidation(JSONObject responseGetCartForWallet) throws Exception {
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertTrue(responseGetCartForWalletResult.has("wallets"), "wallets is not present in response");
		Assert.assertNotNull(responseGetCartForWalletResult.getJSONArray("wallets"),
				"wallets is not present in response");
		double applicableAmount = 0;
		JSONArray wallets = responseGetCartForWalletResult.getJSONArray("wallets");
		for (int i = wallets.length() - 1; i >= 0; i--) {
			log.info("hfiuweriwekdwknd");
			JSONObject walletsJson = wallets.getJSONObject(i);
			Assert.assertNotNull(walletsJson.getString("message"), "message is null");
			double balance = MoneyUtil.getWalletBalanceBasedOnWalletType(
					responseGetCartForWalletResult.getJSONObject("customer").getString("telephone"),
					Environments.X_API_CLIENT, walletsJson.getString("type"));
			Assert.assertEquals(walletsJson.getDouble("balance"), balance, "balance is not correct");
			double implicitAmount = 0;
			for (int j = 0; j < responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts")
					.length(); j++) {
				if (responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").getJSONObject(j)
						.getString("type").equals("implicit"))
					implicitAmount = responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts")
							.getJSONObject(j).getDouble("amount");
			}
			double applyDiscountAmount = (responseGetCartForWalletResult.getJSONObject("totals").getDouble("subTotal")
					+ responseGetCartForWalletResult.getJSONObject("totals").getDouble("shipping")) - implicitAmount
					- applicableAmount;
			applicableAmount = MoneyUtil.applicableAmountOnCart(applyDiscountAmount, walletsJson.getString("type"),
					balance, responseGetCartForWalletResult.getString("cartType"));
			codeAmount.put(walletsJson.getString("type"), applicableAmount);
			log.info("hsgdhjsfhd" + walletsJson.getString("type"));
			log.info("hsgdhjsfhd" + applicableAmount);
			log.info("hsgdhjsfhd" + walletsJson.getDouble("applicableAmount"));
			Assert.assertEquals(walletsJson.getInt("applicableAmount"), (int) Math.round(applicableAmount),
					"applicableAmount is not correct");
			responseGetCartForWalletResult.remove("wallets");
			responseGetCartForWalletResult.remove("cartVersion");
			responseGetCartForWallet.remove("traceId");
		}
	}

	private void totalsValidation(JSONObject responseGetCartForWallet) throws Exception {
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		JSONObject totals = responseGetCartForWalletResult.getJSONObject("totals");
		Assert.assertNotEquals(totals.getJSONArray("discounts").length(), 0, " discounts array is empty");
		JSONArray discounts = totals.getJSONArray("discounts");
		double totalDiscountAmount = 0;
		for (int i = 0; i < discounts.length(); i++) {
			JSONObject discountsJson = discounts.getJSONObject(i);
			Assert.assertNotNull(discountsJson.getString("type"), "type is not coming");
			Assert.assertNotNull(discountsJson.getString("amount"), "amount is not coming");
			totalDiscountAmount = discountsJson.getDouble("amount") + totalDiscountAmount;
			if (discountsJson.getString("type").equals("lenskart")
					|| discountsJson.getString("type").equals("lenskartplus")) {
				Assert.assertEquals(discountsJson.getInt("amount"),
						(int) Math.round(codeAmount.get(discountsJson.getString("type"))),
						"applicableAmount is not correct");
			}

		}
		double totalDiscount = totals.getDouble("totalDiscount");
		Assert.assertEquals(totalDiscountAmount, totalDiscount, "Total discount applied is mismatching");
		log.info("dmfnjksdfkd" + totals.getDouble("total"));
		log.info("dmfnjksdfkd" + (totals.getDouble("subTotal") + totals.getDouble("shipping") - totalDiscount));
		Assert.assertEquals(totals.getInt("total"),
				(int)(Double.parseDouble(df.format(
						(totals.getDouble("subTotal") + totals.getDouble("totalTax") + totals.getDouble("shipping"))
								- totalDiscount))),
				"total balance is not correct");
		responseGetCartForWalletResult.remove("totals");
		responseGetCartForWalletResult.remove("cartVersion");
		JSONArray items = responseGetCartForWalletResult.getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			JSONObject itemsJson = items.getJSONObject(i);
			itemsJson.getJSONObject("amount").remove("total");
			itemsJson.getJSONObject("amount").remove("totalDiscount");
			itemsJson.getJSONObject("amount").remove("totalTax");
			itemsJson.getJSONObject("amount").remove("taxes");
		}
	}

	public void responeValidation(JSONObject responseGetCartForWallet, boolean applyWallet) throws Exception {

		if (applyWallet) {
			walletValidation(responseGetCartForWallet);
			totalsValidation(responseGetCartForWallet);
			getCartResponse.getJSONObject("result").remove("totals");
			getCartResponse.getJSONObject("result").remove("total");
			getCartResponse.remove("traceId");
			getCartResponse.getJSONObject("result").remove("cartVersion");
			JSONArray items = getCartResponse.getJSONObject("result").getJSONArray("items");
			for (int i = 0; i < items.length(); i++) {
				JSONObject itemsJson = items.getJSONObject(i);
				itemsJson.getJSONObject("amount").remove("total");
				itemsJson.getJSONObject("amount").remove("totalDiscount");
				itemsJson.getJSONObject("amount").remove("totalTax");
				itemsJson.getJSONObject("amount").remove("taxes");
			}
		} else {
			walletValidation(responseGetCartForWallet);
			JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
			JSONObject totals = responseGetCartForWalletResult.getJSONObject("totals");
			Assert.assertEquals(totals.getJSONArray("discounts").length(), 0, " discounts array is empty");
			getCartResponse.remove("traceId");
			getCartResponse.getJSONObject("result").remove("cartVersion");
		}

		log.info("eijfoewo3" + responseGetCartForWallet);
		log.info("eijfoewo3" + getCartResponse);
		Assert.assertTrue(JsonUtil.compare_json_objects(responseGetCartForWallet, getCartResponse),
				"JsonObject are not same");
	}

	public void addToCartWithDifferentCatagoryId(String sessionToken, String categoryName) throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId(categoryName),
				Environments.X_API_CLIENT);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		JSONObject cart_response = CartUtil.createCart(sessionToken, reqObj,Environments.X_API_CLIENT);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id in create cart response is null.");
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessionToken, Environments.X_API_CLIENT, address, null);
		getCartResponse = CartUtil.getCart(sessionToken, Environments.X_API_CLIENT);
	}

	@Test(enabled = true) // 5% discount for lenskart
	public void getCartForWallet_mobileLogin_WalletOnly_ApplyWalletTrue_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_WalletOnly_ApplyWalletFalse_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "false"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, false);
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_EmailNotVerified_CartNotPresent_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_EmailNotVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses");
		CartUtil.clearCart(sessionToken);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		Assert.assertEquals(responseGetCartForWallet.getJSONObject("result").getJSONArray("items").length(), 0,
				"Items should be empty");
	}

	@Test(enabled = true) // 0% discount for lenskart
	public void getCartForWallet_mobileLogin_EmailVerified_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_EmailVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		Assert.assertEquals(responseGetCartForWallet.getJSONObject("result").getJSONObject("totals")
				.getJSONArray("discounts").length(), 0, "Items should be empty");
	}

	@Test(enabled = true) // 10% discount in lenskart
	public void getCartForWallet_mobileLogin_EmailVerified_BothWalletType_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_EmailVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "100",
				"lenskartplus", null);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskartplus");
		Assert.assertEquals(100.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = false)
	public void getCartForWallet_mobileLogin_EmailVerified_BothWalletType_WithDifferentShipping_Successful()
			throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_EmailVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "100",
				"lenskartplus", null);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskartplus");
		Assert.assertEquals(100.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true) // 15% discount in lenskart
	public void getCartForWallet_emailLogin_MobileVerified_WalletTypeLenskart_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_MobileVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true) // Fully used lenskart plus only
	public void getCartForWallet_emailLogin_MobileVerified_WalletTypeLenskartPlus_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_MobileVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		MoneyUtil.bulkDebit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber,
				String.valueOf(amount), "lenskart");
		authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "600",
				"lenskartplus", null);
		amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, Environments.X_API_CLIENT,
				"lenskartplus");
		Assert.assertEquals(600.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true)
	public void getCartForWallet_emailLogin_NewMobileNumber_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_NewMobileNumber(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), Environments.X_API_CLIENT);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, true);
		String productId = product.get("productId");
		String packageId = product.get("packageId");
		String coating_id = product.get("coating_id");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setPackageId(packageId);
		reqObj.setQuantity(Integer.parseInt("1"));
		reqObj.setAddOns(coating_id);
		JSONObject cart_response = CartUtil.createCart(sessionToken, reqObj,Environments.X_API_CLIENT);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessionToken, Environments.X_API_CLIENT, address, null);
		getCartResponse = CartUtil.getCart(sessionToken, Environments.X_API_CLIENT);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true) // fully applied lenskart upto 500
	public void getCartForWallet_emailLogin_RegisteredMobileNumberNotVerified_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_RegisteredMobileNumberNotVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "6000",
				"lenskart", null);
		double lenskartAmount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		Assert.assertEquals(6000.0 + amount, lenskartAmount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = false) // fully applied lenskart upto 500
	public void getCartForWallet_emailLogin_RegisteredMobileNumberNotVerified_LenskartLessThan500_Successful()
			throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_RegisteredMobileNumberNotVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkDebit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber,
				String.valueOf(amount), "lenskart");
		double lenskartAmount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		Assert.assertEquals(lenskartAmount, 0.0, "Amount didnt get updated");

		authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "450",
				"lenskart", null);
		lenskartAmount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		Assert.assertEquals(Double.parseDouble("450"), lenskartAmount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		addToCartWithDifferentCatagoryId(sessionToken, "category_accessories");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true)
	public void getCartForWallet_emailLogin_MobileOnly_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_MobileOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_contact_lens_aqualens"),
				Environments.X_API_CLIENT);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "",
				false);
		String productId = product.get("productId");
		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setPrescription(presObj);
		JSONObject cart_response = CartUtil.createCart(sessionToken, reqObj,Environments.X_API_CLIENT);
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"), "Cart Id is null.");
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessionToken, Environments.X_API_CLIENT, address, null);
		getCartResponse = CartUtil.getCart(sessionToken, Environments.X_API_CLIENT);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true) // HEC Cart discount using lenskart amount only
	public void getCartForWallet_emailLogin_MobileOnlyWithEmailNotVerified_HEC_Lenskart_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_MobileOnlyWithEmailNotVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		JSONObject cart_response = CartUtil.HTOCart(sessionToken, Environments.X_API_CLIENT);
		Assert.assertEquals(cart_response.getJSONObject("result").getString("cartType"), "HEC", "Cart is not HEC");
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessionToken, Environments.X_API_CLIENT, address, null);
		getCartResponse = CartUtil.getCart(sessionToken, Environments.X_API_CLIENT);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true) // HEC Cart discount using Both Wallet Type
	public void getCartForWallet_emailLogin_MobileOnlyWithEmailNotVerified_HEC_BothWalletType_Successful()
			throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_MobileOnlyWithEmailNotVerified(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "10",
				"lenskartplus", null);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskartplus");
		Assert.assertEquals(10.0, amount, "Amount didnt get updated");
		JSONObject cart_response = CartUtil.HTOCart(sessionToken, Environments.X_API_CLIENT);
		Assert.assertEquals(cart_response.getJSONObject("result").getString("cartType"), "HEC", "Cart is not HEC");
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessionToken, Environments.X_API_CLIENT, address, null);
		getCartResponse = CartUtil.getCart(sessionToken, Environments.X_API_CLIENT);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true) // HEC Cart discount using lenskartplus amount only
	public void getCartForWallet_mobileLogin_WalletOnly_HEC_LenskartPlus_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		MoneyUtil.bulkDebit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber,
				String.valueOf(amount), "lenskart");
		authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "600",
				"lenskartplus", null);
		amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, Environments.X_API_CLIENT,
				"lenskartplus");
		Assert.assertEquals(600.0, amount, "Amount didnt get updated");
		JSONObject cart_response = CartUtil.HTOCart(sessionToken, Environments.X_API_CLIENT);
		Assert.assertEquals(cart_response.getJSONObject("result").getString("cartType"), "HEC", "Cart is not HEC");
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(sessionToken, Environments.X_API_CLIENT, address, null);
		getCartResponse = CartUtil.getCart(sessionToken, Environments.X_API_CLIENT);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true)
	public void getCartForWallet_emailLogin_MobileNumberNotPresentInDB_ApplyWalletTrue_Failurecase() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_MobileNumberNotPresentInDB(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallets array is empty");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");
	}

	@Test(enabled = true)
	public void getCartForWallet_emailLogin_MobileNumberNotPresentInDB_ApplyWalletFalse_Failurecase() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.emailLogin_MobileNumberNotPresentInDB(sessionToken,
				Environments.X_API_CLIENT, mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "false"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallets array is empty");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");

	}

	@Test(enabled = true)
	public void getCartForWallet_oldEmailLogin_Failurecase() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.oldEmailLogin(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallets array is empty");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");
	}

	@Test(enabled = true)
	public void getCartForWallet_GuestSession_Failurecase() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallets array is empty");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_WalletOnly_OtherPremiumProduct_LenskartWalletType_FailureCase()
			throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_rayban_sunglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");
		Assert.assertNotEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallet array is empty");
	}

	@Test(enabled = false)
	public void getCartForWallet_mobileLogin_WalletOnly_v3_SuccessfulCase() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_VC");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		CartUtil.addGiftVoucher(sessionToken, gvCode);
		String RequestUrl = "http://internal-juno-v2-279863379.ap-southeast-1.elb.amazonaws.com/juno-cart/v3/carts";
		httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrl, headers, null);
		JSONObject responseGetCartForWalletV3 = RequestUtil
				.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		Assert.assertTrue(JsonUtil.compare_json_objects(responseGetCartForWallet, responseGetCartForWalletV3),
				"JsonObject are not same");
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_WalletOnly_InvalidGV_FailureCase() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_VC");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject gvResponse = CartUtil.addGiftVoucher(sessionToken, "lkau");
		Assert.assertEquals(gvResponse.getInt("status"), 422, "status mismatch");
		Assert.assertEquals(gvResponse.getString("message"), "Voucher code lkau does not exist.", "message mismatch");
		Assert.assertEquals(gvResponse.getString("error"), "Unprocessable Entity", "error mismatch");
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_WalletOnly_OtherPremiumProduct_LenskartPlusWalletType_FailureCase()
			throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkDebit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber,
				String.valueOf(amount), "lenskart");
		authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "600",
				"lenskartplus", null);
		amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, Environments.X_API_CLIENT,
				"lenskartplus");
		Assert.assertEquals(600.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_rayban_sunglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");
		Assert.assertNotEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallet array is empty");
	}

	@Test(enabled = false) // Not implemented
	public void getCartForWallet_mobileLogin_WalletOnly_MixCart_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "100",
				"lenskartplus", null);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskartplus");
		Assert.assertEquals(100.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_rayban_sunglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallet array is empty");
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_WalletOnly_MixCartEyeglassSunglasses_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "100",
				"lenskartplus", null);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskartplus");
		Assert.assertEquals(100.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses_sunpocket");
		addToCartWithDifferentCatagoryId(sessionToken, "category_sunglasses_JJ");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_WalletOnly_ApplyWalletTrue_WithGV_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
		JSONObject gvResponse = CartUtil.addGiftVoucher(sessionToken, gvCode);
		Assert.assertTrue(gvResponse.has("result"),
				"API Response: " + GenericUtil.printAPICallDetails(null, gvResponse));
		JSONArray discounts = gvResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
		for (int i = 0; i < discounts.length(); i++) {
			Assert.assertNotEquals(discounts.getJSONObject(i).getString("type"), "lenskart",
					"lenskart Wallet should not be present");
			Assert.assertNotEquals(discounts.getJSONObject(i).getString("type"), "lenskartplus",
					"lenskartplus Wallet should not be present");
			if (discounts.getJSONObject(i).getString("type").equals("gv")) {
				Assert.assertEquals(discounts.getJSONObject(i).getString("code"), gvCode, "GV code mismatch");
			}
		}
	}

	@Test(enabled = true)
	public void getCartForWallet_mobileLogin_WalletOnly_ApplyWalletTrue_WithoutGV_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses_FFF");
		JSONObject gvResponse = CartUtil.addGiftVoucher(sessionToken, gvCode);
		Assert.assertTrue(gvResponse.has("result"),
				"API Response: " + GenericUtil.printAPICallDetails(null, gvResponse));
		JSONArray discounts = gvResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
		for (int i = 0; i < discounts.length(); i++) {
			Assert.assertNotEquals(discounts.getJSONObject(i).getString("type"), "lenskart",
					"lenskart Wallet should not be present");
			Assert.assertNotEquals(discounts.getJSONObject(i).getString("type"), "lenskartplus",
					"lenskartplus Wallet should not be present");
			if (discounts.getJSONObject(i).getString("type").equals("gv")) {
				Assert.assertEquals(discounts.getJSONObject(i).getString("code"), gvCode, "GV code mismatch");
			}
		}
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		Assert.assertNotNull(
				responseGetCartForWallet.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts"),
				"discount is null");
		JSONArray discount = responseGetCartForWallet.getJSONObject("result").getJSONObject("totals")
				.getJSONArray("discounts");
		for (int i = 0; i < discount.length(); i++) {
			Assert.assertEquals(discount.getJSONObject(i).getString("type"), "lenskart",
					"lenskart Wallet should not be present");
			Assert.assertNotEquals(discount.getJSONObject(i).getString("type"), "gv",
					"lenskartplus Wallet should not be present");
		}
	}

	@Test(enabled = false)
	public void getCartForWallet_mobileLogin_WalletOnly_BothGVAndWallet_Successful() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		addToCartWithDifferentCatagoryId(sessionToken, "category_eyeglasses");
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
		JSONObject gvResponse = CartUtil.addGiftVoucher(sessionToken, gvCode);
		Assert.assertTrue(gvResponse.has("result"),
				"API Response: " + GenericUtil.printAPICallDetails(null, gvResponse));
		JSONArray discounts = gvResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
		for (int i = 0; i < discounts.length(); i++) {
			if (discounts.getJSONObject(i).getString("type").equals("gv")) {
				Assert.assertEquals(discounts.getJSONObject(i).getString("code"), gvCode, "GV code mismatch");
			} else {
				Assert.assertEquals(discounts.getJSONObject(i).getString("type"), "lenskart",
						"lenskart Wallet should be present");
			}
		}
	}

	@Test(enabled = true, dataProvider = "getCartForPreniumProduct")
	public void getCartForWallet_mobileLogin_WalletOnly_ForAllPremiumProduct_Successful(String categoryName)
			throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		MoneyUtil.bulkDebit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber,
				String.valueOf(amount), "lenskart");
		authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "600",
				"lenskartplus", null);
		amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, Environments.X_API_CLIENT,
				"lenskartplus");
		Assert.assertEquals(600.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, categoryName);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject responseGetCartForWalletResult = responseGetCartForWallet.getJSONObject("result");
		Assert.assertEquals(responseGetCartForWalletResult.getJSONObject("totals").getJSONArray("discounts").length(),
				0, " discounts array is empty");
		Assert.assertNotEquals(responseGetCartForWalletResult.getJSONArray("wallets").length(), 0,
				" wallet array is empty");
		amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, Environments.X_API_CLIENT,
				"lenskartplus");
		Assert.assertEquals(600.0, amount, "Amount didnt get updated");
	}

	@Test(enabled = true, dataProvider = "getCartForNonPreniumProduct")
	public void getCartForWallet_mobileLogin_WalletOnly_ForAllNonPremiumProduct_Successful(String categoryName)
			throws Exception {
		log.info("--------------------------------" + categoryName + "------------------------------");
		String sessionToken = SessionUtil.createNewSession();
		String mobileNumber = MobileAndEmailLogin.teleSeries + GenericUtil.createRandomNumber(7);
		sessionToken = MobileAndEmailLogin.mobileLogin_WalletOnly(sessionToken, Environments.X_API_CLIENT,
				mobileNumber);
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		double amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber,
				Environments.X_API_CLIENT, "lenskart");
		MoneyUtil.bulkDebit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber,
				String.valueOf(amount), "lenskart");
		authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(Environments.X_API_CLIENT, sessionToken, authToken, mobileNumber, "600",
				"lenskartplus", null);
		amount = MoneyUtil.getWalletBalanceBasedOnWalletType(mobileNumber, Environments.X_API_CLIENT,
				"lenskartplus");
		Assert.assertEquals(600.0, amount, "Amount didnt get updated");
		addToCartWithDifferentCatagoryId(sessionToken, categoryName);
		String RequestUrlGetCartForWallet = Environments.SERVICES_ENVIRONMENT + CartPathConstants.GetCart;
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("applyWallet", "true"));
		HttpResponse httpResponseGetCartForWallet = RequestUtil.getRequest(RequestUrlGetCartForWallet, headers, query);
		JSONObject responseGetCartForWallet = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCartForWallet);
		Assert.assertEquals(httpResponseGetCartForWallet.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		responeValidation(responseGetCartForWallet, true);
	}
}
