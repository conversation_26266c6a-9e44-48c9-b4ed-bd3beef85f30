package org.lenskart.test.cart.v2;

import java.util.HashMap;

import org.apache.log4j.Logger;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.MoneyUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.v2.cart.CartItem;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.lenskart.juno.schema.v2.common.PrescriptionType;
import com.utilities.GenericUtil;

public class TaxCalculationUsecases {
	private static final Logger log = GenericUtil.InitLogger(TaxCalculationUsecases.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;
	private static HashMap<String, String> product = new HashMap<>();
	
	@DataProvider(name = "Buy_On_call_useCases")
	public static Object[][] useCases() throws Exception {
		return new Object[][] { 
			
{},
		};}

	
	@Test
	public void guestUser_taxCalculation_1Item() throws Exception{
		String sessionToken = SessionUtil.createNewSession();
		double totalDiscount =0;
		double percentageOfGST = 12;

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.DESKTOP);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		log.info("product: " + product);
		CartItem reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		JSONObject cartResponse = CartUtil.createCart(sessionToken, reqObj, ApplicationConstants.XApiClient.DESKTOP);
		//Item level validation
		
		double productValue = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
				.getJSONObject("price").getDouble("value");
		log.info("productValue =" + productValue);
		double subTotal = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getDouble("subTotal");
		log.info("subTotal =" + subTotal);

		Assert.assertEquals(productValue, subTotal);
		int isDiscountApplied = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("discounts").length();
		log.info("isDiscountApplied =" + isDiscountApplied);

		if(isDiscountApplied>=1){
			 totalDiscount = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getInt("totalDiscount");
				log.info("totalDiscount =" + totalDiscount);
		}else if(isDiscountApplied == 0){
			totalDiscount =0;
		}
		double totalAmountForTaxCalculation = productValue-totalDiscount;
		log.info("totalAmountForTaxCalculation =" + totalAmountForTaxCalculation);

		double taxAmount = (double) ((percentageOfGST/100) * totalAmountForTaxCalculation);
		log.info("taxAmount =" + taxAmount);

		double taxAmountFromResponse = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("taxAmountFromResponse =" + taxAmountFromResponse);

		Assert.assertEquals(taxAmount, taxAmountFromResponse);
		double totalTaxForAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("totalTax");
		log.info("totalTaxForAnItem =" + totalTaxForAnItem);

		Assert.assertEquals(taxAmount, totalTaxForAnItem);
		double totalofAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("total");
		log.info("totalofAnItem =" + totalofAnItem);
		Assert.assertEquals(totalofAnItem, totalAmountForTaxCalculation+taxAmount);
		
		//Total Order level validation
		double totalOrderDiscount = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalDiscount");
		log.info("totalOrderDiscount =" + totalOrderDiscount);

		Assert.assertEquals(totalOrderDiscount, totalDiscount);

		double totalOrderTaxAmount = cartResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("totalOrderTaxAmount =" + totalOrderTaxAmount);

		double totalOrderTax = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalTax");
		log.info("totalOrderTax =" + totalOrderTax);

		Assert.assertEquals(totalOrderTaxAmount, totalOrderTax);
		Assert.assertEquals(totalOrderTax, totalTaxForAnItem);
		double totalOrderSubTotal = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("subTotal");
		log.info("totalOrderSubTotal =" + totalOrderSubTotal);

		Assert.assertEquals(totalOrderSubTotal, subTotal);

		double totalOrderValue = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
		log.info("totalOrderValue =" + totalOrderValue);

		Assert.assertEquals(totalOrderValue, totalofAnItem);
	}
	
	@Test
	public void guestUser_taxCalculation_2Items() throws Exception{
		String sessionToken = SessionUtil.createNewSession();
		double totalDiscount =0;
		double percentageOfGST = 12;
		double totalTaxForAnItem = 0;
		double subTotal =0;
		double totalofAnItem =0;
		
		double totalDiscount_Allitems = 0;
		double totalDiscountAmountInArray_Allitems = 0;
		double totalTax_AllItems = 0;
		double totalTaxAmountInArray_AllItems = 0;
		double subTotal_ALlItems =0;
		double total_AllItems = 0;

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.DESKTOP);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		log.info("product: " + product);
		CartItem reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		JSONObject cartResponse = CartUtil.createCart(sessionToken, reqObj, ApplicationConstants.XApiClient.DESKTOP);
		
		jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_JJ"), ApplicationConstants.XApiClient.DESKTOP);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		cartResponse = CartUtil.createCart(sessionToken, reqObj, ApplicationConstants.XApiClient.DESKTOP);
		int itemLength = cartResponse.getJSONObject("result").getJSONArray("items").length();
		
		//Item level validation
		for(int i=0; i<itemLength; i++){
		double productValue = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i)
				.getJSONObject("price").getDouble("value");
		log.info("productValue =" + productValue);
		 subTotal = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i)
				.getJSONObject("amount").getDouble("subTotal");
		log.info("subTotal =" + subTotal);
		subTotal_ALlItems = subTotal_ALlItems + subTotal;
		Assert.assertEquals(productValue, subTotal);
		int isDiscountApplied = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getJSONArray("discounts").length();
		log.info("isDiscountApplied =" + isDiscountApplied);

		if(isDiscountApplied>=1){
			 totalDiscount = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getInt("totalDiscount");
			 totalDiscount_Allitems = totalDiscount_Allitems + totalDiscount;
				log.info("totalDiscount =" + totalDiscount);
		}else if(isDiscountApplied == 0){
			totalDiscount =0;
		}
		double totalAmountForTaxCalculation = productValue-totalDiscount;
		log.info("totalAmountForTaxCalculation =" + totalAmountForTaxCalculation);

		double taxAmount = (double) ((percentageOfGST/100) * totalAmountForTaxCalculation);
		log.info("taxAmount =" + taxAmount);

		double taxAmountFromResponse = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("taxAmountFromResponse =" + taxAmountFromResponse);

		Assert.assertEquals(taxAmount, taxAmountFromResponse);
		 totalTaxForAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getDouble("totalTax");
		log.info("totalTaxForAnItem =" + totalTaxForAnItem);
		totalTax_AllItems = totalTax_AllItems + totalTaxForAnItem;
		Assert.assertEquals(taxAmount, totalTaxForAnItem);
		 totalofAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getDouble("total");
		 total_AllItems = total_AllItems + totalofAnItem;
		log.info("totalofAnItem =" + totalofAnItem);
		Assert.assertEquals(totalofAnItem, totalAmountForTaxCalculation+taxAmount);
		}
		//Total Order level validation
		double totalOrderDiscount = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalDiscount");
		log.info("totalOrderDiscount =" + totalOrderDiscount);

		Assert.assertEquals(totalOrderDiscount, totalDiscount_Allitems);

		double totalOrderTaxAmount = cartResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("totalOrderTaxAmount =" + totalOrderTaxAmount);

		double totalOrderTax = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalTax");
		log.info("totalOrderTax =" + totalOrderTax);

		Assert.assertEquals(totalOrderTaxAmount, totalOrderTax);
		Assert.assertEquals(totalOrderTax, totalTax_AllItems);
		double totalOrderSubTotal = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("subTotal");
		log.info("totalOrderSubTotal =" + totalOrderSubTotal);

		Assert.assertEquals(totalOrderSubTotal, subTotal_ALlItems);

		double totalOrderValue = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
		log.info("totalOrderValue =" + totalOrderValue);

		Assert.assertEquals(totalOrderValue, total_AllItems);
	}

	@Test
	public void guestUser_taxCalculation_2Items_plus_Gold() throws Exception{
		String sessionToken = SessionUtil.createNewSession();
		double totalDiscount =0;
		double percentageOfGST = 12;
		double percentageOfGST_Gold = 18;
		double totalTaxForAnItem = 0;
		double subTotal =0;
		double totalofAnItem =0;
		double taxAmount=0;
		double taxAmount_ValueRounded =0;
		double totalDiscount_Allitems = 0;
		double totalTax_AllItems = 0;
		double subTotal_ALlItems =0;
		double total_AllItems = 0;

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_JJ"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		CartItem reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		JSONObject cartResponse = CartUtil.createCart(sessionToken, reqObj,
				ApplicationConstants.XApiClient.ANDROID);
		
		jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		 reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		 cartResponse = CartUtil.createCart(sessionToken, reqObj,
				ApplicationConstants.XApiClient.ANDROID);
		
		int itemLength = cartResponse.getJSONObject("result").getJSONArray("items").length();
		
		//Item level validation
		for(int i=0; i<itemLength; i++){
		double productValue = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i)
				.getJSONObject("price").getDouble("value");
		log.info("productValue =" + productValue);
		 subTotal = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i)
				.getJSONObject("amount").getDouble("subTotal");
		log.info("subTotal =" + subTotal);
		subTotal_ALlItems = subTotal_ALlItems + subTotal;
		Assert.assertEquals(productValue, subTotal);
		int isDiscountApplied = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getJSONArray("discounts").length();
		log.info("isDiscountApplied =" + isDiscountApplied);

		if(isDiscountApplied>=1){
			 totalDiscount = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getInt("totalDiscount");
			 totalDiscount_Allitems = totalDiscount_Allitems + totalDiscount;
				log.info("totalDiscount =" + totalDiscount);
		}else if(isDiscountApplied == 0){
			totalDiscount =0;
		}
		double totalAmountForTaxCalculation = productValue-totalDiscount;
		log.info("totalAmountForTaxCalculation =" + totalAmountForTaxCalculation);
		if(!"128269".equals(cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getString("productId"))){
		 taxAmount = (double) ((percentageOfGST/100) * totalAmountForTaxCalculation);
		log.info("taxAmount =" + taxAmount);
	     taxAmount_ValueRounded = Math.round(taxAmount * 100D) / 100D;
		}else{
			 taxAmount = (double) ((percentageOfGST_Gold/100) * totalAmountForTaxCalculation);
			log.info("taxAmount =" + taxAmount);
		     taxAmount_ValueRounded = Math.round(taxAmount * 100D) / 100D;

		}
		double taxAmountFromResponse = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("taxAmountFromResponse =" + taxAmountFromResponse);

		Assert.assertEquals(taxAmount_ValueRounded, taxAmountFromResponse);
		 totalTaxForAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getDouble("totalTax");
		log.info("totalTaxForAnItem =" + totalTaxForAnItem);
		totalTax_AllItems = totalTax_AllItems + totalTaxForAnItem;
		Assert.assertEquals(taxAmount_ValueRounded, totalTaxForAnItem);
		 totalofAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(i).getJSONObject("amount").getDouble("total");
		 total_AllItems = total_AllItems + totalofAnItem;
		log.info("totalofAnItem =" + totalofAnItem);
		Assert.assertEquals(totalofAnItem, totalAmountForTaxCalculation+taxAmount_ValueRounded);
		}
		//Total Order level validation
		double totalOrderDiscount = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalDiscount");
		log.info("totalOrderDiscount =" + totalOrderDiscount);

		Assert.assertEquals(totalOrderDiscount, totalDiscount_Allitems);

		double totalOrderTaxAmount = cartResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("totalOrderTaxAmount =" + totalOrderTaxAmount);

		double totalOrderTax = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalTax");
		log.info("totalOrderTax =" + totalOrderTax);

		Assert.assertEquals(totalOrderTaxAmount, totalOrderTax);
		Assert.assertEquals(totalOrderTax, totalTax_AllItems);
		double totalOrderSubTotal = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("subTotal");
		log.info("totalOrderSubTotal =" + totalOrderSubTotal);

		Assert.assertEquals(totalOrderSubTotal, subTotal_ALlItems);

		double totalOrderValue = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
		log.info("totalOrderValue =" + totalOrderValue);
		total_AllItems = Math.round(total_AllItems * 100D) / 100D;

		Assert.assertEquals(totalOrderValue, total_AllItems);
	}
	
	@Test
	public void guestUser_taxCalculation_1Item_CL() throws Exception{
		String sessionToken = SessionUtil.createNewSession();
		double totalDiscount =0;
		double percentageOfGST = 12;
		
		JSONObject categoryDetails_2 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_contact_lens"), ApplicationConstants.XApiClient.ANDROID);
		String product = categoryDetails_2.getJSONObject("result").getJSONArray("product_list").getJSONObject(2)
				.getString("id");

		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("-0.75");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("-0.75");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		JSONObject cartResponse = CartUtil.createCart(sessionToken, reqObj, Environments.X_API_CLIENT);
		
		
		//Item level validation
		double itemQuantity = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getDouble("quantity");
		double productValue = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
				.getJSONObject("price").getDouble("value");
		productValue = productValue * itemQuantity;
		log.info("productValue =" + productValue);
		double subTotal = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getDouble("subTotal");
		log.info("subTotal =" + subTotal);

		Assert.assertEquals(productValue, subTotal);
		int isDiscountApplied = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("discounts").length();
		log.info("isDiscountApplied =" + isDiscountApplied);

		if(isDiscountApplied>=1){
			 totalDiscount = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getInt("totalDiscount");
				log.info("totalDiscount =" + totalDiscount);
		}else if(isDiscountApplied == 0){
			totalDiscount =0;
		}
		double totalAmountForTaxCalculation = productValue-totalDiscount;
		log.info("totalAmountForTaxCalculation =" + totalAmountForTaxCalculation);

		double taxAmount = (double) ((percentageOfGST/100) * totalAmountForTaxCalculation);
		log.info("taxAmount =" + taxAmount);
		
	    double taxAmount_ValueRounded = Math.round(taxAmount * 100D) / 100D;


		double taxAmountFromResponse = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("taxAmountFromResponse =" + taxAmountFromResponse);

		Assert.assertEquals(taxAmount_ValueRounded, taxAmountFromResponse);
		double totalTaxForAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("totalTax");
		log.info("totalTaxForAnItem =" + totalTaxForAnItem);

		Assert.assertEquals(taxAmount_ValueRounded, totalTaxForAnItem);
		double totalofAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("total");
		log.info("totalofAnItem =" + totalofAnItem);
		Assert.assertEquals(totalofAnItem, totalAmountForTaxCalculation+taxAmount);
		
		//Total Order level validation
		double totalOrderDiscount = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalDiscount");
		log.info("totalOrderDiscount =" + totalOrderDiscount);

		Assert.assertEquals(totalOrderDiscount, totalDiscount);

		double totalOrderTaxAmount = cartResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("totalOrderTaxAmount =" + totalOrderTaxAmount);

		double totalOrderTax = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalTax");
		log.info("totalOrderTax =" + totalOrderTax);

		Assert.assertEquals(totalOrderTaxAmount, totalOrderTax);
		Assert.assertEquals(totalOrderTax, totalTaxForAnItem);
		double totalOrderSubTotal = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("subTotal");
		log.info("totalOrderSubTotal =" + totalOrderSubTotal);

		Assert.assertEquals(totalOrderSubTotal, subTotal);

		double totalOrderValue = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
		log.info("totalOrderValue =" + totalOrderValue);

		Assert.assertEquals(totalOrderValue, totalofAnItem);
	}
	

	@Test
	public void loggedInUser_taxCalculation_1Item_SG() throws Exception{
		String create_session = SessionUtil.createNewSession();
		JSONObject responseJSON_mobileAuthenticate = CustomerUtil.mobileAuthenticateWithoutReferCode(ApplicationConstants.XApiClient.MOBILESITE, create_session, "666666", "1"+GenericUtil.createRandomNumber(9)); 
		String sessionToken = responseJSON_mobileAuthenticate.getJSONObject("result").getString("token");
		double totalDiscount =0;
		double percentageOfGST = 18;

		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_sunglasses"), ApplicationConstants.XApiClient.MOBILESITE);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER,
				Boolean.parseBoolean(""));
		CartUtil.clearCart(sessionToken);

		CartItem reqObj = new CartItem();
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		JSONObject cartResponse = CartUtil.createCart(sessionToken, reqObj, ApplicationConstants.XApiClient.MOBILESITE);
		log.info("cartResponse:"+cartResponse);
		//Item level validation
		
		double productValue = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
				.getJSONObject("price").getDouble("value");
		log.info("productValue =" + productValue);
		double subTotal = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
				.getJSONObject("amount").getDouble("subTotal");
		log.info("subTotal =" + subTotal);

		Assert.assertEquals(productValue, subTotal);
		int isDiscountApplied = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("discounts").length();
		log.info("isDiscountApplied =" + isDiscountApplied);

		if(isDiscountApplied>=1){
			 totalDiscount = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getInt("totalDiscount");
				log.info("totalDiscount =" + totalDiscount);
		}else if(isDiscountApplied == 0){
			totalDiscount =0;
		}
		double totalAmountForTaxCalculation = productValue-totalDiscount;
		log.info("totalAmountForTaxCalculation =" + totalAmountForTaxCalculation);

		double taxAmount = (double) ((percentageOfGST/100) * totalAmountForTaxCalculation);
		log.info("taxAmount =" + taxAmount);

		double taxAmountFromResponse = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("taxAmountFromResponse =" + taxAmountFromResponse);

		Assert.assertEquals(taxAmount, taxAmountFromResponse);
		double totalTaxForAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("totalTax");
		log.info("totalTaxForAnItem =" + totalTaxForAnItem);

		Assert.assertEquals(taxAmount, totalTaxForAnItem);
		double totalofAnItem = cartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("total");
		log.info("totalofAnItem =" + totalofAnItem);
		Assert.assertEquals(totalofAnItem, totalAmountForTaxCalculation+taxAmount);
		
		//Total Order level validation
		double totalOrderDiscount = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalDiscount");
		log.info("totalOrderDiscount =" + totalOrderDiscount);

		Assert.assertEquals(totalOrderDiscount, totalDiscount);

		double totalOrderTaxAmount = cartResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
		log.info("totalOrderTaxAmount =" + totalOrderTaxAmount);

		double totalOrderTax = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalTax");
		log.info("totalOrderTax =" + totalOrderTax);

		Assert.assertEquals(totalOrderTaxAmount, totalOrderTax);
		Assert.assertEquals(totalOrderTax, totalTaxForAnItem);
		double totalOrderSubTotal = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("subTotal");
		log.info("totalOrderSubTotal =" + totalOrderSubTotal);

		Assert.assertEquals(totalOrderSubTotal, subTotal);

		double totalOrderValue = cartResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
		log.info("totalOrderValue =" + totalOrderValue);

		Assert.assertEquals(totalOrderValue, totalofAnItem);
	}
	
	private double addAmountToWallet(String session, String xApiClient, String walletType, String telephone)
			throws Exception {
		String authToken = MoneyUtil.generateAuthToken(session, xApiClient);
		double lenskartPlusAmount = MoneyUtil.getWalletBalanceBasedOnWalletType(telephone, xApiClient, walletType);
		if (lenskartPlusAmount > 0.0)
			MoneyUtil.bulkDebit(xApiClient, session, authToken, telephone, String.valueOf(lenskartPlusAmount),
					walletType);
		authToken = MoneyUtil.generateAuthToken(session, xApiClient);
		MoneyUtil.bulkCredit(xApiClient, session, authToken, telephone, "100.0", walletType, null);
		lenskartPlusAmount = MoneyUtil.getWalletBalanceBasedOnWalletType(telephone, xApiClient, walletType);
		Assert.assertEquals(Double.parseDouble("100.0"), lenskartPlusAmount,
				walletType + " Amount didnt get updated");
		return lenskartPlusAmount;
	}
	
	@Test
	public void taxValidationFor_LKCashplus() throws Exception{
		double totalDiscount =0;
		double percentageOfGST = 18;
		String telephone = "1"+GenericUtil.createRandomNumber(9);
		String sessionToken = CustomerUtil.mobileAuthenticateWithoutReferCode(ApplicationConstants.XApiClient.IOS, SessionUtil.createNewSession(), "666666", telephone).getJSONObject("result").getString("token");
		double lenskartPlusAmount = addAmountToWallet(sessionToken, ApplicationConstants.XApiClient.IOS, "lenskartplus", telephone);
		
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_sunglasses_JJ"), ApplicationConstants.XApiClient.IOS);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER,
				Boolean.parseBoolean(""));
		CartUtil.clearCart(sessionToken);

		CartItem reqObj = new CartItem();
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		//JSONObject cartResponse = 
		CartUtil.createCart(sessionToken, reqObj, ApplicationConstants.XApiClient.IOS);
		JSONObject fetchCartResponse =CartUtil.getCart(sessionToken, ApplicationConstants.XApiClient.IOS, "true");
		log.info(fetchCartResponse);
		//Item level validation
		
				double productValue = fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
						.getJSONObject("price").getDouble("value");
				log.info("productValue =" + productValue);
				double subTotal = fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0)
						.getJSONObject("amount").getDouble("subTotal");
				log.info("subTotal =" + subTotal);

				Assert.assertEquals(productValue, subTotal);
				int isDiscountApplied = fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("discounts").length();
				log.info("isDiscountApplied =" + isDiscountApplied);
				Assert.assertEquals(fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("discounts").getJSONObject(0).getString("type"), "lenskartplus");
				if(isDiscountApplied>=1){
					 totalDiscount = fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getInt("totalDiscount");
						log.info("totalDiscount =" + totalDiscount);
				}else if(isDiscountApplied == 0){
					totalDiscount =0;
				}
				double totalAmountForTaxCalculation = productValue-totalDiscount;
				log.info("totalAmountForTaxCalculation =" + totalAmountForTaxCalculation);

				double taxAmount = (double) ((percentageOfGST/100) * totalAmountForTaxCalculation);
				log.info("taxAmount =" + taxAmount);

				double taxAmountFromResponse = fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
				log.info("taxAmountFromResponse =" + taxAmountFromResponse);

				Assert.assertEquals(taxAmount, taxAmountFromResponse);
				double totalTaxForAnItem = fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("totalTax");
				log.info("totalTaxForAnItem =" + totalTaxForAnItem);

				Assert.assertEquals(taxAmount, totalTaxForAnItem);
				double totalofAnItem = fetchCartResponse.getJSONObject("result").getJSONArray("items").getJSONObject(0).getJSONObject("amount").getDouble("total");
				log.info("totalofAnItem =" + totalofAnItem);
				Assert.assertEquals(totalofAnItem, totalAmountForTaxCalculation+taxAmount);
				
				//Total Order level validation
				double totalOrderDiscount = fetchCartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalDiscount");
				log.info("totalOrderDiscount =" + totalOrderDiscount);

				Assert.assertEquals(totalOrderDiscount, totalDiscount);

				double totalOrderTaxAmount = fetchCartResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("taxes").getJSONObject(0).getDouble("amount");
				log.info("totalOrderTaxAmount =" + totalOrderTaxAmount);

				double totalOrderTax = fetchCartResponse.getJSONObject("result").getJSONObject("totals").getDouble("totalTax");
				log.info("totalOrderTax =" + totalOrderTax);

				Assert.assertEquals(totalOrderTaxAmount, totalOrderTax);
				Assert.assertEquals(totalOrderTax, totalTaxForAnItem);
				double totalOrderSubTotal = fetchCartResponse.getJSONObject("result").getJSONObject("totals").getDouble("subTotal");
				log.info("totalOrderSubTotal =" + totalOrderSubTotal);

				Assert.assertEquals(totalOrderSubTotal, subTotal);

				double totalOrderValue = fetchCartResponse.getJSONObject("result").getJSONObject("totals").getDouble("total");
				log.info("totalOrderValue =" + totalOrderValue);

				Assert.assertEquals(totalOrderValue, totalofAnItem);		
	}
}


