package org.lenskart.test.cart.v2;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.ProductCategories;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.CartPathConstants;
import org.lenskart.core.constant.JunoV1PathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import com.lenskart.juno.schema.v2.cart.CartResponse;
import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;

public class AddToCartUsecases {
  private static final Logger log = GenericUtil.InitLogger(AddToCartUsecases.class);

  private String registered_emailId;
  private String apiClient = Environments.X_API_CLIENT;
  private String password;
  private static HashMap<String, String> product = new HashMap<>();
  PropertyFactory pf = new PropertyFactory();
  private static ObjectMapper objectMapper;
  private static JaxbAnnotationModule module;
  static {
    module = new JaxbAnnotationModule();
    objectMapper = new ObjectMapper();
    objectMapper.registerModule(module);
    objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
    objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
    objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
  }
  private static boolean mongoValidationFlag = Environments.dbConnectionFlag;

  // private MongoConnectionUtility mongoCustomerConnectionObject = null;
  private MongoConnectionUtility mongoCartConnectionObject = null;

  private MongoConnectionUtility mongoJunoV1ConnectionObject = null;
  private MySQLConnectionUtility mysqlConnectionObject = null;

  @BeforeClass

  public void initDBConnection() throws IOException {

    if (mongoValidationFlag) {

      mongoCartConnectionObject = CartUtil.getCartMongoConnectionObject();
      mongoJunoV1ConnectionObject = JunoV1Util.getJunoV1MongoConnectionObject();
      mongoCartConnectionObject.initMongoDBConnection();
      mongoJunoV1ConnectionObject.initMongoDBConnection();
      mysqlConnectionObject = JunoV1Util.getJunoV1MySQLConnectionObject();
      mysqlConnectionObject.initMySQLConnection();
    }
  }

  @AfterClass

  public void closeDBConection() {

    if (mongoValidationFlag) {

      mongoCartConnectionObject.closeMongoConnection();
      mongoJunoV1ConnectionObject.closeMongoConnection();
      mysqlConnectionObject.closeMySQLConnection();
    }

  }

  @DataProvider(name = "DualCompanyParam")
  public static Iterator<String[]> supplyData() throws IOException, JSONException {
    CSVReadUtil csv =
        new CSVReadUtil(System.getProperty("user.dir") + "//csv_files//cart//dualCompanyParam.csv");
    List<String[]> list = csv.getEntriesAsList();

    Iterator<String[]> itr = list.iterator();
    return itr;
  }

  @DataProvider(name = "XApiClient")
  public static Iterator<String[]> supplyData1() throws IOException, JSONException {
    char seperator = ',';
    CSVReadUtil csv = new CSVReadUtil(
        System.getProperty("user.dir") + "//csv_files//cart//XApiClientUserType.csv", seperator);
    List<String[]> list = csv.getEntriesAsList();

    Iterator<String[]> itr = list.iterator();
    return itr;
  }

  @Test(enabled = true,
      description = "To Test whether product gets added to cart on passing ValidCart id/SessionId for Logined User when Cart is in Lock State",
      dataProvider = "XApiClient")
  public void addToCartValidRequestCartStatusLock(String xApiClient, String user) throws Exception {
    String sessionToken = null;
    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"), xApiClient);
    if (Integer.parseInt(
        jsonResponse_category.getJSONObject("result").get("num_of_products").toString()) == 0) {
      Assert.assertNull(jsonResponse_category.getJSONObject("result").get("num_of_products"),
          "Products does not exist for the categoryID"
              + ApplicationUtil.getcategoryId("category_sunglasses"));
    }

    product = ProductUtil.getProductDetails(jsonResponse_category, "sunglasses",
        Boolean.parseBoolean("FALSE"), Boolean.parseBoolean("FALSE"));
    log.info("product************: " + product);
    if (product.containsKey("NoOfPackages") && product.get("NoOfPackages").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfPackages"),
          "No of Packages is zero for Product id" + product.get("productId") + "powertype"
              + ApplicationConstants.PowerTypes.SUNGLASSES);
    }
    HashMap<JSONObject, String> map = null;
    if (user.contentEquals("Guest")) {
      map = CartUtil.createGuestCart(product.get("productId"), xApiClient);
    } else if (user.contentEquals("Login")) {
      map = CartUtil.createLoginedCart(ApplicationConstants.userEmail.toLowerCase(),
          ApplicationConstants.userPassword, product.get("productId"), xApiClient);
    } else {
      map = CartUtil.createNewRegUserCart(product.get("productId"), xApiClient);
    }
    CartResponse node1 = new CartResponse();
    JSONObject CartResponseObj = new JSONObject();
    for (Map.Entry<JSONObject, String> entry : map.entrySet()) {
      CartResponseObj = entry.getKey();
      sessionToken = entry.getValue();
    }
    if (CartResponseObj.get("status").toString().contentEquals("200")) {
      Assert.assertTrue(CartResponseObj.getString("status").contentEquals("200"),
          "\nCreateCart API (GET) :" + GenericUtil.printAPICallDetails(null, CartResponseObj));
      JSONObject LoggedInResultObj = CartResponseObj.getJSONObject("result");
      String b = LoggedInResultObj.toString();

      node1 = objectMapper.readValue(b, CartResponse.class);
    } else {
      Assert.assertFalse(false,
          "\nCreateCart API (GET) :" + GenericUtil.printAPICallDetails(null, CartResponseObj));
    }

    Assert.assertNotNull(node1.getCartId());

    JSONObject cartResponseAfterLock = CartUtil.cartStatus_Lock(sessionToken, xApiClient);
    String statusCode = cartResponseAfterLock.getString("status");
    log.info("cartResponseAfterLock: " + cartResponseAfterLock);
    Assert.assertTrue(statusCode.contentEquals("200"), "Status Code is not 200");
    Assert.assertEquals(cartResponseAfterLock.getJSONObject("result").getString("cartStatus"),
        "Lock", "cart is not in lock status");

    product.clear();
    jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"));
    JSONObject AddToCartResponseObject =
        CartUtil.addToCart(sessionToken, product.get("productId"), xApiClient);
    JSONObject addToCartresultObj = AddToCartResponseObject.getJSONObject("result");
    log.info("AddToCART Result:" + AddToCartResponseObject);
    String c = addToCartresultObj.toString();

    CartResponse addToCartNode = objectMapper.readValue(c, CartResponse.class);
    Assert.assertNotNull(addToCartNode.getCartId());
    Assert.assertEquals(addToCartNode.getItemsQty(), node1.getItemsQty(),
        "before cart lock and after cart lock item qty not same but it should be same");
    Assert.assertEquals(
        addToCartNode.getCartId().toString().contentEquals(node1.getCartId().toString()), false,
        "Cart ID Before and after Cart Lock is same");
    Assert.assertEquals(addToCartNode.getCartVersion().toString().contentEquals("0"), true);
  }

  @Test(enabled = true,
      description = "To Test whether product gets added to cart on passing ValidCart id/SessionId for Logined User when Cart is in InActive State",
      dataProvider = "XApiClient")
  public void addToCartValidRequestCartStatusInActive(String xApiClient, String user)
      throws Exception {
    String sessionToken = null;
    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SUNGLASSES, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"));
    log.info("product: " + product);
    if (!product.containsKey("NoOfPackages") && product.get("NoOfPackages").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfPackages"),
          "No of package is zero for category id"
              + ApplicationUtil.getcategoryId("category_eyeglasses"));
    }
    HashMap<JSONObject, String> map = null;
    if (user.contentEquals("Guest")) {
      map = CartUtil.createGuestCart(product.get("productId"), xApiClient);
    } else if (user.contentEquals("Login")) {
      map = CartUtil.createLoginedCart(ApplicationConstants.userEmail.toLowerCase(),
          ApplicationConstants.userPassword, product.get("productId"), xApiClient);
    } else {
      map = CartUtil.createNewRegUserCart(product.get("productId"), xApiClient);
    }
    CartResponse node1 = new CartResponse();
    JSONObject CartResponseObj = new JSONObject();
    for (Map.Entry<JSONObject, String> entry : map.entrySet()) {
      CartResponseObj = entry.getKey();
      sessionToken = entry.getValue();
    }
    if (CartResponseObj.get("status").toString().contentEquals("200")) {
      Assert.assertTrue(CartResponseObj.getString("status").contentEquals("200"),
          "\nCreateCart API (GET) :" + GenericUtil.printAPICallDetails(null, CartResponseObj));
      JSONObject LoggedInResultObj = CartResponseObj.getJSONObject("result");
      String b = LoggedInResultObj.toString();

      node1 = objectMapper.readValue(b, CartResponse.class);
    } else {
      Assert.assertFalse(false, "Status Code is 200");
    }

    Assert.assertNotNull(node1.getCartId());

    JSONObject cartResponseAfterInactive = CartUtil.cartStatus_InActive(sessionToken, xApiClient);
    String statusCode = cartResponseAfterInactive.getString("status");
    Assert.assertEquals(cartResponseAfterInactive.getJSONObject("result").getString("cartStatus"),
        "OrderPlaced", "cart status in not order placed after inactive the cart");
    Assert.assertTrue(statusCode.contentEquals("200"), "Status Code is not 200");
    // after inactive cart should be empty
    JSONObject cartDetails =
        CartUtil.getCart(sessionToken, ApplicationConstants.XApiClient.ANDROID);
    log.info("cartDetails: " + cartDetails);
    Assert.assertEquals(cartDetails.getInt("status"), 404, "status code is incorrect");
    Assert.assertEquals(cartDetails.getString("error"), "Not Found", "error msg incorrect");
    Assert.assertEquals(cartDetails.getString("message"), "Cart not found", "message incorrect");

    product.clear();
    jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"));
    log.info("product**************" + product);
    if (product.containsKey("NoOfPackages") && product.get("NoOfPackages").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfPackages"),
          "No of Packages is zero for Product id" + product.get("productId") + "powertype"
              + ApplicationConstants.PowerTypes.SINGLE_VISION);
    }
    JSONObject AddToCartResponseObject =
        CartUtil.addToCart(sessionToken, product.get("productId"), xApiClient);

    JSONObject addToCartresultObj = AddToCartResponseObject.getJSONObject("result");
    log.info("AddToCART Result:" + AddToCartResponseObject);
    String c = addToCartresultObj.toString();

    CartResponse addToCartNode = objectMapper.readValue(c, CartResponse.class);
    Assert.assertNotNull(addToCartNode.getCartId());
    Assert.assertEquals(node1.getItemsQty(), addToCartNode.getItemsQty(),
        "item qty after and before inactive is not same");
    Assert.assertEquals(
        addToCartNode.getCartId().toString().contentEquals(node1.getCartId().toString()), false,
        "Cart ID Before and after Cart InActive is same");
    Assert.assertEquals(addToCartNode.getCartVersion().toString().contentEquals("0"), true);
  }

  @Test(enabled = true,
      description = "To Test whether product gets added to cart on passing ValidCart id/SessionId for Logined User when Cart is in UnLock State",
      dataProvider = "XApiClient")
  public void addToCartValidRequestCartStatusUnLock(String xApiClient, String user)
      throws Exception {
    String sessionToken = null;
    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"), xApiClient);
    if (Integer.parseInt(
        jsonResponse_category.getJSONObject("result").get("num_of_products").toString()) == 0) {
      Assert.assertFalse(true, "Products does not exist for the categoryID"
          + ApplicationUtil.getcategoryId("category_sunglasses"));
    }
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SUNGLASSES, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"), ApplicationConstants.XApiClient.ANDROID);
    log.info("product******** " + product);
    if (!product.containsKey("NoOfProducts") || product.get("NoOfProducts").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfProducts"),
          "No of Products is zero for category id"
              + ApplicationUtil.getcategoryId("category_sunglasses"));
    }
    HashMap<JSONObject, String> map = null;
    if (user.contentEquals("Guest")) {
      map = CartUtil.createGuestCart(product.get("productId"), xApiClient);
    } else if (user.contentEquals("Login")) {
      map = CartUtil.createLoginedCart(ApplicationConstants.userEmail.toLowerCase(),
          ApplicationConstants.userPassword, product.get("productId"), xApiClient);
    } else {
      map = CartUtil.createNewRegUserCart(product.get("productId"), xApiClient);
    }

    CartResponse node1 = new CartResponse();
    JSONObject CartResponseObj = new JSONObject();
    for (Map.Entry<JSONObject, String> entry : map.entrySet()) {
      CartResponseObj = entry.getKey();
      sessionToken = entry.getValue();
    }
    if (CartResponseObj.get("status").toString().contentEquals("200")) {
      Assert.assertTrue(CartResponseObj.getString("status").contentEquals("200"),
          "\nCreateCart API (GET) :" + GenericUtil.printAPICallDetails(null, CartResponseObj));
      JSONObject LoggedInResultObj = CartResponseObj.getJSONObject("result");
      String b = LoggedInResultObj.toString();

      node1 = objectMapper.readValue(b, CartResponse.class);
    } else {
      Assert.assertFalse(false, "Status Code is 200");
    }

    Assert.assertNotNull(node1.getCartId());

    JSONObject cartObj = CartUtil.cartStatus_UnLock(sessionToken, xApiClient);
    Assert.assertEquals(cartObj.getJSONObject("result").getString("cartStatus"), "Unlock",
        "status is incorrect");
    String statusCode = cartObj.getString("status");

    Assert.assertTrue(statusCode.contentEquals("200"), "Status Code is not 200");
    product.clear();

    jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
    if (Integer.parseInt(
        jsonResponse_category.getJSONObject("result").get("num_of_products").toString()) == 0) {
      Assert.assertFalse(true, "Products does not exist for the categoryID"
          + ApplicationUtil.getcategoryId("category_eyeglasses"));
    }
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"), ApplicationConstants.XApiClient.ANDROID);
    if (!product.containsKey("NoOfProducts") || product.get("NoOfProducts").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfProducts"),
          "No of Products is zero for category id"
              + ApplicationUtil.getcategoryId("category_eyeglasses"));
    }
    if (product.containsKey("NoOfPackages") && product.get("NoOfPackages").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfPackages"),
          "No of Packages is zero for Product id" + product.get("productId") + "powertype"
              + ApplicationConstants.PowerTypes.SINGLE_VISION);
    }
    JSONObject addToCartResponseObject =
        CartUtil.addToCart(sessionToken, product.get("productId"), xApiClient);

    JSONObject addToCartresultObj = addToCartResponseObject.getJSONObject("result");
    log.info("AddToCART Result:" + addToCartResponseObject);
    String c = addToCartresultObj.toString();
    Assert.assertEquals(addToCartResponseObject.getJSONObject("result").getString("cartStatus"),
        cartObj.getJSONObject("result").getString("cartStatus"), "cart status mismatch");

    CartResponse addToCartNode = objectMapper.readValue(c, CartResponse.class);
    Assert.assertNotNull(addToCartNode.getCartId());
    Assert.assertEquals(
        addToCartNode.getCartId().toString().contentEquals(node1.getCartId().toString()), true,
        "Cart ID Before and after Cart Lock is same");
    // Assert.assertEquals(addToCartNode.getCartVersion().toString().contentEquals("0"), true);
  }

  @Test(enabled = true,
      description = "To Test AddToCart when products already in cart are currently Unavailable",
      dataProvider = "XApiClient")
  public void addToCart_CartProductsUnAvailable(String xApiClient, String user) throws Exception {
    CartResponse node1 = null;
    String sessionToken = null;
    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"), xApiClient);
    if (Integer.parseInt(
        jsonResponse_category.getJSONObject("result").get("num_of_products").toString()) == 0) {
      Assert.assertFalse(true, "Products does not exist for the categoryID"
          + ApplicationUtil.getcategoryId("category_sunglasses"));
    }
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SUNGLASSES, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"), ApplicationConstants.XApiClient.ANDROID);
    log.info("product*******" + product);
    if (!product.containsKey("NoOfProducts") || product.get("NoOfProducts").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfProducts"),
          "No of Products is zero for category id"
              + ApplicationUtil.getcategoryId("category_sunglasses"));
    }
    HashMap<JSONObject, String> map = null;
    if (user.contentEquals("Guest")) {
      map = CartUtil.createGuestCart(product.get("productId"), xApiClient);
    } else if (user.contentEquals("Login")) {
      map = CartUtil.createLoginedCart(ApplicationConstants.userEmail.toLowerCase(),
          ApplicationConstants.userPassword, product.get("productId"), xApiClient);
    } else {
      map = CartUtil.createNewRegUserCart(product.get("productId"), xApiClient);
    }
    JSONObject CartResponseObj = new JSONObject();
    for (Map.Entry<JSONObject, String> entry : map.entrySet()) {
      CartResponseObj = entry.getKey();
      sessionToken = entry.getValue();
    }
    if (CartResponseObj.get("status").toString().contentEquals("200")) {
      Assert.assertTrue(CartResponseObj.getString("status").contentEquals("200"),
          "\nCreateCart API (GET) :" + GenericUtil.printAPICallDetails(null, CartResponseObj));
      JSONObject LoggedInResultObj = CartResponseObj.getJSONObject("result");
      String b = LoggedInResultObj.toString();

      node1 = objectMapper.readValue(b, CartResponse.class);
    } else {
      Assert.assertFalse(false, "Status Code is 200");
    }

    Assert.assertNotNull(node1.getCartId());

    JSONObject CartObj = CartUtil.cartStatus_InActive(sessionToken, xApiClient);
    String statusCode = CartObj.getString("status");

    Assert.assertTrue(statusCode.contentEquals("200"), "Status Code is not 200");
    product.clear();

    jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), xApiClient);
    if (Integer.parseInt(
        jsonResponse_category.getJSONObject("result").get("num_of_products").toString()) == 0) {
      Assert.assertFalse(true, "Products does not exist for the categoryID"
          + ApplicationUtil.getcategoryId("category_eyeglasses"));
    }
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"), ApplicationConstants.XApiClient.ANDROID);
    log.info("product********" + product);
    if (!product.containsKey("NoOfProducts") || product.get("NoOfProducts").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfProducts"),
          "No of Products is zero for category id"
              + ApplicationUtil.getcategoryId("category_eyeglasses"));
    }
    if (product.containsKey("NoOfPackages") && product.get("NoOfPackages").contentEquals("0")) {
      Assert.assertFalse(true, "No of Packages is zero for Product id" + product.get("productId")
          + "powertype" + ApplicationConstants.PowerTypes.SINGLE_VISION);
    }
    JSONObject AddToCartResponseObject =
        CartUtil.addToCart(sessionToken, product.get("productId") + '1', xApiClient);

    Assert.assertEquals(AddToCartResponseObject.getString("status").toString(), "422",
        "Response is not proper");
    Assert.assertEquals(AddToCartResponseObject.getString("message"),
        "Product NOT found for ID: " + product.get("productId") + '1',
        "Improper Message in Response");
  }

  @Test(enabled = true,
      description = "To Test AddToCart API when cart has already without HTO product",
      dataProvider = "XApiClient")
  public void addToCartHTOProductCase1(String xApiClient, String user) throws Exception {
    String sessionToken = null;
    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"), xApiClient);
    product = ProductUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SUNGLASSES, Boolean.parseBoolean("FALSE"),
        Boolean.parseBoolean("FALSE"), ApplicationConstants.XApiClient.ANDROID);
    if (!product.containsKey("NoOfProducts") || product.get("NoOfProducts").contentEquals("0")) {
      Assert.assertFalse(product.containsKey("NoOfProducts"),
          "No of Products is zero for category id"
              + ApplicationUtil.getcategoryId("category_sunglasses"));
    }
    HashMap<JSONObject, String> map = null;
    if (user.contentEquals("Guest")) {
      map = CartUtil.createGuestCart(product.get("productId"), xApiClient);
    } else if (user.contentEquals("Login")) {
      map = CartUtil.createLoginedCart(ApplicationConstants.userEmail.toLowerCase(),
          ApplicationConstants.userPassword, product.get("productId"), xApiClient);
    } else {
      map = CartUtil.createNewRegUserCart(product.get("productId"), xApiClient);
    }

    CartResponse node1 = new CartResponse();
    JSONObject CartResponseObj = new JSONObject();
    for (Map.Entry<JSONObject, String> entry : map.entrySet()) {
      CartResponseObj = entry.getKey();
      sessionToken = entry.getValue();
    }
    if (CartResponseObj.get("status").toString().contentEquals("200")) {
      Assert.assertTrue(CartResponseObj.getString("status").contentEquals("200"),
          "\nCreateCart API (GET) :" + GenericUtil.printAPICallDetails(null, CartResponseObj));
      JSONObject LoggedInResultObj = CartResponseObj.getJSONObject("result");
      String b = LoggedInResultObj.toString();

      node1 = objectMapper.readValue(b, CartResponse.class);
    } else {
      Assert.assertFalse(false, "Status Code is 200");
    }

    Assert.assertNotNull(node1.getCartId());
    // 47552 is HTO Product
    JSONObject AddToCartResponseObject = CartUtil.addToCart(sessionToken, "47552", xApiClient);
    System.out.print(AddToCartResponseObject);
    Assert.assertTrue(AddToCartResponseObject.getString("status").equals("200"),
        "Response code is not proper");

    JSONObject resultObj = AddToCartResponseObject.getJSONObject("result");
    log.info(resultObj);
    String b = resultObj.toString();

    CartResponse node = objectMapper.readValue(b, CartResponse.class);
    Assert.assertNotNull(node.getCartId(), "cart_id is null");

    log.info("CART ID in Response:::" + node.getCartId());
    Assert.assertEquals(node.getCartType().toString(), "HEC", "Cart Type is not correct");
    Assert.assertEquals(node.getItemsQty().toString(), "1", "Item Quantity is not 1");
    Assert.assertEquals(node.getItemsCount().toString(), "1", "Item Quantity is not 1");
    JSONObject CartObj = CartUtil.cartStatus_InActive(sessionToken, xApiClient);
    String statusCode = CartObj.getString("status");
    Assert.assertTrue(statusCode.contentEquals("200"), "Status Code is not 200");
  }

  @Test(enabled = true, description = "To Test AddToCart API when cart has already HTO product",
      dataProvider = "XApiClient")
  public void addToCartHTOProductCase2(String xApiClient, String user) throws Exception {
    CartResponse node1 = null;
    String sessionToken = null;

    if (user.contentEquals("Guest")) {
      sessionToken = SessionUtil.createNewSession();

    } else if (user.contentEquals("Login")) {
      sessionToken = CustomerUtil.get_sessionId_after_user_authentication(
          ApplicationConstants.userEmail.toLowerCase(), ApplicationConstants.userPassword,xApiClient);

    } else {
      registered_emailId =
          "CartNewRegisterUser" + GenericUtil.createRandomNumber(8) + "@example.com";
      password = "password";
      String mobileNum = "8" + GenericUtil.createRandomNumber(9);
      sessionToken = CustomerUtil.registerNewCustomer("firstName", "lastName", registered_emailId,
          password, mobileNum);
    }

    JSONObject respJsonObjectData = CartUtil.HTOCart(sessionToken, xApiClient);
    String statusCode = respJsonObjectData.getString("status");
    Assert.assertTrue(statusCode.contentEquals("200"),
        "\nCreateCart API (GET) :" + GenericUtil.printAPICallDetails(null, respJsonObjectData));
    JSONObject resultObj = respJsonObjectData.getJSONObject("result");
    log.info(resultObj);
    String b = resultObj.toString();

    node1 = objectMapper.readValue(b, CartResponse.class);
    Assert.assertNotNull(node1.getCartId(), "cart_id is null");
    log.info("CART ID in Response:::" + node1.getCartId());

    // 47552 is HTO Product
    JSONObject AddToCartResponseObject = CartUtil.addToCart(sessionToken, "47552", xApiClient);
    System.out.print(AddToCartResponseObject);
    Assert.assertTrue(AddToCartResponseObject.getString("status").equals("422"),
        "Response code is not proper");
    Assert.assertEquals(AddToCartResponseObject.getString("message"),
        "Item quantity limit exceeded", "Improper Message in Response");
    JSONObject CartObj = CartUtil.cartStatus_InActive(sessionToken, xApiClient);
    statusCode = CartObj.getString("status");
    Assert.assertTrue(statusCode.contentEquals("200"), "Status Code is not 200");
  }

  @Test(enabled = true, dataProvider = "DualCompanyParam")
  public void addToCartWithAdditionalParamOfDCInGuestCart(String shipToStoreRequired,
      String isLocalFittingRequired) throws Exception {
    String addToCartURL = Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddToCart;
    String sessionToken = SessionUtil.createNewSession();
    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), apiClient);
    product = ApplicationUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SINGLE_VISION, false);
    Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
    JSONObject reqJsonObject = new JSONObject();
    reqJsonObject.put("productId", Long.parseLong(product.get("productId")));
    // reqJsonObject.put("powerType",
    // ApplicationConstants.PowerTypes.SINGLE_VISION);
    // reqJsonObject.put("packageId", product.get("packageId"));
    reqJsonObject.put("shipToStoreRequired", Boolean.parseBoolean(shipToStoreRequired));
    reqJsonObject.put("isLocalFittingRequired", Boolean.parseBoolean(isLocalFittingRequired));
    List<NameValuePair> headers = new ArrayList<NameValuePair>();
    headers.add(new BasicNameValuePair("Content-Type", "application/json"));
    headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
    headers.add(new BasicNameValuePair("X-Api-Client", apiClient));
    headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));

    HttpResponse httpresponse = RequestUtil.postRequest(addToCartURL, headers, null, reqJsonObject);
    JSONObject cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
    Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
        GenericUtil.printAPICallDetails(addToCartURL, reqJsonObject, cart_response));
    String cartId = cart_response.getJSONObject("result").getString("id");
    JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
    for (int i = 0; i < items.length(); i++) {
      JSONObject itemsObject = items.getJSONObject(i);
      Assert.assertEquals(itemsObject.getBoolean("shipToStoreRequired"),
          Boolean.parseBoolean(shipToStoreRequired), "shipToStoreRequired mismatch");
      Assert.assertEquals(itemsObject.getBoolean("isLocalFittingRequired"),
          Boolean.parseBoolean(isLocalFittingRequired), "isLocalFittingRequired mismatch");
    }
    Assert.assertFalse(cart_response.getJSONObject("result").has("shipToStoreRequired"),
        "shipToStoreRequired is present other than item level");
    Assert.assertFalse(cart_response.getJSONObject("result").has("isLocalFittingRequired"),
        "isLocalFittingRequired is present other than item level");
    if (mongoValidationFlag) {
      Map<String, Object> cartDetails = new HashMap<String, Object>();
      cartDetails.put("_id", Long.parseLong(cartId));
      List<Document> getCartDetails = CartUtil.getAllCarts(cartDetails);
      Assert.assertEquals(getCartDetails.size(), 1, "Cart Db is more than one document");
      Document cartRecord = getCartDetails.get(0);
      @SuppressWarnings("unchecked")
      List<Document> itemDb = (List<Document>) cartRecord.get("items");
      Assert.assertEquals(itemDb.size(), 1, "Number item is more then one");
      for (int i = 0; i < itemDb.size(); i++) {
        Document itemObjectDb = itemDb.get(i);
        Assert.assertEquals(itemObjectDb.get("shipToStoreRequired"),
            Boolean.parseBoolean(shipToStoreRequired), "shipToStoreRequired mismatch in Db");
        Assert.assertEquals(itemObjectDb.get("isLocalFittingRequired"),
            Boolean.parseBoolean(isLocalFittingRequired), "isLocalFittingRequired mismatch in Db");
      }
    }
  }

  @Test(enabled = true)
  public void addToCartWithAdditionalParamOfDCInLoggedInCart() throws Exception {
    String addToCartURL = Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddToCart;
    registered_emailId = "CartNewRegisterUser" + GenericUtil.createRandomNumber(8) + "@example.com";
    password = "password";
    String mobileNum = "1265" + GenericUtil.createRandomNumber(6);
    String sessionToken =
        CustomerUtil.registerNewCustomer("test", "test", registered_emailId, password, mobileNum);
    List<NameValuePair> headers = new ArrayList<NameValuePair>();
    headers.add(new BasicNameValuePair("Content-Type", "application/json"));
    headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
    headers.add(new BasicNameValuePair("X-Api-Client", apiClient));
    headers.add(new BasicNameValuePair("X-B3-TraceId", GenericUtil.getRandomHexTraceId()));
    String customerId = CustomerUtil.getMeCustomerDetails(sessionToken, apiClient).getString("id");
    String scCode =
        GenericUtil.createRandomNumber(4) + "-LJ904-F" + GenericUtil.createRandomNumber(3);

    if (mongoValidationFlag) {
      String sql =
          "INSERT INTO storecredit (`storecredit_id`, `store_code`, `balance`, `currency`, `status`, `expired_at`, `customer_id`, `customer_name`, `customer_email`, `recipient_name`, `recipient_email`, `recipient_address`, `message`, `store_id`) VALUES("
              + GenericUtil.createRandomNumber(9) + ", '" + scCode
              + "', 1000000.0000, 'INR', 2, '2020-02-04 07:38:02', " + customerId + ", 'test', '"
              + registered_emailId + "', 'Testetllenskart ', '" + registered_emailId
              + "', NULL, NULL, 1)";
      mysqlConnectionObject.executeInsertQuery(sql);
    }

    JSONObject jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), apiClient);

    product = ApplicationUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SINGLE_VISION, false);
    Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
    JSONObject reqJsonObject = new JSONObject();
    reqJsonObject.put("productId", Long.parseLong(product.get("productId")));

    // reqJsonObject.put("powerType",

    // ApplicationConstants.PowerTypes.SINGLE_VISION);

    // reqJsonObject.put("packageId", product.get("packageId"));

    reqJsonObject.put("shipToStoreRequired", true);
    reqJsonObject.put("isLocalFittingRequired", true);
    HttpResponse httpresponse = RequestUtil.postRequest(addToCartURL, headers, null, reqJsonObject);
    JSONObject cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
    Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
        GenericUtil.printAPICallDetails(addToCartURL, reqJsonObject, cart_response));
    JSONArray item = cart_response.getJSONObject("result").getJSONArray("items");
    for (int i = 0; i < item.length(); i++) {
      JSONObject itemsObject = item.getJSONObject(i);
      Assert.assertEquals(itemsObject.getBoolean("shipToStoreRequired"),
          reqJsonObject.getBoolean("shipToStoreRequired"), "shipToStoreRequired mismatch");

      Assert.assertEquals(itemsObject.getBoolean("isLocalFittingRequired"),
          reqJsonObject.getBoolean("isLocalFittingRequired"), "isLocalFittingRequired mismatch");

    }

    jsonResponse_category = JunoV1Util
        .getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), apiClient);
    product = ApplicationUtil.getProductDetails(jsonResponse_category,
        ApplicationConstants.PowerTypes.SINGLE_VISION, false);
    Assert.assertNotNull(product.get("packageId"), "Product id: " + product.get("productId"));
    JSONObject reqJsonObject1 = new JSONObject();
    reqJsonObject1.put("productId", Long.parseLong(product.get("productId")));
    // reqJsonObject.put("powerType",
    // ApplicationConstants.PowerTypes.SINGLE_VISION);
    // reqJsonObject.put("packageId", product.get("packageId"));
    httpresponse = RequestUtil.postRequest(addToCartURL, headers, null, reqJsonObject1);
    cart_response = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
    Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
        GenericUtil.printAPICallDetails(addToCartURL, reqJsonObject, cart_response));
    String cartId = cart_response.getJSONObject("result").getString("id");
    JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
    Assert.assertEquals(items.getJSONObject(0).getBoolean("shipToStoreRequired"),
        reqJsonObject.getBoolean("shipToStoreRequired"), "shipToStoreRequired mismatch");
    Assert.assertEquals(items.getJSONObject(0).getBoolean("isLocalFittingRequired"),
        reqJsonObject.getBoolean("isLocalFittingRequired"), "isLocalFittingRequired mismatch");
    Assert.assertFalse(items.getJSONObject(1).has("shipToStoreRequired"),
        "shipToStoreRequired is present");
    Assert.assertFalse(items.getJSONObject(1).has("isLocalFittingRequired"),
        "isLocalFittingRequired is present");
    if (mongoValidationFlag) {
      Map<String, Object> cartDetails = new HashMap<String, Object>();
      cartDetails.put("_id", Long.parseLong(cartId));
      List<Document> getCartDetails = CartUtil.getAllCarts(cartDetails);
      Assert.assertEquals(getCartDetails.size(), 1, "Cart Db is more than one document");
      Document cartRecord = getCartDetails.get(0);
      @SuppressWarnings("unchecked")
      List<Document> itemDb = (List<Document>) cartRecord.get("items");
      Assert.assertEquals(itemDb.size(), 2, "Number item is more then one");
      Assert.assertEquals(itemDb.get(0).get("shipToStoreRequired"),
          reqJsonObject.getBoolean("shipToStoreRequired"), "shipToStoreRequired mismatch");
      Assert.assertEquals(itemDb.get(0).get("isLocalFittingRequired"),
          reqJsonObject.getBoolean("isLocalFittingRequired"), "isLocalFittingRequired mismatch");
      Assert.assertNull(itemDb.get(1).get("shipToStoreRequired"), "shipToStoreRequired is present");
      Assert.assertNull(itemDb.get(1).get("isLocalFittingRequired"),
          "isLocalFittingRequired is present");
    }
    CartUtil.addStoreCredit(sessionToken, scCode, 200);

  }

  @Test
  public void addToCart_validate_for_frameColour_and_frameSizeSpecification()
      throws URISyntaxException, Exception {
    String frameSizeSpecification = null;
    String frameColour = null;
    String sizeValue = null;
    String frameColourValue = null;
    // String sessionToken = SessionUtil.createNewSession();
    String sessionToken = CustomerUtil.get_sessionId_after_user_authentication(
        ApplicationConstants.userEmail, ApplicationConstants.userPassword, XApiClient.ANDROID);
    List<String> X_Api_Client = Arrays.asList(XApiClient.DESKTOP, XApiClient.ANDROID,
        XApiClient.IOS, XApiClient.MOBILESITE, XApiClient.POS_IOS);
    List<String> category = Arrays.asList(ProductCategories.EYEGLASSES,
        ProductCategories.SUNGLASSES, ProductCategories.EYEGLASSES_FFF,
        ProductCategories.CONTACT_LENS, ProductCategories.ACCESSORIES);

    for (int i = 0, j = 0; i < X_Api_Client.size() && j < category.size(); i++, j++) {
      JSONObject jsonResponse_category =
          JunoV1Util.getCategoryDetails(category.get(j), X_Api_Client.get(i));
      String product = jsonResponse_category.getJSONObject("result").getJSONArray("product_list")
          .getJSONObject(0).getString("id");

      // Product api for asserting
      String restURL =
          Environments.SERVICES_ENVIRONMENT + JunoV1PathConstants.PRODUCT_API_PATH + product;
      List<NameValuePair> header = new ArrayList<NameValuePair>();
      header.add(new BasicNameValuePair("Content-Type", "application/json"));
      header.add(new BasicNameValuePair("X-Api-Client", X_Api_Client.get(i)));
      HttpResponse httpresponse = RequestUtil.getRequest(restURL, header, null);
      Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
          "Response is not proper for" + restURL);
      JSONObject respJsonObjectproduct = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
      JSONArray specifications_array =
          respJsonObjectproduct.getJSONObject("result").getJSONArray("specifications");

      JSONArray specifications_items = specifications_array.getJSONObject(0).getJSONArray("items");
      for (int b = 0; b < specifications_items.length(); b++) {
        JSONObject specification_urlDetails = specifications_items.getJSONObject(b);
        if (specification_urlDetails.getString("name").equals("Size")) {
          sizeValue = specification_urlDetails.getString("value");
        }
        if (specification_urlDetails.getString("name").equals("Frame colour")) {
          frameColourValue = specification_urlDetails.getString("value");
        }
      }

      JSONObject cartResponse = CartUtil.createCart1(sessionToken, X_Api_Client.get(i), product);
      JSONArray itemsJson = cartResponse.getJSONObject("result").getJSONArray("items");
      if (!category.get(j).equals(ProductCategories.CONTACT_LENS)
          && !category.get(j).equals(ProductCategories.ACCESSORIES)) {
        frameSizeSpecification =
            itemsJson.getJSONObject(itemsJson.length() - 1).getString("frameSizeSpecification");
        Assert.assertEquals(frameSizeSpecification, sizeValue);
        frameColour = itemsJson.getJSONObject(itemsJson.length() - 1).getString("frameColour");
        Assert.assertEquals(frameColour, frameColourValue);
        log.info("!!!!!!!!!!!!!!!!!1" + frameSizeSpecification + "!!" + sizeValue + "!!"
            + frameColour + "!!" + frameColourValue + "!!!!!!!!!!!!!!!1");

      }
    }
  }
}
