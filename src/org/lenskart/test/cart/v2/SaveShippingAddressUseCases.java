package org.lenskart.test.cart.v2;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.CartPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import com.lenskart.juno.schema.v2.cart.CartResponse;
import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.GiftMessage;
import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class SaveShippingAddressUseCases {
	private static final Logger log = GenericUtil.InitLogger(SaveShippingAddressUseCases.class);
	private String saveAddressURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.SaveShippingAddress).trim();
	private String registered_emailId = "testing" + GenericUtil.createRandomNumber(4) + "@example.com";
	private static HashMap<String, String> product = new HashMap<>();
	private String authenticated_sessionId;
	private String password = "password";
	public static String XStoreId = "1";
	Address addressObj = new Address();
	GiftMessage giftMsgObj = new GiftMessage();

	private static JSONObject reqJsonObject = null;
	HashMap<String, String> cartMap = new HashMap<String, String>();
	String cartId = "";
	private static JaxbAnnotationModule module;
	private static ObjectMapper objectMapper;
	static {
		module = new JaxbAnnotationModule();
		objectMapper = new ObjectMapper();
		objectMapper.registerModule(module);
		objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
		objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}
	private static boolean mongoValidationFlag = Environments.dbConnectionFlag;
	private MongoConnectionUtility mongoCartConnectionObject = null;

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (mongoValidationFlag) {
			mongoCartConnectionObject = CartUtil.getCartMongoConnectionObject();
			mongoCartConnectionObject.initMongoDBConnection();
		}
	}

	@AfterClass
	public void closeDBConection() {
		if (mongoValidationFlag) {
			mongoCartConnectionObject.closeMongoConnection();
		}
	}

	@DataProvider(name = "Save_Address")
	public static Iterator<String[]> supplyData1() throws IOException, JSONException {
		char seperator = ',';
		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "//csv_files//cart//Address.csv", seperator);
		List<String[]> list = csv.getEntriesAsList();

		Iterator<String[]> itr = list.iterator();
		return itr;

	}

	public void saveAddress(String cartId, String sessionToken, Address addressObj, GiftMessage giftMsgObj,
			String X_Api_Client) throws Exception {
		String addressJson = objectMapper.writeValueAsString(addressObj);
		String giftJson = objectMapper.writeValueAsString(giftMsgObj);
		String json = "{\"address\":" + addressJson + ",\"giftMessage\":" + giftJson + "}";

		reqJsonObject = new JSONObject(json);
		log.info("AddCoupon request URL : " + saveAddressURL);
		log.info("Request JSON:" + reqJsonObject);
		List<NameValuePair> param = new ArrayList<NameValuePair>();

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		header.add(new BasicNameValuePair("X-Api-Client", X_Api_Client));
		log.info("Header:" + header);
		HttpResponse httpResponse = RequestUtil.postRequest(saveAddressURL, header, param, reqJsonObject);
		int httpStatusCode = httpResponse.getStatusLine().getStatusCode();
		JSONObject respJsonObjectData = RequestUtil.convertHttpResponseToJsonObject(httpResponse);

		if (httpStatusCode == 200) {
			JSONObject resultObj = respJsonObjectData.getJSONObject("result");
			String b = resultObj.toString();
			CartResponse node = objectMapper.readValue(b, CartResponse.class);
			Assert.assertNotNull(node.getCartId());
			Assert.assertEquals(node.getCartId().toString(), cartId, "CartID is not correct");
			log.info("httpResponse" + respJsonObjectData);
			Assert.assertEquals(node.getCustomer().getAddress().getId(), addressObj.getId(),
					"Address ID is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getFirstName(), addressObj.getFirstName(),
					"Address: First Name is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getLastName(), addressObj.getLastName(),
					"Address: Last Name is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getPhone(), addressObj.getPhone(),
					"Address: Phone No is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getEmail(), addressObj.getEmail().trim(),
					"Address : Email is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getAddressType(), "shipping",
					"Address type is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getAddressline1(), addressObj.getAddressline1(),
					"Address: AddressLine1 is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getAddressline2(), addressObj.getAddressline2(),
					"Address: AddressLine2 is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getCity(), addressObj.getCity(),
					"Address:City is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getFloor(), addressObj.getFloor(),
					"Address :Floor is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getLiftAvailable(), addressObj.getLiftAvailable(),
					"Address :Lift avalable value is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getLandmark(), addressObj.getLandmark(),
					"Address :Landmark is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getState(), addressObj.getState(),
					"Address: State is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getPostcode(), addressObj.getPostcode(),
					"Address: PostCode is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getCountry(), addressObj.getCountry(),
					"Address: Country is not Correct");
			Assert.assertEquals(node.getCustomer().getAddress().getAlternatePhone(), addressObj.getAlternatePhone(),
					"Address: Alternate Phone No is not Correct");

			// GiftMessage

			Assert.assertEquals(node.getGiftMessage().getFrom(), giftMsgObj.getFrom(),
					"GiftMessage: From is not Correct");
			Assert.assertEquals(node.getGiftMessage().getTo(), giftMsgObj.getTo(), "GiftMessage: To is not Correct");
			Assert.assertEquals(node.getGiftMessage().getMessage(), giftMsgObj.getMessage(),
					"GiftMessage: Message is not Correct");
			Assert.assertEquals(node.getGiftMessage().getOrderComment(), giftMsgObj.getOrderComment(),
					"GiftMessage: OrderComment is not Correct");
			log.info("address saved" + " for CartID:" + node.getCartId());

			{

			}
		} else {
			throw new Exception("Status code received : " + httpStatusCode + "HTTP RESPONSE:" + respJsonObjectData
					+ "Failed to Save Address in cart  : ");
		}

	}

	@Test(enabled = true, description = " Save Address to Already Registered Logined Cart", dataProvider = "Save_Address")
	public void TC01_SaveAddressLoginedUser(String testcase, String addressType, String addressline1,
			String addressline2, String alternatePhone, String city, String country, String email, String firstName,
			String floor, String id, String landmark, String lastName, String liftAvailable, String locality,
			String phone, String postcode, String state, String from, String Gift_id, String message,
			String orderComment, String to) throws Exception {

		String authSessionToken = CustomerUtil.get_sessionId_after_user_authentication(
				ApplicationConstants.userEmail.toLowerCase(), ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		log.info("authSessionToken while creating cart " + authSessionToken);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ProductUtil.getProductDetails(jsonResponse_category, "bifocal", false, false);
		JSONObject respJsonObjectpowerData = CartUtil.createCart(authSessionToken, product.get("productId"),
				ApplicationConstants.XApiClient.ANDROID);
		JSONObject resultObj = respJsonObjectpowerData.getJSONObject("result");
		log.info(resultObj);
		String b = resultObj.toString();

		CartResponse node = objectMapper.readValue(b, CartResponse.class);
		Assert.assertNotNull(node.getCartId(), "cart_id is null");
		log.info("CART ID in Response:::" + node.getCartId());
		cartId = node.getCartId().toString();

		SetAddressGiftMsgObject(testcase, addressType, addressline1, addressline2, alternatePhone, city, country,
				ApplicationConstants.userEmail.toLowerCase(), firstName, floor, id, landmark, lastName, liftAvailable,
				locality, phone, postcode, state, from, Gift_id, message, orderComment, to);

		saveAddress(cartId, authSessionToken, addressObj, giftMsgObj, ApplicationConstants.XApiClient.ANDROID);

	}

	@Test(enabled = true)
	public void TC01_SaveAddressLoginedUser_for_sanity() throws Exception {

		String authSessionToken = CustomerUtil.get_sessionId_after_user_authentication(
				ApplicationConstants.userEmail.toLowerCase(), ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		log.info("authSessionToken while creating cart " + authSessionToken);

		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject respJsonObjectpowerData = CartUtil.createCart(authSessionToken, product_Id, "android");

		JSONObject resultObj = respJsonObjectpowerData.getJSONObject("result");
		log.info(resultObj);
		String b = resultObj.toString();

		CartResponse node = objectMapper.readValue(b, CartResponse.class);
		Assert.assertNotNull(node.getCartId(), "cart_id is null");
		log.info("CART ID in Response:::" + node.getCartId());
		cartId = node.getCartId().toString();

		SetAddressGiftMsgObject("TC01:Successful case with valid data", "Shipping", "101 Golden Residency", "Bellandur",
				"**********", "Bangalore", "India", "<EMAIL>", "FirstName2", "0", "266", "Centeral Mall",
				"LastName2", "FALSE", "Bellandur", "**********", "110001", "Karnatka", "User2", "2", "*****",
				"OrderComment1", "FirstName2");
		saveAddress(cartId, authSessionToken, addressObj, giftMsgObj, "android");
	}

	@Test(enabled = true)
	public void saveShippingAddress_GuestUserWithSpaceInEmail() throws Exception {

		String sessionToken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject respJsonObjectpowerData = CartUtil.createCart(sessionToken, product_Id, "android");

		JSONObject resultObj = respJsonObjectpowerData.getJSONObject("result");
		log.info(resultObj);
		String b = resultObj.toString();

		CartResponse node = objectMapper.readValue(b, CartResponse.class);
		Assert.assertNotNull(node.getCartId(), "cart_id is null");
		log.info("CART ID in Response:::" + node.getCartId());
		cartId = node.getCartId().toString();

		SetAddressGiftMsgObject("TC01:Successful case with valid data", "Shipping", "101 Golden Residency", "Bellandur",
				"**********", "Bangalore", "India", "	test" + GenericUtil.createRandomNumber(5) + "@example.com	",
				"FirstName2", "0", "266", "Centeral Mall", "LastName2", "FALSE", "Bellandur", "**********", "110001",
				"Karnatka", "User2", "2", "*****", "OrderComment1", "FirstName2");
		saveAddress(cartId, sessionToken, addressObj, giftMsgObj, "android");
	}

	@Test(enabled = true)
	public void saveShippingAddress_LoggedUserWithSpaceInEmail() throws Exception {
		String authSessionToken = CustomerUtil.get_sessionId_after_user_authentication(
				ApplicationConstants.userEmail, ApplicationConstants.userPassword, ApplicationConstants.XApiClient.ANDROID);
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		JSONObject respJsonObjectpowerData = CartUtil.createCart(authSessionToken, product_Id, "android");

		JSONObject resultObj = respJsonObjectpowerData.getJSONObject("result");
		log.info(resultObj);
		String b = resultObj.toString();

		CartResponse node = objectMapper.readValue(b, CartResponse.class);
		Assert.assertNotNull(node.getCartId(), "cart_id is null");
		log.info("CART ID in Response:::" + node.getCartId());
		cartId = node.getCartId().toString();

		SetAddressGiftMsgObject("TC01:Successful case with valid data", "Shipping", "101 Golden Residency", "Bellandur",
				"**********", "Bangalore", "India", " test" + GenericUtil.createRandomNumber(5) + "@example.com   ",
				"FirstName2", "0", "266", "Centeral Mall", "LastName2", "FALSE", "Bellandur", "**********", "110001",
				"Karnatka", "User2", "2", "*****", "OrderComment1", "FirstName2");
		saveAddress(cartId, authSessionToken, addressObj, giftMsgObj, "android");
	}

	@Test(enabled = true, description = " Save Address to Guest Cart", dataProvider = "Save_Address")
	public void TC02_SaveAddressGuestUser(String testcase, String addressType, String addressline1, String addressline2,
			String alternatePhone, String city, String country, String email, String firstName, String floor, String id,
			String landmark, String lastName, String liftAvailable, String locality, String phone, String postcode,
			String state, String from, String Gift_id, String message, String orderComment, String to)
			throws Exception {

		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		String UnAuthorizedsessionToken = SessionUtil.createNewSession();

		JSONObject respJsonObjectpowerData = CartUtil.createCart(UnAuthorizedsessionToken, product_Id,
				ApplicationConstants.XApiClient.ANDROID);

		JSONObject resultObj = respJsonObjectpowerData.getJSONObject("result");
		log.info(resultObj);
		String b = resultObj.toString();

		CartResponse node = objectMapper.readValue(b, CartResponse.class);
		Assert.assertNotNull(node.getCartId(), "cart_id is null");
		log.info("CART ID in Response:::" + node.getCartId());
		cartId = node.getCartId().toString();

		SetAddressGiftMsgObject(testcase, addressType, addressline1, addressline2, alternatePhone, city, country, email,
				firstName, floor, id, landmark, lastName, liftAvailable, locality, phone, postcode, state, from,
				Gift_id, message, orderComment, to);

		saveAddress(cartId, UnAuthorizedsessionToken, addressObj, giftMsgObj, ApplicationConstants.XApiClient.ANDROID);

	}

	@Test(enabled = true)
	public void TC02_SaveAddressGuestUser_for_sanity() throws Exception {

		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");

		String UnAuthorizedsessionToken = SessionUtil.createNewSession();

		JSONObject respJsonObjectpowerData = CartUtil.createCart(UnAuthorizedsessionToken, product_Id, "android");
		JSONObject resultObj = respJsonObjectpowerData.getJSONObject("result");
		log.info(resultObj);
		String b = resultObj.toString();

		CartResponse node = objectMapper.readValue(b, CartResponse.class);
		Assert.assertNotNull(node.getCartId(), "cart_id is null");
		log.info("CART ID in Response:::" + node.getCartId());
		cartId = node.getCartId().toString();

		SetAddressGiftMsgObject("TC01:Successful case with valid data", "Shipping", "101 Golden Residency", "Bellandur",
				"**********", "Bangalore", "India", "<EMAIL>", "FirstName2", "0", "266", "Centeral Mall",
				"LastName2", "FALSE", "Bellandur", "**********", "110001", "Karnatka", "User2", "2", "*****",
				"OrderComment1", "FirstName2");

		saveAddress(cartId, UnAuthorizedsessionToken, addressObj, giftMsgObj, "android");

	}

	@Test(enabled = true, description = " Save Address to New Registered User", dataProvider = "Save_Address")
	public void TC03_SaveAddressNewRegisteredLoginedUser(String testcase, String addressType, String addressline1,
			String addressline2, String alternatePhone, String city, String country, String email, String firstName,
			String floor, String id, String landmark, String lastName, String liftAvailable, String locality,
			String phone, String postcode, String state, String from, String Gift_id, String message,
			String orderComment, String to) throws Exception {

		// create a new user
		registered_emailId = "testAddress" + GenericUtil.createRandomNumber(9) + "@example.com";
		password = "password";
		String mobileNum = "8" + GenericUtil.createRandomNumber(9);
		authenticated_sessionId = CustomerUtil.registerNewCustomer("Test", "Cart", registered_emailId, password,
				mobileNum);

		log.info("authSessionToken while creating cart " + authenticated_sessionId);
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject respJsonObjectpowerData = CartUtil.createCart(authenticated_sessionId, product_Id,
				ApplicationConstants.XApiClient.ANDROID);
		JSONObject resultObj = respJsonObjectpowerData.getJSONObject("result");
		log.info(resultObj);
		String b = resultObj.toString();

		CartResponse node = objectMapper.readValue(b, CartResponse.class);
		Assert.assertNotNull(node.getCartId(), "cart_id is null");
		log.info("CART ID in Response:::" + node.getCartId());
		cartId = node.getCartId().toString();

		SetAddressGiftMsgObject(testcase, addressType, addressline1, addressline2, alternatePhone, city, country,
				registered_emailId, firstName, floor, id, landmark, lastName, liftAvailable, locality, phone, postcode,
				state, from, Gift_id, message, orderComment, to);

		saveAddress(cartId, authenticated_sessionId, addressObj, giftMsgObj, ApplicationConstants.XApiClient.ANDROID);

	}

	void SetAddressGiftMsgObject(String testcase, String addressType, String addressline1, String addressline2,
			String alternatePhone, String city, String country, String email, String firstName, String floor, String id,
			String landmark, String lastName, String liftAvailable, String locality, String phone, String postcode,
			String state, String from, String Gift_id, String message, String orderComment, String to) {

		if (!StringUtils.isBlank(addressType))
			addressObj.setAddressType(addressType);

		if (!StringUtils.isBlank(addressline1))
			addressObj.setAddressline1(addressline1);

		if (!StringUtils.isBlank(addressline2))
			addressObj.setAddressline2(addressline2);

		if (!StringUtils.isBlank(city))
			addressObj.setCity(city);

		if (!StringUtils.isBlank(country))
			addressObj.setCountry(country);

		if (!StringUtils.isBlank(email))
			addressObj.setEmail(email);

		if (!StringUtils.isBlank(firstName))
			addressObj.setFirstName(firstName);

		if (!StringUtils.isBlank(floor))
			addressObj.setFloor(Long.valueOf(floor));

		if (!StringUtils.isBlank(id))
			addressObj.setId(Integer.valueOf(id));

		if (!StringUtils.isBlank(landmark))
			addressObj.setLandmark(landmark);

		if (!StringUtils.isBlank(lastName))
			addressObj.setLastName(lastName);

		if (!StringUtils.isBlank(liftAvailable))
			addressObj.setLiftAvailable(Boolean.valueOf(liftAvailable));

		if (!StringUtils.isBlank(locality))
			addressObj.setLocality(locality);

		if (!StringUtils.isBlank(phone))
			addressObj.setPhone(phone);

		if (!StringUtils.isBlank(postcode))
			addressObj.setPostcode(postcode);

		if (!StringUtils.isBlank(state))
			addressObj.setState(state);

		if (!StringUtils.isBlank(from))
			giftMsgObj.setFrom(from);

		if (!StringUtils.isBlank(Gift_id))
			giftMsgObj.setId(Long.valueOf(Gift_id));

		if (!StringUtils.isBlank(message))
			giftMsgObj.setMessage(message);

		if (!StringUtils.isBlank(orderComment))
			giftMsgObj.setOrderComment(orderComment);

		if (!StringUtils.isBlank(to))
			giftMsgObj.setTo(to);
	}

}
