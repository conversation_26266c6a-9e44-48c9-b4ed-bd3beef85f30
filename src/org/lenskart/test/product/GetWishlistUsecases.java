package org.lenskart.test.product;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.JunoV2ProductPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;
public class GetWishlistUsecases {
	private static final String RequestUrl_Shortlist = Environments.SERVICES_ENVIRONMENT
			+ JunoV2ProductPathConstants.WISHLIST_GET_V2;
	private static final Logger log = Logger.getLogger(GetWishlistUsecases.class);

	private static String sessiontoken;
	private static String product_Id;
	private static MongoConnectionUtility mongoConnectionObject = null;
	private static boolean db_validation = Environments.dbConnectionFlag;

	@BeforeClass
	public void setup() throws Exception {
		if (db_validation) {
			mongoConnectionObject = JunoV1Util.getJunoV1MongoConnectionObject();
			mongoConnectionObject.initMongoDBConnection();
		}
	}

	@AfterClass
	public void destroy() throws Exception {
		if (db_validation) {
			mongoConnectionObject.closeMongoConnection();
		}
	}

	@Test(enabled = true)
	public void guestUserGetWishlistSuccessful() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject response_wishlist = ProductUtil.saveWishlist(sessiontoken, product_Id);
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("attributes", "true"));
		HttpResponse httpResponse2 = RequestUtil.getRequest(RequestUrl_Shortlist, headers, param);
		JSONObject responseJson1 = RequestUtil.convertHttpResponseToJsonObject(httpResponse2);
		int httpResponse2_statusCode = httpResponse2.getStatusLine().getStatusCode();
		Assert.assertEquals(httpResponse2_statusCode, 200, "invalid statusCode");
		JSONObject result = responseJson1.getJSONObject("result");
		Assert.assertEquals(response_wishlist.getJSONObject("result").getString("numberOfProducts"),
				result.getString("numberOfProducts"), "number of products are not equal");
		Assert.assertNotEquals(result.getInt("numberOfProducts"), 0, "numberOfProducts should not be zero");
		JSONArray productList = result.getJSONArray("productList");
		int count = 0;
		for (int i = 0; i < productList.length(); i++) {
			JSONObject productDetails = productList.getJSONObject(i);
			if (productDetails.getString("id").equals(product_Id))
				count++;
		}
		Assert.assertEquals(count, 1, "product is not present in response");
		Assert.assertNotNull(result.getString("productIds"), "productIds not null");
//		result.remove("productIds");
//		JSONObject v1_getWishlist = JunoV1Util.getWishlist(sessiontoken);
//		v1_getWishlist.remove("productIds");
//		Assert.assertEquals(result, v1_getWishlist,"v1 and v2 results are different");
		if(db_validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("sessionToken", sessiontoken);
			List<Document> wishlistDetails = mongoConnectionObject.executeQuery("wishlist", object);
			String productId = wishlistDetails.get(0).getString("productId");
			Assert.assertEquals(product_Id, productId, "productId mismatch");
		
		}

	}

	@Test(enabled = true)
	public void guestUserGetWishlistwithoutSavewishlist() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		log.info("sessiontoken: "+sessiontoken);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("attributes", "true"));
		HttpResponse httpResponse2 = RequestUtil.getRequest(RequestUrl_Shortlist, headers, param);
		JSONObject responseJson1 = RequestUtil.convertHttpResponseToJsonObject(httpResponse2);
		int httpResponse2_statusCode = httpResponse2.getStatusLine().getStatusCode();
		Assert.assertEquals(httpResponse2_statusCode, 200, "invalid statusCode");
		JSONObject result = responseJson1.getJSONObject("result");
		Assert.assertEquals(result.getInt("numberOfProducts"), 0, "numberOfProducts should not be zero");
		Assert.assertNotNull(result.getString("productIds"), "productIds not null");
	}

	@Test(enabled = true)
	public void guestUserGetWishlistSuccessfulWithParamFalse() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject response_wishlist = ProductUtil.saveWishlist(sessiontoken, product_Id);
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("attributes", "false"));
		HttpResponse httpResponse2 = RequestUtil.getRequest(RequestUrl_Shortlist, headers, param);
		JSONObject responseJson1 = RequestUtil.convertHttpResponseToJsonObject(httpResponse2);
		int httpResponse2_statusCode = httpResponse2.getStatusLine().getStatusCode();
		Assert.assertEquals(httpResponse2_statusCode, 200, "invalid statusCode");
		JSONObject result = responseJson1.getJSONObject("result");
		Assert.assertEquals(response_wishlist.getJSONObject("result").getString("numberOfProducts"),
				result.getString("numberOfProducts"), "number of products are not equal");
		Assert.assertNotEquals(result.getInt("numberOfProducts"), 0, "numberOfProducts should not be zero");
		JSONArray productIds = result.getJSONArray("productIds");
		int count = 0;
		for (int i = 0; i < productIds.length(); i++) {
			if (productIds.getString(i).equals(product_Id))
				count++;
		}
		Assert.assertEquals(count, 1, "product is not present in response");
		Assert.assertNotNull(result.getString("productList"), "productIds is null");
		if(db_validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("sessionToken", sessiontoken);
			List<Document> wishlistDetails = mongoConnectionObject.executeQuery("wishlist", object);
			String productId = wishlistDetails.get(0).getString("productId");
			Assert.assertEquals(product_Id, productId, "productId mismatch");
		
		}
	}

	@Test(enabled = true)
	public void guestUserGetWishlistWithoutParam() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject response_wishlist = ProductUtil.saveWishlist(sessiontoken, product_Id);
		HttpResponse httpResponse2 = RequestUtil.getRequest(RequestUrl_Shortlist, headers, null);
		JSONObject responseJson1 = RequestUtil.convertHttpResponseToJsonObject(httpResponse2);
		int httpResponse2_statusCode = httpResponse2.getStatusLine().getStatusCode();
		Assert.assertEquals(httpResponse2_statusCode, 200, "invalid statusCode");
		JSONObject result = responseJson1.getJSONObject("result");
		Assert.assertEquals(response_wishlist.getJSONObject("result").getString("numberOfProducts"),
				result.getString("numberOfProducts"), "number of products are not equal");
		Assert.assertNotEquals(result.getInt("numberOfProducts"), 0, "numberOfProducts should not be zero");
		JSONArray productIds = result.getJSONArray("productIds");
		int count = 0;
		for (int i = 0; i < productIds.length(); i++) {
			if (productIds.getString(i).equals(product_Id))
				count++;
		}
		Assert.assertEquals(count, 1, "product is not present in response");
		Assert.assertNotNull(result.getString("productList"), "productIds is null");

	}

	@Test(enabled = true)
	public void loggedInGetWishListSuccessful() throws Exception {
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"),
				ApplicationConstants.XApiClient.ANDROID);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JunoV1Util.saveWishlist(sessiontoken, product_Id);
		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("attributes", "true"));
		HttpResponse httpResponse2 = RequestUtil.getRequest(RequestUrl_Shortlist, headers, param);
		JSONObject responseJson1 = RequestUtil.convertHttpResponseToJsonObject(httpResponse2);
		int httpResponse2_statusCode = httpResponse2.getStatusLine().getStatusCode();
		Assert.assertEquals(httpResponse2_statusCode, 200, "invalid statusCode");
		JSONObject result = responseJson1.getJSONObject("result");
		Assert.assertNotEquals(result.getInt("numberOfProducts"), 0, "numberOfProducts should not be zero");
		JSONArray productList = result.getJSONArray("productList");
		int count = 0;
		for (int i = 0; i < productList.length(); i++) {
			JSONObject productDetails = productList.getJSONObject(i);
			if (productDetails.getString("id").equals(product_Id))
				count++;
		}
		Assert.assertEquals(count, 1, "product is not present in response");
		Assert.assertNotNull(result.getString("productList"), "productIds is null");
		ProductUtil.deleteWishlist(sessiontoken);
		if(db_validation) {
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("sessionToken", sessiontoken);
			List<Document> wishlistDetails = mongoConnectionObject.executeQuery("wishlist", object);
			log.info("wishlistDetails:" +wishlistDetails);
//			String productId = wishlistDetails.getString("productId");
//			log.info("wishlistDetails:" +wishlistDetails);
//			Assert.assertEquals(product_Id, productId, "productId mismatch");
		
		}

	}

	@Test(enabled = true)
	public void MergeFlowforGetWishlist() throws Exception {
		sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		 ProductUtil.saveWishlist(sessiontoken, product_Id);
		String authSessionToken = CustomerUtil.get_sessionId_after_user_authentication(sessiontoken,
				ApplicationConstants.userEmail, ApplicationConstants.userPassword,ApplicationConstants.XApiClient.ANDROID);
		product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(1)
				.getString("id");
		JSONObject response_wishlist = ProductUtil.saveWishlist(authSessionToken, product_Id);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", authSessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		List<NameValuePair> param = new ArrayList<NameValuePair>();
		param.add(new BasicNameValuePair("attributes", "true"));
		HttpResponse httpResponse2 = RequestUtil.getRequest(RequestUrl_Shortlist, headers, param);

		JSONObject responseJson1 = RequestUtil.convertHttpResponseToJsonObject(httpResponse2);
		int httpResponse2_statusCode = httpResponse2.getStatusLine().getStatusCode();
		Assert.assertEquals(httpResponse2_statusCode, 200, "invalid statusCode");
		JSONObject result = responseJson1.getJSONObject("result");
		Assert.assertEquals(responseJson1.getJSONObject("result").getInt("numberOfProducts"),2,"numberOf products mismatch");
		ProductUtil.deleteWishlist(authSessionToken);
	}

}
