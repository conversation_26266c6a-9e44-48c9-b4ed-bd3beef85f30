package org.lenskart.test.money;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.MoneyPathConstants;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;
import com.utilities.JsonUtil;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;

public class CreateTransactionUsecases {
	private static String sessiontoken;
	private boolean database_enabled = Environments.dbConnectionFlag;
	private static JSONObject object = null;
	private static BigDecimal balance_fc;
	private static BigDecimal balance_gv;
	private static BigDecimal balance_pd;
	private static BigDecimal balance_gv1;
	private static BigDecimal balance_sc;

	private static final Logger log = GenericUtil.InitLogger(CreateTransactionUsecases.class);
	private static boolean db_validation = Environments.dbConnectionFlag;
	private MySQLConnectionUtility mysqlConnectionObject = null;
	private static String customerId = GenericUtil.createRandomNumber(4);
	private static String customerEmail = "test" + GenericUtil.createRandomNumber(7) + "@gmail.com";
	private static String storeId = "1";

	@BeforeClass
	public void setup() throws Exception {
		if (db_validation) {
			PropertyFactory pf = new PropertyFactory("MoneyService");
			mysqlConnectionObject = new MySQLConnectionUtility(pf);
			mysqlConnectionObject.initMySQLConnection();
			MoneyDatabaseUtil.initMySQLDB();
		}
	}

	@AfterClass
	public void destroy() throws Exception {
		if (db_validation) {
			mysqlConnectionObject.closeMySQLConnection();
			MoneyDatabaseUtil.closeMySQLDB();
		}
	}

	@DataProvider(name = "create_money_transation")
	public static Iterator<String[]> supplyData() throws IOException {
		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "//csv_files//Create_Transaction_Money.csv");
		List<String[]> list = csv.getEntriesAsList();

		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "create_money_transation_for_fc")
	public static Iterator<String[]> supplyData_of_fc() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "//csv_files//Create_Transaction_Money_for_FC.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@DataProvider(name = "create_money_transation_for_prepaid_discount")
	public static Iterator<String[]> supplyData_of_pd() throws IOException {

		CSVReadUtil csv = new CSVReadUtil(
				System.getProperty("user.dir") + "//csv_files//Create_Transaction_Money_for_PD.csv");
		List<String[]> list = csv.getEntriesAsList();
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@Test(enabled = true, dataProvider = "create_money_transation_for_prepaid_discount")
	public void create_money_transaction_with_only_prepaid_discount_successcase(String db_check, String usecase,
			String x_api_client, String orderAmount, String code, String debitAmount, String transactionType,
			String status, String error_status, String message, String errors) throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info("--------------------------------" + usecase + "---------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("orderAmount", orderAmount);
		object.put("comment", "prepaid discount");
		JSONArray prepaidDiscounts = new JSONArray();
		JSONObject prepaidDiscountsObject = new JSONObject();
		prepaidDiscountsObject.put("prepaidDiscountCode", code);
		if (debitAmount == null || debitAmount.equals("") || debitAmount.equals("sdcsd"))
			prepaidDiscountsObject.put("prepaidDiscountValue", debitAmount);
		else
			prepaidDiscountsObject.put("prepaidDiscountValue", Integer.parseInt(debitAmount));
		prepaidDiscountsObject.put("transactionType", transactionType);
		prepaidDiscounts.put(prepaidDiscountsObject);
		object.put("prepaidDiscounts", prepaidDiscounts);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_api_client));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		if (db_check.equals(storeId) && database_enabled) {

			String balance_prepaid = MoneyDatabaseUtil.get_column_value("'"
					+ object.getJSONArray("prepaidDiscounts").getJSONObject(0).getString("prepaidDiscountCode") + "'",
					"prepaiddiscount_code", "prepaiddiscount", "balance");
			log.info("pd :" + balance_prepaid);
			balance_pd = new BigDecimal(balance_prepaid);
		}

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		if (httpResponsecreatemoney.getStatusLine().getStatusCode() == 200) {
			JSONObject responseJson_Createmoney_result = RequestUtil
					.convertHttpResponseToJsonObject(httpResponsecreatemoney);
			Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), Integer.parseInt(status),
					"Status is not correct");
			JSONObject responseJson_Createmoney = responseJson_Createmoney_result.getJSONObject("result");
			Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("transactionId"),
					"transactionId is not same");
			Assert.assertNotNull(responseJson_Createmoney.getInt("transactionId"), "transactionId is  null");
			Assert.assertEquals(responseJson_Createmoney.getString("transactionStatus"), "SUCCESS",
					"status is not correct");
			Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("orderId"),
					"orderId is not correct");
			Assert.assertEquals(object.getString("comment"), responseJson_Createmoney.getString("comment"),
					"orderId is not correct");
			Assert.assertEquals(object.getInt("storeId"), responseJson_Createmoney.getInt("storeId"),
					"storeId is not correct");
			Assert.assertEquals(object.getString("customerEmail"), responseJson_Createmoney.getString("customerEmail"),
					"customerEmail is not correct");
			Assert.assertEquals(object.getInt("customerId"), responseJson_Createmoney.getInt("customerId"),
					"customerId is not correct");
			Assert.assertEquals(
					JsonUtil.compare_json_arrays(object.getJSONArray("prepaidDiscounts"),
							responseJson_Createmoney.getJSONArray("prepaidDiscounts")),
					true, "prepaidDiscounts is not same");
			Assert.assertEquals(responseJson_Createmoney.getString("franchiseCredits"), "[]",
					"franchiseCredits is not correct");
			Assert.assertEquals(responseJson_Createmoney.getString("giftVouchers"), "[]",
					"franchiseCredits is not correct");
			Assert.assertEquals(responseJson_Createmoney.getString("storeCredits"), "[]",
					"franchiseCredits is not correct");
			Assert.assertEquals(responseJson_Createmoney_result.getInt("status"), 200, "status is not correct");
			if (database_enabled) {
				String balnace_prepaid1 = MoneyDatabaseUtil.get_column_value(
						"'" + object.getJSONArray("prepaidDiscounts").getJSONObject(0).getString("prepaidDiscountCode")
								+ "'",
						"prepaiddiscount_code", "prepaiddiscount", "balance");
				log.info("pd :" + balnace_prepaid1);
				BigDecimal balance_pd1_after_response = new BigDecimal(balnace_prepaid1);
				// Assert.assertEquals(
				// balance_pd1_after_response.add(new
				// BigDecimal(object.getJSONArray("prepaidDiscounts")
				// .getJSONObject(0).getInt("prepaidDiscountValue"))),
				// balance_pd, " Prepaid discount is not updated in database table");

				log.info("-----checking transactionID created in prepaiddiscount_history table-----");
				String get_all_details_prepaiddiscount_history = MoneyDatabaseUtil.get_all_details(
						responseJson_Createmoney.getString("transactionId"), "order_increment_id",
						"prepaiddiscount_history");
				Assert.assertNotEquals(get_all_details_prepaiddiscount_history, "",
						"TransactionID is not created in database table");
				Assert.assertNotEquals(get_all_details_prepaiddiscount_history, null,
						"TransactionID is not created in database table");
				String[] pd = get_all_details_prepaiddiscount_history.split("\r\n");
				String[] pd1 = pd[0].split(",");
				log.info("gv history " + get_all_details_prepaiddiscount_history);
				MoneyDatabaseUtil.db_validation_PD(responseJson_Createmoney, pd1, 0, "");

			}
		} else {
			JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);
			Assert.assertEquals(responseJson_Createmoney.getString("status"), error_status, "status is not same");
			Assert.assertEquals(responseJson_Createmoney.getString("message"), message, "message is not correct");
			Assert.assertEquals(responseJson_Createmoney.getString("errors"), errors, "errors is not correct");
		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_multiple_prepaid_discount_successcase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info("-------------------------------Multiple prepaid discount---------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);

		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("orderAmount", "500");
		object.put("comment", "prepaid discount");
		JSONArray prepaidDiscounts = new JSONArray();
		JSONObject prepaidDiscountsObject1 = new JSONObject();
		prepaidDiscountsObject1.put("prepaidDiscountCode", "9555-3VCBP-QMGX");
		prepaidDiscountsObject1.put("prepaidDiscountValue", 100);
		prepaidDiscountsObject1.put("transactionType", "DEBIT");
		prepaidDiscounts.put(prepaidDiscountsObject1);
		JSONObject prepaidDiscountsObject2 = new JSONObject();
		prepaidDiscountsObject2.put("prepaidDiscountCode", "9555-3VCBP-QMGU");
		prepaidDiscountsObject2.put("prepaidDiscountValue", 100);
		prepaidDiscountsObject2.put("transactionType", "DEBIT");
		prepaidDiscounts.put(prepaidDiscountsObject2);
		object.put("prepaidDiscounts", prepaidDiscounts);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "desktop"));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400, "Status is not correct");
		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"),
				"Multiple prepaid discounts are not allowed.", "message is not correct");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"),
				"[\"Multiple prepaid discounts are not allowed.\"]", "errors is not correct");
	}

	@Test(enabled = false)
	public void create_money_transaction_prepaid_discount_amount_less_than_expected_successcase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"---------------------------------Create money transaction for prepaid discount less than expected---------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);

		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("orderAmount", "100");
		object.put("comment", "prepaid discount");
		JSONArray prepaidDiscounts = new JSONArray();
		JSONObject prepaidDiscountsObject = new JSONObject();
		prepaidDiscountsObject.put("prepaidDiscountCode", "9555-3VCBP-QMGU");
		prepaidDiscountsObject.put("prepaidDiscountValue", 5);
		prepaidDiscountsObject.put("transactionType", "DEBIT");
		prepaidDiscounts.put(prepaidDiscountsObject);
		object.put("prepaidDiscounts", prepaidDiscounts);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 200, "Status is not correct");
		// JSONObject responseJson_Createmoney =
		// RequestUtil.convert_HttpResponse_to_JsonObject(httpResponsecreatemoney);
		// Assert.assertEquals(responseJson_Createmoney.getString("status"),
		// "PRECONDITION_REQUIRED",
		// "status is not same");
		// Assert.assertEquals(responseJson_Createmoney.getString("message"),
		// "Prepaid discount amount is not valid as per order amount.", "message
		// is not
		// correct");
		// Assert.assertEquals(responseJson_Createmoney.getString("errors"),
		// "[\"Prepaid discount amount is not valid as per order amount.\"]",
		// "errors is
		// not correct");
	}

	@Test(enabled = true)
	public void create_money_transaction_with_two_gv_sc_pd_successcase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"---------------------------------Create money transaction with 2 gv ,sc and pd---------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.DESKTOP));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		String storeCredit = GenericUtil.createRandomNumber(4) + "-LJ904-F" + GenericUtil.createRandomNumber(3);
		if (db_validation) {
			String sql = "INSERT INTO storecredit (`storecredit_id`, `store_code`, `balance`, `currency`, `status`, `expired_at`, `customer_id`, `customer_name`, `customer_email`, `recipient_name`, `recipient_email`, `recipient_address`, `message`, `store_id`) VALUES("
					+ GenericUtil.createRandomNumber(9) + ", '" + storeCredit
					+ "', 10000.0000, 'INR', 2, '2020-02-04 07:38:02', " + "12437" + ", 'test', '"
					+ "<EMAIL>" + "', 'Testetllenskart ', '" + "<EMAIL>"
					+ "', NULL, NULL, 1)";

			mysqlConnectionObject.executeInsertQuery(sql);
		}

		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "sc and gv");
		object.put("orderAmount", "500");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-9948-IMOTZ-NKHY");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);

		JSONObject giftVouchers2 = new JSONObject();
		giftVouchers2.put("giftVoucherCode", "LENS-JIT-ZMFY0-WTGR");
		giftVouchers2.put("giftVoucherValue", 7);
		giftVouchers2.put("transactionType", "CREDIT");
		giftVouchers.put(giftVouchers2);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", storeCredit);
		storeCredits1.put("storeCreditValue", 3);
		storeCredits1.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits1);
		object.put("storeCredits", storeCredits);

		JSONArray prepaidDiscounts = new JSONArray();
		JSONObject prepaidDiscountsObject = new JSONObject();
		prepaidDiscountsObject.put("prepaidDiscountCode", "9555-3VCBP-QMGU");
		prepaidDiscountsObject.put("prepaidDiscountValue", 100);
		prepaidDiscountsObject.put("transactionType", "DEBIT");
		prepaidDiscounts.put(prepaidDiscountsObject);
		object.put("prepaidDiscounts", prepaidDiscounts);

		// if (database_enabled) {
		//
		// String balnace_giftvocher = MoneyDatabaseUtil.get_column_value(
		// "'" +
		// object.getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode")
		// +
		// "'",
		// "gift_code", "giftvoucher", "balance");
		// balance_gv = new BigDecimal(balnace_giftvocher);
		//
		// String balnace_giftvocher1 = MoneyDatabaseUtil.get_column_value(
		// "'" +
		// object.getJSONArray("giftVouchers").getJSONObject(1).getString("giftVoucherCode")
		// +
		// "'",
		// "gift_code", "giftvoucher", "balance");
		// balance_gv1 = new BigDecimal(balnace_giftvocher1);
		//
		// String balnace_storeCredits = MoneyDatabaseUtil.get_column_value(
		// "'" +
		// object.getJSONArray("storeCredits").getJSONObject(0).getString("storeCreditCode")
		// +
		// "'",
		// "store_code", "storecredit", "balance");
		// balance_sc = new BigDecimal(balnace_storeCredits);
		//
		// String balance_prepaid = MoneyDatabaseUtil.get_column_value("'"
		// +
		// object.getJSONArray("prepaidDiscounts").getJSONObject(0).getString("prepaidDiscountCode")
		// +
		// "'",
		// "prepaiddiscount_code", "prepaiddiscount", "balance");
		// log.info("pd :" + balance_prepaid);
		// balance_pd = new BigDecimal(balance_prepaid);
		// }

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		JSONObject responseJson_Createmoney_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 200, "Status is not correct");
		JSONObject responseJson_Createmoney = responseJson_Createmoney_result.getJSONObject("result");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("transactionId"),
				"transactionId is not same");
		Assert.assertNotNull(responseJson_Createmoney.getInt("transactionId"), "transactionId is  null");
		Assert.assertEquals(responseJson_Createmoney.getString("transactionStatus"), "SUCCESS",
				"status is not correct");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("orderId"),
				"orderId is not correct");
		Assert.assertEquals(object.getString("comment"), responseJson_Createmoney.getString("comment"),
				"orderId is not correct");
		Assert.assertEquals(object.getInt("storeId"), responseJson_Createmoney.getInt("storeId"),
				"storeId is not correct");
		Assert.assertEquals(object.getString("customerEmail"), responseJson_Createmoney.getString("customerEmail"),
				"customerEmail is not correct");
		Assert.assertEquals(object.getInt("customerId"), responseJson_Createmoney.getInt("customerId"),
				"customerId is not correct");
		Assert.assertEquals(JsonUtil.compare_json_arrays(object.getJSONArray("giftVouchers"),
				responseJson_Createmoney.getJSONArray("giftVouchers")), true, "giftVouchers is not same");
		Assert.assertEquals(JsonUtil.compare_json_arrays(object.getJSONArray("storeCredits"),
				responseJson_Createmoney.getJSONArray("storeCredits")), true, "storeCredits is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("franchiseCredits"), "[]",
				"franchiseCredits is not correct");
		Assert.assertEquals(responseJson_Createmoney_result.getInt("status"), 200, "status is not correct");
		// if (database_enabled) {
		// String balnace_giftvocher1 = MoneyDatabaseUtil.get_column_value("'" +
		// responseJson_Createmoney
		// .getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode") +
		// "'",
		// "gift_code",
		// "giftvoucher", "balance");
		// BigDecimal balance_gv1_after_response = new BigDecimal(balnace_giftvocher1);
		// Assert.assertEquals(balance_gv1_after_response.add(new BigDecimal(5)),
		// balance_gv,
		// " GV is not updated in database table");
		//
		// String balnace_giftvocher = MoneyDatabaseUtil.get_column_value("'" +
		// responseJson_Createmoney
		// .getJSONArray("giftVouchers").getJSONObject(1).getString("giftVoucherCode") +
		// "'",
		// "gift_code",
		// "giftvoucher", "balance");
		// BigDecimal balance_gv1_after_response1 = new BigDecimal(balnace_giftvocher);
		// Assert.assertEquals(balance_gv1_after_response1, balance_gv1.add(new
		// BigDecimal(7)),
		// " GV is not updated in database table");
		//
		// String balnace_prepaid1 = MoneyDatabaseUtil.get_column_value("'"
		// +
		// object.getJSONArray("prepaidDiscounts").getJSONObject(0).getString("prepaidDiscountCode")
		// +
		// "'",
		// "prepaiddiscount_code", "prepaiddiscount", "balance");
		// log.info("pd :" + balnace_prepaid1);
		// BigDecimal balance_pd1_after_response = new BigDecimal(balnace_prepaid1);
		// Assert.assertEquals(balance_pd1_after_response, balance_pd,
		// " Prepaid discount is not updated in database table");
		//
		// String balnace_storeCredits = MoneyDatabaseUtil.get_column_value("'" +
		// responseJson_Createmoney
		// .getJSONArray("storeCredits").getJSONObject(0).getString("storeCreditCode") +
		// "'",
		// "store_code",
		// "storecredit", "balance");
		// BigDecimal balance_sc_after_response = new BigDecimal(balnace_storeCredits);
		// Assert.assertEquals(balance_sc_after_response.add(new BigDecimal(3)),
		// balance_sc,
		// " SC is not updated in database table");
		//
		// log.info("-----checking transactionID created in storecredit_history
		// table-----");
		// String get_all_details_storecredit_history =
		// MoneyDatabaseUtil.get_all_details(
		// responseJson_Createmoney.getString("transactionId"), "order_increment_id",
		// "storecredit_history");
		// String[] sc = get_all_details_storecredit_history.split(",");
		// log.info("sc histore " + get_all_details_storecredit_history);
		// Assert.assertNotEquals(get_all_details_storecredit_history, "",
		// "TransactionID is not created in database table");
		// Assert.assertNotEquals(get_all_details_storecredit_history, null,
		// "TransactionID is not created in database table");
		// MoneyDatabaseUtil.db_validation_SC(balnace_storeCredits,
		// responseJson_Createmoney, sc, 0,
		// "");
		//
		// log.info("-----checking transactionID created in giftvoucher_history
		// table-----");
		// String get_all_details_giftvoucher_history =
		// MoneyDatabaseUtil.get_all_details(
		// responseJson_Createmoney.getString("transactionId"), "order_increment_id",
		// "giftvoucher_history");
		// Assert.assertNotEquals(get_all_details_giftvoucher_history, "",
		// "TransactionID is not created in database table");
		// Assert.assertNotEquals(get_all_details_giftvoucher_history, null,
		// "TransactionID is not created in database table");
		// String[] gv = get_all_details_giftvoucher_history.split("\r\n");
		// String[] gv1 = gv[0].split(",");
		// String[] gv2 = gv[1].split(",");
		//
		// log.info("gv history " + get_all_details_giftvoucher_history);
		// MoneyDatabaseUtil.db_validation_GV(responseJson_Createmoney, gv1, 0, "");
		// MoneyDatabaseUtil.db_validation_GV(responseJson_Createmoney, gv2, 1, "");
		//
		// log.info("-----checking transactionID created in prepaiddiscount_history
		// table-----");
		// String get_all_details_prepaiddiscount_history =
		// MoneyDatabaseUtil.get_all_details(
		// responseJson_Createmoney.getString("transactionId"), "order_increment_id",
		// "prepaiddiscount_history");
		// Assert.assertNotEquals(get_all_details_prepaiddiscount_history, "",
		// "TransactionID is not created in database table");
		// Assert.assertNotEquals(get_all_details_prepaiddiscount_history, null,
		// "TransactionID is not created in database table");
		// String[] pd = get_all_details_prepaiddiscount_history.split("\r\n");
		// String[] pd1 = pd[0].split(",");
		// log.info("gv history " + get_all_details_prepaiddiscount_history);
		// MoneyDatabaseUtil.db_validation_PD(responseJson_Createmoney, pd1, 0, "");
		//
		// }
	}

	@Test(enabled = true)
	public void create_money_transaction_with_two_gv_sc_successcase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"---------------------------------Create money transaction with 2 gv and sc---------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "GV AND SC");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-9948-IMOTZ-NKHY");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);

		JSONObject giftVouchers2 = new JSONObject();
		giftVouchers2.put("giftVoucherCode", "LENS-JIT-ZMFY0-WTGR");
		giftVouchers2.put("giftVoucherValue", 7);
		giftVouchers2.put("transactionType", "CREDIT");
		giftVouchers.put(giftVouchers2);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", "0837-GI6JJ-LGCS");
		storeCredits1.put("storeCreditValue", 3);
		storeCredits1.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits1);
		object.put("storeCredits", storeCredits);
		if (database_enabled) {

			String balnace_giftvocher = MoneyDatabaseUtil.get_column_value(
					"'" + object.getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode") + "'",
					"gift_code", "giftvoucher", "balance");
			balance_gv = new BigDecimal(balnace_giftvocher);

			String balnace_giftvocher1 = MoneyDatabaseUtil.get_column_value(
					"'" + object.getJSONArray("giftVouchers").getJSONObject(1).getString("giftVoucherCode") + "'",
					"gift_code", "giftvoucher", "balance");
			balance_gv1 = new BigDecimal(balnace_giftvocher1);

			String balnace_storeCredits = MoneyDatabaseUtil.get_column_value(
					"'" + object.getJSONArray("storeCredits").getJSONObject(0).getString("storeCreditCode") + "'",
					"store_code", "storecredit", "balance");
			balance_sc = new BigDecimal(balnace_storeCredits);
		}

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);

		JSONObject responseJson_Createmoney_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		JSONObject responseJson_Createmoney = responseJson_Createmoney_result.getJSONObject("result");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("transactionId"),
				"transactionId is not same");
		Assert.assertNotNull(responseJson_Createmoney.getInt("transactionId"), "transactionId is  null");
		Assert.assertEquals(responseJson_Createmoney.getString("transactionStatus"), "SUCCESS",
				"status is not correct");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("orderId"),
				"orderId is not correct");
		Assert.assertEquals(object.getString("comment"), responseJson_Createmoney.getString("comment"),
				"orderId is not correct");
		Assert.assertEquals(object.getInt("storeId"), responseJson_Createmoney.getInt("storeId"),
				"storeId is not correct");
		Assert.assertEquals(object.getString("customerEmail"), responseJson_Createmoney.getString("customerEmail"),
				"customerEmail is not correct");
		Assert.assertEquals(object.getInt("customerId"), responseJson_Createmoney.getInt("customerId"),
				"customerId is not correct");
		Assert.assertEquals(JsonUtil.compare_json_arrays(object.getJSONArray("giftVouchers"),
				responseJson_Createmoney.getJSONArray("giftVouchers")), true, "giftVouchers is not same");
		Assert.assertEquals(JsonUtil.compare_json_arrays(object.getJSONArray("storeCredits"),
				responseJson_Createmoney.getJSONArray("storeCredits")), true, "storeCredits is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("franchiseCredits"), "[]",
				"franchiseCredits is not correct");
		Assert.assertEquals(responseJson_Createmoney_result.getInt("status"), 200, "status is not correct");
		if (database_enabled) {
			String balnace_giftvocher1 = MoneyDatabaseUtil.get_column_value("'" + responseJson_Createmoney
					.getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode") + "'", "gift_code",
					"giftvoucher", "balance");
			BigDecimal balance_gv1_after_response = new BigDecimal(balnace_giftvocher1);
			Assert.assertEquals(balance_gv1_after_response.add(new BigDecimal(5)), balance_gv,
					" GV is not updated in database table");

			String balnace_giftvocher = MoneyDatabaseUtil.get_column_value("'" + responseJson_Createmoney
					.getJSONArray("giftVouchers").getJSONObject(1).getString("giftVoucherCode") + "'", "gift_code",
					"giftvoucher", "balance");
			BigDecimal balance_gv1_after_response1 = new BigDecimal(balnace_giftvocher);
			Assert.assertEquals(balance_gv1_after_response1, balance_gv1.add(new BigDecimal(7)),
					" GV is not updated in database table");

			String balnace_storeCredits = MoneyDatabaseUtil.get_column_value("'" + responseJson_Createmoney
					.getJSONArray("storeCredits").getJSONObject(0).getString("storeCreditCode") + "'", "store_code",
					"storecredit", "balance");
			BigDecimal balance_sc_after_response = new BigDecimal(balnace_storeCredits);
			Assert.assertEquals(balance_sc_after_response.add(new BigDecimal(3)), balance_sc,
					" SC is not updated in database table");

			log.info("-----checking transactionID created in storecredit_history table-----");
			String get_all_details_storecredit_history = MoneyDatabaseUtil.get_all_details(
					responseJson_Createmoney.getString("transactionId"), "order_increment_id", "storecredit_history");
			String[] sc = get_all_details_storecredit_history.split(",");
			log.info("sc histore " + get_all_details_storecredit_history);
			Assert.assertNotEquals(get_all_details_storecredit_history, "",
					"TransactionID is not created in database table");
			Assert.assertNotEquals(get_all_details_storecredit_history, null,
					"TransactionID is not created in database table");
			MoneyDatabaseUtil.db_validation_SC(balnace_storeCredits, responseJson_Createmoney, sc, 0, "");

			log.info("-----checking transactionID created in giftvoucher_history table-----");
			String get_all_details_giftvoucher_history = MoneyDatabaseUtil.get_all_details(
					responseJson_Createmoney.getString("transactionId"), "order_increment_id", "giftvoucher_history");
			Assert.assertNotEquals(get_all_details_giftvoucher_history, "",
					"TransactionID is not created in database table");
			Assert.assertNotEquals(get_all_details_giftvoucher_history, null,
					"TransactionID is not created in database table");
			String[] gv = get_all_details_giftvoucher_history.split("\r\n");
			String[] gv1 = gv[0].split(",");
			String[] gv2 = gv[1].split(",");

			log.info("gv history " + get_all_details_giftvoucher_history);
			MoneyDatabaseUtil.db_validation_GV(responseJson_Createmoney, gv1, 0, "");
			MoneyDatabaseUtil.db_validation_GV(responseJson_Createmoney, gv2, 1, "");

		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_only_gv_successcase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"---------------------------------Create money transaction for Only GV---------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "GV AND SC");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-9948-IMOTZ-NKHY");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);
		object.put("giftVouchers", giftVouchers);
		if (database_enabled) {

			String balnace_giftvocher = MoneyDatabaseUtil.get_column_value(
					"'" + object.getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode") + "'",
					"gift_code", "giftvoucher", "balance");
			log.info("dvsdf" + balnace_giftvocher);
			balance_gv = new BigDecimal(balnace_giftvocher);
		}

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);

		JSONObject responseJson_Createmoney_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		JSONObject responseJson_Createmoney = responseJson_Createmoney_result.getJSONObject("result");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("transactionId"),
				"transactionId is not same");
		Assert.assertNotNull(responseJson_Createmoney.getInt("transactionId"), "transactionId is  null");
		Assert.assertEquals(responseJson_Createmoney.getString("transactionStatus"), "SUCCESS",
				"status is not correct");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("orderId"),
				"orderId is not correct");
		Assert.assertEquals(object.getString("comment"), responseJson_Createmoney.getString("comment"),
				"orderId is not correct");
		Assert.assertEquals(object.getInt("storeId"), responseJson_Createmoney.getInt("storeId"),
				"storeId is not correct");
		Assert.assertEquals(object.getString("customerEmail"), responseJson_Createmoney.getString("customerEmail"),
				"customerEmail is not correct");
		Assert.assertEquals(object.getInt("customerId"), responseJson_Createmoney.getInt("customerId"),
				"customerId is not correct");
		Assert.assertEquals(JsonUtil.compare_json_arrays(object.getJSONArray("giftVouchers"),
				responseJson_Createmoney.getJSONArray("giftVouchers")), true, "giftVouchers is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("franchiseCredits"), "[]",
				"franchiseCredits is not correct");
		Assert.assertEquals(responseJson_Createmoney_result.getInt("status"), 200, "status is not correct");
		if (database_enabled) {
			String balnace_giftvocher1 = MoneyDatabaseUtil.get_column_value("'" + responseJson_Createmoney
					.getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode") + "'", "gift_code",
					"giftvoucher", "balance");
			BigDecimal balance_gv1_after_response = new BigDecimal(balnace_giftvocher1);
			Assert.assertEquals(balance_gv1_after_response.add(new BigDecimal(5)), balance_gv,
					" GV is not updated in database table");

			log.info("-----checking transactionID created in giftvoucher_history table-----");
			String get_all_details_giftvoucher_history = MoneyDatabaseUtil.get_all_details(
					responseJson_Createmoney.getString("transactionId"), "order_increment_id", "giftvoucher_history");
			Assert.assertNotEquals(get_all_details_giftvoucher_history, "",
					"TransactionID is not created in database table");
			Assert.assertNotEquals(get_all_details_giftvoucher_history, null,
					"TransactionID is not created in database table");
			String[] gv = get_all_details_giftvoucher_history.split("\r\n");
			String[] gv1 = gv[0].split(",");
			log.info("gv history " + get_all_details_giftvoucher_history);
			MoneyDatabaseUtil.db_validation_GV(responseJson_Createmoney, gv1, 0, "");

		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_same_two_sc_failurecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"-------------------------------Create money transaction with 2 same sc------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "GV AND SC");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-8604-8OVV2-HTTE");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", "0837-GI6JJ-LGCS");
		storeCredits1.put("storeCreditValue", 3);
		storeCredits1.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits1);
		JSONObject storeCredits2 = new JSONObject();
		storeCredits2.put("storeCreditCode", "0837-GI6JJ-LGCS");
		storeCredits2.put("storeCreditValue", 3);
		storeCredits2.put("transactionType", "CREDIT");
		storeCredits.put(storeCredits2);
		object.put("storeCredits", storeCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"), "Duplicate store credit code found.",
				"status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"), "[\"Duplicate store credit code found.\"]",
				"status is not same");
		if (database_enabled) {
			log.info("-----checking transactionID created in storecredit_history table-----");
			String transaction = MoneyDatabaseUtil.get_column_value(object.getString("orderId"), "order_increment_id",
					"storecredit_history", "order_increment_id");
			Assert.assertEquals(transaction, "", "TransactionID is not created in database table");

			log.info("-----checking transactionID created in giftvoucher_history table-----");
			String transactionID = MoneyDatabaseUtil.get_column_value(object.getString("orderId"), "order_increment_id",
					"giftvoucher_history", "order_increment_id");
			Assert.assertEquals(transactionID, "", "TransactionID is not created in database table");
		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_different_sc_Successfulusecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"-------------------------------Create money transaction both credit and debit for different SC ------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "GV AND SC");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-9948-IMOTZ-NKHY");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", "0837-GI6JJ-LGCS");
		storeCredits1.put("storeCreditValue", 3);
		storeCredits1.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits1);
		JSONObject storeCredits2 = new JSONObject();
		storeCredits2.put("storeCreditCode", "0157-5SU53-QCDI");
		storeCredits2.put("storeCreditValue", 3);
		storeCredits2.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits2);
		object.put("storeCredits", storeCredits);
		if (database_enabled) {

			String balnace_giftvocher = MoneyDatabaseUtil.get_column_value(
					"'" + object.getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode") + "'",
					"gift_code", "giftvoucher", "balance");
			log.info("sadsd" + balnace_giftvocher);
			balance_gv = new BigDecimal(balnace_giftvocher);

			String balnace_storeCredits = MoneyDatabaseUtil.get_column_value(
					"'" + object.getJSONArray("storeCredits").getJSONObject(0).getString("storeCreditCode") + "'",
					"store_code", "storecredit", "balance");
			log.info("sadsd" + balnace_storeCredits);
			balance_sc = new BigDecimal(balnace_storeCredits);

			String balnace_giftvocher1 = MoneyDatabaseUtil.get_column_value(
					"'" + object.getJSONArray("storeCredits").getJSONObject(1).getString("storeCreditCode") + "'",
					"store_code", "storecredit", "balance");
			log.info("sadsd" + balnace_giftvocher1);
			balance_gv1 = new BigDecimal(balnace_giftvocher1);
		}
		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		JSONObject responseJson_Createmoney = responseJson_Createmoney_result.getJSONObject("result");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("transactionId"),
				"transactionId is not same");
		Assert.assertNotNull(responseJson_Createmoney.getInt("transactionId"), "transactionId is  null");
		Assert.assertEquals(responseJson_Createmoney.getString("transactionStatus"), "SUCCESS",
				"status is not correct");
		Assert.assertEquals(object.getInt("orderId"), responseJson_Createmoney.getInt("orderId"),
				"orderId is not correct");
		Assert.assertEquals(object.getString("comment"), responseJson_Createmoney.getString("comment"),
				"orderId is not correct");
		Assert.assertEquals(object.getInt("storeId"), responseJson_Createmoney.getInt("storeId"),
				"storeId is not correct");
		Assert.assertEquals(object.getString("customerEmail"), responseJson_Createmoney.getString("customerEmail"),
				"customerEmail is not correct");
		Assert.assertEquals(object.getInt("customerId"), responseJson_Createmoney.getInt("customerId"),
				"customerId is not correct");
		Assert.assertEquals(JsonUtil.compare_json_arrays(object.getJSONArray("giftVouchers"),
				responseJson_Createmoney.getJSONArray("giftVouchers")), true, "giftVouchers is not same");
		Assert.assertEquals(JsonUtil.compare_json_arrays(object.getJSONArray("storeCredits"),
				responseJson_Createmoney.getJSONArray("storeCredits")), true, "storeCredits is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("franchiseCredits"), "[]",
				"franchiseCredits is not correct");
		Assert.assertEquals(responseJson_Createmoney_result.getInt("status"), 200, "status is not correct");
		if (database_enabled) {
			String balnace_giftvocher1 = MoneyDatabaseUtil.get_column_value("'" + responseJson_Createmoney
					.getJSONArray("giftVouchers").getJSONObject(0).getString("giftVoucherCode") + "'", "gift_code",
					"giftvoucher", "balance");
			BigDecimal balance_gv1_after_response = new BigDecimal(balnace_giftvocher1);
			log.info("sadsd" + balnace_giftvocher1);
			Assert.assertEquals(balance_gv1_after_response.add(new BigDecimal(5)), balance_gv,
					" GV is not updated in database table");

			String balnace_storeCredits = MoneyDatabaseUtil.get_column_value("'" + responseJson_Createmoney
					.getJSONArray("storeCredits").getJSONObject(0).getString("storeCreditCode") + "'", "store_code",
					"storecredit", "balance");
			BigDecimal balance_sc_after_response = new BigDecimal(balnace_storeCredits);
			log.info("sadsd" + balnace_storeCredits);
			Assert.assertEquals(balance_sc_after_response.add(new BigDecimal(3)), balance_sc,
					" SC is not updated in database table");

			String balnace_giftvocher = MoneyDatabaseUtil.get_column_value("'" + responseJson_Createmoney
					.getJSONArray("storeCredits").getJSONObject(1).getString("storeCreditCode") + "'", "store_code",
					"storecredit", "balance");
			BigDecimal balance_gv1_after_response1 = new BigDecimal(balnace_giftvocher);
			log.info("sadsd" + balnace_giftvocher);
			Assert.assertEquals(balance_gv1_after_response1.add(new BigDecimal(3)), balance_gv1,
					" SC is not updated in database table");
			log.info("-----checking transactionID created in storecredit_history table-----");
			String get_all_details_storecredit_history = MoneyDatabaseUtil.get_all_details(
					responseJson_Createmoney.getString("transactionId"), "order_increment_id", "storecredit_history");
			String[] sc = get_all_details_storecredit_history.split("\r\n");
			String[] sc1 = sc[0].split(",");
			String[] sc2 = sc[1].split(",");
			log.info("sc histore " + get_all_details_storecredit_history);
			Assert.assertNotEquals(get_all_details_storecredit_history, "",
					"TransactionID is not created in database table");
			Assert.assertNotEquals(get_all_details_storecredit_history, null,
					"TransactionID is not created in database table");
			MoneyDatabaseUtil.db_validation_SC(balnace_storeCredits, responseJson_Createmoney, sc1, 0, "");
			MoneyDatabaseUtil.db_validation_SC(balnace_giftvocher, responseJson_Createmoney, sc2, 1, "");

			log.info("-----checking transactionID created in giftvoucher_history table-----");
			String get_all_details_giftvoucher_history = MoneyDatabaseUtil.get_all_details(
					responseJson_Createmoney.getString("transactionId"), "order_increment_id", "giftvoucher_history");
			Assert.assertNotEquals(get_all_details_giftvoucher_history, "",
					"TransactionID is not created in database table");
			Assert.assertNotEquals(get_all_details_giftvoucher_history, null,
					"TransactionID is not created in database table");
			String[] gv = get_all_details_giftvoucher_history.split("\r\n");
			String[] gv1 = gv[0].split(",");

			log.info("gv history " + get_all_details_giftvoucher_history);
			MoneyDatabaseUtil.db_validation_GV(responseJson_Createmoney, gv1, 0, "");

		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_same_two_gv_sc_failurecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info("---------------------------Create money transaction 2 same gv and sc------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "GV AND SC");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-8604-8OVV2-HTTE");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);
		JSONObject giftVouchers2 = new JSONObject();
		giftVouchers2.put("giftVoucherCode", "GIFT-8604-8OVV2-HTTE");
		giftVouchers2.put("giftVoucherValue", 7);
		giftVouchers2.put("transactionType", "CREDIT");
		giftVouchers.put(giftVouchers2);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", "0837-GI6JJ-LGCS");
		storeCredits1.put("storeCreditValue", 3);
		storeCredits1.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits1);
		object.put("storeCredits", storeCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"), "Duplicate gift voucher found.",
				"status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"), "[\"Duplicate gift voucher found.\"]",
				"status is not same");
		if (database_enabled) {
			log.info("-----checking transactionID created in storecredit_history table-----");
			String transaction = MoneyDatabaseUtil.get_column_value(object.getString("orderId"), "order_increment_id",
					"storecredit_history", "order_increment_id");
			Assert.assertEquals(transaction, "", "TransactionID is not created in database table");

			log.info("-----checking transactionID created in giftvoucher_history table-----");
			String transactionID = MoneyDatabaseUtil.get_column_value(object.getString("orderId"), "order_increment_id",
					"giftvoucher_history", "order_increment_id");
			Assert.assertEquals(transactionID, "", "TransactionID is not created in database table");
		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_Blank_fc_jsonarray_failurecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"------------------------------------Create money transaction with Blank FC------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "");
		JSONArray franchiseCredits = new JSONArray();
		JSONObject franchiseCredits1 = new JSONObject();
		franchiseCredits.put(franchiseCredits1);
		object.put("franchiseCredits", franchiseCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);

		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"), "Invalid franchise credit value.",
				"message is not correct");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"), "[\"Invalid franchise credit value.\"]",
				"errors is not correct");

		if (database_enabled) {
			log.info("-----checking transactionID created in transactionfranchisecredit table-----");
			String get_all_details_transactionfranchisecredit = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "franchisecredit_history");
			Assert.assertEquals(get_all_details_transactionfranchisecredit, "",
					"TransactionID is not created in database table");
		}

	}

	@Test(enabled = true)
	public void create_money_transaction_with_multiple_FC_failurecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"------------------------------------Create money transaction with multiple FC-------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "refund");
		JSONArray franchiseCredits = new JSONArray();
		JSONObject franchiseCredits1 = new JSONObject();
		franchiseCredits1.put("franchiseCreditCode", "6181-9FR92-HAMC");
		franchiseCredits1.put("franchiseCreditValue", 5);
		franchiseCredits1.put("transactionType", "DEBIT");
		franchiseCredits.put(franchiseCredits1);
		JSONObject franchiseCredits2 = new JSONObject();
		franchiseCredits2.put("franchiseCreditCode", "0123-sddsf-wee");
		franchiseCredits2.put("franchiseCreditValue", 5);
		franchiseCredits2.put("transactionType", "DEBIT");
		franchiseCredits.put(franchiseCredits2);
		object.put("franchiseCredits", franchiseCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", "android"));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);

		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"),
				"Multiple franchise credits are not allowed.", "message is not correct");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"),
				"[\"Multiple franchise credits are not allowed.\"]", "errors is not correct");

		if (database_enabled) {
			log.info("-----checking transactionID created in transactionfranchisecredit table-----");
			String get_all_details_transactionfranchisecredit = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "franchisecredit_history");
			Assert.assertEquals(get_all_details_transactionfranchisecredit, "",
					"TransactionID is not created in database table");
		}

	}

	@Test(enabled = true)
	public void create_money_transaction_with_gv_sc_fc_failurecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"------------------------------------Create money transaction with FC,SC,GV-----------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-8604-8OVV2-HTTE");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);
		JSONObject giftVouchers2 = new JSONObject();
		giftVouchers2.put("giftVoucherCode", "LENS-JIT-ZMFY0-WTGR");
		giftVouchers2.put("giftVoucherValue", 7);
		giftVouchers2.put("transactionType", "CREDIT");
		giftVouchers.put(giftVouchers2);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", "0837-GI6JJ-LGCS");
		storeCredits1.put("storeCreditValue", 3);
		storeCredits1.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits1);
		object.put("storeCredits", storeCredits);

		JSONArray franchiseCredits = new JSONArray();
		JSONObject franchiseCredits1 = new JSONObject();
		franchiseCredits1.put("franchiseCreditCode", "6181-9FR92-HAMC");
		franchiseCredits1.put("franchiseCreditValue", 5);
		franchiseCredits1.put("transactionType", "DEBIT");
		franchiseCredits.put(franchiseCredits1);
		object.put("franchiseCredits", franchiseCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);

		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"),
				"Franchise credit should not be clubbed with Gift Voucher or Store Credit", "message is not correct");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"),
				"[\"Franchise credit should not be clubbed with Gift Voucher or Store Credit\"]",
				"errors is not correct");
		if (database_enabled) {
			log.info("-----checking transactionID created in transactionfranchisecredit table-----");
			String get_all_details_transactionfranchisecredit = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "franchisecredit_history");
			Assert.assertEquals(get_all_details_transactionfranchisecredit, "",
					"TransactionID is not created in database table");

			log.info("-----checking transactionID created in transactionstorecredit table-----");
			String get_all_details_transactionstorecredit = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "storecredit_history");
			Assert.assertEquals(get_all_details_transactionstorecredit, "",
					"TransactionID is not created in database table");

			log.info("-----checking transactionID created in transactiongiftvoucher table-----");
			String get_all_details_transactiongiftvoucher = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "giftvoucher_history");
			Assert.assertEquals(get_all_details_transactiongiftvoucher, "",
					"TransactionID is not created in database table");
		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_blank_sc_failurecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"-----------------------------------Create money transaction with blank sc-----------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "");
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", "GIFT-8604-8OVV2-HTTE");
		giftVouchers1.put("giftVoucherValue", 5);
		giftVouchers1.put("transactionType", "DEBIT");
		giftVouchers.put(giftVouchers1);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits.put(storeCredits1);
		object.put("storeCredits", storeCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);

		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"), "Invalid store credit value.",
				"message is not correct");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"), "[\"Invalid store credit value.\"]",
				"errors is not correct");
		if (database_enabled) {

			log.info("-----checking transactionID created in transactionstorecredit table-----");
			String get_all_details_transactionstorecredit = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "storecredit_history");
			Assert.assertEquals(get_all_details_transactionstorecredit, "",
					"TransactionID is not created in database table");

			log.info("-----checking transactionID created in transactiongiftvoucher table-----");
			String get_all_details_transactiongiftvoucher = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "storecredit_history");
			Assert.assertEquals(get_all_details_transactiongiftvoucher, "",
					"TransactionID is not created in database table");
		}
	}

	@Test(enabled = true)
	public void create_money_transaction_with_Blank_GV_Failurecase() throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info(
				"------------------------------Create money transaction with blank gv--------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("customerId", customerId);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", "");
		JSONArray giftVouchers = new JSONArray();
		JSONObject gv = new JSONObject();
		giftVouchers.put(gv);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", "0837-GI6JJ-LGCS");
		storeCredits1.put("storeCreditValue", 3);
		storeCredits1.put("transactionType", "DEBIT");
		storeCredits.put(storeCredits1);
		object.put("storeCredits", storeCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);

		Assert.assertEquals(responseJson_Createmoney.getString("status"), "BAD_REQUEST", "status is not same");
		Assert.assertEquals(responseJson_Createmoney.getString("message"), "Invalid gift voucher value.",
				"message is not correct");
		Assert.assertEquals(responseJson_Createmoney.getString("errors"), "[\"Invalid gift voucher value.\"]",
				"errors is not correct");
		if (database_enabled) {
			log.info("-----checking transactionID created in transactionstorecredit table-----");
			String get_all_details_transactionstorecredit = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "storecredit_history");
			Assert.assertEquals(get_all_details_transactionstorecredit, "",
					"TransactionID is not created in database table");

			log.info("-----checking transactionID created in transactiongiftvoucher table-----");
			String get_all_details_transactiongiftvoucher = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "storecredit_history");
			Assert.assertEquals(get_all_details_transactiongiftvoucher, "",
					"TransactionID is not created in database table");
		}
	}

	@Test(enabled = true, dataProvider = "create_money_transation")
	public void create_money_transaction_with_all_failurecases(String UsecaseName, String customerID,
			String customerEmail, String OrderID, String storeId, String comment, String gv_code, String gv_credit,
			String gv_transactionType, String sc_code, String sc_credit, String sc_tranasactionType, String Status_code,
			String Error_status, String Error_message, String Errors) throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info("-------------------------------------" + UsecaseName + "---------------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		if (OrderID != null && OrderID.equals("ORDERID")) {
			OrderID = GenericUtil.createRandomNumber(7);
		}
		object = new JSONObject();
		object.put("customerId", customerID);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", comment);
		JSONArray giftVouchers = new JSONArray();
		JSONObject giftVouchers1 = new JSONObject();
		giftVouchers1.put("giftVoucherCode", gv_code);
		giftVouchers1.put("giftVoucherValue", gv_credit);
		giftVouchers1.put("transactionType", gv_transactionType);
		giftVouchers.put(giftVouchers1);
		object.put("giftVouchers", giftVouchers);

		JSONArray storeCredits = new JSONArray();
		JSONObject storeCredits1 = new JSONObject();
		storeCredits1.put("storeCreditCode", sc_code);
		storeCredits1.put("storeCreditValue", sc_credit);
		storeCredits1.put("transactionType", sc_tranasactionType);
		storeCredits.put(storeCredits1);
		object.put("storeCredits", storeCredits);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		JSONObject responseJson_Createmoney = RequestUtil.convertHttpResponseToJsonObject(httpResponsecreatemoney);
		log.info("responseJson_Createmoney:****" + responseJson_Createmoney);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), Integer.parseInt(Status_code),
				"Response Status is not proper");
		if (httpResponsecreatemoney.getStatusLine().getStatusCode() == 400
				|| httpResponsecreatemoney.getStatusLine().getStatusCode() == 403
				|| httpResponsecreatemoney.getStatusLine().getStatusCode() == 428) {
			Assert.assertEquals(responseJson_Createmoney.getString("status"), Error_status, "status is not same");
			Assert.assertEquals(responseJson_Createmoney.getString("message"), Error_message, "message is not correct");
			Assert.assertEquals(responseJson_Createmoney.getString("errors"), Errors, "errors is not correct");
		}

		if (OrderID != null && database_enabled && !OrderID.equals("")) {
			log.info("-----checking transactionID created in transactionstorecredit table-----");
			String get_all_details_transactionstorecredit = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "storecredit_history");
			Assert.assertEquals(get_all_details_transactionstorecredit, "",
					"TransactionID is not created in database table");

			log.info("-----checking transactionID created in transactiongiftvoucher table-----");
			String get_all_details_transactiongiftvoucher = MoneyDatabaseUtil
					.get_all_details(object.getString("orderId"), "order_increment_id", "storecredit_history");
			Assert.assertEquals(get_all_details_transactiongiftvoucher, "",
					"TransactionID is not created in database table");
		}
	}

	@Test(enabled = true, dataProvider = "create_money_transation_for_fc")
	public void create_money_transaction_with_fc_cases(String usecase_name, String customerID, String customerEmail,
			String OrderID, String storeId, String comment, String fc_code, String fc_credit, String fc_transactionType,
			String Status_code, String Error_status, String Error_message, String Errors) throws Exception {
		String RequstUrlCreateMoney = Environments.SERVICES_ENVIRONMENT
				+ MoneyPathConstants.post_create_money_transaction;
		log.info("----------------------------" + usecase_name + "-------------------------------------");

		log.info("Request Header for Post:" + RequstUrlCreateMoney);
		if (OrderID != null && OrderID.equals("ORDERID"))
			OrderID = GenericUtil.createRandomNumber(7);

		object = new JSONObject();
		object.put("customerId", customerID);
		object.put("customerEmail", customerEmail);
		object.put("orderId", OrderID);
		object.put("storeId", storeId);
		object.put("comment", comment);
		JSONArray franchiseCredits = new JSONArray();
		JSONObject franchiseCredits1 = new JSONObject();
		franchiseCredits1.put("franchiseCreditCode", fc_code);
		franchiseCredits1.put("franchiseCreditValue", fc_credit);
		franchiseCredits1.put("transactionType", fc_transactionType);
		franchiseCredits.put(franchiseCredits1);
		object.put("franchiseCredits", franchiseCredits);
		if (fc_code != null && database_enabled && fc_code.equals("6181-9FR92-HAMC")) {
			String balance_franchisecredit = MoneyDatabaseUtil.get_column_value("'"
					+ object.getJSONArray("franchiseCredits").getJSONObject(0).getString("franchiseCreditCode") + "'",
					"franchise_code", "franchisecredit", "balance");
			balance_fc = new BigDecimal(balance_franchisecredit);
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreatemoney = RequestUtil.postRequest(RequstUrlCreateMoney, headers, object);
		Assert.assertEquals(httpResponsecreatemoney.getStatusLine().getStatusCode(), Integer.parseInt(Status_code),
				"Response Status is not proper");
		JSONObject responseJson_Createmoney_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreatemoney);

		if (httpResponsecreatemoney.getStatusLine().getStatusCode() == 200) {
			JSONObject responseJson_Createmoney = responseJson_Createmoney_result.getJSONObject("result");
			Assert.assertEquals(object.getString("orderId"), responseJson_Createmoney.getString("transactionId"),
					"transactionId is not same");
			Assert.assertEquals(responseJson_Createmoney.getString("transactionStatus"), "SUCCESS",
					"status is not correct");
			Assert.assertEquals(object.getString("orderId"), responseJson_Createmoney.getString("orderId"),
					"orderId is not correct");
			Assert.assertEquals(object.getString("storeId"), responseJson_Createmoney.getString("storeId"),
					"storeId is not correct");
			Assert.assertEquals(object.getString("customerEmail"), responseJson_Createmoney.getString("customerEmail"),
					"customerEmail is not correct");
			Assert.assertEquals(object.getString("customerId"), responseJson_Createmoney.getString("customerId"),
					"customerId is not correct");
			Assert.assertEquals(responseJson_Createmoney.getString("giftVouchers"), "[]",
					"giftVouchers is not correct");
			Assert.assertEquals(responseJson_Createmoney.getString("storeCredits"), "[]",
					"storeCredits is not correct");
			Assert.assertEquals(responseJson_Createmoney_result.getInt("status"), 200, "status is not correct");
			if (database_enabled) {

				log.info("-----checking transactionID created in franchise_history table-----");
				String balance_franchisecredit = MoneyDatabaseUtil.get_column_value(
						"'" + object.getJSONArray("franchiseCredits").getJSONObject(0).getString("franchiseCreditCode")
								+ "'",
						"franchise_code", "franchisecredit", "balance");
				BigDecimal balance_fc_after_response = new BigDecimal(balance_franchisecredit);
				if (object.getJSONArray("franchiseCredits").getJSONObject(0).getString("transactionType")
						.equals("DEBIT"))
					Assert.assertEquals(
							balance_fc_after_response.add(new BigDecimal(object.getJSONArray("franchiseCredits")
									.getJSONObject(0).getString("franchiseCreditValue"))),
							balance_fc, " FC is not updated in database table");
				else
					Assert.assertEquals(balance_fc_after_response, balance_fc.add(new BigDecimal(object
							.getJSONArray("franchiseCredits").getJSONObject(0).getString("franchiseCreditValue"))),
							" FC is not updated in database table");

				String get_all_details_transactionfranchisecredit = MoneyDatabaseUtil.get_all_details(
						responseJson_Createmoney.getString("transactionId"), "order_increment_id",
						"franchisecredit_history");
				Assert.assertNotEquals(get_all_details_transactionfranchisecredit, "",
						"TransactionID is not created in database table");
				Assert.assertNotEquals(get_all_details_transactionfranchisecredit, null,
						"TransactionID is not created in database table");
				String[] fc = get_all_details_transactionfranchisecredit.split(",");
				log.info("fc history " + get_all_details_transactionfranchisecredit);
				MoneyDatabaseUtil.db_validation_FC(balance_franchisecredit, responseJson_Createmoney, fc, 0, "");

			}
		}

		if (httpResponsecreatemoney.getStatusLine().getStatusCode() == 400
				|| httpResponsecreatemoney.getStatusLine().getStatusCode() == 428
				|| httpResponsecreatemoney.getStatusLine().getStatusCode() == 403) {
			Assert.assertEquals(responseJson_Createmoney_result.getString("status"), Error_status,
					"status is not same");
			Assert.assertEquals(responseJson_Createmoney_result.getString("message"), Error_message,
					"message is not correct");
			Assert.assertEquals(responseJson_Createmoney_result.getString("errors"), Errors, "errors is not correct");
			if (!OrderID.equals(null) && !OrderID.equals("") && database_enabled) {
				log.info("-----checking transactionID created in transactionfranchisecredit table-----");
				String get_all_details_transactionfranchisecredit = MoneyDatabaseUtil
						.get_all_details(object.getString("orderId"), "order_increment_id", "franchisecredit_history");
				Assert.assertEquals(get_all_details_transactionfranchisecredit, "",
						"TransactionID is not created in database table");
			}
		}
	}
}
