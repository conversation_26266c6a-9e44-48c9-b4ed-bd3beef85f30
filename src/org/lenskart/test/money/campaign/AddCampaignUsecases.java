package org.lenskart.test.money.campaign;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.Profiles;
import org.lenskart.core.constant.MoneyPathConstants;
import org.lenskart.core.util.MoneyUtil;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RequestUtil;

public class AddCampaignUsecases {
	private static String RequestUrlAddCampaign = Environments.SERVICES_ENVIRONMENT
			+ MoneyPathConstants.ADD_CAMPAIGN_PATH;
//	private static final Logger log = GenericUtil.InitLogger(AddCampaignUsecases.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;
	private MongoConnectionUtility mongoMoneyConnectionObject;
	private static String campName;
	private static final Logger log = Logger.getLogger(AddCampaignUsecases.class);

	@BeforeClass
	public void initDBConnection() throws IOException {
		if (db_Validation) {
			mongoMoneyConnectionObject = MoneyUtil.getMoneyMongoConnectionObject();
			mongoMoneyConnectionObject.initMongoDBConnection();
			MoneyUtil.setCollectionName("campaigns");
		}
	}

	@AfterClass
	public void closeDBConection() throws IOException {
		if (db_Validation) {
			mongoMoneyConnectionObject.closeMongoConnection();
		}
	}

	private List<NameValuePair> getHeader(String sessionToken, String xApiClient, String xApiAuthToken)
			throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", xApiClient));
		headers.add(new BasicNameValuePair("X-Api-Auth-Token", xApiAuthToken));
		headers.add(new BasicNameValuePair("X-Auth-Token", "8e8b0816-4c73-4f08-8f7d-022dcd186a91"));
		return headers;
	}

	private JSONObject requestJsonObject(String creditComments, String creditExpiryDays, String creditWalletType,
			String isActive, String maxAmount, String creditMessage, String alreadyUsedMessage, String pendingMessage,
			String minAmount, String name, String scoreMultiplier, String sendSms, String smsTemplateName)
			throws Exception {
		JSONObject requestObject = new JSONObject();
		requestObject.put("creditComments", creditComments);
		requestObject.put("creditExpiryDays", creditExpiryDays);
		requestObject.put("creditWalletType", creditWalletType);
		requestObject.put("isActive", isActive);
		requestObject.put("maxAmount", maxAmount);
		JSONObject messageTypeWiseMessageMap = new JSONObject();
		messageTypeWiseMessageMap.put("CREDITED", creditMessage);
		messageTypeWiseMessageMap.put("ALREADY_USED", alreadyUsedMessage);
		messageTypeWiseMessageMap.put("PENDING", pendingMessage);
		requestObject.put("messageTypeWiseMessageMap", messageTypeWiseMessageMap);
		requestObject.put("minAmount", minAmount);
		requestObject.put("name", name);
		requestObject.put("scoreMultiplier", scoreMultiplier);
		requestObject.put("sendSms", sendSms);
		requestObject.put("smsTemplateName", smsTemplateName);
		return requestObject;
	}

	@Test(enabled = true, priority = 1)
	public void addCampaignSuccessful() throws Exception {
		JSONObject requestObject = null;
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT, authToken);
		campName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		if (Environments.environment.equals(Profiles.PROD)) {
			 requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "false", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campName, "10", "false", "walletcampaign");
		}else {
			 requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
					"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
					"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
					"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
					"0", campName, "10", "true", "walletcampaign");
		}

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		JSONObject responseJson_AddCampaign = RequestUtil.convertHttpResponseToJsonObject(httpResponseAddCampaign);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_AddCampaignResult = responseJson_AddCampaign.getJSONObject("result");
		if (db_Validation) {
			Map<String, Object> campaignCreate = new HashMap<String, Object>();
			ObjectId db_id = new ObjectId(responseJson_AddCampaignResult.getString("id"));
			campaignCreate.put("_id", db_id);
			List<Document> getCampaignDetails = MoneyUtil.getMoneyEntry(campaignCreate);
			Assert.assertEquals(getCampaignDetails.size(), 1, "Campaign is not created");
			Document getCampaign = getCampaignDetails.get(0);
			Assert.assertEquals(getCampaign.getString("name"), responseJson_AddCampaignResult.getString("name"),
					"name mismatch");
			Assert.assertEquals(getCampaign.get("isActive"), responseJson_AddCampaignResult.getBoolean("isActive"),
					"isActive mismatch");
			Assert.assertEquals(getCampaign.get("maxAmount"), responseJson_AddCampaignResult.get("maxAmount"),
					"maxAmount mismatch");
			Assert.assertEquals(getCampaign.get("minAmount"), responseJson_AddCampaignResult.get("minAmount"),
					"minAmount mismatch");
			Assert.assertEquals(getCampaign.get("sendSms"), responseJson_AddCampaignResult.getBoolean("sendSms"),
					"sendSms mismatch");
			Assert.assertEquals(getCampaign.get("scoreMultiplier"),
					responseJson_AddCampaignResult.get("scoreMultiplier"), "scoreMultiplier mismatch");
			Assert.assertEquals(getCampaign.get("creditExpiryDays"),
					responseJson_AddCampaignResult.get("creditExpiryDays"), "creditExpiryDays mismatch");
			Assert.assertEquals(getCampaign.getString("creditComments"),
					responseJson_AddCampaignResult.getString("creditComments"), "creditComments mismatch");
			Assert.assertEquals(getCampaign.getString("creditWalletType"),
					responseJson_AddCampaignResult.getString("creditWalletType"), "creditWalletType mismatch");
			Assert.assertEquals(getCampaign.getString("smsTemplateName"),
					responseJson_AddCampaignResult.getString("smsTemplateName"), "smsTemplateName mismatch");
			Document messageTypeWiseMessageMap = (Document) getCampaign.get("messageTypeWiseMessageMap");
			Assert.assertEquals(messageTypeWiseMessageMap.getString("CREDITED"),
					responseJson_AddCampaignResult.getJSONObject("messageTypeWiseMessageMap").getString("CREDITED"),
					"CREDITED mismatch");
			Assert.assertEquals(messageTypeWiseMessageMap.getString("ALREADY_USED"),
					responseJson_AddCampaignResult.getJSONObject("messageTypeWiseMessageMap").getString("ALREADY_USED"),
					"ALREADY_USED mismatch");
			Assert.assertEquals(messageTypeWiseMessageMap.getString("PENDING"),
					responseJson_AddCampaignResult.getJSONObject("messageTypeWiseMessageMap").getString("PENDING"),
					"PENDING mismatch");
			Assert.assertNotNull(getCampaign.get("createdAt"), "createdAt is null");
			Assert.assertNotNull(getCampaign.get("updatedAt"), "updatedAt is null");
		}
	}

	@Test(enabled = true, priority = 3)
	public void addCampaignSameAuthToken() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT, authToken);
		campName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campName, "10", "true", "walletcampaign");

		log.info("requestObject: "+requestObject);
		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		HttpResponse httpResponseAddCampaign1 = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		JSONObject responseJson_AddCampaign1 = RequestUtil.convertHttpResponseToJsonObject(httpResponseAddCampaign1);
		Assert.assertEquals(httpResponseAddCampaign1.getStatusLine().getStatusCode(), 403,
				"Response Status is not proper");
		Assert.assertEquals(responseJson_AddCampaign1.getString("message"), "Invalid API Auth Token",
				"message mismatch");
		Assert.assertEquals(responseJson_AddCampaign1.getString("error"), "Forbidden", "error mismatch");
		Assert.assertEquals(responseJson_AddCampaign1.getString("status"), "403", "status mismatch");
	}

	@Test(enabled = true, priority = 2)
	public void addCampaignSameCampaignName() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT, authToken);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		JSONObject responseJson_AddCampaign = RequestUtil.convertHttpResponseToJsonObject(httpResponseAddCampaign);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 428,
				"Response Status is not proper");
		Assert.assertEquals(responseJson_AddCampaign.getString("message"), campName + " campaign already exists",
				"message mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("error"), "Precondition Required", "error mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("status"), "428", "status mismatch");
	}

	@Test(enabled = true)
	public void addCampaignBlankAuthToken() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT, "");
		String campaignName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campaignName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		JSONObject responseJson_AddCampaign = RequestUtil.convertHttpResponseToJsonObject(httpResponseAddCampaign);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 403,
				"Response Status is not proper");
		Assert.assertEquals(responseJson_AddCampaign.getString("message"), "Invalid API Auth Token",
				"message mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("error"), "Forbidden", "error mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("status"), "403", "status mismatch");
	}

	@Test(enabled = true)
	public void addCampaignInvalidAuthToken() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		List<NameValuePair> headers = getHeader(sessionToken, Environments.X_API_CLIENT, "hjdfjdfhjd");
		String campaignName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campaignName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		JSONObject responseJson_AddCampaign = RequestUtil.convertHttpResponseToJsonObject(httpResponseAddCampaign);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 403,
				"Response Status is not proper");
		Assert.assertEquals(responseJson_AddCampaign.getString("message"), "Invalid API Auth Token",
				"message mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("error"), "Forbidden", "error mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("status"), "403", "status mismatch");
	}

	@Test(enabled = false)//bug
	public void addCampaignBlankXApiCient() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = getHeader(sessionToken, "", authToken);
		String campaignName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campaignName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 401,
				"Response Status is not proper");
	}

	@Test(enabled = false)//bug
	public void addCampaignBlankSessionToken() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = getHeader("", Environments.X_API_CLIENT, authToken);
		String campaignName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campaignName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
	}

	@Test(enabled = false)//Bug
	public void addCampaignInvalidSessionToken() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = getHeader("ejfkhjsdfdkgjkfl", Environments.X_API_CLIENT, authToken);
		String campaignName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campaignName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
	}

	@Test(enabled = true)
	public void addCampaignInvalidApiAuthToken() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", Environments.X_API_CLIENT));
		headers.add(new BasicNameValuePair("X-Api-Auth-Token", authToken));
		headers.add(new BasicNameValuePair("X-Auth-Token", "8e8b0816-4c73-4f08-8f7d-022dcd"));
		String campaignName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campaignName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		JSONObject responseJson_AddCampaign = RequestUtil.convertHttpResponseToJsonObject(httpResponseAddCampaign);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 400,
				"Response Status is not proper");
		Assert.assertEquals(responseJson_AddCampaign.getString("message"), "Wrong Header: Auth Token",
				"message mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("error"), "Bad Request", "error mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("status"), "400", "status mismatch");
	}

	@Test(enabled = true)
	public void addCampaignBlankApiAuthToken() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String authToken = MoneyUtil.generateAuthToken(sessionToken, Environments.X_API_CLIENT);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessionToken));
		headers.add(new BasicNameValuePair("X-Api-Client", Environments.X_API_CLIENT));
		headers.add(new BasicNameValuePair("X-Api-Auth-Token", authToken));
		headers.add(new BasicNameValuePair("X-Auth-Token", ""));
		String campaignName = "iplLkcash" + GenericUtil.createRandomNumber(3);
		JSONObject requestObject = requestJsonObject("Play & Earn Bonus", "2", "lenskart", "true", "5000",
				"Rs %amount% %wallettype% has been credited to Lenskart wallet for %mobilenumber% Valid for 2 days only. Shop Now",
				"Rs %amount% %wallettype% already credited to %mobilenumber% Lenskart wallet on %createdat%. Shop Now",
				"There is no Lenskart wallet for this number %mobilenumber%, please verify this number in app with OTP & Rs %amount% %wallettype% will be automatically credited",
				"0", campaignName, "10", "true", "walletcampaign");

		HttpResponse httpResponseAddCampaign = RequestUtil.postRequest(RequestUrlAddCampaign, headers, null,
				requestObject);
		JSONObject responseJson_AddCampaign = RequestUtil.convertHttpResponseToJsonObject(httpResponseAddCampaign);
		Assert.assertEquals(httpResponseAddCampaign.getStatusLine().getStatusCode(), 401,
				"Response Status is not proper");
		Assert.assertEquals(responseJson_AddCampaign.getString("message"), "Missing Header: Auth Token",
				"message mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("error"), "Unauthorized", "error mismatch");
		Assert.assertEquals(responseJson_AddCampaign.getString("status"), "401", "status mismatch");
	}
}
