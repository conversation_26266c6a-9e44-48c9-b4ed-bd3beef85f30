package org.lenskart.test.sanity;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.Profiles;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.ProductCategories;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.CartPathConstants;
import org.lenskart.core.constant.CatalogServicePathConstants;
import org.lenskart.core.constant.InventoryPathConstant;
import org.lenskart.core.constant.JunoV1PathConstants;
import org.lenskart.core.constant.JunoV2ProductPathConstants;
import org.lenskart.core.constant.MoneyPathConstants;
import org.lenskart.core.constant.OrderPaymentConstants;
import org.lenskart.core.constant.UtilityPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CartUtil;
import org.lenskart.core.util.CatalogUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.HeadersUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.JunoV1_1Util;
import org.lenskart.core.util.MoneyUtil;
import org.lenskart.core.util.OrderUtil;
import org.lenskart.core.util.PaymentUtil;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.lenskart.test.hto.FetchAllZonesAPI;
import org.lenskart.test.miscellaneous.OrderCancellation;
import org.lenskart.test.utility.FetchLKCountriesUseCases;
import org.testng.Assert;
import org.testng.ITestResult;
import org.testng.annotations.AfterClass;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.juno.schema.v2.cart.CartItem;
import com.lenskart.juno.schema.v2.common.Address;
import com.lenskart.juno.schema.v2.common.GiftMessage;
import com.lenskart.juno.schema.v2.common.Power;
import com.lenskart.juno.schema.v2.common.PowerType;
import com.lenskart.juno.schema.v2.common.Prescription;
import com.lenskart.juno.schema.v2.common.PrescriptionType;
import com.utilities.GenericUtil;
import com.utilities.RequestUtil;
import com.utilities.RestAssuredUtils;

import io.restassured.response.Response;

public class JunoProdSanity {
	private String loggedInSessiontoken;
	private String guestSessiontoken;
	private String sessiontoken;
	private static ObjectMapper objectMapper;
	private final String x_api_client = ApplicationConstants.XApiClient.DESKTOP;
	private final String category_eyeglasses_FFF = ApplicationConstants.ProductCategories.EYEGLASSES_FFF;
	private final String category_eyeglasses = ApplicationConstants.ProductCategories.EYEGLASSES;
	private final String category_sunglasses = ApplicationConstants.ProductCategories.SUNGLASSES;
	private final String category_accessories = ApplicationConstants.ProductCategories.ACCESSORIES;
	private final String category_reading_eyeglass = ApplicationConstants.ProductCategories.READING_EYEGLASSES;
	private final String category_contact_lens = ApplicationConstants.ProductCategories.CONTACT_LENS;
	private final String category_aqualens = ApplicationConstants.ProductCategories.AQUALENS;
	private static String category_eyeglasses_VC = "4893";
	private static String category_eyeglasses_JJ = "3329";
	private static String category_contactlens_CL = "7244"; // "2415";
	private static HashMap<String, String> product = new HashMap<>();
	private static String goldPid = "128269";
	private static String goldPid1 = "148393";
	private final String userEmail = "<EMAIL>";
	private final String password = "LENSKART@123";
	private static String xCatalogServiceId = "junocatalog";
	private static String xCatalogServicekey = "cWne1BWNW6";

	private static String[] goldPids = { "148393", "128269" };

	private final String userPhone = "1262183385";
	private static String page_size = "50";
	private static String categoryId;
	String guestSession;

	private final String gvCode = "LENS-GAGAN";
	private final String storeCreditCode = "0977-MYBZB-DVFCF";

	Map<String, String> orderIds = new HashMap<String, String>();

	private static final Logger log = GenericUtil.InitLogger(JunoProdSanity.class);

	private Address shippingAddress() {
		Address address = new Address();
		address.setFirstName("Test");
		address.setLastName("Test");
		address.setAddressline1("Test order");
		address.setCity("Test order");
		address.setGender("Female");
		address.setEmail(userEmail);
		address.setPhone("1262183385");
		address.setState("test");
		if (Environments.country.name().equals("AE")) {
			address.setCountry("AE");
			address.setPostcode("000000");
		} else {
			address.setCountry("IN");
			address.setPostcode("110001");
		}

		return address;
	}

	private String printSessionTokenOnConsole(String sessionToken) {
		return "\nSessionToken: " + sessionToken;
	}

	private String getFrameTypeFromProductDetails(JSONObject product_response) throws JSONException {
		String frame_type = null;
		JSONArray specifications = product_response.getJSONObject("result").getJSONArray("specifications");
		for (int i = 0; i < specifications.length(); i++) {
			if (specifications.getJSONObject(i).getString("name").equals("technical")) {
				JSONArray items = specifications.getJSONObject(i).getJSONArray("items");
				for (int j = 0; j < items.length(); j++) {
					if (items.getJSONObject(j).getString("name").equals("Frame Type")) {
						frame_type = items.getJSONObject(j).getString("value").toLowerCase().trim().replace(" ", "_");
					}
				}
			}
		}
		Assert.assertNotNull(frame_type, "frame_type is not present for this product_id");
		return frame_type;
	}

	private HashMap<String, String> getProductDetails(JSONObject jsonResponse_category, String power_type,
			Boolean additionalOption) throws URISyntaxException, Exception {
		HashMap<String, String> productDetails = new HashMap<>();
		JSONArray product_list = jsonResponse_category.getJSONObject("result").getJSONArray("product_list");
		for (int i = product_list.length() - 3; i >= 0; i--) {
			if (power_type.equals("single_vision") || power_type.equals("bifocal") || power_type.equals("zero_power")
					|| power_type.equals("sunglasses")) {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_api_client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				String frame_type = getFrameTypeFromProductDetails(jsonResponse_product);
				if (frame_type != null) {
					JSONObject jsonResponse_Package = JunoV1_1Util.getPackageIdFromBuyOptionApiV1_1(
							productDetails.get("productId"), power_type, frame_type, x_api_client);
					if (!jsonResponse_Package.isNull("result")
							&& !jsonResponse_Package.getJSONObject("result").isNull("packages")) {
						if (jsonResponse_Package.getJSONObject("result").getJSONArray("packages").length() > 0) {
							JSONArray packages = jsonResponse_Package.getJSONObject("result").getJSONArray("packages");
							// for (int j = 0; j < packages.length(); j++) {
							aa: for (int j = packages.length() - 1; j >= 0; j--) {
								productDetails.put("packageId", packages.getJSONObject(j).getString("id"));
								Assert.assertNotNull(productDetails.get("packageId"),
										"package_id is not available for this product");
								if (additionalOption) {
									log.info(!packages.getJSONObject(j).isNull("addons"));
									if (!packages.getJSONObject(j).isNull("addons")) {
										JSONArray addons = packages.getJSONObject(j).getJSONArray("addons");
										log.info(addons.length());
										bb: for (int z = 0; z < addons.length(); z++) {
											if (addons.getJSONObject(z).getString("title").toLowerCase()
													.contains("Scratch Resistant".toLowerCase())
													|| addons.getJSONObject(z).getString("title").toLowerCase()
															.contains("Coating".toLowerCase())) {
												log.info("Coating ID : " + addons.getJSONObject(z).getString("id"));
												productDetails.put("coating_id",
														addons.getJSONObject(z).getString("id"));
												break aa;
											}
										}
										// break;
									}
								} else {
									break;
								}
							}
							break;
						}
					}
				}
//				if (additionalOption) {
//
//				}
// add this code when ever required
			} else if (additionalOption && power_type.equals(ApplicationConstants.PowerTypes.CONTACT_LENS)) {
				productDetails.put("productId", product_list.getJSONObject(product_list.length() - 1).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_api_client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				if (jsonResponse_product.getJSONObject("result").has("subscription")) {
					break;
				}

			} else {
				productDetails.put("productId", product_list.getJSONObject(i).getString("id"));
				JSONObject jsonResponse_product = JunoV1Util.getProductDetails(productDetails.get("productId"),
						x_api_client);
				Assert.assertNotNull(jsonResponse_product, "product API is not working");
				break;
			}
		}
		log.debug("Product Id and Package Id");
		for (String key : productDetails.keySet()) {
			log.debug("Keys: " + key + "  Values:" + productDetails.get(key));
		}
		return productDetails;
	}

	public String returnCategoryId(String categoryType, String type) throws Exception {
		String url = Environments.SERVICES_ENVIRONMENT
				+ String.format("/v2/products/subcategory/gender/women/catalog/%s", categoryType);
		log.info("url: " + url);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", x_api_client));
		HttpResponse httpResponseGetCategoryId = RequestUtil.getRequest(url, headers, null);

		JSONObject getCategoryId = RequestUtil.convertHttpResponseToJsonObject(httpResponseGetCategoryId);
		log.info("getCategoryId:" + getCategoryId);
		Assert.assertEquals(httpResponseGetCategoryId.getStatusLine().getStatusCode(), 200,
				"Status code is not correct");
		JSONObject getCategoryIdResult = getCategoryId.getJSONObject("result");
		JSONArray subcategory = getCategoryIdResult.getJSONArray("subcategory");
		try {
			Assert.assertTrue(subcategory.length() > 0, "subcategory array is empty");
			for (int i = 0; i < subcategory.length(); i++) {
				if (subcategory.getJSONObject(i).getString("name").equalsIgnoreCase(type))
					return subcategory.getJSONObject(i).getString("id");
				JSONArray children = subcategory.getJSONObject(i).getJSONArray("children");
				for (int j = 0; j < children.length(); j++) {
					if (children.getJSONObject(j).getString("name").equalsIgnoreCase(type)) {
						return children.getJSONObject(j).getString("id");
					}
				}
			}
			String categoryId = subcategory.getJSONObject(2).getString("id");
			return categoryId;
		} catch (Exception ex) {
			log.debug("NO CATEGORY ID IS PRESENT IN SUBCATEGORY API");
			return null;
		}

	}

	private void clearCart() throws ParseException, IOException, JSONException, URISyntaxException {
		String clearCart_URL = Environments.SERVICES_ENVIRONMENT + "/v2/carts/items";
		log.debug(" request URL : " + clearCart_URL);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", loggedInSessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", "android")); // header.add(new
		// BasicNameValuePair("X-B3-TraceId", // GenericUtil.getRandomHexTraceId()));

		log.debug("header " + header);
		HttpResponse httpResponse = RequestUtil.deleteRequest(clearCart_URL, header);
		log.debug("httpResponse " + httpResponse);
		JSONObject jsonClearCart = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(jsonClearCart);
		boolean clearCartStatus = (httpResponse.getStatusLine().getStatusCode() == 200
				|| httpResponse.getStatusLine().getStatusCode() == 404);
		Assert.assertTrue(clearCartStatus,
				"\nClearCart :"
						+ GenericUtil.printAPICallDetails(loggedInSessiontoken, clearCart_URL, null, jsonClearCart) + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		log.debug(httpResponse.getStatusLine().getStatusCode());
	}

	public void makeCartInactive(String loggedInSessiontoken) throws Exception {
		String clearCartURL = Environments.SERVICES_ENVIRONMENT + "/v2/carts/status";

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", x_api_client));
		headers.add(new BasicNameValuePair("X-Session-Token", loggedInSessiontoken));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("status", "Inactive"));

		HttpResponse httpResponse = RequestUtil.putRequest(clearCartURL, query, headers);
		RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(httpResponse.getStatusLine().getStatusCode());

	}

	public void clearCart(String loggedInSessiontoken) throws Exception {
		String clearCart_URL = Environments.SERVICES_ENVIRONMENT + "/v2/carts/items/";
		log.info(clearCart_URL);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("X-Session-Token", loggedInSessiontoken));
		header.add(new BasicNameValuePair("X-Api-Client", "android"));
		log.info(loggedInSessiontoken);
		log.info("header " + header);

		HttpResponse httpResponse = RequestUtil.deleteRequest(clearCart_URL, header);
		log.info("httpResponse " + httpResponse);
		JSONObject jsonClearCart = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info(httpResponse.getStatusLine().getStatusCode());
		log.info(jsonClearCart);
		boolean clearCartStatus = (httpResponse.getStatusLine().getStatusCode() == 200
				|| httpResponse.getStatusLine().getStatusCode() == 404);
		Assert.assertTrue(clearCartStatus, "\nClearCart :"
				+ GenericUtil.printAPICallDetails(loggedInSessiontoken, clearCart_URL, null, jsonClearCart));
		log.info(httpResponse.getStatusLine().getStatusCode());
		if (httpResponse.getStatusLine().getStatusCode() == 404) {
			CartUtil.getCart(loggedInSessiontoken, x_api_client);
			makeCartInactive(loggedInSessiontoken);
		}
	}

	@BeforeClass
	public void userLogin() throws Exception {
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);

	}

	@BeforeMethod
	public void beforeMethod() throws Exception {
		log.info("-----------BEFORE METHOD---------");
		clearCart(loggedInSessiontoken);
	}

	@AfterMethod
	public void afterMethod(ITestResult result) {
		String executionStatus = null;
		if (result.getStatus() == ITestResult.SUCCESS) {
			executionStatus = "PASS";
		} else if (result.getStatus() == ITestResult.FAILURE) {
			executionStatus = "FAIL";
		} else if (result.getStatus() == ITestResult.SKIP) {
			executionStatus = "SKIP";
		}

		log.info("Test Case: " + result.getName() + ", Status: " + executionStatus);
	}

	@Test(enabled = true, priority = 0)
	public void GoldOnlyOrder() throws Exception {
		log.info("------------------------Gold Only Order-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(goldPid));
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");

		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id in create cart response is null. Session token : "
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct in create cart API response. \nCart response received : "
						+ cart_response + "" + printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassFirstFrameFreeSingleVision() throws Exception {
		log.info(
				"------------------------Eyeglasses FIRST FRAME FREE with Single Vision-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);

		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		// // reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");

		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id in create cart response is null. Session token : "
						+ printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct in create cart API response. \nCart response received : "
//						+ cart_response + "" + printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassFirstFrameFreeBifocal() throws Exception {
		log.info(
				"------------------------Eyeglasses FIRST FRAME FREE with Bifocal-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.BIFOCAL, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		Assert.assertNotNull(product.get("packageId"), product.get("productId") + " does not have package");
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.BIFOCAL);
		// // reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);

		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "dc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);

	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassFirstFrameFreeZeroPower() throws Exception {
		log.info(
				"------------------------Eyeglasses FIRST FRAME FREE with Zero power-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;

			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);

		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);

		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = false)
	public void loggedInUser_eyeglassFirstFrameFreeWithoutPower() throws Exception {
		log.info(
				"------------------------Eyeglasses FIRST FRAME FREE without Power-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		clearCart();
		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = category_eyeglasses_FFF;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		// // reqObj.setStoreInventory(1);
		reqObj.setQuantity(1);
		Thread.sleep(1000);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, "PU:162B", "nb",
				"PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);

	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassSingleVision() throws Exception {
		// clearcart();
		log.info("------------------------Eyeglasses with Single Vision-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "VC");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		// // reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId:" + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = false)
	public void loggedInUser_eyeglassZeroPowerGoldFalse() throws Exception {
		log.info("------------------------LoggedIn User for which isloyal False-------------------------------------");
		String mobileNumber = "3212343209";
		loggedInSessiontoken = SessionUtil.createNewSession();
		JSONObject mobileAuthenticateJson = CustomerUtil.mobileAuthenticateWithoutReferCode(x_api_client,
				loggedInSessiontoken, ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), mobileNumber);
		loggedInSessiontoken = mobileAuthenticateJson.getJSONObject("result").getString("token");
		clearCart(loggedInSessiontoken);
		categoryId = returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		// // reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		if (cart_response.getJSONObject("result").getJSONArray("items").length() > 1) {
			for (int i = 0; i < cart_response.getJSONObject("result").getJSONArray("items").length(); i++) {
				String getProductIdFromItmes = cart_response.getJSONObject("result").getJSONArray("items")
						.getJSONObject(i).getString("productId");
				if (getProductIdFromItmes.equals(ApplicationConstants.goldPid)) {
					Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 2,
							"Number of items added is not correct. \nCart response received : " + cart_response + ""
									+ printSessionTokenOnConsole(loggedInSessiontoken));
				}
			}
		} else {
			Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
					"Number of items added is not correct. \nCart response received : " + cart_response + ""
							+ printSessionTokenOnConsole(loggedInSessiontoken));
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId:" + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, powerType1);
		}
		JSONObject getAllOrder = OrderUtil.getAllOrdersFromAPI(loggedInSessiontoken, x_api_client);
		Assert.assertTrue(getAllOrder.getJSONArray("result").length() > 1, "Get All order api is Failing");
		JSONObject getCustomer = CustomerUtil.getMeCustomerDetails(loggedInSessiontoken, x_api_client);
		Assert.assertFalse(getCustomer.getBoolean("isLoyalty"), "isLoyalty is not false");
		if (Environments.environment.equals(Profiles.PREPROD) || Environments.environment.equals(Profiles.PROD)) {
			OrderUtil.cancelInvoice(loggedInSessiontoken, x_api_client, orderId, "1", "Test");
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassithCoating() throws Exception {
		log.info(
				"------------------------Eyeglasses with Single Vision with Coating-------------------------------------");

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "Full-Rim");

		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.BIFOCAL, true);
		log.info(product);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		Assert.assertNotNull(product.get("coating_id"),
				"Product id: " + product.get("productId") + " does not have coating in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.BIFOCAL);
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setQuantity(Integer.parseInt("1"));
		// reqObj.setStoreInventory(1);
		reqObj.setAddOns(product.get("coating_id"));
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassBifocal() throws Exception {
		// clearCart();
		log.info("------------------------Eyeglasses with Bifocal-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "VC");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.BIFOCAL, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.BIFOCAL);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassZeroPower() throws Exception {
		log.info("------------------------Eyeglasses with Zero power-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "dc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);

	}

	@Test(enabled = true)
	public void loggedInUser_eyeglassWithoutPower() throws Exception {
		log.info("------------------------Eyeglasses without Power-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, "PU:162B", "nb",
				"PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_sunglassesWithPower() throws Exception {
		log.info("------------------------Sunglass with power-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("sunglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_sunglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SUNGLASSES, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SUNGLASSES);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_sunglassesWithoutPower() throws Exception {
		log.info("------------------------Sunglasses without Power-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("sunglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_sunglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		String productId = product.get("productId");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_accessories() throws Exception {
		log.info("------------------------Accessories-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "Accessories_revenue");
		if (categoryId == null) {
			categoryId = category_accessories;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		// reqObj.setStoreInventory(1);

		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct\nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cc", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_readingEyeglasses() throws Exception {
		log.info("------------------------Reading Eyeglasses-------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		categoryId = returnCategoryId("eyeglasses", "Reading");
		if (categoryId == null) {
			categoryId = category_reading_eyeglass;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct\nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_contactLensWithSolutionAndStoreCredit() throws Exception {
		String addStoreCreditURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddStoreCredit).trim();
		log.info("------------------------CONTACT LENS WITH SOLUTION------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		// categoryId = returnCategoryId("aqualens", "CL");
		if (categoryId == null) {
			categoryId = category_contact_lens;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.CONTACT_LENS, false);
		String productId1 = product.get("productId");
		JSONObject jsonResponse_ContactLensSolution = JunoV1Util.getContactLensSolutions(x_api_client);
		String productId2 = jsonResponse_ContactLensSolution.getJSONArray("result").getJSONObject(1).getString("id");
		Assert.assertNotNull(productId2, "product_id of solution not present for this category");
		Prescription presObj = new Prescription();

		presObj.setPrescriptionType(
				PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();
		leftPower.setSph("Call Me/Email Me for Power");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("Call Me/Email Me for Power");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId1));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		// reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		JSONObject cart_response_cl = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------Create Cart response with Contact lens-----------");
		log.debug(cart_response_cl);
		log.debug("------------------------------------------------------------");
		Assert.assertNotNull(cart_response_cl.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));

		JSONObject cart_response_solution = CartUtil.createCart(loggedInSessiontoken, productId2, null, "1", null,
				x_api_client);
		log.debug("-----------Create Cart response with cl solution-----------");
		log.debug(cart_response_solution);
		log.debug("-----------------------------------------------------------");
		Assert.assertNotNull(cart_response_solution.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response_solution.getJSONObject("result").getInt("itemsQty"), 2,
				"Number of items added is not correct.\nCart response received : " + cart_response_solution + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response_solution.getJSONObject("result").getInt("itemsCount"), 5,
				"Number of items added is not correct.\nCart response received : " + cart_response_solution + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONArray items = cart_response_solution.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			if (items.getJSONObject(i).getString("productId").equals(productId1)) {
				Assert.assertEquals(items.getJSONObject(i).getString("powerRequired"), "POWER_SUBMITTED",
						"Power is not submitted");
			}
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		String addStoreCredit_URL = addStoreCreditURL.replace("STORECREDITCODE", storeCreditCode);
		addStoreCredit_URL = addStoreCredit_URL.replace("STORECREDITAMOUNT", Integer.toString(1));
		JSONObject scResponse = CartUtil.addStoreCredit(loggedInSessiontoken,
				ApplicationConstants.ReturnStorecredit.returnStorecredit(), 1);
		Assert.assertTrue(scResponse.has("result"), "API Response: "
				+ GenericUtil.printAPICallDetails(loggedInSessiontoken, addStoreCredit_URL, null, scResponse));
		log.debug("*********"
				+ GenericUtil.printAPICallDetails(loggedInSessiontoken, addStoreCredit_URL, null, scResponse));
		JSONArray discounts = scResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
		for (int i = 0; i < discounts.length(); i++) {
			if (discounts.getJSONObject(i).getString("type").equals("sc")) {
				Assert.assertEquals(discounts.getJSONObject(i).getString("code"),
						ApplicationConstants.ReturnStorecredit.returnStorecredit(), "sc code mismatch");
			}
		}
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cc", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response_solution.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response_solution.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response_solution.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_contactLensSolutionAndGiftVoucher() throws Exception {
		String addGVURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddGV).trim();
		log.info("----------------------CONTACT LENS WITHOUT SOLUTION------------------------------");
		// loggedInSessiontoken =
		// CustomerUtil.get_sessionId_after_user_authentication(userEmail, password,
		// x_api_client);
		loggedInSessiontoken = SessionUtil.createNewSession();
		// categoryId = returnCategoryId("contact_lenses", "CL");
		if (categoryId == null) {
			categoryId = category_contact_lens;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.CONTACT_LENS, false);
		String productId = product.get("productId");
		Prescription presObj = new Prescription();

		presObj.setPrescriptionType(
				PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power

		Power leftPower = new Power();
		leftPower.setSph("Call Me/Email Me for Power");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("Call Me/Email Me for Power");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		// reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);

		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct.\nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsCount"), 4,
//				"Number of items added is not correct.\nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));

		JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			if (items.getJSONObject(i).getString("productId").equals(product.get("productId"))) {
				Assert.assertEquals(items.getJSONObject(i).getString("powerRequired"), "POWER_SUBMITTED",
						"Power is not submitted");
			}
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject gvResponse = CartUtil.addGiftVoucher(loggedInSessiontoken,
				ApplicationConstants.ReturnGiftVoucher.returnGiftVoucher());
		String addGV_url = addGVURL.replace("GV", ApplicationConstants.ReturnGiftVoucher.returnGiftVoucher());
		Assert.assertTrue(gvResponse.has("result"),
				"API Response: " + GenericUtil.printAPICallDetails(loggedInSessiontoken, addGV_url, null, gvResponse));
		log.debug("**********" + GenericUtil.printAPICallDetails(loggedInSessiontoken, null, null, gvResponse));
		JSONArray discounts = gvResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
		for (int i = 0; i < discounts.length(); i++) {
			if (discounts.getJSONObject(i).getString("type").equals("gv")) {
				Assert.assertEquals(discounts.getJSONObject(i).getString("code"),
						ApplicationConstants.ReturnGiftVoucher.returnGiftVoucher(), "GV code mismatch");
			}
		}

		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_HTO() throws Exception {
		log.info("------------------------HTO CASES------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		// clearCart();
		JSONObject cart_response = CartUtil.HTOCart(loggedInSessiontoken, x_api_client);
		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");
		Assert.assertEquals(cart_response.getJSONObject("result").getString("cartType"), "HEC", "Cart is not HEC");
		Address address = shippingAddress();
		GiftMessage giftMsgObj = new GiftMessage();
		giftMsgObj = null;
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, giftMsgObj);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_contactLensBothEyes() throws Exception {
		// public void loggedInUser_contactLensWithSubscriptionBothEyes() throws
		// Exception {
		log.info(
				"------------------------CONTACT LENS WITH SUBSCRIPTION FOR BOTH EYES------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);

		categoryId = returnCategoryId("contact_lenses", "CL");

		if (categoryId == null) {
			categoryId = category_contact_lens;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.CONTACT_LENS, true);
		String productId = product.get("productId");
		// JSONObject subscriptionDetails =
		// JunoV1Util.getSubscriptionDetailsWithProductId(productId, "true");
		// JSONObject result=subscriptionDetails.getJSONObject("result");
		// JSONArray subscriptions=result.getJSONArray("subscriptions");
		// String subscriptionId =
		// subscriptions.getJSONObject(0).getString("subscriptionId");
		// String numberOfBoxes =
		// subscriptions.getJSONObject(0).getString("numberOfBoxes");

		String power = ProductUtil.getPowerForAProduct(productId)
				.getString(ProductUtil.getPowerForAProduct(productId).length() - 1);
		Prescription presObj;
		Power leftPower;
		Power rightPower;
		CartItem reqObj;
		JSONObject cart_response;

		presObj = new Prescription();

		presObj.setPrescriptionType(
				PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		leftPower = new Power();
		leftPower.setSph(power);
		// leftPower.setBoxes(Integer.valueOf(subscriptionId));
		leftPower.setBoxes(1);

		presObj.setLeft(leftPower);
		// Right Power
		rightPower = new Power();
		rightPower.setSph(power);
		rightPower.setBoxes(1);
		// rightPower.setBoxes(Integer.valueOf(subscriptionId));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");
		reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		// reqObj.setSubscriptionId(subscriptionId);
		// reqObj.setStoreInventory(1);
		reqObj.setIsBothEye("Y");
		reqObj.setPrescription(presObj);
		cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");

		try {
			cart_response.getJSONObject("result").getString("cartId");
		} catch (JSONException e) {
			presObj = new Prescription();

			presObj.setPrescriptionType(
					PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj.setDob("12-Aug-92");
			presObj.setGender("Female");
			// Left Power
			leftPower = new Power();
			leftPower.setSph("Call Me/Email Me for Power");
			leftPower.setBoxes(1);

			presObj.setLeft(leftPower);
			// Right Power
			rightPower = new Power();
			rightPower.setSph("Call Me/Email Me for Power");
			rightPower.setBoxes(1);

			presObj.setRight(rightPower);
			presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj.setUserName("user1");
			reqObj = new CartItem();
			reqObj.setProductId(Long.parseLong(productId));
			reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);

			reqObj.setIsBothEye("Y");
			reqObj.setPrescription(presObj);
			cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);

		}

		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct.\nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsCount"), Integer.parseInt("2"),
				"Number of items added is not correct.\nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));

		// Assert.assertNotNull(
		// cart_response.getJSONObject("result").getJSONArray("items").getJSONObject(0)
		// .getJSONObject("subscription"),
		// "Subscription detail is missing.\nCart response received : " + cart_response
		// + ""
		// + printSessionTokenOnConsole(loggedInSessiontoken));
		JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			if (items.getJSONObject(i).getString("productId").equals(productId)) {
				Assert.assertEquals(items.getJSONObject(i).getString("powerRequired"), "POWER_SUBMITTED",
						"Power is not submitted");
				Assert.assertNotNull(items.getJSONObject(i).getJSONObject("prescription").getJSONObject("left"),
						"Left prescription details is missing");
				Assert.assertNotNull(items.getJSONObject(i).getJSONObject("prescription").getJSONObject("right"),
						"Left prescription details is missing");
			}
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "dc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response.\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_contactLensSingleEye() throws Exception {
		// public void loggedInUser_contactLensWithSubscriptionSingleEye() throws
		// Exception {
		// clearcart();
		log.info(
				"------------------------CONTACT LENS WITH SUBSCRIPTION FOR SINGLE EYE------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		clearCart();

		categoryId = returnCategoryId("contact_lenses", "CL");

		if (categoryId == null) {
			categoryId = category_contact_lens;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase(),
				true);
		String productId = product.get("productId");

		// JSONObject subscriptionDetails =
		// JunoV1Util.getSubscriptionDetailsWithProductId(productId, "false");
		// JSONObject result=subscriptionDetails.getJSONObject("result");
		// JSONArray subscriptions=result.getJSONArray("subscriptions");
		// String subscriptionId =
		// subscriptions.getJSONObject(0).getString("subscriptionId");
		// String numberOfBoxes =
		// subscriptions.getJSONObject(0).getString("numberOfBoxes");

		String power = ProductUtil.getPowerForAProduct(productId)
				.getString(ProductUtil.getPowerForAProduct(productId).length() - 1);

		Prescription presObj;
		Power leftPower;
		CartItem reqObj;
		JSONObject cart_response;

		presObj = new Prescription();
		presObj.setPrescriptionType(
				PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		leftPower = new Power();
		leftPower.setSph(power);
		// leftPower.setBoxes(Integer.valueOf(subscriptionId));
		leftPower.setBoxes(1);

		presObj.setLeft(leftPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");
		reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase());
		// reqObj.setSubscriptionId(subscriptionId);
		// reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");
		try {
			cart_response.getJSONObject("result").getString("cartId");
		} catch (JSONException e) {
			presObj = new Prescription();

			presObj.setPrescriptionType(
					PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj.setDob("12-Aug-92");
			presObj.setGender("Female");
			// Left Power
			leftPower = new Power();
			leftPower.setSph("Call Me/Email Me for Power");
			leftPower.setBoxes(1);

			presObj.setLeft(leftPower);

			presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj.setUserName("user1");
			reqObj = new CartItem();
			reqObj.setProductId(Long.parseLong(productId));
			reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase());

			reqObj.setIsBothEye("Y");
			reqObj.setPrescription(presObj);
			cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);

		}

		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct.\nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsCount"), Integer.parseInt("1"),
				"Number of items added is not correct.\nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		// Assert.assertNotNull(
		// cart_response.getJSONObject("result").getJSONArray("items").getJSONObject(0)
		// .getJSONObject("subscription"),
		// "Subscription detail is missing.\nCart response received : " + cart_response
		// + ""
		// + printSessionTokenOnConsole(loggedInSessiontoken));
		JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			if (items.getJSONObject(i).getString("productId").equals(productId)) {
				Assert.assertEquals(items.getJSONObject(i).getString("powerRequired"), "POWER_SUBMITTED",
						"Power is not submitted");
				Assert.assertNotNull(items.getJSONObject(i).getJSONObject("prescription").getJSONObject("left"),
						"Left prescription details is missing");
			}
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void loggedInUser_contactLensWithTwoSolutions() throws Exception {
		// public void loggedInUser_contactLensSubscriptionWithSolutions() throws
		// Exception {
		// clearcart();
		log.info(
				"------------------------CONTACT LENS SUBSCRIPTION WITH SOLUTIONS------------------------------------");
		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(userEmail, password, x_api_client);
		clearCart();

		categoryId = returnCategoryId("contact_lenses", "CL");
		if (categoryId == null) {
			categoryId = category_contact_lens;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase(),
				true);
		String productId = product.get("productId");
		// String subscriptionId = JunoV1Util.getSubscriptionIdWithProductId(productId,
		// "false");
		// log.info("subscriptionId: " + subscriptionId);
		JSONObject jsonResponse_ContactLensSolution = JunoV1Util.getContactLensSolutions(x_api_client);
		JSONArray result = jsonResponse_ContactLensSolution.getJSONArray("result");
		String productId_solution1 = result.getJSONObject(0).getString("id");
		Assert.assertNotNull(productId_solution1, "product_id of solution not present");
		String productId_solution2 = result.getJSONObject(1).getString("id");
		Assert.assertNotNull(productId_solution2, "product_id of solution not present");

		String power = ProductUtil.getPowerForAProduct(productId)
				.getString(ProductUtil.getPowerForAProduct(productId).length() - 1);
		JSONObject cart_response;

		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(
				PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();

		leftPower.setSph(power);
		leftPower.setBoxes(1);
		// leftPower.setBoxes(Integer.valueOf(subscriptionId));

		presObj.setLeft(leftPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		// reqObj.setStoreInventory(1);
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase());
		// reqObj.setSubscriptionId(subscriptionId);

		reqObj.setPrescription(presObj);
		List<Long> relatedProducts = new ArrayList<Long>();
		relatedProducts.add(Long.parseLong(productId_solution1));
		relatedProducts.add(Long.parseLong(productId_solution2));
		reqObj.setRelatedProductIds(relatedProducts);
		cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");
		try {
			cart_response.getJSONObject("result").getString("cartId");
		} catch (JSONException e) {
			log.info("Catch is executig -----------");
			Prescription presObj1 = new Prescription();
			presObj1.setPrescriptionType(
					PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj1.setDob("12-Aug-92");
			presObj1.setGender("Female");
			Power leftPower1 = new Power();
			leftPower1.setSph("Call Me/Email Me for Power");
			leftPower1.setBoxes(1);

			presObj1.setLeft(leftPower1);
			presObj1.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj1.setUserName("user1");
			CartItem reqObj1 = new CartItem();
			reqObj1.setProductId(Long.parseLong(productId));
			reqObj1.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase());
			reqObj1.setPrescription(presObj1);
			List<Long> relatedProducts1 = new ArrayList<Long>();
			relatedProducts1.add(Long.parseLong(productId_solution1));
			relatedProducts1.add(Long.parseLong(productId_solution2));
			reqObj1.setRelatedProductIds(relatedProducts);
			cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj1, x_api_client);
		}

		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 3,
				"Number of items added is not correct.\nCart Response:" + cart_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsCount"),
				// 2 + Integer.parseInt(subscriptionId),
				3, "Number of items added is not correct.\nCart response received : " + cart_response + ""

						+ printSessionTokenOnConsole(loggedInSessiontoken));
		// Assert.assertNotNull(
		// cart_response.getJSONObject("result").getJSONArray("items").getJSONObject(0)
		// .getJSONObject("subscription"),
		// "Subscription detail is missing.\nCart response received : " + cart_response
		// + ""
		// + printSessionTokenOnConsole(loggedInSessiontoken));
		JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			if (items.getJSONObject(i).getString("productId").equals(productId)) {
				Assert.assertEquals(items.getJSONObject(i).getString("powerRequired"), "POWER_SUBMITTED",
						"Power is not submitted");
				Assert.assertNotNull(items.getJSONObject(i).getJSONObject("prescription").getJSONObject("left"),
						"Left prescription details is missing");
			}
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, "PU:162B", "nb",
				"PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response.\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response.\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void registerNewUser() throws Exception {

		log.info("------------------------Register New User------------------------------------");
		loggedInSessiontoken = CustomerUtil.registerNewCustomer("test", "test",
				"lenskart.test" + GenericUtil.createRandomNumber(12) + "@gmail.com", "123456",
				"1870" + GenericUtil.createRandomNumber(6));

		Assert.assertNotNull(loggedInSessiontoken, "Session token is not valid means user is not getting registered");
	}

	@Test(enabled = true)
	public void mergeCartUsecase() throws Exception {
		log.info("------------------------Merge Cart Usecase------------------------------------");
		String sessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("sunglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_sunglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		String productId = product.get("productId");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(sessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		JSONArray itemsArray = cart_response.getJSONObject("result").getJSONArray("items");
		String pid = null;
		for (int i = 0; i <= itemsArray.length() - 1; i++) {
			pid = itemsArray.getJSONObject(i).getString("productId");
			if (Arrays.stream(goldPids).anyMatch(pid::equals))
				break;
		}
		if (Arrays.stream(goldPids).anyMatch(pid::equals)) {
			Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 2,
					"Number of items added is not correct. \nCart response received : " + cart_response + ""
							+ printSessionTokenOnConsole(loggedInSessiontoken));
		} else if (pid.equals(goldPid1)) {
			Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 2,
					"Number of items added is not correct. \nCart response received : " + cart_response + ""
							+ printSessionTokenOnConsole(loggedInSessiontoken));
		} else {
			Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
					"Number of items added is not correct. \nCart response received : " + cart_response + ""
							+ printSessionTokenOnConsole(loggedInSessiontoken));
		}

		loggedInSessiontoken = CustomerUtil.get_sessionId_after_user_authentication(sessiontoken, userEmail, password,
				x_api_client);
		JSONObject jsonResponse_category1 = JunoV1Util.getCategoryDetails(category_eyeglasses, x_api_client);
		product = getProductDetails(jsonResponse_category1, "", false);
		CartItem reqObj1 = new CartItem();
		reqObj1.setProductId(Long.parseLong(product.get("productId")));
		reqObj1.setStoreInventory(1);
		JSONObject cart_response1 = CartUtil.createCart(loggedInSessiontoken, reqObj1, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response1);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response1.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
		// NEED TO CHANGE TO 3 ONCE THE AUTO GOLD PID FEATURE IS STOPPED
		Assert.assertEquals(cart_response1.getJSONObject("result").getInt("itemsQty"), 2,
				"Number of items added is not correct. \nCart response received : " + cart_response1 + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));

		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, "", "cc", "");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response");
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response");
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = false)
	public void numberOfProductCheckForVC() throws Exception {

		log.info("------------------------Number of product check for VC------------------------------------");
		guestSession = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = JunoProdSanity.category_eyeglasses_VC;
			log.debug("Hard coded Category id");
		}
		String RequstUrlCategoryApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.CATEGORY_PATH, categoryId);

		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("page", "0"));
		query.add(new BasicNameValuePair("page-size", page_size));

		HttpResponse httpResponseCategoryApi = RequestUtil.getRequest(RequstUrlCategoryApi, header, query);
		JSONObject jsonResponse_category = RequestUtil.convertHttpResponseToJsonObject(httpResponseCategoryApi);
		Assert.assertEquals(httpResponseCategoryApi.getStatusLine().getStatusCode(), 200, "\nCategory API (GET) :"
				+ GenericUtil.printAPICallDetails(guestSession, RequstUrlCategoryApi, null, jsonResponse_category));
		int numberOfProduct = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").length();
		Assert.assertEquals(String.valueOf(numberOfProduct), page_size, "number of product is less than 200");
		Assert.assertNotNull(jsonResponse_category.getJSONObject("result").getString("num_of_products"),
				"num_of_products param is missing in category response");
		Assert.assertTrue(jsonResponse_category.getJSONObject("result").getInt("num_of_products") > 100,
				"num_of_products is less than 100");
	}

	@Test(enabled = false)
	public void numberOfProductCheckForJJ() throws Exception {
		log.info("------------------------Number of product check for JJ------------------------------------");
		guestSession = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "John Jacobs");
		if (categoryId == null) {
			categoryId = JunoProdSanity.category_eyeglasses_JJ;
			log.debug("Hard coded Category id");
		}
		String RequstUrlCategoryApi = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV1PathConstants.CATEGORY_PATH, categoryId);
		List<NameValuePair> header = new ArrayList<NameValuePair>();
		header.add(new BasicNameValuePair("Content-Type", "application/json"));
		header.add(new BasicNameValuePair("X-Api-Client", x_api_client));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("page", "0"));
		query.add(new BasicNameValuePair("page-size", page_size));

		HttpResponse httpResponseCategoryApi = RequestUtil.getRequest(RequstUrlCategoryApi, header, query);
		JSONObject jsonResponse_category = RequestUtil.convertHttpResponseToJsonObject(httpResponseCategoryApi);
		Assert.assertEquals(httpResponseCategoryApi.getStatusLine().getStatusCode(), 200, "\nCategory API (GET) :"
				+ GenericUtil.printAPICallDetails(guestSession, RequstUrlCategoryApi, null, jsonResponse_category));
		int numberOfProduct = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").length();
		Assert.assertEquals(String.valueOf(numberOfProduct), page_size, "number of product is less than 200");
		Assert.assertNotNull(jsonResponse_category.getJSONObject("result").getString("num_of_products"),
				"num_of_products param is missing in category response");
		Assert.assertTrue(jsonResponse_category.getJSONObject("result").getInt("num_of_products") > 100,
				"num_of_products is less than 100");
	}

	@Test(enabled = false)
	public void loggedInUser_loyalTrue() throws Exception {
		categoryId = returnCategoryId("eyeglasses", "John Jacobs");
		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		log.debug("product: " + product);
		// creating loyal user

		// String loginTelephone = "56467" + GenericUtil.createRandomNumber(5);
		String loginTelephone = "";
		String sessionToken = SessionUtil.createNewSession();
		// if (Environments.environment.equals(Profiles.PROD)) {
		// loginTelephone = "5646734511";

		// }

		JSONObject mobileAuthenticateJson = CustomerUtil.mobileAuthenticateWithoutReferCode(x_api_client, sessionToken,
				ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), loginTelephone);
		loggedInSessiontoken = mobileAuthenticateJson.getJSONObject("result").getString("token");
		clearCart();
		JSONObject customerDetails = CustomerUtil.getMeCustomerDetails(loggedInSessiontoken, x_api_client);
		Assert.assertFalse(customerDetails.getBoolean("isLoyalty"),
				"isLoyalty should be false or its previous order is still not cancelled");
		CartItem reqObj = new CartItem();
		reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		int amount = cart_response.getJSONObject("result").getJSONObject("totals").getInt("subTotal");

		/* Need to uncomment this code once the auto add is made off */
//		reqObj = new CartItem();
//		reqObj.setProductId(Long.parseLong(goldPid));
//		CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);

		reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		Assert.assertEquals(cart_response.getJSONObject("result").getString("itemsCount"), "3",
				"auto add is not applying");
		Assert.assertTrue(cart_response.getJSONObject("result").getBoolean("bogoApplied"),
				"bogoApplied param should be true");
		Assert.assertTrue(cart_response.getJSONObject("result").getJSONObject("totals").getInt("total") < 2 * amount,
				"bogo discount is not getting updated.");
		int count = 0;
		JSONArray appliedRuleIds = cart_response.getJSONObject("result").getJSONArray("appliedRuleIds");
		for (int i = 0; i < appliedRuleIds.length(); i++) {
			if (appliedRuleIds.getString(i).equals("610004") || appliedRuleIds.getString(i).equals("610003")
					|| appliedRuleIds.getString(i).equals("610005")) {
				count = count + 1;
			}
		}
		Assert.assertTrue(count >= 1, "No bogo rule is applied");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId1 = payment_response_result.getJSONObject("order").getString("id");
		customerDetails = CustomerUtil.getMeCustomerDetails(loggedInSessiontoken, x_api_client);
		Assert.assertTrue(customerDetails.getBoolean("isLoyalty"), "bogo is not working");
		// Assert.assertEquals(customerDetails.getInt("yearlyLoyaltyOrderCount"),1,
		// "yearlyLoyaltyOrderCount is not updated");
		// Assert.assertEquals(customerDetails.getInt("monthlyLoyaltyOrderCount"),1,
		// "monthlyLoyaltyOrderCount is not updated");
		Assert.assertNotNull(customerDetails.getString("loyaltyStartDate"), "loyaltyStartDate is not present");
		Assert.assertNotNull(customerDetails.getString("loyaltyExpiryDate"), "loyaltyExpiryDate is not present");

		//////////////////////////

		clearCart();
		reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		amount = cart_response.getJSONObject("result").getJSONObject("totals").getInt("subTotal");
		reqObj = new CartItem();
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		Assert.assertEquals(cart_response.getJSONObject("result").getString("itemsCount"), "2",
				"item count is not correct");
		// Assert.assertTrue(cart_response.getJSONObject("result").getBoolean("bogoApplied"),
		// "bogoApplied param should bt true");
		Assert.assertTrue(cart_response.getJSONObject("result").getJSONObject("totals").getInt("total") < 2 * amount,
				"bogo discount is not getting updated.");
		int count1 = 0;
		appliedRuleIds = cart_response.getJSONObject("result").getJSONArray("appliedRuleIds");
		for (int i = 0; i < appliedRuleIds.length(); i++) {
			if (appliedRuleIds.getString(i).equals("610004") || appliedRuleIds.getString(i).equals("610003")
					|| appliedRuleIds.getString(i).equals("610005")) {
				count1 = count + 1;
			}
		}
		Assert.assertTrue(count1 >= 1, "No bogo rule is applied");
		address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		payment_response_result = payment_response.getJSONObject("result");
		String orderId2 = payment_response_result.getJSONObject("order").getString("id");
		customerDetails = CustomerUtil.getMeCustomerDetails(loggedInSessiontoken, x_api_client);
		Assert.assertTrue(customerDetails.getBoolean("isLoyalty"), "bogo is not working");
		// Assert.assertEquals(customerDetails.getInt("yearlyLoyaltyOrderCount"),2,
		// "yearlyLoyaltyOrderCount is not updated");
		// Assert.assertEquals(customerDetails.getInt("monthlyLoyaltyOrderCount"),2,
		// "monthlyLoyaltyOrderCount is not updated");
		Assert.assertNotNull(customerDetails.getString("loyaltyStartDate"), "loyaltyStartDate is not present");
		Assert.assertNotNull(customerDetails.getString("loyaltyExpiryDate"), "loyaltyExpiryDate is not present");
		//
		// if (Environments.environment.equals(Profiles.PREPROD) ||
		// Environments.environment.equals(Profiles.PROD)) {
		// OrderUtil.cancelInvoice(loggedInSessiontoken, x_api_client, orderId1, "1",
		// "Test");
		// OrderUtil.cancelInvoice(loggedInSessiontoken, x_api_client, orderId2, "1",
		// "Test");
		// }
		if (Environments.environment.equals(Profiles.PREPROD) || Environments.environment.equals(Profiles.PROD)) {
			OrderUtil.updateOrderStatus(loggedInSessiontoken, x_api_client, orderId1, "CANCELED", "CANCELED");
			OrderUtil.updateOrderStatus(loggedInSessiontoken, x_api_client, orderId2, "CANCELED", "CANCELED");
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId2);

	}

	@Test(enabled = false)
	public void mobileWalletLogin() throws Exception {
		String sessionToken = SessionUtil.createNewSession();
		String loginTelephone = "222" + GenericUtil.createRandomNumber(7);

		JSONObject mobileAuthenticateJson = CustomerUtil.mobileAuthenticateWithoutReferCode(x_api_client, sessionToken,
				ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), loginTelephone);
		loggedInSessiontoken = mobileAuthenticateJson.getJSONObject("result").getString("token");
		CartUtil.clearCart(loggedInSessiontoken);
		String xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);
		double lenskartplusAmount = MoneyUtil.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken,
				x_api_client, "lenskartplus");
		if (lenskartplusAmount > 0) {
			MoneyUtil.bulkDebit(x_api_client, loggedInSessiontoken, xApiAuthToken, loginTelephone,
					String.valueOf(lenskartplusAmount), "lenskartplus");
		}
		xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(x_api_client, loggedInSessiontoken, xApiAuthToken, loginTelephone, "200", "lenskartplus",
				null);

		categoryId = returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(loggedInSessiontoken, reqObj, x_api_client);
		// double orderAmount =
		// cart_response.getJSONObject("result").getJSONObject("totals").getDouble("total");
		Address address = shippingAddress();
		CartUtil.saveAddress(loggedInSessiontoken, x_api_client, address, null);
		JSONObject getCartAfterWalletApplied = CartUtil.getCartBasedOnApplyWallet(loggedInSessiontoken, x_api_client,
				"true");
		JSONArray discount = getCartAfterWalletApplied.getJSONObject("result").getJSONObject("totals")
				.getJSONArray("discounts");
		double discountAmount = 0;
		for (int i = 0; i < discount.length(); i++) {
			JSONObject discountObject = discount.getJSONObject(i);
			if (discountObject.getString("type").equals("lenskartplus")) {
				discountAmount = discountObject.getDouble("amount");
				break;
			}
		}
		log.debug("sjhshjd" + discountAmount);
		PaymentUtil.getAllPaymentMethodsv3(loggedInSessiontoken, x_api_client);
		JSONObject payment_response = PaymentUtil.orderPayment(loggedInSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(loggedInSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(loggedInSessiontoken, orderId, itemId, powerType1);
		}
		Assert.assertEquals(
				MoneyUtil.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken, x_api_client,
						"lenskartplus"),
				Double.parseDouble("200") - discountAmount, "Get wallet balance api is not updated");
		if (Environments.environment.equals(Profiles.PREPROD) || Environments.environment.equals(Profiles.PROD)) {
			OrderUtil.cancelInvoice(loggedInSessiontoken, x_api_client, orderId, "1", "Test");
		}
	}

	@Test(enabled = true)
	public void searchQuery() throws Exception {
		log.info(
				"------------------------THIS TEST CASE WILL SEARCH FOR EYE GLASS KEYWORD------------------------------------");
		String RequestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_SEARCH_QUERY_V2, "Eye+glass");

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", x_api_client));
		HttpResponse httpResponse = RequestUtil.getRequest(RequestUrl, headers, null);
		JSONObject jsonResponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);
		int num_of_products = jsonResponse.getJSONObject("result").getInt("num_of_products");
		Assert.assertTrue(num_of_products > 0);
		Assert.assertTrue(jsonResponse.getJSONObject("result").getJSONArray("product_list").length() > 0);
	}

	@Test(enabled = true)
	public void bulkCreditAndBulkDebit() throws Exception {
		log.info("------------------------BULK CREDIT AND BULK DEBIT------------------------------------");
		String sessionToken = SessionUtil.createNewSession();
		JSONObject mobileAuthenticateJson = CustomerUtil.mobileAuthenticateWithoutReferCode(x_api_client, sessionToken,
				ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), userPhone);
		loggedInSessiontoken = mobileAuthenticateJson.getJSONObject("result").getString("token");
		String xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);
		double lenskartplusAmount = MoneyUtil.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken,
				x_api_client, "lenskartplus");
		double lenskartAmount = MoneyUtil.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken,
				x_api_client, "lenskart");

		xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(x_api_client, loggedInSessiontoken, xApiAuthToken, userPhone, "2", "lenskartplus", null);

		xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);
		MoneyUtil.bulkCredit(x_api_client, loggedInSessiontoken, xApiAuthToken, userPhone, "2", "lenskart", null);

		xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);
		double lenskartplusAmountAfterBulkCredit = MoneyUtil
				.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken, x_api_client, "lenskartplus");

		double lenskartAmountAfterBulkCredit = MoneyUtil
				.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken, x_api_client, "lenskart");

		Assert.assertEquals(lenskartplusAmount + 2, lenskartplusAmountAfterBulkCredit);
		Assert.assertEquals(lenskartAmount + 2, lenskartAmountAfterBulkCredit);

		xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);

		MoneyUtil.bulkDebit(x_api_client, loggedInSessiontoken, xApiAuthToken, userPhone, "2", "lenskartplus");
		xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);

		MoneyUtil.bulkDebit(x_api_client, loggedInSessiontoken, xApiAuthToken, userPhone, "2", "lenskart");
		xApiAuthToken = MoneyUtil.generateAuthToken(loggedInSessiontoken, Environments.X_API_CLIENT);
		double lenskartplusAmountAfterBulkDebit = MoneyUtil
				.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken, x_api_client, "lenskartplus");

		double lenskartAmountAfterBulkDebit = MoneyUtil
				.getWalletBalanceBasedOnWalletTypeForLoggedIn(loggedInSessiontoken, x_api_client, "lenskart");

		Assert.assertEquals(lenskartplusAmount, lenskartplusAmountAfterBulkDebit);
		Assert.assertEquals(lenskartAmount, lenskartAmountAfterBulkDebit);

	}

	@Test(enabled = false)
	public void fetchSalesMan() throws Exception {
		log.info("------------------------FETCH SALES MAN------------------------------------");
		String URL;
		if (Environments.environment.equals(Profiles.PROD)) {
			URL = Environments.SERVICES_ENVIRONMENT + String.format(UtilityPathConstants.GET_SALESMAN, "12");
		} else {
			URL = Environments.SERVICES_ENVIRONMENT + String.format(UtilityPathConstants.GET_SALESMAN, "11");
		}
		HttpResponse httpResponse = RequestUtil.getRequest1(URL, SessionUtil.createNewSession());
		JSONObject response = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);
		if (Environments.environment.equals(Profiles.PROD)) {
			Assert.assertEquals(response.getJSONObject("result").getString("name"), "dinesh");
		} else {
			Assert.assertEquals(response.getJSONObject("result").getString("name"), "Farook Shaikh");

		}
	}

	// New test cases

	@Test(enabled = true)
	public void guestUser_contactLensWithTwoSolutions() throws Exception {
		// public void guestInUser_contactLensSubscriptionWithSolutions() throws
		// Exception {
		log.info(
				"------------------------ GUEST USER CONTACT LENS SUBSCRIPTION WITH SOLUTIONS------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		clearCart();

		categoryId = returnCategoryId("contact_lenses", "CL");
		if (categoryId == null) {
			categoryId = category_contact_lens;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase(),
				true);
		String productId = product.get("productId");
		// String subscriptionId = JunoV1Util.getSubscriptionIdWithProductId(productId,
		// "false");
		// log.info("subscriptionId: " + subscriptionId);
		JSONObject jsonResponse_ContactLensSolution = JunoV1Util.getContactLensSolutions(x_api_client);
		JSONArray result = jsonResponse_ContactLensSolution.getJSONArray("result");
		String productId_solution1 = result.getJSONObject(0).getString("id");
		Assert.assertNotNull(productId_solution1, "product_id of solution not present");
		String productId_solution2 = result.getJSONObject(1).getString("id");
		Assert.assertNotNull(productId_solution2, "product_id of solution not present");

		String power = ProductUtil.getPowerForAProduct(productId)
				.getString(ProductUtil.getPowerForAProduct(productId).length() - 1);
		JSONObject cart_response;

		Prescription presObj = new Prescription();
		presObj.setPrescriptionType(
				PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power
		Power leftPower = new Power();

		leftPower.setSph(power);
		leftPower.setBoxes(1);
		// leftPower.setBoxes(Integer.valueOf(subscriptionId));

		presObj.setLeft(leftPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		// reqObj.setStoreInventory(1);
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase());
		// reqObj.setSubscriptionId(subscriptionId);

		reqObj.setPrescription(presObj);
		List<Long> relatedProducts = new ArrayList<Long>();
		relatedProducts.add(Long.parseLong(productId_solution1));
		relatedProducts.add(Long.parseLong(productId_solution2));
		reqObj.setRelatedProductIds(relatedProducts);
		cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");
		try {
			cart_response.getJSONObject("result").getString("cartId");
		} catch (JSONException e) {
			log.info("Catch is executig -----------");
			Prescription presObj1 = new Prescription();
			presObj1.setPrescriptionType(
					PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj1.setDob("12-Aug-92");
			presObj1.setGender("Female");
			Power leftPower1 = new Power();
			leftPower1.setSph("Call Me/Email Me for Power");
			leftPower1.setBoxes(1);

			presObj1.setLeft(leftPower1);
			presObj1.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
			presObj1.setUserName("user1");
			CartItem reqObj1 = new CartItem();
			reqObj1.setProductId(Long.parseLong(productId));
			reqObj1.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase());
			reqObj1.setPrescription(presObj1);
			List<Long> relatedProducts1 = new ArrayList<Long>();
			relatedProducts1.add(Long.parseLong(productId_solution1));
			relatedProducts1.add(Long.parseLong(productId_solution2));
			reqObj1.setRelatedProductIds(relatedProducts);
			cart_response = CartUtil.createCart(guestSessiontoken, reqObj1, x_api_client);
		}

		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 3,
//				"Number of items added is not correct.\nCart Response:" + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsCount"),
//				// 2 + Integer.parseInt(subscriptionId),
//				3, "Number of items added is not correct.\nCart response received : " + cart_response + ""
//
//						+ printSessionTokenOnConsole(guestSessiontoken));
		// Assert.assertNotNull(
		// cart_response.getJSONObject("result").getJSONArray("items").getJSONObject(0)
		// .getJSONObject("subscription"),
		// "Subscription detail is missing.\nCart response received : " + cart_response
		// + ""
		// + printSessionTokenOnConsole(loggedInSessiontoken));
		JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			if (items.getJSONObject(i).getString("productId").equals(productId)) {
				Assert.assertEquals(items.getJSONObject(i).getString("powerRequired"), "POWER_SUBMITTED",
						"Power is not submitted");
				Assert.assertNotNull(items.getJSONObject(i).getJSONObject("prescription").getJSONObject("left"),
						"Left prescription details is missing");
			}
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, "PU:162B", "nb", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response.\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response.\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_eyeglassFirstFrameFreeSingleVision() throws Exception {
		log.info(
				"------------------------GUEST User Eyeglasses FIRST FRAME FREE with Single Vision-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();

		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		// // reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");

		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id in create cart response is null. Session token : "
						+ printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct in create cart API response. \nCart response received : "
//						+ cart_response + "" + printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"\nCart Id: " + cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_eyeglassFirstFrameFreeBifocal() throws Exception {
		log.info(
				"------------------------Guest User Eyeglasses FIRST FRAME FREE with Bifocal-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.BIFOCAL, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		Assert.assertNotNull(product.get("packageId"), product.get("productId") + " does not have package");
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.BIFOCAL);
		// // reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);

		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "dc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);

	}

	@Test(enabled = true)
	public void guestUser_eyeglassFirstFrameFreeZeroPower() throws Exception {
		log.info(
				"------------------------Guest User Eyeglasses FIRST FRAME FREE with Zero power-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;

			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);

		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);

		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = false)
	public void guestUser_eyeglassFirstFrameFreeWithoutPower() throws Exception {
		log.info(
				"------------------------Guest User Eyeglasses FIRST FRAME FREE without Power-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		clearCart();
		categoryId = returnCategoryId("eyeglasses", "eyeglasses_FFF");
		if (categoryId == null) {
			categoryId = category_eyeglasses_FFF;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		// // reqObj.setStoreInventory(1);
		reqObj.setQuantity(1);
		Thread.sleep(1000);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, "PU:162B", "nb", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);

	}

	@Test(enabled = true)
	public void guestInUser_eyeglassSingleVision() throws Exception {
		log.info(
				"------------------------Guest User Eyeglasses with Single Vision-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "VC");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SINGLE_VISION, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		// // reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId:" + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_eyeglassithCoating() throws Exception {
		log.info(
				"------------------------Guest User Eyeglasses with Single Vision with Coating-------------------------------------");

		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "Full-Rim");

		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.BIFOCAL, true);
		log.info(product);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		Assert.assertNotNull(product.get("coating_id"),
				"Product id: " + product.get("productId") + " does not have coating in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.BIFOCAL);
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setQuantity(Integer.parseInt("1"));
		// reqObj.setStoreInventory(1);
		reqObj.setAddOns(product.get("coating_id"));
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_eyeglassBifocal() throws Exception {
		// clearCart();
		log.info("------------------------Guest User Eyeglasses with Bifocal-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "VC");
		if (categoryId == null) {
			categoryId = ProductCategories.CATEGORY_VC_RECTANGLE;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.BIFOCAL, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.BIFOCAL);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_eyeglassZeroPower() throws Exception {
		log.info("------------------------Guest User Eyeglasses with Zero power-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.ZERO_POWER, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.ZERO_POWER);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
				"Number of items added is not correct. \nCart response received : " + cart_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "dc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);

	}

	@Test(enabled = true)
	public void guestUser_eyeglassWithoutPower() throws Exception {
		log.info("------------------------Guest User Eyeglasses without Power-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_eyeglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(guestSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, "PU:162B", "nb", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_sunglassesWithPower() throws Exception {
		log.info("------------------------Guest UserSunglass with power-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("sunglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_sunglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.SUNGLASSES, false);
		Assert.assertNotNull(product.get("packageId"),
				"Product id: " + product.get("productId") + " does not have package in category id: " + categoryId);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SUNGLASSES);
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(guestSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		log.info("orderId: " + orderId);
		String itemId = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("id");
		String pid = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("productId");
		if (payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
				.getString("powerRequired").equals("POWER_REQUIRED")) {
			String powerType1 = payment_response_result.getJSONObject("order").getJSONArray("items").getJSONObject(0)
					.getJSONObject("prescription").getString("powerType");
			OrderUtil.updateOrderItemPrescription_usecase(guestSessiontoken, orderId, itemId, pid, powerType1);
		}
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_sunglassesWithoutPower() throws Exception {
		log.info("------------------------Guest User Sunglasses without Power-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("sunglasses", "Vincent Chase");
		if (categoryId == null) {
			categoryId = category_sunglasses;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		String productId = product.get("productId");
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		// reqObj.setStoreInventory(1);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct. \nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(guestSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cod", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_HTO() throws Exception {
		log.info("------------------------Guest User HTO CASES------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		// clearCart();
		JSONObject cart_response = CartUtil.HTOCart(guestSessiontoken, x_api_client);
		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");
		Assert.assertEquals(cart_response.getJSONObject("result").getString("cartType"), "HEC", "Cart is not HEC");
		Address address = shippingAddress();
		GiftMessage giftMsgObj = new GiftMessage();
		giftMsgObj = null;
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, giftMsgObj);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_accessories() throws Exception {
		log.info("------------------------Accessories-------------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		categoryId = returnCategoryId("eyeglasses", "Accessories_revenue");
		if (categoryId == null) {
			categoryId = category_accessories;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, "", false);
		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		// reqObj.setStoreInventory(1);

		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);
		log.debug("-----------cart_response-------------");
		log.debug(cart_response);
		log.debug("--------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct\nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(loggedInSessiontoken));
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cc", null);
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
		Assert.assertEquals(payment_response.getJSONObject("result").length(), 2,
				"Order Payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	@Test(enabled = true)
	public void guestUser_contactLensSolutionAndGiftVoucher() throws Exception {
		String addGVURL = (Environments.SERVICES_ENVIRONMENT + CartPathConstants.AddGV).trim();
		log.info("----------------------Guest User CONTACT LENS WITHOUT SOLUTION------------------------------");
		guestSessiontoken = SessionUtil.createNewSession();
		// categoryId = returnCategoryId("contact_lenses", "CL");
		if (categoryId == null) {
			categoryId = category_contactlens_CL;
			log.debug("Hard coded Category id");
		}
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(categoryId, x_api_client);
		product = getProductDetails(jsonResponse_category, ApplicationConstants.PowerTypes.CONTACT_LENS, false);
		String productId = product.get("productId");
		Prescription presObj = new Prescription();

		presObj.setPrescriptionType(
				PrescriptionType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setDob("12-Aug-92");
		presObj.setGender("Female");
		// Left Power

		Power leftPower = new Power();
		leftPower.setSph("Call Me/Email Me for Power");
		leftPower.setBoxes(Integer.valueOf("2"));

		presObj.setLeft(leftPower);
		// Right Power
		Power rightPower = new Power();
		rightPower.setSph("Call Me/Email Me for Power");
		rightPower.setBoxes(Integer.valueOf("2"));

		presObj.setRight(rightPower);
		presObj.setPowerType(PowerType.valueOf(ApplicationConstants.PowerTypes.CONTACT_LENS.toUpperCase()));
		presObj.setUserName("user1");

		CartItem reqObj = new CartItem();
		reqObj.setProductId(Long.parseLong(productId));
		reqObj.setPowerType(ApplicationConstants.PowerTypes.CONTACT_LENS);
		// reqObj.setStoreInventory(1);
		reqObj.setPrescription(presObj);
		JSONObject cart_response = CartUtil.createCart(guestSessiontoken, reqObj, x_api_client);

		log.debug("-----------Create Cart response-----------");
		log.debug(cart_response);
		log.debug("------------------------------------------");
		Assert.assertNotNull(cart_response.getJSONObject("result").getString("cartId"),
				"Cart Id is null." + printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsQty"), 1,
//				"Number of items added is not correct.\nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(guestSessiontoken));
//		Assert.assertEquals(cart_response.getJSONObject("result").getInt("itemsCount"), 5,
//				"Number of items added is not correct.\nCart response received : " + cart_response + ""
//						+ printSessionTokenOnConsole(guestSessiontoken));

		JSONArray items = cart_response.getJSONObject("result").getJSONArray("items");
		for (int i = 0; i < items.length(); i++) {
			if (items.getJSONObject(i).getString("productId").equals(product.get("productId"))) {
				Assert.assertEquals(items.getJSONObject(i).getString("powerRequired"), "POWER_SUBMITTED",
						"Power is not submitted");
			}
		}
		Address address = shippingAddress();
		CartUtil.saveAddress(guestSessiontoken, x_api_client, address, null);
		JSONObject gvResponse = CartUtil.addGiftVoucher(guestSessiontoken,
				ApplicationConstants.ReturnGiftVoucher.returnGiftVoucher());
		String addGV_url = addGVURL.replace("GV", ApplicationConstants.ReturnGiftVoucher.returnGiftVoucher());
		Assert.assertTrue(gvResponse.has("result"),
				"API Response: " + GenericUtil.printAPICallDetails(guestSessiontoken, addGV_url, null, gvResponse));
		log.debug("**********" + GenericUtil.printAPICallDetails(guestSessiontoken, null, null, gvResponse));
		JSONArray discounts = gvResponse.getJSONObject("result").getJSONObject("totals").getJSONArray("discounts");
		for (int i = 0; i < discounts.length(); i++) {
			if (discounts.getJSONObject(i).getString("type").equals("gv")) {
				Assert.assertEquals(discounts.getJSONObject(i).getString("code"),
						ApplicationConstants.ReturnGiftVoucher.returnGiftVoucher(), "GV code mismatch");
			}
		}

		JSONObject payment_response = PaymentUtil.orderPayment(guestSessiontoken, x_api_client, null, "cc", "PU");
		log.debug("------------OrderPayment Response-------------");
		log.debug(payment_response);
		log.debug("-----------------------------------------------");
//		Assert.assertEquals(payment_response.getJSONObject("result").length(), 1,
//				"Order Payment is not giving correct response\nCart Id is: "
//						+ cart_response.getJSONObject("result").getString("cartId")
//						+ "\nOrder Payment response received : " + payment_response + ""
//						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("order"),
				"Order Payment order is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		Assert.assertNotNull(payment_response.getJSONObject("result").getString("payment"),
				"Order Payment payment is not giving correct response\nCart Id is: "
						+ cart_response.getJSONObject("result").getString("cartId")
						+ "\nOrder Payment response received : " + payment_response + ""
						+ printSessionTokenOnConsole(guestSessiontoken));
		JSONObject payment_response_result = payment_response.getJSONObject("result");
		String orderId = payment_response_result.getJSONObject("order").getString("id");
		orderIds.put(Thread.currentThread().getStackTrace()[1].getMethodName(), orderId);
	}

	// Get Active inventory
	@Test(enabled = true)
	public void getActiveInventoryAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		String product_id = product.get("productId");
		String RequstUrlGetActiveInventory = Environments.SERVICES_ENVIRONMENT
				+ String.format(InventoryPathConstant.GET_ACTIVE_INVENTORY_PATH, product_id);
		System.out.println("--------------------------GET  ACTIVE INVENTORY----------------------------------");
		System.out.println("Request Header for Get:" + RequstUrlGetActiveInventory);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("productIds", product_id));

		HttpResponse httpResponsegetActiveinventory = RequestUtil.getRequest(RequstUrlGetActiveInventory, headers,
				queryparam);
		JSONObject responseJson_GetActiveinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsegetActiveinventory);
		Assert.assertEquals(httpResponsegetActiveinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_GetActiveinventory = responseJson_GetActiveinventory_result.getJSONObject("result");
		Assert.assertNotNull(
				responseJson_GetActiveinventory.getJSONArray(product_id).getJSONObject(0).getString("productId"),
				"product id cannot be null");

	}

	@Test
	public void readAddresses() throws Exception {
		System.out.println("--------------------------READ ADDRESS API----------------------------------");
		sessiontoken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, ApplicationConstants.XApiClient.DESKTOP);
		JSONObject readAddressesResponse = CustomerUtil
				.getAllAddressesForCustomer(ApplicationConstants.XApiClient.DESKTOP, sessiontoken);
		JSONArray readAddressesResponseResult = readAddressesResponse.getJSONArray("result");
		log.info(readAddressesResponseResult);

		JSONObject addressObject = readAddressesResponseResult.getJSONObject(0);
		String addressObjectFirstName = addressObject.getString("firstName");
		String addressObjectLastName = addressObject.getString("lastName");
		log.info("First Name :" + addressObjectFirstName);
		log.info("Last Name :" + addressObjectLastName);

		// mongoDBAssertion(customerId,result);
	}

	@Test
	public void fetchProductAPI() throws Exception {
		System.out.println("--------------------------FETCH PRODUCT CATALOG API----------------------------------");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglass_new"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		log.info(productId);
		String fetchProductAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.FETCH_PRODUCT_API, productId);
		Response response = RestAssuredUtils.GET(fetchProductAPIUrl, HeadersUtil.headers_CatalogService(
				CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey), queryParams);
		log.info(response);
		Assert.assertEquals(response.jsonPath().getString("result.productId"), productId);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");

	}

	@Test
	public void getStoreCreditForAGivenCode() throws Exception {
		System.out.println(
				"--------------------------GET STORECREDIT MONEY SERVICE API----------------------------------");
		String orderId = OrderUtil.giveMeOrder(true, false, XApiClient.ANDROID, "cod", "sunglasses")
				.getJSONObject("order").getString("id");
		JSONObject sc = MoneyUtil.storeCreditProcessing(orderId, SessionUtil.createNewSession(), "amount", "1", 200);
		String code = sc.getJSONObject("result").getString("storeCode");
		String sessionToken = CustomerUtil.get_sessionId_after_user_authentication(ApplicationConstants.userEmail,
				ApplicationConstants.userPassword, XApiClient.ANDROID);
		HashMap<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("code", code);
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(
				MoneyPathConstants.GET_STORE_CREDIT_DETAILS_FOR_A_GIVEN_CODE, pathParams,
				HeadersUtil.headers_sessionToken(sessionToken));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.storeId"), "1");
		Assert.assertEquals(response.jsonPath().getString("result.storeCreditCode").substring(0, 3),
				code.substring(0, 3));
		Assert.assertEquals(response.jsonPath().getString("result.balance"), "1.0");
		Assert.assertEquals(response.jsonPath().getString("result.currency"),
				GenericUtil.CountryProperties("currency_code"));
		Assert.assertEquals(response.jsonPath().getString("result.status"), "Active");
		Assert.assertEquals(response.jsonPath().getString("result.customerEmail"), ApplicationConstants.userEmail);
		Assert.assertNotNull(response.jsonPath().getLong("result.expiredAt"));
	}

	@Test
	public void updatePaymentInOrderAndPaymentFromSCM() throws Exception {
		System.out.println(
				"--------------------------CREATE MARKETPLACE ORDER, ORDERPAYMENT SERVICE API----------------------------------");
		JSONObject orderPaymentResponse = OrderUtil.giveMeOrder(false, false, XApiClient.ANDROID, "cc", "eyeglasses");
		log.info(orderPaymentResponse);
		String orderId = orderPaymentResponse.getJSONObject("order").getString("id");
		String URL = Environments.SERVICES_ENVIRONMENT
				+ String.format(OrderPaymentConstants.UPDATE_PAYMENT_PATH, orderId);
		List<NameValuePair> headers = HeadersUtil.headers(SessionUtil.createNewSession(), XApiClient.VSM,
				ApplicationConstants.xAuthToken);
		JSONObject request = new JSONObject();
		JSONObject paymentGatewayInfo = new JSONObject();
		paymentGatewayInfo.put("gatewayName", "COD");
		paymentGatewayInfo.put("paymentMethod", "COD");
		request.put("paymentGatewayInfo", paymentGatewayInfo);
		request.put("url", "test.com");
		request.put("httpMethod", "PUT");
		request.put("contentType", "application/json");
		HttpResponse httpResponse = RequestUtil.putRequest(URL, headers, request);
		JSONObject response = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);
		Assert.assertEquals(response.getJSONObject("result").getBoolean("success"), true);
	}

	@Test
	public void syncStatus() throws Exception {
		System.out.println("--------------------------SYNC STATUS ORDER SERVICE API----------------------------------");

		String sessionToken = SessionUtil.createNewSession();
		String orderId = OrderUtil.giveMeOrder(false, false, Environments.X_API_CLIENT, "cod", "sunglasses")
				.getJSONObject("order").getString("id");
		JSONObject order_details = OrderUtil.getOrder(sessionToken, XApiClient.POS_IOS, orderId,
				ApplicationConstants.xAuthToken);
		log.info(order_details);
		JSONArray itemsArray = order_details.getJSONObject("result").getJSONArray("items");
		String itemID = itemsArray.getJSONObject(0).getString("id");
		String itemLevelState = null;
		try {
			itemLevelState = itemsArray.getJSONObject(0).getJSONObject("status").getString("state");
		} catch (JSONException e) {
			if (itemsArray.length() == 1) {
				itemLevelState = order_details.getJSONObject("result").getJSONObject("status").getString("state");
			}
		}
		if (itemLevelState.equals("PROCESSING")) {
			OrderCancellation.syncStatusAPI(sessionToken, orderId, itemID, "CLOSED", "ORDER_NOT_CONFIRMED", false,
					false);
		} else if (itemLevelState.equals("NEW")) {
			OrderCancellation.syncStatusAPI(sessionToken, orderId, itemID, "CANCELED", "CANCELED", false, false);
		} else if (itemLevelState.equals("HOLDED")) {
			OrderCancellation.syncStatusAPI(sessionToken, orderId, itemID, "CLOSED", "ORDER_NOT_CONFIRMED", false,
					false);
		}
	}

	// Get v5 payment-methods API
	@Test
	public void getPaymentMethodsV5() throws Exception {
		log.info("-----------------V5 Payment Method API------------------");
		String sessiontoken = SessionUtil.createNewSession();
		String telephone = ApplicationConstants.userMobileNumber;
		String mobileLoginSession = CustomerUtil
				.mobileAuthenticateWithoutReferCode(ApplicationConstants.XApiClient.ANDROID, sessiontoken,
						ApplicationConstants.ReturnMasterOtp.returnMasterOtp(), telephone)
				.getJSONObject("result").getString("token");
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_JJ"), ApplicationConstants.XApiClient.ANDROID);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		CartUtil.clearCart(mobileLoginSession);
		CartItem reqObj = new CartItem();
		reqObj.setPowerType(ApplicationConstants.PowerTypes.SINGLE_VISION);
		reqObj.setPackageId(product.get("packageId"));
		reqObj.setProductId(Long.parseLong(product.get("productId")));
		CartUtil.createCart(mobileLoginSession, reqObj, ApplicationConstants.XApiClient.ANDROID);
		// CartUtil.createCart(mobileLoginSession, reqObj,
		// ApplicationConstants.XApiClient.ANDROID);
		Address address = ApplicationUtil.saveShippingAddress();
		CartUtil.saveAddress(mobileLoginSession, ApplicationConstants.XApiClient.ANDROID, address, null);
		JSONObject paymentMethodsResponse = PaymentUtil.getAllPaymentMethodsv5(mobileLoginSession,
				ApplicationConstants.XApiClient.ANDROID);
		log.info("PaymentMethods**" + paymentMethodsResponse);
		JSONObject result = paymentMethodsResponse.getJSONObject("result");
		Assert.assertEquals(result.getBoolean("packageAvailForCod"), true, "packageAvailForCod is not correct");
		// Assert.assertEquals(result.getString("defaultMethodCode"), "cc",
		// "defaultMethodCode is not correct");
		Assert.assertEquals(paymentMethodsResponse.getInt("status"), 200, "status is not same");

		JSONArray payMethodsArray = result.getJSONArray("paymentMethods");
		log.info("Different Payment Methods");
		for (int n = 0; n < payMethodsArray.length(); n++) {
			JSONObject objec = payMethodsArray.getJSONObject(n).getJSONArray("methods").getJSONObject(0);
			log.info("Payment Method " + objec.getString("code") + "   enabled: " + objec.getBoolean("enabled"));

			if (objec.getString("code").equals("po")) {
				Assert.assertEquals(objec.getBoolean("enabled"), false, "For loggedIn user sc and po should be false");
			}
			if (objec.getString("code").equals("partialpayment") || objec.getString("code").equals("sm")
					|| objec.getString("code").equals("exchangep")) {
				Assert.assertEquals(objec.getBoolean("enabled"), false,
						"Payment method" + objec.getString("code") + "should be false");
			}
			if (objec.getString("code").equals("sc") || objec.getString("code").equals("gv")
					|| objec.getString("code").equals("cc") || objec.getString("code").equals("dc")
					|| objec.getString("code").equals("nb") || objec.getString("code").equals("lenskartwallet")) {
				Assert.assertEquals(objec.getBoolean("enabled"), true,
						"For loggedIn user sc ,gv,cc,nb,dc  code:" + objec.getString("code"));
			}
		}
	}

	// Product Doc Search
	@Test
	public void productDocumentSearchAPI() throws Exception {
		log.info("-----------------GET PRODUCT DOC SEARCH API------------------");
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.PRODUCT_DOCUMENT_SEARCH_API;
		;

		HashMap<String, String> queryparam = new HashMap<String, String>();
		queryparam.put("productIds", "119237,93870,209422");
		queryparam.put("page", "0");
		queryparam.put("size", "20");
		queryparam.put("country", ApplicationConstants.Countries.INDIA);

		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.size"), "3",
				"total number of products is not correct");

	}

	@Test
	public void fetchLKCountries() throws Exception {
		log.info("-----------------FETCH LKCOUNTRIES UTILITY API------------------");
		FetchLKCountriesUseCases fetchLKCountriesUseCases = new FetchLKCountriesUseCases();
		fetchLKCountriesUseCases.fetchLkCountries_sucessful_usecase();
	}
	
	@Test
	public void fetchAllZonesAPI() throws Exception {
		log.info("-----------------FETCH ALL ZONES HTO SERVICE API------------------");
		FetchAllZonesAPI FetchAllZonesAPI = new FetchAllZonesAPI();
		FetchAllZonesAPI.fetchAllZonesAPIWithoutClient();
	}

	@AfterClass
	public void printAllOrders() {
		orderIds.entrySet().forEach(entry -> {
			log.info(entry.getKey() + " " + entry.getValue());
		});
	}
}
