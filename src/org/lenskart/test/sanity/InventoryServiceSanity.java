package org.lenskart.test.sanity;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.InventoryPathConstant;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.HeadersUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.SessionUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.lenskart.juno.schema.product.ScmInventoryRequest;
import com.lenskart.juno.schema.v2.common.Power;
import com.utilities.GenericUtil;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;

public class InventoryServiceSanity {

	private static final Logger log = GenericUtil.InitLogger(InventoryServiceSanity.class);
	private static JSONObject object;
	private static String[] request_values = new String[3];
	private static String sessiontoken;
	private static String x_Api_Client = XApiClient.ANDROID;
	private static HashMap<String, String> product = new HashMap<>();
	private static String productID1;
	private static String productID2;
	private static String transaction_ID;
	private BigDecimal initial_pro_qty;
	private BigDecimal initial_pro_qty_issue;
	private static boolean db_validation = Environments.dbConnectionFlag;
	private MySQLConnectionUtility mysqlConnectionObject = null;

	@BeforeClass
	public void setup() throws Exception {
		if (db_validation) {
			PropertyFactory pf = new PropertyFactory("InventoryService");
			mysqlConnectionObject = new MySQLConnectionUtility(pf);
			mysqlConnectionObject.initMySQLConnection();
		}
	}

	@AfterClass
	public void destroy() throws Exception {
		if (db_validation) {
			mysqlConnectionObject.closeMySQLConnection();
		}
	}

	private String executeSqlQuery(String sql) throws SQLException {
		String output = mysqlConnectionObject.executeSelectQuery(sql);
		return output.replaceAll("\n", "").replaceAll("\r", "");
	}

	private String get_transactionID_details_from_inventory_transaction(String transactionId) throws SQLException {
		String sql = "select * from inventory_transaction where transaction_id=" + transactionId;
		String output = mysqlConnectionObject.executeSelectQuery(sql);
		return output;
	}

	private String get_column_value(String value, String column_name, String table_name,
			String corresponding_column_name) throws SQLException {
		String sql = "select " + column_name + " from " + table_name + " where " + corresponding_column_name + "="
				+ value;
		String output = mysqlConnectionObject.executeSelectQuery(sql);
		return output.replaceAll("\n", "").replaceAll("\r", "");
	}

	private void create_inventory_transaction() throws Exception {
		String RequstUrlCreateInventory = Environments.SERVICES_ENVIRONMENT
				+ InventoryPathConstant.CREATE_INVENTORY_TRANSACTION_PATH;
		System.out.println(
				"--------------------------Create inventory transaction both issue and add----------------------------------");

		System.out.println("Request Header for Post:" + RequstUrlCreateInventory);
		String OrderID = GenericUtil.createRandomNumber(7);
		object = new JSONObject();
		object.put("orderId", OrderID);
		object.put("storeId", 1);
		JSONArray item = new JSONArray();
		JSONObject object1 = new JSONObject();
		JSONObject jsonResponse_category1 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_VC"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category1, "", false);
		productID1 = product.get("productId");
		object1.put("productId", Integer.parseInt(productID1));
		object1.put("quantity", 1);
		object1.put("transactionType", "ADD");
		// object1.put("hubCode", "LKH03");

		item.put(object1);
		JSONObject object2 = new JSONObject();
		JSONObject jsonResponse_category2 = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_VC"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category2, "", false);
		productID2 = product.get("productId");
		object2.put("productId", Integer.parseInt(productID2));
		object2.put("quantity", 1);
		object2.put("transactionType", "ISSUE");
		// object2.put("hubCode", "LKH03");

		item.put(object2);
		object.put("itemArray", item);

		if (db_validation) {
			String initial_qty_product_added = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID1
							+ " and website_id=1");
			initial_pro_qty = new BigDecimal(initial_qty_product_added);
			String initial_qty_product_issue = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID2
							+ " and website_id=1");
			initial_pro_qty_issue = new BigDecimal(initial_qty_product_issue);
			System.out.println("initial_pro_qty_issue" + initial_qty_product_issue);
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreateinventory = RequestUtil.postRequest(RequstUrlCreateInventory, headers, object);
		JSONObject responseJson_Createinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreateinventory);
		Assert.assertEquals(httpResponsecreateinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");

		JSONObject responseJson_Createinventory = responseJson_Createinventory_result.getJSONObject("result");
		Assert.assertEquals(object.getString("orderId"), responseJson_Createinventory.getString("transactionId"),
				"transactionId is not same");
		Assert.assertEquals(responseJson_Createinventory.getString("status"), "SUCCESS", "status is not correct");
		Assert.assertNotNull(responseJson_Createinventory.getJSONArray("itemArray"), "itemArray is null");
		Assert.assertEquals(object.getString("orderId"), responseJson_Createinventory.getString("orderId"),
				"orderId is not correct");
		Assert.assertEquals(object.getString("storeId"), responseJson_Createinventory.getString("storeId"),
				"storeId is not correct");
		String qty_product_added_after_response = null;
		String qty_product_issue_after_response = null;
		if (db_validation) {
			transaction_ID = get_transactionID_details_from_inventory_transaction(
					responseJson_Createinventory.getString("transactionId"));

			qty_product_added_after_response = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID1
							+ " and website_id=1");
			BigDecimal response_pro_qty_added = new BigDecimal(qty_product_added_after_response);

			qty_product_issue_after_response = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID2
							+ " and website_id=1");
			BigDecimal response_pro_qty_issue = new BigDecimal(qty_product_issue_after_response);

			Assert.assertEquals(response_pro_qty_added, initial_pro_qty.add(new BigDecimal(1)),
					"product is not getting added");
			Assert.assertEquals(response_pro_qty_issue.add(new BigDecimal(1)), initial_pro_qty_issue,
					"product is not issued");

		}
		request_values[0] = responseJson_Createinventory.getString("transactionId");
		request_values[1] = qty_product_added_after_response;
		request_values[2] = qty_product_issue_after_response;

	}

	@Test(enabled = true)
	public void get_Inventory_valid_product_store() throws Exception {
		String qty_cataloginventory_stock_status = null, qty_cataloginventory_stock_item = null;
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		String product_id = product.get("productId");
		String RequstUrlGetInventory = Environments.SERVICES_ENVIRONMENT
				+ String.format(InventoryPathConstant.GET_INVENTORY_PATH, product_id);
		System.out.println(
				"--------------------------GET INVENTORY DETAILS WHEN VAILD PRODUCT AND STORE ID 1----------------------------------");
		System.out.println("Request Header for Get:" + RequstUrlGetInventory);

		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("storeid", "1"));

		if (db_validation) {
			qty_cataloginventory_stock_status = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + product_id
							+ " and website_id=1");
			qty_cataloginventory_stock_item = executeSqlQuery(
					"select qty from cataloginventory_stock_item where product_id=" + product_id + " and stock_id=1");
			Assert.assertEquals(qty_cataloginventory_stock_status, qty_cataloginventory_stock_item,
					"Both the table in Database is not same");
			System.out.println("ajdjd");
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsegetinventory = RequestUtil.getRequest(RequstUrlGetInventory, headers, queryparam);
		JSONObject responseJson_Getinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsegetinventory);
		Assert.assertEquals(httpResponsegetinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_Getinventory = responseJson_Getinventory_result.getJSONObject("result");
		Assert.assertNotNull(responseJson_Getinventory.getString("productId"), "product id cannot be null");
		Assert.assertNotNull(responseJson_Getinventory.getString("storeId"), "store id cannot be null");
		if (db_validation) {
			if (executeSqlQuery("select stock_status from cataloginventory_stock_status where product_id=" + product_id
					+ " and website_id=1").equals("1")) {
				Assert.assertEquals(responseJson_Getinventory.getBoolean("stockStatus"), true,
						"no quatity is available");
				Assert.assertEquals((int) responseJson_Getinventory.getDouble("quantity"),
						(int) Double.parseDouble(qty_cataloginventory_stock_item), "no of quatity is not correct");
			} else {
				System.out.println("StockStatus is 0 means stock not available");
				Assert.assertEquals(responseJson_Getinventory.getBoolean("stockStatus"), false,
						"no quatity is available");
				Assert.assertEquals(responseJson_Getinventory.getString("quantity"), "0", "no quatity is available");
			}
		}
	}

	@Test(enabled = true)
	public void create_Inventory_transaction_ADD() throws Exception {

		String RequstUrlCreateInventory = Environments.SERVICES_ENVIRONMENT
				+ InventoryPathConstant.CREATE_INVENTORY_TRANSACTION_PATH;
		System.out.println(
				"--------------------------Create inventory transaction only add----------------------------------");
		System.out.println("Request Header for Post:" + RequstUrlCreateInventory);

		String OrderID = GenericUtil.createRandomNumber(6);
		JSONObject object = new JSONObject();
		object.put("orderId", OrderID);
		object.put("storeId", "1");
		JSONArray item = new JSONArray();
		JSONObject object1 = new JSONObject();
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_sunglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		productID1 = product.get("productId");
		object1.put("productId", productID1);
		object1.put("quantity", "3");
		object1.put("transactionType", "ADD");
		// object1.put("hubCode", "LKH03");

		item.put(object1);
		object.put("itemArray", item);

		if (db_validation) {
			String intital_qty_product = executeSqlQuery(
					"select qty from cataloginventory_stock_item where product_id=" + productID1 + " and stock_id=1");
			initial_pro_qty = new BigDecimal(intital_qty_product);
			log.info("sdfsf" + initial_pro_qty);
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecreateinventory = RequestUtil.postRequest(RequstUrlCreateInventory, headers, object);

		Assert.assertEquals(httpResponsecreateinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_Createinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreateinventory);
		JSONObject responseJson_Createinventory = responseJson_Createinventory_result.getJSONObject("result");
		Assert.assertEquals(object.getString("orderId"), responseJson_Createinventory.getString("transactionId"),
				"transactionId is not same");
		Assert.assertEquals(responseJson_Createinventory.getString("status"), "SUCCESS", "status is not correct");
		Assert.assertNotNull(responseJson_Createinventory.getJSONArray("itemArray"), "itemArray is null");
		Assert.assertEquals(object.getString("orderId"), responseJson_Createinventory.getString("orderId"),
				"orderId is not correct");
		Assert.assertEquals(object.getString("storeId"), responseJson_Createinventory.getString("storeId"),
				"storeId is not correct");
		if (db_validation) {
			String TransactionID = get_transactionID_details_from_inventory_transaction(
					responseJson_Createinventory.getString("transactionId"));
			log.info(TransactionID);
			Assert.assertNotNull(TransactionID, "TransactionID is not created in database table");

			String qty_product_after_response = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID1
							+ " and website_id=1");
			log.info(executeSqlQuery("select qty from cataloginventory_stock_status where product_id=" + productID1
					+ " and website_id=1"));
			log.info(qty_product_after_response);

			BigDecimal response_pro_qty = new BigDecimal(qty_product_after_response);
			log.info("sdfdf" + response_pro_qty);
			log.info(object.getJSONArray("itemArray").getJSONObject(0).getString("quantity"));

			/*
			 * Commenting this line bcz not sure if it's getting updated in
			 * cataloginventory_stock_status as well now
			 */
			// Assert.assertEquals(response_pro_qty,
//					initial_pro_qty.add(
//							new BigDecimal(object.getJSONArray("itemArray").getJSONObject(0).getString("quantity"))),
//					"product is not getting added");
		}
	}

	@Test(enabled = true)
	public void create_Inventory_transaction_productId_Issue() throws Exception {
		String RequstUrlCreateInventory = Environments.SERVICES_ENVIRONMENT
				+ InventoryPathConstant.CREATE_INVENTORY_TRANSACTION_PATH;
		System.out.println(
				"--------------------------Create inventory transaction only issue----------------------------------");

		System.out.println("Request Header for Post:" + RequstUrlCreateInventory);
		String OrderID = GenericUtil.createRandomNumber(6);
		JSONObject object = new JSONObject();
		object.put("orderId", OrderID);
		object.put("storeId", "1");
		JSONArray item = new JSONArray();
		JSONObject object1 = new JSONObject();
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		productID1 = product.get("productId");
		object1.put("productId", productID1);
		object1.put("quantity", "1");
		object1.put("transactionType", "ISSUE");
		// object1.put("hubCode", "LKH03");

		item.put(object1);
		object.put("itemArray", item);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		if (db_validation) {

			String initial_qty_product = executeSqlQuery(
					"select qty from cataloginventory_stock_item where product_id=" + productID1 + " and stock_id=1");
			initial_pro_qty = new BigDecimal(initial_qty_product);
		}

		HttpResponse httpResponsecreateinventory = RequestUtil.postRequest(RequstUrlCreateInventory, headers, object);
		Assert.assertEquals(httpResponsecreateinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_Createinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecreateinventory);
		JSONObject responseJson_Createinventory = responseJson_Createinventory_result.getJSONObject("result");

		Assert.assertEquals(object.getString("orderId"), responseJson_Createinventory.getString("transactionId"),
				"transactionId is not same");
		Assert.assertEquals(responseJson_Createinventory.getString("status"), "SUCCESS", "status is not correct");
		Assert.assertNotNull(responseJson_Createinventory.getJSONArray("itemArray"), "itemArray is null");
		Assert.assertEquals(object.getString("orderId"), responseJson_Createinventory.getString("orderId"),
				"orderId is not correct");
		Assert.assertEquals(object.getString("storeId"), responseJson_Createinventory.getString("storeId"),
				"storeId is not correct");
		if (db_validation) {
			String TransactionID = get_transactionID_details_from_inventory_transaction(
					responseJson_Createinventory.getString("transactionId"));
			Assert.assertNotNull(TransactionID, "TransactionID is not created in database table");

			String qty_product_after_response = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID1
							+ " and website_id=1");
			BigDecimal response_pro_qty = new BigDecimal(qty_product_after_response);
			Assert.assertEquals(
					response_pro_qty.add(
							new BigDecimal(object.getJSONArray("itemArray").getJSONObject(0).getString("quantity"))),
					initial_pro_qty, "product is not issued");
		}

	}

	@Test(enabled = true)
	public void check_Inventory_transaction() throws Exception {

		String RequstUrlCheckInventory = Environments.SERVICES_ENVIRONMENT
				+ InventoryPathConstant.CHECK_INVENTORY_TRANSACTION_PATH;
		System.out.println(
				"--------------------------Create inventory transaction only add----------------------------------");
		System.out.println("Request Header for Post:" + RequstUrlCheckInventory);

		String OrderID = GenericUtil.createRandomNumber(6);
		JSONObject object = new JSONObject();
		object.put("orderId", OrderID);
		object.put("storeId", "1");
		object.put("transactionId", OrderID);
		object.put("status", "SUCCESS");
		JSONArray item = new JSONArray();
		JSONObject object1 = new JSONObject();
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_aqualens"), ApplicationConstants.XApiClient.ANDROID);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		productID1 = product.get("productId");
		object1.put("productId", productID1);
		object1.put("quantity", "1");
		object1.put("transactionType", "ADD");
		// object1.put("hubCode", "LKH03");
		item.put(object1);
		object.put("itemArray", item);

		if (db_validation) {
			String intital_qty_product = executeSqlQuery(
					"select qty from cataloginventory_stock_item where product_id=" + productID1 + " and stock_id=1");
			initial_pro_qty = new BigDecimal(intital_qty_product);
			log.info("sdfsf" + initial_pro_qty);
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponsecheckinventory = RequestUtil.postRequest(RequstUrlCheckInventory, headers, object);
		JSONObject responseJson_Checkinventory = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsecheckinventory);
		log.info(responseJson_Checkinventory);

		Assert.assertEquals(httpResponsecheckinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");

		Assert.assertEquals(responseJson_Checkinventory.getString("status"), "200",
				"Status in API response is incorrect");
		Assert.assertTrue(responseJson_Checkinventory.has("traceId"), "Trace Id is not present in the response");

	}

	@Test(enabled = true)
	public void rollback_Inventory_Transaction() throws Exception {
		create_inventory_transaction();
		transaction_ID = request_values[0];
		String RequstUrlRollbackTransaction = Environments.SERVICES_ENVIRONMENT
				+ String.format(InventoryPathConstant.ROLLBACK_INVENTORY_TRANSACTION_PATH, transaction_ID);
		System.out
				.println("--------------------------Rollback Inventory Transaction----------------------------------");
		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("status", "rolledback"));
		System.out.println("Request Header for Put:" + RequstUrlRollbackTransaction);

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));

		HttpResponse httpResponserollbackinventory = RequestUtil.putRequest(RequstUrlRollbackTransaction, queryparam,
				headers);
		Assert.assertEquals(httpResponserollbackinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_Rollbackinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponserollbackinventory);
		JSONObject responseJson_Rollbackinventory = responseJson_Rollbackinventory_result.getJSONObject("result");
		Assert.assertEquals(responseJson_Rollbackinventory.getString("transactionId"), request_values[0],
				"Transaction Id does not exist");
		Assert.assertEquals(responseJson_Rollbackinventory.getString("status"), "rolledback",
				"Transaction is not rolled back");
		Assert.assertNotNull(responseJson_Rollbackinventory.getString("itemArray"), "itemArray is null");

		if (db_validation) {
			String Status_inventory_transaction = get_column_value(transaction_ID, "status", "inventory_transaction",
					"transaction_id");
			Assert.assertEquals(Status_inventory_transaction, "ROLLEDBACKROLLEDBACK",
					"Transaction is not rolledBacked");

			String qty_product_added_after_response = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID1
							+ " and website_id=1");
			BigDecimal response_pro_qty_added = new BigDecimal(qty_product_added_after_response);

			String qty_product_issue_after_response = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + productID2
							+ " and website_id=1");
			BigDecimal response_pro_qty_issue = new BigDecimal(qty_product_issue_after_response);

			Assert.assertEquals(response_pro_qty_added.add(new BigDecimal(1)), new BigDecimal(request_values[1]),
					"product is not getting added");
			Assert.assertEquals(response_pro_qty_issue, new BigDecimal(request_values[2]).add(new BigDecimal(1)),
					"product is not issued");
		}

	}

	@Test
	public void getNonJitInventory() throws Exception {
		String shellId;
		String URL = Environments.SERVICES_ENVIRONMENT + InventoryPathConstant.POST_NONJIT_INVENTORY_PATH;
		log.info("URL " + URL);
		List<NameValuePair> headers = HeadersUtil.headers(SessionUtil.createNewSession());
		JSONArray payload = new JSONArray();
		ScmInventoryRequest request = new ScmInventoryRequest();
		request.setClassification("CONTACT_LENS");
		request.setIndex((long) 0);
		Power left = new Power();
		left.setSph("0.00");
		left.setBoxes(1);
		request.setLeft(left);
		Power right = new Power();
		right.setBoxes(1);
		right.setSph("0.00");
		request.setRight(right);

		JSONObject requestWithoutShellId = new JSONObject(request);
		if (db_validation) {
			shellId = executeSqlQuery(
					"select product_id from catalog_product_flat_1_extra_attributes where jit=0 limit 1");
			requestWithoutShellId.put("shell_id", shellId);
		} else {
			shellId = "38723";
			requestWithoutShellId.put("shell_id", shellId);
		}
		payload.put(requestWithoutShellId);
		HttpResponse httpResponse = RequestUtil.postRequestFormData(URL, headers, null, payload);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response Status is not proper");
		JSONObject jsonResponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse).getJSONObject("result")
				.getJSONObject("result").getJSONObject("0");
		Assert.assertEquals(jsonResponse.getString("shell_id"), shellId);
		Assert.assertNotNull(jsonResponse.getJSONObject("left"));
		Assert.assertNotNull(jsonResponse.getJSONObject("right"));
	}

	// Get NonJITInventory JUNO
	@Test
	public void getNonJitInventoryJUNOAPI() throws Exception {
		String shellId;
		String URL = Environments.SERVICES_ENVIRONMENT + InventoryPathConstant.POST_NONJIT_INVENTORY_PATH_JUNO;
		log.info("URL " + URL);
		List<NameValuePair> headers = HeadersUtil.headers(SessionUtil.createNewSession());
		JSONArray payload = new JSONArray();
		ScmInventoryRequest request = new ScmInventoryRequest();
		request.setClassification("CONTACT_LENS");
		request.setIndex((long) 0);
		Power left = new Power();
		left.setSph("0.00");
		left.setBoxes(1);
		request.setLeft(left);
		Power right = new Power();
		right.setBoxes(1);
		right.setSph("0.00");
		request.setRight(right);

		JSONObject requestWithoutShellId = new JSONObject(request);
		if (db_validation) {
			shellId = executeSqlQuery(
					"select product_id from catalog_product_flat_1_extra_attributes where jit=0 limit 1");
			requestWithoutShellId.put("shell_id", shellId);
		} else {
			shellId = "38723";
			requestWithoutShellId.put("shell_id", shellId);
		}
		payload.put(requestWithoutShellId);
		HttpResponse httpResponse = RequestUtil.postRequestFormData(URL, headers, null, payload);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response Status is not proper");
		JSONObject jsonResponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse).getJSONObject("result")
				.getJSONObject("result").getJSONObject("0");
		Assert.assertEquals(jsonResponse.getString("shell_id"), shellId);
		Assert.assertNotNull(jsonResponse.getJSONObject("left"));
		Assert.assertNotNull(jsonResponse.getJSONObject("right"));
	}

	@Test
	public void nonJitInverntoryPowerWiseIds() throws Exception {
		String URL = Environments.SERVICES_ENVIRONMENT + InventoryPathConstant.POST_NONJIT_INVENTORY_POWERWISEID_PATH;
		log.info("URL " + URL);
		List<NameValuePair> headers = HeadersUtil.headers(SessionUtil.createNewSession());
		HttpResponse httpResponse = RequestUtil.postRequestFormData(URL, headers, null,
				new JSONArray(Arrays.asList(90005912)));
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response Status is not proper");
		JSONObject jsonResponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse).getJSONObject("result")
				.getJSONArray("result").getJSONObject(0);
		Assert.assertEquals(jsonResponse.getString("product_id"), "90005912");
		Assert.assertNotNull(jsonResponse.getString("qty"));
	}

	// Get Active inventory
	@Test(enabled = true)
	public void getActiveInventoryAPI() throws Exception {
		String qty_cataloginventory_stock_status = null, qty_cataloginventory_stock_item = null;
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		String product_id = product.get("productId");
		String RequstUrlGetActiveInventory = Environments.SERVICES_ENVIRONMENT
				+ String.format(InventoryPathConstant.GET_ACTIVE_INVENTORY_PATH, product_id);
		System.out.println("--------------------------GET  ACTIVE INVENTORY----------------------------------");
		System.out.println("Request Header for Get:" + RequstUrlGetActiveInventory);

		if (db_validation) {
			qty_cataloginventory_stock_status = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + product_id
							+ " and website_id=1");
			qty_cataloginventory_stock_item = executeSqlQuery(
					"select qty from cataloginventory_stock_item where product_id=" + product_id + " and stock_id=1");
			Assert.assertEquals(qty_cataloginventory_stock_status, qty_cataloginventory_stock_item,
					"Both the table in Database is not same");
			System.out.println("ajdjd");
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("productIds", product_id));

		HttpResponse httpResponsegetActiveinventory = RequestUtil.getRequest(RequstUrlGetActiveInventory, headers,
				queryparam);
		JSONObject responseJson_GetActiveinventory_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsegetActiveinventory);
		Assert.assertEquals(httpResponsegetActiveinventory.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_GetActiveinventory = responseJson_GetActiveinventory_result.getJSONObject("result");
		Assert.assertNotNull(
				responseJson_GetActiveinventory.getJSONArray(product_id).getJSONObject(0).getString("productId"),
				"product id cannot be null");

		if (db_validation) {
			if (executeSqlQuery("select stock_status from cataloginventory_stock_status where product_id=" + product_id
					+ " and website_id=1").equals("1")) {
				Assert.assertEquals(responseJson_GetActiveinventory.getBoolean("stockStatus"), true,
						"no quatity is available");
				Assert.assertEquals((int) responseJson_GetActiveinventory.getDouble("quantity"),
						(int) Double.parseDouble(qty_cataloginventory_stock_item), "no of quatity is not correct");
			} else {
				System.out.println("StockStatus is 0 means stock not available");
				Assert.assertEquals(responseJson_GetActiveinventory.getBoolean("stockStatus"), false,
						"no quatity is available");
				Assert.assertEquals(responseJson_GetActiveinventory.getString("quantity"), "0",
						"no quatity is available");
			}
		}
	}

	//

	// Get Active inventory facility
	@Test(enabled = true)
	public void getActiveInventoryfacilityAPI() throws Exception {
		String qty_cataloginventory_stock_status = null, qty_cataloginventory_stock_item = null;
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		String product_id = product.get("productId");
		String RequstUrlGetActiveInventoryFacility = Environments.SERVICES_ENVIRONMENT
				+ String.format(InventoryPathConstant.GET_ACTIVE_INVENTORY_FACILITY_PATH, product_id);
		System.out.println("--------------------------GET  ACTIVE INVENTORY----------------------------------");
		System.out.println("Request Header for Get:" + RequstUrlGetActiveInventoryFacility);

		if (db_validation) {
			qty_cataloginventory_stock_status = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + product_id
							+ " and website_id=1");
			qty_cataloginventory_stock_item = executeSqlQuery(
					"select qty from cataloginventory_stock_item where product_id=" + product_id + " and stock_id=1");
			Assert.assertEquals(qty_cataloginventory_stock_status, qty_cataloginventory_stock_item,
					"Both the table in Database is not same");
			System.out.println("ajdjd");
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();

		HttpResponse httpResponsegetActiveinventoryFacility = RequestUtil
				.getRequest(RequstUrlGetActiveInventoryFacility, headers, queryparam);
		JSONObject responseJson_GetActiveinventoryFacility_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsegetActiveinventoryFacility);
		Assert.assertEquals(httpResponsegetActiveinventoryFacility.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONArray responseJson_GetActiveinventoryFacility = responseJson_GetActiveinventoryFacility_result
				.getJSONArray("result");
		Assert.assertNotNull(responseJson_GetActiveinventoryFacility.getJSONObject(0).getString("productId"),
				"product id cannot be null");

	}

	// Get Active inventory for powerWiseIds
	@Test(enabled = true)
	public void getActiveInventoryPowerwiseIDAPI() throws Exception {
		String qty_cataloginventory_stock_status = null, qty_cataloginventory_stock_item = null;
		JSONObject jsonResponse_category = JunoV1Util
				.getCategoryDetails(ApplicationUtil.getcategoryId("category_eyeglasses"), x_Api_Client);
		product = ApplicationUtil.getProductDetails(jsonResponse_category, "", false);
		String product_id = product.get("productId");
		String RequstUrlGetActiveInventoryPowerwiseID = Environments.SERVICES_ENVIRONMENT
				+ String.format(InventoryPathConstant.GET_ACTIVE_INVENTORY_POWERWISEID_PATH, product_id);
		System.out.println("--------------------------GET  ACTIVE INVENTORY----------------------------------");
		System.out.println("Request Header for Get:" + RequstUrlGetActiveInventoryPowerwiseID);

		if (db_validation) {
			qty_cataloginventory_stock_status = executeSqlQuery(
					"select qty from cataloginventory_stock_status where product_id=" + product_id
							+ " and website_id=1");
			qty_cataloginventory_stock_item = executeSqlQuery(
					"select qty from cataloginventory_stock_item where product_id=" + product_id + " and stock_id=1");
			Assert.assertEquals(qty_cataloginventory_stock_status, qty_cataloginventory_stock_item,
					"Both the table in Database is not same");
			System.out.println("ajdjd");
		}

		sessiontoken = SessionUtil.createNewSession();
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Session-Token", sessiontoken));
		headers.add(new BasicNameValuePair("X-Api-Client", x_Api_Client));
		headers.add(new BasicNameValuePair("X-B3-TraceId", RequestUtil.getRandomHexTraceId()));
		List<NameValuePair> queryparam = new ArrayList<NameValuePair>();
		queryparam.add(new BasicNameValuePair("powerWiseIds", "90124074"));

		HttpResponse httpResponsegetActiveinventoryPowerwiseID = RequestUtil
				.getRequest(RequstUrlGetActiveInventoryPowerwiseID, headers, queryparam);
		JSONObject responseJson_GetActiveinventoryPowerwiseID_result = RequestUtil
				.convertHttpResponseToJsonObject(httpResponsegetActiveinventoryPowerwiseID);
		Assert.assertEquals(httpResponsegetActiveinventoryPowerwiseID.getStatusLine().getStatusCode(), 200,
				"Response Status is not proper");
		JSONObject responseJson_GetActiveinventoryPowerwiseID = responseJson_GetActiveinventoryPowerwiseID_result
				.getJSONObject("result");
		Assert.assertNotNull(responseJson_GetActiveinventoryPowerwiseID.getJSONArray("90124074").getJSONObject(0)
				.getString("productId"), "product id cannot be null");

		if (db_validation) {
			if (executeSqlQuery("select stock_status from cataloginventory_stock_status where product_id=" + product_id
					+ " and website_id=1").equals("1")) {
				Assert.assertEquals(responseJson_GetActiveinventoryPowerwiseID.getBoolean("stockStatus"), true,
						"no quatity is available");
				Assert.assertEquals((int) responseJson_GetActiveinventoryPowerwiseID.getDouble("quantity"),
						(int) Double.parseDouble(qty_cataloginventory_stock_item), "no of quatity is not correct");
			} else {
				System.out.println("StockStatus is 0 means stock not available");
				Assert.assertEquals(responseJson_GetActiveinventoryPowerwiseID.getBoolean("stockStatus"), false,
						"no quatity is available");
				Assert.assertEquals(responseJson_GetActiveinventoryPowerwiseID.getString("quantity"), "0",
						"no quatity is available");
			}
		}
	}

}
