package org.lenskart.test.sanity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.Profiles;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.CatalogServicePathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CatalogUtil;
import org.lenskart.core.util.HeadersUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.UtilityUtil;
import org.lenskart.test.juno.catalog.FetchCatalogUserAPI;
import org.lenskart.test.juno.catalog.FetchProductCatalogMetadataAPI;
import org.lenskart.test.juno.catalog.GetAllPackagesAPI;
import org.lenskart.test.juno.catalog.GetAllUnbxdConfigAPI;
import org.lenskart.test.juno.catalog.GetCategoryAPI;
import org.lenskart.test.juno.catalog.GetCategorySampleCsvAPI;
import org.lenskart.test.juno.catalog.GetCoatingAPI;
import org.lenskart.test.juno.catalog.GetCoatingCsvExportAPI;
import org.lenskart.test.juno.catalog.GetCoatingCsvSampleAPI;
import org.lenskart.test.juno.catalog.GetCoatingFilterAPI;
import org.lenskart.test.juno.catalog.GetPackageCsvExportAPI;
import org.lenskart.test.juno.catalog.GetPackageCsvSampleAPI;
import org.lenskart.test.juno.catalog.GetPackageProductMappingAPI;
import org.lenskart.test.juno.catalog.GetPackageProductMappingByIdAPI;
import org.lenskart.test.juno.catalog.GetProductImagesSampleCsvAPI;
import org.lenskart.test.juno.catalog.GetProductTemplateMappingSampleCsvAPI;
import org.lenskart.test.juno.catalog.GetSKUsByPowerType;
import org.lenskart.test.juno.catalog.GetSampleCategoryProductMappingCsvAPI;
import org.lenskart.test.juno.catalog.GetSampleTemplateCsvAPI;
import org.lenskart.test.juno.catalog.GetcatalogMetadataAPI;
import org.testng.Assert;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.RestAssuredUtils;

import io.restassured.response.Response;

public class JunoCatalogServiceSanity {
	private static final Logger log = GenericUtil.InitLogger(JunoCatalogServiceSanity.class);
	private static MongoConnectionUtility mongo_dao = null;
	private static String mongoCollectionName;
	private static String uri = Environments.CATALOG_SERVICE;
	public static String xCatalogServiceId = "junocatalog";
	private static String xCatalogServicekey = "cWne1BWNW6";
	private static String xAuthToken = "8e8b0816-4c73-4f08-8f7d-022dcd186a91";
	private static String coatingId = "563ca5151314273042bc6266";
	private static String packageId = "570546668dadb4c657ed0997";
	private static String templateId = "611ca3917638d447f318c05a";
	private static String categoryName;
	private static String categoryId;
	private static String configId = "PRODUCT_TYPE";
	private static String packageIdFromDb;
	private static String coatingIdFromDb;
	private static String templateIdFromDb;
	private static String packageProductMappingId;
	private static String urlPathFromDb;
	private static String user = "<EMAIL>";
	private static String password = "junocatalogtesting";
	private static String categoryIdValue = "8254";
	private static String categoryIdForRule = "5284";
	private static String RuleIdValue = "615eb667260933167de1ad65";
	private static String liquidationValue = "65a76c7533774242a616521e";
	private static String liquidationId;
	private static String UrlRewriteId;

	public static String getCollectionName() {
		return mongoCollectionName;
	}

	public static void setCollectionName(String collectionName) {
		mongoCollectionName = collectionName;
	}

	// analytics-sync-controller
	@Test
	public void analyticsSyncAllPidsAPI() throws Exception {
		Response response = RestAssuredUtils.POST_Method(uri, CatalogServicePathConstants.ANALYTICS_SYNC_ALL_PIDS_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId,
						xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	// catalog-metadata-controller
	@Test
	public void getcatalogMetadataAPI() throws Exception {
		GetcatalogMetadataAPI GetcatalogMetadataAPI = new GetcatalogMetadataAPI();
		GetcatalogMetadataAPI.getMetadataAPI();
	}

	// catalog-product-controller
	@Test
	public void fetchProductAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglass_new"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		log.info(productId);
		String fetchProductAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.FETCH_PRODUCT_API, productId);
		Response response = RestAssuredUtils.GET(fetchProductAPIUrl, HeadersUtil.headers_CatalogService(
				CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey), queryParams);
		log.info(response);
		Assert.assertEquals(response.jsonPath().getString("result.productId"), productId);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");

	}
	@Test
	public void getExportCatalogProductsFilterwiseAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("countryCodes", ApplicationConstants.Countries.INDIA);
		queryParams.put("text", productId);
		Response response = RestAssuredUtils.GET(uri,
				CatalogServicePathConstants.GET_EXPORT_CATALOG_PRODUCT_FILTERWISE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (Environments.environment.equals(Profiles.PREPROD)) {
			Assert.assertTrue(response.asString().contains("Eyeglasses"), "Category is not Correct");
		}
	}

	@Test
	public void fetchCatalogProductFilterWiseAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("categoryId", ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		queryParams.put("mappedInCategory", "true");
		queryParams.put("pageNumber", "1");
		queryParams.put("pageSize", "30");
		Response response = RestAssuredUtils.GET(
				uri, CatalogServicePathConstants.FETCH_CATALOG_PRODUCT_FILTERWISE_API, HeadersUtil
						.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (response.jsonPath().get("result.totalCount").equals(0)) {
			log.info("No products found");
		} else {
			Assert.assertTrue(response.jsonPath().getList("result.products").size() >= 1);
			Assert.assertEquals(response.jsonPath().getString("result.products[0].category"), "Eyeglasses",
					"category is not Correct");
		}
	}

	@Test
	public void getCatalogMetadataAPI() throws Exception {
		FetchProductCatalogMetadataAPI FetchProductCatalogMetadataAPI = new FetchProductCatalogMetadataAPI();
		FetchProductCatalogMetadataAPI.getMetadataAPI();
	}

	// catalog-user-controller
	@Test
	public void fetchCatalogUserAPI() throws Exception {
		FetchCatalogUserAPI FetchCatalogUserAPI = new FetchCatalogUserAPI();
		FetchCatalogUserAPI.fetchCatalogUser();
	}

	@Test
	public void catalogUserLogin() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("id", user);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.LOGIN_USER_API,
				HeadersUtil.headers_CatalogService(xAuthToken, xCatalogServiceId, xCatalogServicekey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), user);
	}

	@Test
	public void catalogUserRegister() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("id", "junocatalogtesting" + GenericUtil.createRandomNumber(4) + "@gmail.com");
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.REGISTER_USER_API,
				HeadersUtil.headers_CatalogService(xAuthToken, xCatalogServiceId, xCatalogServicekey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test
	public void catalogUserUpdateAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("id", user);
		payload.put("isActive", true);
		JSONArray permissions = new JSONArray();
		permissions.put("VIEW");
		permissions.put("UPDATE");
		JSONArray readPermissionCountries = new JSONArray();
		readPermissionCountries.put(ApplicationConstants.Countries.INDIA);
		readPermissionCountries.put(ApplicationConstants.Countries.SINGAPORE);
		readPermissionCountries.put(ApplicationConstants.Countries.UAE);
		readPermissionCountries.put(ApplicationConstants.Countries.USA);
		JSONArray writePermissionCountries = new JSONArray();
		writePermissionCountries.put(ApplicationConstants.Countries.INDIA);
		writePermissionCountries.put(ApplicationConstants.Countries.SINGAPORE);
		writePermissionCountries.put(ApplicationConstants.Countries.UAE);
		writePermissionCountries.put(ApplicationConstants.Countries.USA);
		payload.put("permissions", permissions);
		payload.put("readPermissionCountries", readPermissionCountries);
		payload.put("writePermissionCountries", writePermissionCountries);
		Response response = RestAssuredUtils.PATCH(uri, CatalogServicePathConstants.UPDATE_USER_PATCH_API,
				HeadersUtil.headers_CatalogService(xAuthToken, xCatalogServiceId, xCatalogServicekey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}

	@Test(enabled = false)
	public void catalogUserProviderloginAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("id", user);
		payload.put("password", password);
		payload.put("isActive", true);
		JSONArray permissions = new JSONArray();
		permissions.put("VIEW");
		permissions.put("UPDATE");
		JSONArray readPermissionCountries = new JSONArray();
		readPermissionCountries.put(ApplicationConstants.Countries.INDIA);
		readPermissionCountries.put(ApplicationConstants.Countries.SINGAPORE);
		readPermissionCountries.put(ApplicationConstants.Countries.UAE);
		readPermissionCountries.put(ApplicationConstants.Countries.USA);
		JSONArray writePermissionCountries = new JSONArray();
		writePermissionCountries.put(ApplicationConstants.Countries.INDIA);
		writePermissionCountries.put(ApplicationConstants.Countries.SINGAPORE);
		writePermissionCountries.put(ApplicationConstants.Countries.UAE);
		writePermissionCountries.put(ApplicationConstants.Countries.USA);
		payload.put("permissions", permissions);
		payload.put("readPermissionCountries", readPermissionCountries);
		payload.put("writePermissionCountries", writePermissionCountries);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.PROVIDER__USER_LOGIN_API,
				HeadersUtil.headers_CatalogService(xAuthToken, xCatalogServiceId, xCatalogServicekey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), user);
	}

	@Test
	public void catalogUserAuthenticateAPI() throws Exception {
		JSONObject payload = new JSONObject();
		JSONArray permissionList = new JSONArray();
		permissionList.put("VIEW");
		permissionList.put("UPDATE");
		payload.put("permissionList", permissionList);
		Response response = RestAssuredUtils.PUT(
				uri, CatalogServicePathConstants.AUTHENTICATE_USER_API, HeadersUtil
						.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.permissions").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.isActive"), "true", "User is not Active");
	}

	@Test
	public void getCategoryCsvExportAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("text", "Mobile");
		queryParams.put("lkcountries", ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_CATEGORY_CSV_EXPORT_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("IN"), "LkCountry is not IN");
	}

	@Test
	public void getCategorySampleCsvAPI() throws Exception {
		GetCategorySampleCsvAPI GetCategorySampleCsvAPI = new GetCategorySampleCsvAPI();
		GetCategorySampleCsvAPI.getCategorySampleCsv();
	}

	@Test
	public void getCoatingAPI() throws Exception {
		GetCoatingAPI GetCoatingAPI = new GetCoatingAPI();
		GetCoatingAPI.getAllCoatings();
	}

	@Test
	public void getCoatingCsvExportAPI() throws Exception {
		GetCoatingCsvExportAPI GetCoatingCsvExportAPI = new GetCoatingCsvExportAPI();
		GetCoatingCsvExportAPI.getCoatingCsvExportAPI();
	}

	@Test
	public void getCoatingCsvSampleAPI() throws Exception {
		GetCoatingCsvSampleAPI GetCoatingCsvSampleAPI = new GetCoatingCsvSampleAPI();
		GetCoatingCsvSampleAPI.getCoatingSampleCsvAPI();
	}

	@Test
	public void getCoatingByCoatingIdAPI() throws Exception {
		String getCoatingAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_COATING_BY_COATINGID, coatingId);
		Response response = RestAssuredUtils.GET(uri, getCoatingAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), coatingId);
	}

	@Test
	public void getCoatingWithId() throws Exception {
		String getCoatingAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_COATING_DETAILS, coatingId);
		Response response = RestAssuredUtils.GET(uri, getCoatingAPIURL,
				HeadersUtil.headers_CatalogService(ApplicationConstants.xAuthToken,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), coatingId);
	}

	@Test
	public void getFilteredTemplatesAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("productId", productId);
		queryParams.put("templateType", CatalogUtil.PACKAGE);
		queryParams.put("isMappedInProduct", "true");
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_FILTERED_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (response.jsonPath().get("result.numberOfElements").equals(0)) {
			log.info("No Templates found");
		} else {
			Assert.assertTrue(response.jsonPath().getList("result.templates").size() >= 1);
		}
	}

	@Test
	public void getPackageByPackageId() throws Exception {
		String getPackageByPackageIdURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_PACKAGE_BY_PACKAGEID, packageId);
		Response response = RestAssuredUtils.GET(uri, getPackageByPackageIdURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), packageId);
	}

	@Test
	public void getAllPackagesAPI() throws Exception {
		GetAllPackagesAPI GetAllPackagesAPI = new GetAllPackagesAPI();
		GetAllPackagesAPI.getAllPackages();
	}

	@Test
	public void getPackageCsvExportAPI() throws Exception {
		GetPackageCsvExportAPI GetPackageCsvExportAPI = new GetPackageCsvExportAPI();
		GetPackageCsvExportAPI.getPackageCsvExportAPI();
	}

	@Test
	public void getPackageCsvSampleAPI() throws Exception {
		GetPackageCsvSampleAPI GetPackageCsvSampleAPI = new GetPackageCsvSampleAPI();
		GetPackageCsvSampleAPI.getPackageCsvSampleAPI();
	}

	@Test
	public void getPackagesByCoatingId() throws Exception {
		String getPackagesByCoatingIdAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_PACKAGES_BY_COATINGID, coatingId);
		Response response = RestAssuredUtils.GET(uri, getPackagesByCoatingIdAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.packages").size() >= 1);
	}

	@Test
	public void getSampleCategoryProductMappingCsvAPI() throws Exception {
		GetSampleCategoryProductMappingCsvAPI GetSampleCategoryProductMappingCsvAPI = new GetSampleCategoryProductMappingCsvAPI();
		GetSampleCategoryProductMappingCsvAPI.getSampleCategoryProductMappingCsv();
	}

	@Test
	public void getSampleTemplateCsv() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("type", CatalogUtil.POWER);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_TEMPLATE_CSV_SAMPLE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("true"), "isEnabled is not correct");
	}

	@Test
	public void getSKUsWithValidPowerType() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("powerType", ApplicationConstants.PowerTypes.SINGLE_VISION.toUpperCase());
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_SKU_WITH_POWERTYPE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);
	}

	@Test
	public void getTemplateByTemplateId() throws Exception {
		String getCoatingAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_TEMPLATE_BY_TEMPLATEID, templateId);
		Response response = RestAssuredUtils.GET(uri, getCoatingAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), templateId);
	}

	@Test
	public void getAllTemplates() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "40");
		queryParams.put("templateType", CatalogUtil.INVENTORY);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_ALL_TEMPLATES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (response.jsonPath().get("result.numberOfElements").equals(0)) {
			log.info("No Templates found");
		} else {
			Assert.assertTrue(response.jsonPath().getList("result.templates").size() >= 1);
			Assert.assertEquals(response.jsonPath().getString("result.templates[0].lkCountry"), "IN",
					"lkCountry is not correct");
		}
	}

	@Test
	public void fetchCatalogMetadataAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.FETCH_CATALOG_METADATA_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId,
						xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.filters").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.filters[0].type"), "radio", "type is not correct");
	}

	@Test
	public void getCategoryProductMappingAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("mapping-type", CatalogUtil.MANUAL);
		queryParams.put("pageNumber", "1");
		queryParams.put("pageSize", "10");
		String getCategoryProductMappingAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_CATEGORY_PRODUCT_MAPPING_API,
						ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		Response response = RestAssuredUtils.GET(getCategoryProductMappingAPIUrl, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.categoryProductMappingList").size() >= 1);
	}

	@Test
	public void getRuleByCategoryIdAPI() throws Exception {
		String getCategoryProductMappingAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_RULE_BY_CATEGORYID_API,
						ApplicationUtil.getcategoryId("category_sunglasses"));
		Response response = RestAssuredUtils.GET(getCategoryProductMappingAPIUrl, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		if (response.statusCode() != 200) {
			log.info("No Rule Found for the categoryId");
		} else {
			log.info("rulesName matched is : " + response.jsonPath().getString("result.ruleName"));
		}
	}

	@Test
	public void getCategoryPathAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		queryParams.put("text", ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_CATEGORY_PATH_API, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (response.statusCode() != 200) {
			log.info("category Path Not found");
		} else {
			log.info("categoryPath is : " + response.jsonPath().getString("result"));
		}
	}

	@Test
	public void getCategoryTreeAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("countryCode", ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_CATEGORY_TREE_API, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.childCategories").size() >= 1);
	}

	@Test
	public void getProductAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String getProductAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_PRODUCT_API, productId);
		Response response = RestAssuredUtils.GET(getProductAPIUrl, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("product_id"), productId);
	}

	@Test
	public void getProductTemplateMappingSampleCsv() throws Exception {
		Response response = RestAssuredUtils.GET(uri,
				CatalogServicePathConstants.GET_PRODUCT_TEMPLATE_MAPPING_SAMPLE_CSV_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId,
						xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("IN"), "LkCountry is not IN");
	}

	@Test
	public void getTemplatesByPackagesAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "1");
		queryParams.put("pageSize", "20");
		queryParams.put("powerType", ApplicationConstants.PowerTypes.SINGLE_VISION.toUpperCase());
		queryParams.put("sku", "pack_hydrophobic_premium_sv_sr_ipad_sg");
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_TEMPLATES_BY_PACKAGES_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId,
						xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.templates").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.templates[0].type"), "PACKAGE",
				"type is not correct");
	}

	@Test
	public void getFilteredPackagesAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("frameType", ApplicationConstants.FrameTypes.FULLRIM);
		queryParams.put("powerType", ApplicationConstants.PowerTypes.SINGLE_VISION.toUpperCase());
		queryParams.put("productType", ApplicationConstants.ProductTypes.EYEGLASSES);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_FILTERED_PACKAGES_API, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.packages").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.packages[0].powerType"), "SINGLE_VISION",
				"powerType is not correct");
	}

	@Test
	public void getProductCategoryMappingAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("mapping-type", CatalogUtil.MANUAL);
		queryParams.put("pageNumber", "1");
		queryParams.put("pageSize", "30");
		String getProductCategoryMappingAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_PRODUCT_CATEGORY_MAPPING_API, productId);
		Response response = RestAssuredUtils.GET(getProductCategoryMappingAPIUrl, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.categoryProductMappingList").size() >= 1);
	}

	@Test(enabled = false) // getting 500
	public void getPowerOptionsForProductsAndPowerTypeAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		String getPowerOptionsForProductsAndPowerTypeAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_POWEROPTIONS_FOR_PRODUCT_AND_POWERTYPE_API, productId);
		Response response = RestAssuredUtils.GET(getPowerOptionsForProductsAndPowerTypeAPIUrl, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.productId"), productId);
		Assert.assertEquals(response.jsonPath().getString("result.countryWisePowers[0].lkCountry"), "IN",
				"LkCountry is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.countryWisePowers").size() >= 1);
	}

	@Test
	public void getCoatingsFilterAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("enable", "true");
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "20");
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_COATINGS_FILTER_API, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.coatings").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.coatings[0].lkCountry"), "IN",
				"LkCountry is not correct");
	}

	@Test
	public void getCategoriesAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_CATEGORIES_API, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.categories").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.categories[0].lkCountry"), "IN",
				"LkCountry is not correct");
	}

	@Test
	public void getCategoryByCategoryIdAPI() throws Exception {
		String getCategoryByCategoryIdAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_CATEGORY_BY_CATEGORYID_API,
						ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		Response response = RestAssuredUtils.GET(getCategoryByCategoryIdAPIUrl, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"),
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
	}

	@Test
	public void getTemplateExportCSVAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("templateType", CatalogUtil.PACKAGE);
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_TEMPLATES_CSV_EXPORT_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId,
						xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("IN"), "LkCountry is not Correct");
	}

	@Test
	public void getSampleCSVForBulkCategoryProductMappingCsvUpload() throws Exception {
		Response response = RestAssuredUtils.GET(uri,
				CatalogServicePathConstants.GET_SAMPLE_CSV_FOR_BULK_CATEGORY_PRODUCT_MAPPING_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId,
						xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("ADD"), "Operation Type is not Correct");
	}

	@Test
	public void getAllRulesFilterByCountryAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.GET(
				uri, CatalogServicePathConstants.GET_ALL_RULES_FILTER_BY_COUNTRY_API, HeadersUtil
						.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result[0].lkCountry"), "IN", "LkCountry is not correct");
	}

	@Test
	public void getRuleByRuleIdAPI() throws Exception {
		String getPowerOptionsForProductsAndPowerTypeAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_RULE_BY_RULEID_API, RuleIdValue);
		Response response = RestAssuredUtils.GET(getPowerOptionsForProductsAndPowerTypeAPIUrl, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), RuleIdValue);
	}

	@Test
	public void getAllRulesFilterPagewiseAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("country", ApplicationConstants.Countries.INDIA);
		queryParams.put("page-number", "1");
		queryParams.put("page-size", "40");
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_ALL_RULES_PAGEWISE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId,
						xCatalogServicekey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.rules").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.rules[0].lkCountry"), "IN",
				"LkCountry is not correct");
	}

	@Test
	public void getCategoryProductMappingRulePanelAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri,
				CatalogServicePathConstants.GET_CATEGORY_PRODUCT_MAPPING_PANEL_API, HeadersUtil.headers_CatalogService(
						CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.conditionsAllowed.NUMBER").size() >= 1);
	}

	@Test
	public void createCategoryAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("alternateName", "testings");
		payload.put("canonical", "Testing in progress");
		payload.put("catImgClickLink", "Url not available");
		payload.put("description", "testing is under higher supervison");
		payload.put("h1", "Testing player");
		payload.put("isActive", true);
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("metaAlternate", "category testing");
		payload.put("metaCanonical", "category tested");
		payload.put("metaDescription", "category test");
		payload.put("metaKeywords", "juno testing");
		payload.put("metaTitle", "testification");
		payload.put("msiteBannerLink", "No banner link");
		payload.put("name", "My testing12345");
		payload.put("offerName", "No offers avaliable");
		payload.put("parentId", 2);
		payload.put("strikeoutAttributes", "No attributes");
		payload.put("urlKey", "iconic-jj-styles-jj-winter-collectionsh" + GenericUtil.createRandomNumber(4));
		JSONObject localeSpecificAttributes = new JSONObject();
		payload.put("localeSpecificAttributes", localeSpecificAttributes);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.CREATE_CATEGORY_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		categoryName = "My testing12345";
		categoryId = response.jsonPath().getString("result.id");
		urlPathFromDb = response.jsonPath().getString("result.urlKey");
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.name"), categoryName);
	}

	@Test(dependsOnMethods = "createCategoryAPI")
	public void updateCategoryAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("alternateName", "tested");
		payload.put("canonical", "Testing in progress");
		payload.put("catImgClickLink", "Url not available");
		payload.put("description", "testing is under higher supervison");
		payload.put("h1", "tested player");
		payload.put("isActive", true);
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("metaAlternate", "category tested");
		payload.put("metaCanonical", "category tested");
		payload.put("metaDescription", "category tested");
		payload.put("metaKeywords", "juno tested");
		payload.put("metaTitle", "testification");
		payload.put("msiteBannerLink", "No banner link");
		payload.put("name", "My testing12345");
		payload.put("offerName", "No offers avaliable");
		payload.put("parentId", 2);
		payload.put("strikeoutAttributes", "No attributes");
		payload.put("urlKey", urlPathFromDb);
		JSONObject localeSpecificAttributes = new JSONObject();
		payload.put("localeSpecificAttributes", localeSpecificAttributes);
		String updatecategoryApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_CATGEORY_API, categoryId);
		Response response = RestAssuredUtils.PUT(updatecategoryApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.name"), categoryName);
	}

	@Test()
	public void createCoatingAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("name", "junocatalogtesting01" + GenericUtil.randomString(9));
		payload.put("subtitle", "testing123");
		payload.put("title", "Mytesting123");
		payload.put("enable", true);
		payload.put("hoverImageUrl", "https://static.lenskart.com/images/cust_mailer/1-Aug-18/scratch-resistant.jpg");
		payload.put("imageUrl", "https://static.lenskart.com/images/cust_mailer/1-Aug-18/scratch-resistant.jpg");
		payload.put("marketPrice", 250);
		payload.put("lenskartPrice", 200);
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("currencyCode", "INR");
		payload.put("sortOrder", 10);
		payload.put("group", "testing12345");
		payload.put("description", "testing123");
		JSONObject localeSpecificAttributes = new JSONObject();
		JSONArray facilityCodes = new JSONArray();
		facilityCodes.put("DK02");
		payload.put("localeSpecificAttributes", localeSpecificAttributes);
		payload.put("facilityCodes", facilityCodes);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.CREATE_COATING_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject createcoatingpayload = payload;
		String coatingNameFromDB = (String) createcoatingpayload.get("name");
		log.info(coatingNameFromDB);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (Environments.dbConnectionFlag) {
			MongoConnectionUtility mongoConnectionObject = null;
			mongoConnectionObject = UtilityUtil.getMongoConnectionObject("readall");
			mongoConnectionObject.initMongoDBConnection();
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("name", coatingNameFromDB);
			List<Document> resultList = mongoConnectionObject.executeQuery("catalog", "coatings", object);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document.getString("_id"));
			String CategoryIdFromDB = document.getString("_id");
			coatingIdFromDb = CategoryIdFromDB.substring(9, CategoryIdFromDB.length() - 2);
			log.info(coatingIdFromDb);
			mongoConnectionObject.closeMongoConnection();
		}
	}

	@Test(dependsOnMethods = "createCoatingAPI")
	public void updateCoatingStatusAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("enable", true);
		JSONArray ids = new JSONArray();
		ids.put(coatingIdFromDb);
		payload.put("ids", ids);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.UPDATE_COATING__STATUS_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	// works only with dbconnection true
	@Test(dependsOnMethods = "createCoatingAPI", enabled = false)
	public void updateCoatingAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("name", "junocatalogtesting01" + GenericUtil.createRandomNumber(3));
		payload.put("subtitle", "tested");
		payload.put("title", "tested");
		payload.put("enable", true);
		payload.put("hoverImageUrl", "https://static.lenskart.com/images/cust_mailer/1-Aug-18/scratch-resistant.jpg");
		payload.put("imageUrl", "https://static.lenskart.com/images/cust_mailer/1-Aug-18/scratch-resistant.jpg");
		payload.put("marketPrice", 300);
		payload.put("lenskartPrice", 300);
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("currencyCode", "INR");
		payload.put("sortOrder", 10);
		payload.put("group", "tested");
		payload.put("description", "tested");
		JSONObject localeSpecificAttributes = new JSONObject();
		String updatecoatingApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_COATING_API, coatingIdFromDb);
		Response response = RestAssuredUtils.PUT(updatecoatingApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	// works only with dbconnection true
	@Test(dependsOnMethods = "updateCoatingAPI", enabled = false, priority = 1)
	public void deleteCoatingAPI() throws Exception {
		String deletecoatingApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.DELETE_COATING_API, coatingIdFromDb);
		Response response = RestAssuredUtils.DELETE(deletecoatingApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test(enabled = false)
	public void createPackageAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("sku", "pack_regular_anti" + GenericUtil.createRandomNumber(4));
		payload.put("name", "Regular Anti EyeGlare123");
		payload.put("imageUrl", "https://static.lenskart.com/images/cust_mailer/1-Aug-18/scratch-resistant.jpg");
		payload.put("powerType", "BIFOCAL");
		payload.put("frameType", "FULL_RIM");
		payload.put("productType", "SUNGLASSES");
		payload.put("group", "GroupAB");
		payload.put("label", "Standard Thickness");
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("storePackageName", "hjvjzhsc");
		payload.put("enable", "true");
		JSONArray facilityCodes = new JSONArray();
		facilityCodes.put("DK02");
		JSONArray coatings = new JSONArray();
		coatings.put("563ca5151314273042bc6266");
		coatings.put("563ca5151314273042bc6268");
		payload.put("coatings", coatings);
		JSONObject localeSpecificAttributes = new JSONObject();
		payload.put("localeSpecificAttributes", localeSpecificAttributes);
		JSONArray specifications = new JSONArray();
		JSONObject specifications1 = new JSONObject();
		specifications1.put("title", "Crack Resistant");
		specifications1.put("subtitle", "");
		specifications1.put("hoverImageUrl", "");
		specifications1.put("imageUrl", "https://static.lenskart.com/images/cust_mailer/12-Apr-18/Crack-resistant.png");
		specifications1.put("sortOrder", 2);
		specifications1.put("collapsedTitle", "Crack Resistant");
		specifications1.put("collapsedImageUrl", "");
		specifications1.put("enabled", true);
		specifications1.put("group", "SCRATCH_AND_CRACK_RESISTANT");
		specifications.put(specifications1);
		payload.put("specifications", specifications);
		payload.put("facilityCodes", facilityCodes);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.CREATE_PACKAGE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject createpackagepayload = payload;
		String packageSkuFromDB = (String) createpackagepayload.get("sku");
		log.info(packageSkuFromDB);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (Environments.dbConnectionFlag) {
			MongoConnectionUtility mongoConnectionObject = null;
			mongoConnectionObject = UtilityUtil.getMongoConnectionObject("readall");
			mongoConnectionObject.initMongoDBConnection();
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("sku", packageSkuFromDB);
			List<Document> resultList = mongoConnectionObject.executeQuery("catalog", "packages", object);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document.getString("_id"));
			String packageIdFromDB = document.getString("_id");
			packageIdFromDb = packageIdFromDB.substring(9, packageIdFromDB.length() - 2);
			log.info(packageIdFromDb);
			mongoConnectionObject.closeMongoConnection();
		}
	}

	// works only with dbconnection true
	@Test(dependsOnMethods = "createPackageAPI", enabled = false)
	public void updatePackageAPI() throws Exception {
		if (Environments.dbConnectionFlag) {
			JSONObject payload = new JSONObject();
			payload.put("sku", "pack_regular_anti" + GenericUtil.randomString(8));
			payload.put("name", "Regular Anti EyeGlare123");
			payload.put("imageUrl", "https://static.lenskart.com/images/cust_mailer/1-Aug-18/scratch-resistant.jpg");
			payload.put("powerType", "BIFOCAL");
			payload.put("frameType", "FULL_RIM");
			payload.put("productType", "SUNGLASSES");
			payload.put("group", "Group changed");
			payload.put("label", "Standard Thickness");
			payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
			payload.put("storePackageName", "updated");
			payload.put("subtitle", "");
			JSONArray facilityCodes = new JSONArray();
			facilityCodes.put("DK02");
			JSONArray coatings = new JSONArray();
			coatings.put("563ca5151314273042bc6266");
			coatings.put("563ca5151314273042bc6268");
			payload.put("coatings", coatings);
			JSONObject localeSpecificAttributes = new JSONObject();
			payload.put("localeSpecificAttributes", localeSpecificAttributes);
			JSONArray specifications = new JSONArray();
			JSONObject specifications1 = new JSONObject();
			specifications1.put("title", "Crack Resistant");
			specifications1.put("subtitle", "updated");
			specifications1.put("hoverImageUrl", "");
			specifications1.put("imageUrl",
					"https://static.lenskart.com/images/cust_mailer/12-Apr-18/Crack-resistant.png");
			specifications1.put("sortOrder", 2);
			specifications1.put("collapsedTitle", "Crack Resistant");
			specifications1.put("collapsedImageUrl", "updated");
			specifications1.put("enabled", true);
			specifications1.put("group", "SCRATCH_AND_CRACK_RESISTANT");
			specifications.put(specifications1);
			payload.put("specifications", specifications);
			payload.put("facilityCodes", facilityCodes);
			String updatePackageAPIUrl = Environments.CATALOG_SERVICE
					+ String.format(CatalogServicePathConstants.UPDATE_PACKAGE_API, packageIdFromDb);
			Response response = RestAssuredUtils.PUT(updatePackageAPIUrl,
					HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
							ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
							ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
					GenericUtil.jsonToString(payload));
			categoryName = response.jsonPath().getString("result.name");
			Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		}
	}

	// works only with dbconnection true
	@Test(dependsOnMethods = "updatePackageAPI", enabled = false, priority = 2)
	public void deletePackageAPI() throws Exception {
		if (Environments.dbConnectionFlag) {
			String deletepackageApiUrl = Environments.CATALOG_SERVICE
					+ String.format(CatalogServicePathConstants.DELETE_PACKAGE_API, packageIdFromDb);
			Response response = RestAssuredUtils.DELETE(deletepackageApiUrl,
					HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
							ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
							ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
			Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		}
	}

	@Test()
	public void createTemplateAPI() throws Exception {
		JSONArray platformEnabled = new JSONArray();
		platformEnabled.put("DESKTOP");
		platformEnabled.put("ANDROID");
		JSONArray packages = new JSONArray();
		JSONObject packages1 = new JSONObject();
		packages1.put("packageSku", "package_r_antiglare");
		packages1.put("sortOrder", 1);
		packages1.put("audienceType", "default,high_end,mid_end,low_end");
		packages1.put("marketPrice", 150.0);
		packages1.put("lenskartPrice", 120.0);
		packages1.put("actualMarketPrice", 150.0);
		packages1.put("actualLenskartPrice", 120.0);
		packages1.put("enabled", true);
		packages1.put("offerText", "myoffers");
		packages1.put("recommendationLabel1", "recommendationLabel1");
		packages1.put("recommendationLabel2", "recommendationLabel2");
		packages1.put("platformEnabled", platformEnabled);
		packages.put(packages1);
		JSONArray powerPackages = new JSONArray();
		JSONObject powerPackages1 = new JSONObject();
		powerPackages1.put("powerType", "SINGLE_VISION");
		powerPackages1.put("packages", packages);
		powerPackages.put(powerPackages1);
		JSONObject payload = new JSONObject();
		payload.put("name", "Eyeglasses_FullRim_JJ_SingleClicked_IN" + GenericUtil.createRandomNumber(5));
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("type", "PACKAGE");
		payload.put("powerPackages", powerPackages);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.CRETE_TEMPLATE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject createtemplatepayload = payload;
		String templateNameFromDB = (String) createtemplatepayload.get("name");
		log.info(templateNameFromDB);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (Environments.dbConnectionFlag) {
			MongoConnectionUtility mongoConnectionObject = null;
			mongoConnectionObject = UtilityUtil.getMongoConnectionObject("readall");
			mongoConnectionObject.initMongoDBConnection();
			Map<String, Object> object = new HashMap<String, Object>();

			object.put("name", templateNameFromDB);
			List<Document> resultList = mongoConnectionObject.executeQuery("catalog", "templates", object);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document.getString("_id"));
			String templateIdFromDB = document.getString("_id");
			templateIdFromDb = templateIdFromDB.substring(9, templateIdFromDB.length() - 2);
			log.info(templateIdFromDb);
			mongoConnectionObject.closeMongoConnection();
		}
	}

	// works only with dbconnection true
	@Test(dependsOnMethods = "createTemplateAPI", enabled = false)
	public void updateTemplateAPI() throws Exception {
		JSONArray platformEnabled = new JSONArray();
		platformEnabled.put("DESKTOP");
		platformEnabled.put("ANDROID");
		platformEnabled.put("IOS");
		platformEnabled.put("MOBILESITE");
		JSONArray packages = new JSONArray();
		JSONObject packages1 = new JSONObject();
		packages1.put("packageSku", "package_r_antiglare");
		packages1.put("sortOrder", 3);
		packages1.put("audienceType", "default,high_end,mid_end");
		packages1.put("marketPrice", 300.0);
		packages1.put("lenskartPrice", 210.0);
		packages1.put("actualMarketPrice", 0.0);
		packages1.put("actualLenskartPrice", 0.0);
		packages1.put("enabled", true);
		packages1.put("offerText", "myoffers");
		packages1.put("recommendationLabel1", "recommendationLabel111");
		packages1.put("recommendationLabel2", "recommendationLabel222");
		packages1.put("platformEnabled", platformEnabled);
		packages.put(packages1);
		JSONArray powerPackages = new JSONArray();
		JSONObject powerPackages1 = new JSONObject();
		powerPackages1.put("powerType", "SINGLE_VISION");
		powerPackages1.put("packages", packages);
		powerPackages.put(powerPackages1);
		JSONObject payload = new JSONObject();
		payload.put("name", "Eyeglasses_FullRim_JJ_SingleClicked_IN" + GenericUtil.createRandomNumber(2));
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("type", "PACKAGE");
		payload.put("powerPackages", powerPackages);
		String updateTemplateAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_TEMPLATE_API, templateIdFromDb);
		Response response = RestAssuredUtils.PUT(updateTemplateAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	// works only with dbconnection true
	@Test(dependsOnMethods = "updateTemplateAPI", enabled = false, priority = 3)
	public void deleteTemplateAPI() throws Exception {
		String deletepackageApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.DELETE_TEMPLATE_API, templateIdFromDb);
		Response response = RestAssuredUtils.DELETE(deletepackageApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test(enabled = false)
	public void resetPowerOptionAPI() throws Exception {
		String resetPowerOptionAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.RESET_POWER_OPTION_API, categoryIdValue);
		Response response = RestAssuredUtils.PUT(resetPowerOptionAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				"");
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result[0].success"), "true", "Success field is not correct");
	}

	@Test(enabled = false)
	public void resetPowerOptionCountryBaseAPI() throws Exception {
		String resetPowerOptionAPICountryBaseUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.RESET_POWER_OPTION_COUNTRY_BASE_API, categoryIdValue,
						ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.PUT(resetPowerOptionAPICountryBaseUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				"");
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), ApplicationConstants.Countries.INDIA);
	}

	@Test(enabled = false) // getting 500
	public void getPowerOptionAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String GetPowerOptionAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_POWER_OPTION_API, productId);
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		queryParams.put("powerType", "SPH");
		Response response = RestAssuredUtils.GET(GetPowerOptionAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.productId"), productId);
		Assert.assertTrue(response.jsonPath().getList("result.countryWisePowers").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.countryWisePowers[0].lkCountry"), "IN",
				"LkCountry is not correct");
	}

	@Test(enabled = false)
	public void updatePowerOptionAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String UpdatePowerOptionAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_POWER_OPTION_API,
						ApplicationUtil.getcategoryId("category_power_option"));
		JSONObject payload = new JSONObject();
		JSONArray options = new JSONArray();
		JSONObject optionsObject = new JSONObject();
		optionsObject.put("title", "-12.25");
		optionsObject.put("sortOrder", 4);
		options.put(optionsObject);
		JSONArray powerOptions = new JSONArray();
		JSONObject powerOptionsObject = new JSONObject();
		powerOptionsObject.put("type", "SPH");
		powerOptionsObject.put("label", "Sph");
		powerOptionsObject.put("inputType", "drop_down");
		powerOptionsObject.put("sortOrder", 0);
		powerOptionsObject.put("isEnabled", true);
		powerOptionsObject.put("options", options);
		powerOptions.put(powerOptionsObject);
		payload.put("lkCountry", ApplicationConstants.Countries.SINGAPORE);
		payload.put("productId", productId);
		payload.put("powerOptions", powerOptions);
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		queryParams.put("powerType", "SPH");
		Response response = RestAssuredUtils.PUT(UpdatePowerOptionAPIUrl, queryParams,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"),
				ApplicationUtil.getcategoryId("category_power_option"));
	}

	@Test
	public void mapADDTemplateIdToProductsAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_vc"),
				ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String MapTemplateIdToProductsApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.MAP_TEMPLATEID_TO_PRODUCTS_LIST_API, templateId);
		JSONObject payload = new JSONObject();
		payload.put("operationType", "ADD");
		JSONArray productIds = new JSONArray();
		productIds.put(productId);
		productIds.put(productId);
		payload.put("productIds", productIds);
		Response response = RestAssuredUtils.PUT(MapTemplateIdToProductsApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result[0].id"), productId);
		Assert.assertEquals(response.jsonPath().getString("result[1].id"), productId);
	}

	@Test(dependsOnMethods = "mapADDTemplateIdToProductsAPI")
	public void mapREMOVETemplateIdToProductsAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String MapTemplateIdToProductsApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.MAP_TEMPLATEID_TO_PRODUCTS_LIST_API, templateId);
		JSONObject payload = new JSONObject();
		payload.put("operationType", "REMOVE");
		JSONArray productIds = new JSONArray();
		productIds.put(productId);
		productIds.put(productId);
		payload.put("productIds", productIds);
		Response response = RestAssuredUtils.PUT(MapTemplateIdToProductsApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result[0].id"), productId);
		Assert.assertEquals(response.jsonPath().getString("result[1].id"), productId);
	}

	@Test
	public void mapCategoryManualToProductsAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String mapCategoryManualToProductsAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.MAP_CATEGORY_MANUAL_TO_PRODUCTS_API, productId);
		JSONArray payload = new JSONArray();
		payload.put(ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		Response response = RestAssuredUtils.PUT(mapCategoryManualToProductsAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result[0].id"),
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
	}

	@Test
	public void mapADDTemplatesToProductsAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String MapADDTemplatesToProductsApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.MAP_TEMPLATES_TO_PRODUCT_API, productId);
		JSONObject payload = new JSONObject();
		payload.put("operationType", "ADD");
		JSONArray templateIds = new JSONArray();
		templateIds.put(templateId);
		payload.put("templateIds", templateIds);
		Response response = RestAssuredUtils.PUT(MapADDTemplatesToProductsApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result[0].id"), templateId);
	}

	@Test(dependsOnMethods = "mapADDTemplatesToProductsAPI", enabled = false)
	public void mapREMOVETemplatesToProductsAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String MapREMOVETemplatesToProductsApiUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.MAP_TEMPLATES_TO_PRODUCT_API, productId);
		JSONObject payload = new JSONObject();
		payload.put("operationType", "REMOVE");
		JSONArray templateIds = new JSONArray();
		templateIds.put(templateId);
		payload.put("templateIds", templateIds);
		Response response = RestAssuredUtils.PUT(MapREMOVETemplatesToProductsApiUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result[0].id"), templateId);
	}

	@Test
	public void mappingCategoryIdWithRuleIdAPI() throws Exception {
		String MappingCategoryIdWithRuleIdAPIUrl = Environments.CATALOG_SERVICE + String.format(
				CatalogServicePathConstants.MAPPING_OF_CATEGORY_WITH_RULEID_API, categoryIdForRule, RuleIdValue);
		Response response = RestAssuredUtils.PUT(MappingCategoryIdWithRuleIdAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				"");
		if (response.statusCode() != 200) {
			log.info(response.jsonPath().getString("message"));
		} else {
			Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
			Assert.assertEquals(response.jsonPath().getString("result.id"), RuleIdValue, "RuleId is not correct");
		}
	}

	@Test(dependsOnMethods = "mappingCategoryIdWithRuleIdAPI", priority = 1)
	public void unMapCategoryIdWithRuleIdAPI() throws Exception {
		String unMapCategoryIdWithRuleIdAPIUrl = Environments.CATALOG_SERVICE + String.format(
				CatalogServicePathConstants.DELETE_MAPPING_OF_CATEGORY_WITH_RULEID_API, categoryIdForRule, RuleIdValue);
		Response response = RestAssuredUtils.DELETE(unMapCategoryIdWithRuleIdAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test
	public void getProductImagesSampleCsvAPI() throws Exception {
		GetProductImagesSampleCsvAPI GetProductImagesSampleCsvAPI = new GetProductImagesSampleCsvAPI();
		GetProductImagesSampleCsvAPI.getProductImagesSampleCsvAPI();
	}

	@Test
	public void getProductImagesAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglass_new"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String getProductImagesAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_PRODUCT_IMAGES_API, productId);
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.GET(uri, getProductImagesAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);
		Assert.assertTrue(response.jsonPath().getBoolean("result[0].baseImage"), "Filed is not correct");
	}

	@Test
	public void getTemplatesCsvExportAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		queryParams.put("templateType", CatalogUtil.POWER);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_TEMPLATES_CSV_EXPORT_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("true"), "Enabled is not true");
	}

	@Test
	public void getSKUsByPowerType() throws Exception {
		GetSKUsByPowerType GetSKUsByPowerType = new GetSKUsByPowerType();
		GetSKUsByPowerType.getSKUsWithEmptyCatalogServiceId(CatalogUtil.PACKAGE);
	}

	@Test
	public void getSampleTemplateCsvAPI() throws Exception {
		GetSampleTemplateCsvAPI GetSampleTemplateCsvAPI = new GetSampleTemplateCsvAPI();
		GetSampleTemplateCsvAPI.getSampleTemplateCsv(CatalogUtil.POWER);
	}

	@Test
	public void getProductTemplateMappingSampleCsvAPI() throws Exception {
		GetProductTemplateMappingSampleCsvAPI GetProductTemplateMappingSampleCsvAPI = new GetProductTemplateMappingSampleCsvAPI();
		GetProductTemplateMappingSampleCsvAPI.getCoatingSampleCsvWithoutAuthToken();

	}

	@Test
	public void getProductPricesAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglass_new"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String getProductPricesAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_PRODUCT_PRICES_API, productId);
		Response response = RestAssuredUtils.GET(uri, getProductPricesAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);
	}

	@Test(enabled = false) // getting 500
	public void getPowerOptionsForProductAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		String GetPowerOptionsForProductAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_POWER_OPTION_API, productId);
		Response response = RestAssuredUtils.GET(uri, GetPowerOptionsForProductAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.countryWisePowers").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result.countryWisePowers[0].lkCountry"), "IN",
				"LkCountry is not correct");
	}

	@Test
	public void getPackageProductMappingByIdAPI() throws Exception {
		GetPackageProductMappingByIdAPI GetPackageProductMappingByIdAPI = new GetPackageProductMappingByIdAPI();
		GetPackageProductMappingByIdAPI.getPackageProductMappingByIdAPI();
	}

	@Test
	public void getPackageProductMappingAPI() throws Exception {
		GetPackageProductMappingAPI GetPackageProductMappingAPI = new GetPackageProductMappingAPI();
		GetPackageProductMappingAPI.getPackageProductMappingAPI();
	}

	@Test
	public void getLinkedProductsByIdAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String getproductAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_LINKED_PRODUCTS_BY_ID_API, productId);
		Response response = RestAssuredUtils.GET(uri, getproductAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), productId);

	}

	@Test
	public void getLinkedProductsAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("lkCountry", ApplicationConstants.Countries.INDIA);
		queryParams.put("id", productId);
		queryParams.put("pageNumber", "0");
		queryParams.put("pageSize", "20");
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_LINKED_PRODUCTS_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if(Environments.environment.equals(Profiles.PREPROD)) {
		Assert.assertEquals(response.jsonPath().getString("result.linkedProductDtos[0].id"), productId);}
	}

	@Test
	public void getCoatingFiltersAPI() throws Exception {
		GetCoatingFilterAPI GetCoatingFilterAPI = new GetCoatingFilterAPI();
		GetCoatingFilterAPI.getCoatingFiltersAPI(ApplicationConstants.Countries.INDIA, "true");
	}

	@Test
	public void getCategoryAPI() throws Exception {
		GetCategoryAPI GetCategoryAPI = new GetCategoryAPI();
		GetCategoryAPI.getCategoryAPI();
	}

	@Test
	public void getAllUnbxdFiledsAPI() throws Exception {
		String getAllUnbxdFiledsAPIURL = Environments.CATALOG_SERVICE + String
				.format(CatalogServicePathConstants.GET_ALL_UNBXD_FIELDS_API, ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.GET(uri, getAllUnbxdFiledsAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);
		Assert.assertEquals(response.jsonPath().getString("result[0].dataType"), "TEXT", "Field is not correct");
	}

	@Test
	public void getAllUnbxdConfigAPI() throws Exception {
		GetAllUnbxdConfigAPI GetAllUnbxdConfigAPI = new GetAllUnbxdConfigAPI();
		GetAllUnbxdConfigAPI.getAllUnbxdConfigAPI();
	}

	@Test
	public void runRuleForCategoryIdToMapWithPidAPI() throws Exception {
		String runRuleForCategoryIdToMapWithPidAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.RUN_RULE_TO_MAP_CATEGORYID_WITH_PID_API, categoryIdForRule);
		Response response = RestAssuredUtils.POST_Method(uri, runRuleForCategoryIdToMapWithPidAPIURL, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		if (response.statusCode() != 200) {
			log.info(response.jsonPath().getString("message"));
		} else {
			Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		}

	}

	@Test
	public void runAllRulesForProdvidedCountryAPI() throws Exception {
		String runAllRulesForProdvidedCountryAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.RUN_ALL_RULES_FOR_PROVIDED_COUNTRY_API,
						ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.POST_Method(uri, runAllRulesForProdvidedCountryAPIURL, HeadersUtil
				.headers_CatalogService(CatalogUtil.CatalogLoginToken(), xCatalogServiceId, xCatalogServicekey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test
	public void getUploadFacebookDataAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_UPLOAD_FACEBOOK_DATA_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}

	@Test
	public void getUploadgmcDataAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_UPLOAD_GMC_DATA_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}

	@Test
	public void getLinkedProductTemplateMappingSampleCsvAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri,
				CatalogServicePathConstants.GET_LINKED_PRODUCTS_TEMPLATEMAPPING_SAMPLEAPI,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("true"), "Enabled is not true");

	}

	@Test
	public void getExportLinkedProductsCSVAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("id", productId);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_EXPORT_LINKED_PRODUCTS_CSV_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (Environments.environment.equals(Profiles.PREPROD)) {
			Assert.assertTrue(response.asString().contains("true"), "Enabled is not true");
		}

	}

	@Test
	public void getLiquidationAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_LIQUIDATION_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.productLiquidation").size() >= 1);
	}

	@Test
	public void getLiquidationByIdAPI() throws Exception {
		String getLiquidationByIdAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_LIQUIDATION_BY_ID_API, liquidationValue);
		Response response = RestAssuredUtils.GET(uri, getLiquidationByIdAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), liquidationValue);
	}

	@Test
	public void getExportLiquidationAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_EXPORT_LIQUIDATION_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("st270"), "Facility Code is not correct");
	}

	@Test
	public void getFilteredLiquidationAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_FILTERED_LIQUIDATION_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.productLiquidation").size() >= 1);
	}

	@Test
	public void getSampleLiquidationInCSVAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_SAMPLE_LIQUIDATION_IN_CSV_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("ID"), "ID not present");
	}

	@Test(priority = 30)
	public void createLiquidationAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject payload = new JSONObject();
		payload.put("productId", productId);
		payload.put("facilityCode", "lks18" + GenericUtil.createRandomNumber(4));
		payload.put("percentageDiscount", 99);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.CREATE_LIQUIDATION_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject createliquidationpayload = payload;
		int PercentageDiscountForLiquidation = (int) createliquidationpayload.get("percentageDiscount");
		String ProductIdForLiquidation = (String) createliquidationpayload.get("productId");
		log.info(PercentageDiscountForLiquidation);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (Environments.dbConnectionFlag) {
			MongoConnectionUtility mongoConnectionObject = null;
			mongoConnectionObject = UtilityUtil.getMongoConnectionObject("readall");
			mongoConnectionObject.initMongoDBConnection();
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("percentageDiscount", PercentageDiscountForLiquidation);
			List<Document> resultList = mongoConnectionObject.executeQuery("catalog", "liquidation_discount", object);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document.getString("_id"));
			String liquidationIdFromDb = document.getString("_id");
			liquidationId = liquidationIdFromDb.substring(9, liquidationIdFromDb.length() - 2);
			mongoConnectionObject.closeMongoConnection();
		}
	}

//not working, getting 400 api has issue
	@Test(dependsOnMethods = "createLiquidationAPI", enabled = false)
	public void updateLiquidationAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("percentageDiscount", "18");
		String updateLiquidationAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_LIQUIDATION_API, liquidationId);
		Response response = RestAssuredUtils.PUT(uri, updateLiquidationAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	// not working, getting 400 api has issue
	@Test(dependsOnMethods = "updateLiquidationAPI", enabled = false, priority = 4)
	public void deleteLiquidationAPI() throws Exception {
		String deleteLiquidationAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.DELETE_LIQUIDATION_API, liquidationId);
		Response response = RestAssuredUtils.DELETE(deleteLiquidationAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test
	public void getPackageProductMappingFilterAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri,
				CatalogServicePathConstants.GET_PACKAGE_PRODUCT_MAPPING_FILTER_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.packageProductMappings").size() >= 1);
	}

//not working, getting 500. api has issue
	@Test(enabled=false)
	public void createPackageProductMappingAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("productId", "144036");
		payload.put("packageId", "146534");
		payload.put("isPackageQtyUpdate", false);
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.CREATE_PACKAGE_PRODUCT_MAPPING_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject createPackageProductMappingPayload = payload;
		String PackageForPackageProductMapping = (String) createPackageProductMappingPayload.get("packageId");
		String ProductIdForPackageProductMapping = (String) createPackageProductMappingPayload.get("productId");
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		if (Environments.dbConnectionFlag) {
			MongoConnectionUtility mongoConnectionObject = null;
			mongoConnectionObject = UtilityUtil.getMongoConnectionObject("readall");
			mongoConnectionObject.initMongoDBConnection();
			Map<String, Object> object = new HashMap<String, Object>();
			object.put("productId", ProductIdForPackageProductMapping);
			List<Document> resultList = mongoConnectionObject.executeQuery("catalog", "package_product_mapping",
					object);
			JSONObject document = new JSONObject(resultList.get(0).toJson());
			log.info(document.getString("_id"));
			String packageProductMappingIdFromDb = document.getString("_id");
			packageProductMappingId = packageProductMappingIdFromDb.substring(9,
					packageProductMappingIdFromDb.length() - 2);
			mongoConnectionObject.closeMongoConnection();
		}
	}

//not working, getting 500. api has issue
	@Test(dependsOnMethods = "createPackageProductMappingAPI", enabled = false)
	public void updatePackageProductMappingAPI() throws Exception {
		JSONObject payload = new JSONObject();
		payload.put("productId", "144036");
		payload.put("packageId", "146534");
		payload.put("isPackageQtyUpdate", true);
		String updatePackageProductMappingAPIURL = Environments.CATALOG_SERVICE + String
				.format(CatalogServicePathConstants.UPDATE_PACKAGE_PRODUCT_MAPPING_API, packageProductMappingId);
		Response response = RestAssuredUtils.PUT(uri, updatePackageProductMappingAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test(dependsOnMethods = "updatePackageProductMappingAPI", enabled = false, priority = 5)
	public void deletePackageProductMappingAPI() throws Exception {
		String deletePackageProductMappingAPIURL = Environments.CATALOG_SERVICE + String
				.format(CatalogServicePathConstants.DELETE_PACKAGE_PRODUCT_MAPPING_API, packageProductMappingId);
		Response response = RestAssuredUtils.DELETE(deletePackageProductMappingAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test
	public void fetchConfigsAPI() throws Exception {
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.FETCH_CONFIGS_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.PRODUCT_TYPE").size() >= 1);
	}

	@Test
	public void fetchConfigByIdAPI() throws Exception {
		String fetchConfigByIdAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.FETCH_CONFIG_BY_ID_API, configId);
		Response response = RestAssuredUtils.GET(uri, fetchConfigByIdAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);
	}

	@Test
	public void addConfigAPI() throws Exception {
		String addConfigAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.CREATE_CONFIG_API, configId);
		JSONArray payload = new JSONArray();
		payload.put("Testing" + GenericUtil.genterateRandomString());
		Response response = RestAssuredUtils.POST(uri, addConfigAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test
	public void mapProductToCategoryAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String mapProductToCategoryAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.MAP_PRODUCT_TO_CATEGORY_API, productId);
		JSONArray categoryIds = new JSONArray();
		categoryIds.put(ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		JSONObject payload = new JSONObject();
		payload.put("action", "ADD");
		payload.put("categoryIds", categoryIds);
		Response response = RestAssuredUtils.POST(uri, mapProductToCategoryAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		if (response.statusCode() != 200) {
			log.info(response.jsonPath().getString("message"));
		} else {
			Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
			log.info("response.result");
		}

	}

	@Test
	public void duplicateProductAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String duplicateProductAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.DUPLICATE_PRODUCT_API, productId);
		Response response = RestAssuredUtils.POST_Method(uri, duplicateProductAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test(enabled = false)
	public void updateImagesAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String updateImagesAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_IMAGES_API, productId);
		JSONArray labels = new JSONArray();
		labels.put("Image_Front");
		JSONObject imagesObject = new JSONObject();
		imagesObject.put("baseImage", true);
		imagesObject.put("caseImage", true);
		imagesObject.put("desktopImage", true);
		imagesObject.put("mobileImage", true);
		imagesObject.put("name", "/v/i/vincent-chase-vc-e10775-s-c1-eyeglasses_eyeglasses_124036.jpg");
		imagesObject.put("smallImage", true);
		imagesObject.put("sortOrder", 1);
		imagesObject.put("thumbnail", true);
		imagesObject.put("url",
				"https://static5.lenskart.com/media/catalog/product/pro/1/thumbnail/480x480/9df78eab33525d08d6e5fb8d27136e95//v/i/vincent-chase-vc-2309-c-c2-eyeglasses_m_0151_1.jpg");
		imagesObject.put("labels", labels);
		JSONArray images = new JSONArray();
		images.put(imagesObject);
		JSONObject payload = new JSONObject();
		payload.put("lkCountry", ApplicationConstants.Countries.INDIA);
		payload.put("images", images);
		Response response = RestAssuredUtils.PUT(uri, updateImagesAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject updateImagepayload = payload;
		String updateImagepayloadvalue = (String) updateImagepayload.getJSONArray("images").getJSONObject(0)
				.getString("name");
		log.info(updateImagepayloadvalue);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result[0].name"), updateImagepayloadvalue);
	}

	@Test
	public void getProductStatusAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglass_new"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String getProductStatusAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.GET_PRODUCT_STATUS_API, productId);
		Response response = RestAssuredUtils.GET(uri, getProductStatusAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.countries").size() >= 1);
	}

	@Test(enabled = false)
	public void updateProductPriceAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglass_new"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String updateProductPriceAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_PRODUCT_PRICE_API, productId);
		JSONObject payloadObject = new JSONObject();
		payloadObject.put("buyFourPrice", 30);
		payloadObject.put("buyOnePrice", 30);
		payloadObject.put("buyTwoPrice", 30);
		payloadObject.put("costPrice", 30);
		payloadObject.put("countryCode", ApplicationConstants.Countries.SINGAPORE);
		payloadObject.put("currencyCode", "SGD");
		payloadObject.put("freeFramePrice", 30);
		payloadObject.put("lenskartPrice", 30);
		payloadObject.put("marketPrice", 30);
		payloadObject.put("images", 30);
		payloadObject.put("newUserLkPrice", 30);
		payloadObject.put("specialOfferPrice", 30);
		JSONArray payload = new JSONArray();
		payload.put(payloadObject);
		Response response = RestAssuredUtils.PUT(uri, updateProductPriceAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result[0].countryCode"),
				ApplicationConstants.Countries.SINGAPORE);
	}

	@Test
	public void updateProductStatusAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String updateProductPriceAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_PRODUCT_STATUS_API, productId);
//		JSONArray enabledPlatforms = new JSONArray();
//		enabledPlatforms.put("ANDROID");
//		JSONObject additionalProp1 = new JSONObject();
//		additionalProp1.put("enabledPlatforms", enabledPlatforms);
//		additionalProp1.put("isActive", true);
//		JSONObject additionalProp2 = new JSONObject();
//		additionalProp2.put("enabledPlatforms", enabledPlatforms);
//		additionalProp2.put("isActive", true);
//		JSONObject additionalProp3 = new JSONObject();
//		additionalProp3.put("enabledPlatforms", enabledPlatforms);
//		additionalProp3.put("isActive", true);
//		JSONObject countryWiseStatus = new JSONObject();
//		countryWiseStatus.put("additionalProp1", additionalProp1);
//		countryWiseStatus.put("additionalProp2", additionalProp2);
//		countryWiseStatus.put("additionalProp3", additionalProp3);
		JSONArray countries = new JSONArray();
		countries.put(ApplicationConstants.Countries.INDIA);
		JSONObject payload = new JSONObject();
		payload.put("countries", countries);
//		payload.put("countryWiseStatus", countryWiseStatus);
		payload.put("visibility", "CATALOG_SEARCH");
		Response response = RestAssuredUtils.POST(uri, updateProductPriceAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.countries").size() >= 1);
	}

	@Test
	public void fetchProductExportAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("type", CatalogUtil.LOCALE);
		queryParams.put("categoryId", ApplicationUtil.getcategoryId("category_eyeglass_new"));
		queryParams.put("mappedInCategory", "true");
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.FETCH_PRODUCT_EXPORT_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("IN"), "LkCountry is not IN");
	}

	@Test
	public void fetchProductCSVSampleAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("type", CatalogUtil.LOCALE);
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.FETCH_PRODUCT_CSV_SAMPLE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.asString().contains("IN"), "Type is not Eyeglasses");
	}

	@Test
	public void getCatalogProductFilterWiseAPI() throws Exception {
		HashMap<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("categoryId", ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		Response response = RestAssuredUtils.GET(uri, CatalogServicePathConstants.GET_CATLOG_PRODUCT_FILTERWISE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				queryParams);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result.products").size() >= 1);
	}

	@Test(enabled = false)
	public void addDataFeederFieldAPI() throws Exception {
		String addProductPriceAPIURL = Environments.CATALOG_SERVICE + String
				.format(CatalogServicePathConstants.ADD_UNBXD_DATAFEEDER_FILED_API, ApplicationConstants.Countries.USA);
		JSONObject payload = new JSONObject();
		payload.put("id", "name");
		payload.put("dataType", "TEXT");
		Response response = RestAssuredUtils.POST(uri, addProductPriceAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject addDataFeederFieldAPIpayload = payload;
		String idvalue = (String) addDataFeederFieldAPIpayload.get("id");
		String datatypeValue = (String) addDataFeederFieldAPIpayload.getString("dataType");
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), idvalue);
		Assert.assertEquals(response.jsonPath().getString("result.dataType"), datatypeValue);
	}

	@Test(dependsOnMethods = "addDataFeederFieldAPI", enabled = false)
	public void updateDataFeederFieldAPI() throws Exception {
		String updateProductPriceAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_UNBXD_DATAFEEDER_FILED_API, "name",
						ApplicationConstants.Countries.USA);
		JSONObject payload = new JSONObject();
		payload.put("id", "name");
		payload.put("dataType", "TEXT");
		payload.put("multiValued", false);
		Response response = RestAssuredUtils.PUT(uri, updateProductPriceAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		JSONObject addDataFeederFieldAPIpayload = payload;
		String idvalue = (String) addDataFeederFieldAPIpayload.get("id");
		String datatypeValue = (String) addDataFeederFieldAPIpayload.getString("dataType");
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), idvalue);
		Assert.assertEquals(response.jsonPath().getString("result.dataType"), datatypeValue);
	}

	@Test(dependsOnMethods = "updateDataFeederFieldAPI", enabled = false)
	public void deleteDataFeederFieldAPI() throws Exception {
		String deleteProductPriceAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.DELETE_UNBXD_DATAFEEDER_FILED_API, "name",
						ApplicationConstants.Countries.USA);
		Response response = RestAssuredUtils.DELETE(uri, deleteProductPriceAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test(enabled = false)
	public void addDataFeederConfigAPI() throws Exception {
		String addDataFeederConfigAPIURL = Environments.CATALOG_SERVICE + String.format(
				CatalogServicePathConstants.ADD_UNBXD_DATAFEEDER_CONFIG_API, ApplicationConstants.Countries.USA);
		String payload = "Save config testing";
		Response response = RestAssuredUtils.POST(uri, addDataFeederConfigAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				payload);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.siteKey"), payload);
	}

	@Test(dependsOnMethods = "addDataFeederConfigAPI", enabled = false)
	public void updateDataFeederConfigAPI() throws Exception {
		String updateDataFeederConfigAPIURL = Environments.CATALOG_SERVICE + String.format(
				CatalogServicePathConstants.UPDATE_UNBXD_DATAFEEDER_CONFIG_API, ApplicationConstants.Countries.USA);
		String payload = "Update config testing";
		Response response = RestAssuredUtils.PUT(uri, updateDataFeederConfigAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				payload);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.siteKey"), payload);
	}

	@Test(dependsOnMethods = "updateDataFeederConfigAPI", enabled = false)
	public void deleteDataFeederConfigAPI() throws Exception {
		String deleteDataFeederConfigAPIURL = Environments.CATALOG_SERVICE + String.format(
				CatalogServicePathConstants.DELETE_UNBXD_DATAFEEDER_CONFIG_API, ApplicationConstants.Countries.USA);
		Response response = RestAssuredUtils.DELETE(uri, deleteDataFeederConfigAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
	}

	@Test
	public void unbxdReleaseFeedLockAPI() throws Exception {
		String unbxdReleaseFeedLockAPIURL = Environments.CATALOG_SERVICE + String
				.format(CatalogServicePathConstants.UNBXD_RELEASE_FEED_LOCK_API, ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.POST_Method(uri, unbxdReleaseFeedLockAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}

	// Url rewrite
	@Test
	public void addCustomUrlRewriteAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject payload = new JSONObject();
		payload.put("categoryId", ApplicationUtil.getcategoryId("category_eyeglasses_FFF"));
		payload.put("country", ApplicationConstants.Countries.INDIA);
		payload.put("idPath", "category/8229/" + GenericUtil.genterateRandomString());
		payload.put("productId", productId);
		payload.put("requestPath",
				"eyeglasses/marketing/" + GenericUtil.genterateRandomString() + "classic-eyeglasses-aviator-men.html");
		payload.put("targetPath", "catalog/category/view/id/8229");
		Response response = RestAssuredUtils.POST(uri, CatalogServicePathConstants.ADD_CUSTOM_URLREWRITE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		UrlRewriteId = response.jsonPath().getString("result.id");
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.productId"), productId);
		Assert.assertEquals(response.jsonPath().getString("result.categoryId"),
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"));
	}

	@Test(dependsOnMethods = "addCustomUrlRewriteAPI")
	public void modifyUrlRewriteAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		JSONObject payload = new JSONObject();
		payload.put("id", UrlRewriteId);
		payload.put("categoryId", ApplicationUtil.getcategoryId("category_eyeglasses_VC"));
		payload.put("country", ApplicationConstants.Countries.INDIA);
		payload.put("idPath", "category/8229/" + GenericUtil.genterateRandomString());
		payload.put("productId", productId);
		payload.put("requestPath",
				"eyeglasses/marketing/" + GenericUtil.genterateRandomString() + "classic-eyeglasses-aviator-men.html");
		payload.put("targetPath", "catalog/category/view/id/8229");
		Response response = RestAssuredUtils.PUT(uri, CatalogServicePathConstants.ADD_CUSTOM_URLREWRITE_API,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.productId"), productId);
		Assert.assertEquals(response.jsonPath().getString("result.categoryId"),
				ApplicationUtil.getcategoryId("category_eyeglasses_FFF"));
	}

	@Test(dependsOnMethods = "modifyUrlRewriteAPI")
	public void deleteUrlRewriteByIdAPI() throws Exception {
		String deleteUrlRewriteByIdAPIUrl = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.DELETE_URLREWRITE_BY_API, UrlRewriteId);
		Response response = RestAssuredUtils.DELETE(uri, deleteUrlRewriteByIdAPIUrl,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}

	@Test(enabled = false)
	public void updateProductAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String updateProductAPIURL = Environments.CATALOG_SERVICE
				+ String.format(CatalogServicePathConstants.UPDATE_PRODUCT_API, productId);
		JSONObject payload = new JSONObject();
		Response response = RestAssuredUtils.PATCH(uri, updateProductAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), productId);
	}

	@Test(enabled = false)
	public void unbxdFullFeedUloadAPI() throws Exception {
		String unbxdFullFeedUloadAPIURL = Environments.CATALOG_SERVICE + String
				.format(CatalogServicePathConstants.UNBXD_FULL_FEED_UPLOAD_API, ApplicationConstants.Countries.INDIA);
		Response response = RestAssuredUtils.POST_Method(uri, unbxdFullFeedUloadAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey));
		if (response.statusCode() != 200) {
			log.info(response.jsonPath().getString("message"));
		} else {
			Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
			log.info("Delta Feed upload is success");
		}
	}

	@Test
	public void unbxdDeltaFeedUploadAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_eyeglasses_VC"), ApplicationConstants.XApiClient.ANDROID);
		String productId = jsonResponse_category.getJSONObject("result").getJSONArray("product_list").getJSONObject(0)
				.getString("id");
		String unbxdDeltaFeedUploadAPIURL = Environments.CATALOG_SERVICE + String.format(
				CatalogServicePathConstants.UNBXD_DELTA_FEED_UPLOAD_API, "add", ApplicationConstants.Countries.INDIA);
		JSONArray payload = new JSONArray();
		payload.put(productId);
		Response response = RestAssuredUtils.POST(uri, unbxdDeltaFeedUploadAPIURL,
				HeadersUtil.headers_CatalogService(CatalogUtil.CatalogLoginToken(),
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceId,
						ApplicationConstants.CatalogServiceKeyAndId.XCatalogServiceKey),
				GenericUtil.jsonToString(payload));
		if (response.statusCode() != 200) {
			log.info(response.jsonPath().getString("message"));
		} else {
			Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
			log.info("Delta Feed upload is success");
		}
	}
}
