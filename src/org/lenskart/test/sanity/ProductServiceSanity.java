package org.lenskart.test.sanity;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.lenskart.core.Environments;
import org.lenskart.core.Profiles;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.constant.ApplicationConstants.PowerTypes;
import org.lenskart.core.constant.ApplicationConstants.XApiClient;
import org.lenskart.core.constant.JunoV2ProductPathConstants;
import org.lenskart.core.util.ApplicationUtil;
import org.lenskart.core.util.CustomerUtil;
import org.lenskart.core.util.HeadersUtil;
import org.lenskart.core.util.JunoV1Util;
import org.lenskart.core.util.JunoV2Util;
import org.lenskart.core.util.ProductUtil;
import org.lenskart.core.util.SessionUtil;
import org.lenskart.test.product.CreateCustomerReviewUsecases;
import org.lenskart.test.product.DeleteProductFromWishlistUsecases;
import org.lenskart.test.product.DeleteWishlistUsecases;
import org.lenskart.test.product.GetProductReviewUsecases;
import org.lenskart.test.product.GetSubcategoryUsecases;
import org.lenskart.test.product.GetV2BuyPackageUsecases;
import org.lenskart.test.product.GetWishlistUsecases;
import org.lenskart.test.product.UpdateProductStatus;
import org.lenskart.test.product.UpdateWishlistUsecases;
import org.testng.Assert;
import org.testng.annotations.Test;

import com.utilities.GenericUtil;
import com.utilities.JsonUtil;
import com.utilities.RequestUtil;
import com.utilities.RestAssuredUtils;

import io.restassured.RestAssured;
import io.restassured.response.Response;

/**
 * <AUTHOR>
 */
public class ProductServiceSanity {
	private static final Logger log = GenericUtil.InitLogger(ProductServiceSanity.class);
	private static boolean db_Validation = Environments.dbConnectionFlag;

	@Test
	public void autoSuggestAPI() throws Exception {
		String searchText = "eyeglass";
		String getAutoSuggestURL = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_AUTO_SUGGEST_PATH, searchText);
		HttpResponse httpResponse = RequestUtil.getRequest(getAutoSuggestURL,
				HeadersUtil.headers_XApiClient(XApiClient.ANDROID), null);
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		log.info("Total Suggestions = " + responseJSON.getJSONObject("result").getJSONArray("terms").length());
		Assert.assertTrue(responseJSON.getJSONObject("result").getJSONArray("terms").length() >= 0);
	}

	@Test
	public void autoSuggestV2API() throws Exception {
		String searchText = "eyeglass";
		String getAutoSuggestV2URL = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_AUTO_SUGGEST_V2_PATH, searchText);
		HttpResponse httpResponse = RequestUtil.getRequest(getAutoSuggestV2URL,
				HeadersUtil.headers_XApiClient(XApiClient.ANDROID), null);
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		log.info("Total productRecommendations = "
				+ responseJSON.getJSONObject("result").getJSONArray("productRecommendations").length());
		Assert.assertTrue(responseJSON.getJSONObject("result").getJSONArray("productRecommendations").length() >= 0);
	}

	@Test(enabled = false)
	public void get_BuyOption_BuyPackage() throws Exception {
		String URL = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_BUYOPTION_BUYPACKAGE_V2;
		HttpResponse httpResponse = RequestUtil.getRequest(URL);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");

		log.info("responseObject: " + responseObject);
		Assert.assertTrue(responseObject.getJSONObject("result").getJSONArray("buyPackageList").length() >= 400);
		log.info("Packages: " + responseObject.getJSONObject("result").getJSONArray("buyPackageList").length());
	}

	@Test(enabled = false)
	private int get_BuyOption_BuyPackage_count() throws Exception {
		String URL = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_BUYOPTION_BUYPACKAGE_V2;
		HttpResponse httpResponse = RequestUtil.getRequest(URL);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");

		log.info("responseObject: " + responseObject);
		Assert.assertTrue(responseObject.getJSONObject("result").getJSONArray("buyPackageList").length() >= 400);
		log.info("Packages: " + responseObject.getJSONObject("result").getJSONArray("buyPackageList").length());
		return responseObject.getJSONObject("result").getJSONArray("buyPackageList").length();
	}

	@Test(enabled = false)
	public void post_BuyOption_BuyPackage() throws Exception {
		int count = get_BuyOption_BuyPackage_count();
		String sku = "pack_regular_antiglare_" + GenericUtil.createRandomNumber(5);
		String payload = "{\n" + "  \"buyPackage\": {\n" + "    \"addons\": [\n" + "      {\n"
				+ "        \"group\": \"MYLENS\",\n" + "        \"id\": \"563ca5151314273042bc6266\",\n"
				+ "        \"lenskartPrice\": 100,\n" + "        \"marketPrice\": 200,\n"
				+ "        \"title\": \"Scratch-Resistant\"\n" + "      }\n" + "    ],\n" + "    \"coatings\": [\n"
				+ "      \"5a57402be4b0f38075072d2f\"\n" + "    ],\n" + "    \"frameType\": \"full_rim\",\n"
				+ "    \"group\": \"MYLENS\",\n" + "    \"id\": \"testing_test\",\n"
				+ "    \"imageUrl\": \"https://static5.lenskart.com/images/cust_mailer/July-22/BlueEssentialPackage_Attributes.png\",\n"
				+ "    \"label\": \"BLU Essential\",\n" + "    \"lenskartPrice\": 1000,\n"
				+ "    \"marketPrice\": 2000,\n" + "    \"offerText\": \"Buy 1 Get 1 on gold\",\n"
				+ "    \"power\": {\n" + "      \"exception\": \"exception\",\n" + "      \"power_recommendation\": {\n"
				+ "        \"max\": 2,\n" + "        \"min\": 0\n" + "      },\n" + "      \"restriction\": [\n"
				+ "        {\n" + "          \"label\": \"spherical\",\n" + "          \"maxPower\": 6,\n"
				+ "          \"minPower\": 0,\n" + "          \"type\": \"SPH\"\n" + "        }\n" + "      ]\n"
				+ "    },\n" + "    \"productType\": \"Eyeglasses\",\n" + "    \"sku\": " + "\"" + sku + "\"," + "\n" +
				// " \"sku\": \"pack_regular_antiglare_"+ GenericUtil.createRandomNumber(5) +
				// "\"," + "\n" +
				"    \"specification\": [\n" + "    ],\n" + "    \"specificationList\": [\n" + "      {\n"
				+ "        \"collapsedImageUrl\": \"\",\n" + "        \"collapsedTitle\": \"Blocks Blue Light\",\n"
				+ "        \"enabled\": true,\n" + "        \"group\": \"BLUE_RAY_BLOCK\",\n"
				+ "        \"hoverImageUrl\": \"https://static.lenskart.com/images/cust_mailer/24-Apr-18/mobile1.png\",\n"
				+ "        \"imageUrl\": \"https://static.lenskart.com/images/cust_mailer/24-Apr-18/mobile1.png\",\n"
				+ "        \"sortOrder\": 1,\n" + "        \"subtitle\": \"Blocks Blue Light\",\n"
				+ "        \"title\": \"Blocks Blue Light\"\n" + "      }\n" + "    ],\n"
				+ "    \"storePackageName\": \"string\",\n" + "    \"subtitle\": \"string\",\n"
				+ "    \"warranty\": \"string\"\n" + "  }\n" + "}";
		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.GET_BUYOPTION_BUYPACKAGE_V2,
				HeadersUtil.headers(), payload);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result"), "CREATED");
		log.info(count);
		Assert.assertEquals(get_BuyOption_BuyPackage_count(), count + 1);
	}

	@Test(enabled = false)
	public void update_BuyOption_BuyPackage() throws Exception {
		// int count = get_BuyOption_BuyPackage_count();
		String sku = "pack_regular_antiglare_" + GenericUtil.createRandomNumber(5);
		String payload = "{\n" + "  \"buyPackage\": {\n" + "    \"addons\": [\n" + "      {\n"
				+ "        \"group\": \"MYLENS\",\n" + "        \"id\": \"563ca5151314273042bc6266\",\n"
				+ "        \"lenskartPrice\": 100,\n" + "        \"marketPrice\": 200,\n"
				+ "        \"title\": \"Scratch-Resistant\"\n" + "      }\n" + "    ],\n" + "    \"coatings\": [\n"
				+ "      \"5a57402be4b0f38075072d2f\"\n" + "    ],\n" + "    \"frameType\": \"full_rim\",\n"
				+ "    \"group\": \"MYLENS\",\n" + "    \"id\": \"testing_test\",\n"
				+ "    \"imageUrl\": \"https://static5.lenskart.com/images/cust_mailer/July-22/BlueEssentialPackage_Attributes.png\",\n"
				+ "    \"label\": \"BLU Essential\",\n" + "    \"lenskartPrice\": 1000,\n"
				+ "    \"marketPrice\": 2000,\n" + "    \"offerText\": \"Buy 1 Get 1 on gold\",\n"
				+ "    \"power\": {\n" + "      \"exception\": \"exception\",\n" + "      \"power_recommendation\": {\n"
				+ "        \"max\": 2,\n" + "        \"min\": 0\n" + "      },\n" + "      \"restriction\": [\n"
				+ "        {\n" + "          \"label\": \"spherical\",\n" + "          \"maxPower\": 6,\n"
				+ "          \"minPower\": 0,\n" + "          \"type\": \"SPH\"\n" + "        }\n" + "      ]\n"
				+ "    },\n" + "    \"productType\": \"Eyeglasses\",\n" + "    \"sku\": " + "\"" + sku + "\"," + "\n" +
				// " \"sku\": \"pack_regular_antiglare_"+ GenericUtil.createRandomNumber(5) +
				// "\"," + "\n" +
				"    \"specification\": [\n" + "    ],\n" + "    \"specificationList\": [\n" + "      {\n"
				+ "        \"collapsedImageUrl\": \"\",\n" + "        \"collapsedTitle\": \"Blocks Blue Light\",\n"
				+ "        \"enabled\": true,\n" + "        \"group\": \"BLUE_RAY_BLOCK\",\n"
				+ "        \"hoverImageUrl\": \"https://static.lenskart.com/images/cust_mailer/24-Apr-18/mobile1.png\",\n"
				+ "        \"imageUrl\": \"https://static.lenskart.com/images/cust_mailer/24-Apr-18/mobile1.png\",\n"
				+ "        \"sortOrder\": 1,\n" + "        \"subtitle\": \"Blocks Blue Light\",\n"
				+ "        \"title\": \"Blocks Blue Light\"\n" + "      }\n" + "    ],\n"
				+ "    \"storePackageName\": \"string\",\n" + "    \"subtitle\": \"string\",\n"
				+ "    \"warranty\": \"string\"\n" + "  }\n" + "}";
		Response response = RestAssuredUtils.PUT(JunoV2ProductPathConstants.GET_BUYOPTION_BUYPACKAGE_V2,
				HeadersUtil.headers(), payload);
		Assert.assertEquals(response.statusCode(), 200);
		// Assert.assertEquals(response.jsonPath().getString("result"), "CREATED");
		// log.info(count);
		// Assert.assertEquals(get_BuyOption_BuyPackage_count(), count + 1);
	}

	@Test(enabled = false)
	public void get_BuyOption_coating() throws Exception {
		String getCoatingUrl_v2 = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_BUYOPTION_COATING_V2;
		HttpResponse httpResponse = RequestUtil.getRequest(getCoatingUrl_v2);
		JSONObject responseObject = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("responseObject: " + responseObject);
		Assert.assertEquals(responseObject.get("status"), 200, "status mismatch");
		JSONArray coatingArray = responseObject.getJSONObject("result").getJSONArray("coatingList");
		Assert.assertTrue(coatingArray.length() >= 1);
	}

	@Test(enabled = false)
	public void addCoatings() throws URISyntaxException, IOException, JSONException {
		int countBeforeAdding = ProductUtil.coatingCount();
		String payload = "[{\"coating\":{\"enable\":false,\"hoverImageUrl\":\"https:\\/\\/static.lenskart.com\\/images\\/cust_mailer\\/1-Aug-18\\/scratch-resistant.jpg\",\"id\":\"string\",\"imageURL\":\"https:\\/\\/static.lenskart.com\\/images\\/cust_mailer\\/1-Aug-18\\/scratch-resistant.jpg\",\"lenskartPrice\":150,\"marketPrice\":200,\"power\":{\"restriction\":[{\"label\":\"only for spherical\",\"type\":\"sph\",\"maxPower\":10,\"minPower\":0},{\"label\":\"only for cylinderical\",\"type\":\"cyl\",\"maxPower\":10,\"minPower\":0}]},\"refId\":65,\"subtitle\":\"Scratch Resistant 1\",\"title\":\"Scratch Resistant\"}}]";
		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.GET_BUYOPTION_COATING_V2, payload);
		Assert.assertEquals(response.statusCode(), 200);
		int countAfterAdding = ProductUtil.coatingCount();
		Assert.assertEquals(countBeforeAdding + 1, countAfterAdding);
	}

	@Test(enabled = false)
	public void updateCoating() throws URISyntaxException, IOException, JSONException {
		JSONObject obj = ProductUtil.getLastCoatingDetails();
		obj.remove("marketPrice");
		int newPrice = (int) (300 + Math.round(Math.random() * GenericUtil.randomNumber(2)));
		log.info(newPrice);
		obj.put("marketPrice", newPrice);
		JSONObject payload = new JSONObject();
		payload.put("coating", obj);
		Response response = RestAssuredUtils.PUT(JunoV2ProductPathConstants.GET_BUYOPTION_COATING_V2,
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		JSONObject updatedObject = ProductUtil.getLastCoatingDetails();
		Assert.assertEquals(updatedObject.getInt("marketPrice"), newPrice);
	}

	@Test(enabled = true)
	public void carouselBanner() throws Exception {
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.DESKTOP));
		// headers.add(new BasicNameValuePair("X-Client-Org", "LENSKART"));

		String carouselBanner_URL = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_CarouselBanner_V2;

		Set<String> set = new HashSet<>(Arrays.asList(ApplicationUtil.getcategoryId("category_eyeglasses"),
				ApplicationUtil.getcategoryId("category_sunglasses"),
				ApplicationUtil.getcategoryId("category_contact_lens")));

		String string = String.join(",", set);
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		params.add(new BasicNameValuePair("category", string));

		HttpResponse httpResponse = RequestUtil.getRequest(carouselBanner_URL, headers, params);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		JSONArray category_list = responseJson.getJSONArray("result");
		Assert.assertTrue(category_list.length() >= 1);
		for (int a = 0; a < category_list.length(); a++) {
			JSONArray product_list = category_list.getJSONObject(a).getJSONArray("products");
			for (int i = 0; i < product_list.length(); i++) {
				String productID = product_list.getJSONObject(i).getString("id");
				log.info(productID);
				Assert.assertTrue(product_list.length() >= 1);
			}
		}
	}

	@Test(enabled = false)
	public void getCatalogMetaData() throws Exception {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_METADATA,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()));
		Assert.assertEquals(response.statusCode(), 200);
		JSONObject responseBody = GenericUtil.stringToJson(response.asString());
		JSONObject result = responseBody.getJSONObject("result");
		Assert.assertTrue(result.getJSONArray("filters").length() == 5);
		Assert.assertTrue(result.getJSONArray("countries").length() == 4);
		Assert.assertTrue(result.getJSONArray("sortOptions").length() == 2);

		for (int i = 0; i < result.getJSONArray("filters").length(); i++) {
			String id = result.getJSONArray("filters").getJSONObject(i).getString("id");
			if (id.equals("countryCodes")) {
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("name"), "Country Name");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("type"), "radio");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("optionsMap"), "null");
				Assert.assertTrue(
						result.getJSONArray("filters").getJSONObject(i).getJSONArray("options").length() == 4);
				for (int j = 0; j < result.getJSONArray("filters").getJSONObject(i).getJSONArray("options")
						.length(); j++) {
					JSONArray options = result.getJSONArray("filters").getJSONObject(i).getJSONArray("options");
					log.info("Countries " + options.getJSONObject(j).getString("id"));
				}
			} else if (id.equals("brandNames")) {
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("name"), "Brands");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("type"), "checkbox");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("optionsMap"), "null");
				Assert.assertTrue(
						result.getJSONArray("filters").getJSONObject(i).getJSONArray("options").length() >= 300);
			} else if (id.equals("status")) {
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("name"), "Status");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("type"), "checkbox");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("optionsMap"), "null");
				for (int k = 0; k < result.getJSONArray("filters").getJSONObject(i).getJSONArray("options")
						.length(); k++) {
					JSONArray options = result.getJSONArray("filters").getJSONObject(i).getJSONArray("options");
					int id1 = options.getJSONObject(k).getInt("id");
					if (id1 == 1) {
						Assert.assertEquals(options.getJSONObject(k).getString("title"), "Active");
					}
					if (id1 == 0) {
						Assert.assertEquals(options.getJSONObject(k).getString("title"), "Inactive");

					}
				}
			} else if (id.equals("categories")) {
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("name"), "Category");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("type"), "checkbox");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("optionsMap"), "null");
				Assert.assertTrue(
						result.getJSONArray("filters").getJSONObject(i).getJSONArray("options").length() >= 30);
				JSONArray options;
				ArrayList<String> list = new ArrayList<String>();
				for (int l = 0; l < result.getJSONArray("filters").getJSONObject(i).getJSONArray("options")
						.length(); l++) {
					options = result.getJSONArray("filters").getJSONObject(i).getJSONArray("options");
					list.add(options.getJSONObject(l).getString("id"));
				}
				Assert.assertTrue(
						list.contains("Contact Lens") || list.contains("Eyeglasses") || list.contains("Sunglasses"));
			} else if (id.equals("prices")) {
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("name"), "Price");
				Assert.assertEquals(result.getJSONArray("filters").getJSONObject(i).getString("type"), "checkbox");
				JSONObject optionsMap = result.getJSONArray("filters").getJSONObject(i).getJSONObject("optionsMap");
				JSONArray IN = optionsMap.getJSONArray("IN");
				JSONArray SG = optionsMap.getJSONArray("SG");
				JSONArray US = optionsMap.getJSONArray("US");

				Assert.assertEquals(IN.getJSONObject(0).getString("id"), "0-999");
				Assert.assertEquals(IN.getJSONObject(0).getString("title"), "0 ₹ - 999 ₹");
				Assert.assertEquals(IN.getJSONObject(1).getString("id"), "1000-2999");
				Assert.assertEquals(IN.getJSONObject(1).getString("title"), "1000 ₹ - 2999 ₹");
				Assert.assertEquals(IN.getJSONObject(2).getString("id"), "3000-4999");
				Assert.assertEquals(IN.getJSONObject(2).getString("title"), "3000 ₹ - 4999 ₹");
				Assert.assertEquals(IN.getJSONObject(3).getString("id"), "5000-9999");
				Assert.assertEquals(IN.getJSONObject(3).getString("title"), "5000 ₹ - 9999 ₹");
				Assert.assertEquals(IN.getJSONObject(4).getString("id"), "10000+");
				Assert.assertEquals(IN.getJSONObject(4).getString("title"), "10000 ₹+");

				Assert.assertEquals(SG.getJSONObject(0).getString("id"), "0-88");
				Assert.assertEquals(SG.getJSONObject(0).getString("title"), "0 S$ - 88 S$");
				Assert.assertEquals(SG.getJSONObject(1).getString("id"), "89-129");
				Assert.assertEquals(SG.getJSONObject(1).getString("title"), "89 S$ - 129 S$");
				Assert.assertEquals(SG.getJSONObject(2).getString("id"), "130-188");
				Assert.assertEquals(SG.getJSONObject(2).getString("title"), "130 S$ - 188 S$");
				Assert.assertEquals(SG.getJSONObject(3).getString("id"), "188+");
				Assert.assertEquals(SG.getJSONObject(3).getString("title"), "188 S$+");

				Assert.assertEquals(US.getJSONObject(0).getString("id"), "0-75");
				Assert.assertEquals(US.getJSONObject(0).getString("title"), "0 $ - 75 $");
				Assert.assertEquals(US.getJSONObject(1).getString("id"), "76-135");
				Assert.assertEquals(US.getJSONObject(1).getString("title"), "76 $ - 135 $");
				Assert.assertEquals(US.getJSONObject(2).getString("id"), "136-150");
				Assert.assertEquals(US.getJSONObject(2).getString("title"), "136 $ - 150 $");
				Assert.assertEquals(US.getJSONObject(3).getString("id"), "150-199");
				Assert.assertEquals(US.getJSONObject(3).getString("title"), "150 $ - 199 $");
				Assert.assertEquals(US.getJSONObject(4).getString("id"), "200+");
				Assert.assertEquals(US.getJSONObject(4).getString("title"), "200 $+");
			}
		}
		for (int m = 0; m < result.getJSONArray("countries").length(); m++) {
			String code = result.getJSONArray("countries").getJSONObject(m).getString("code");
			String name = result.getJSONArray("countries").getJSONObject(m).getString("name");
			String currencyCode = result.getJSONArray("countries").getJSONObject(m).getString("currencyCode");
			String currencySign = result.getJSONArray("countries").getJSONObject(m).getString("currencySign");
			String currencySymbol = result.getJSONArray("countries").getJSONObject(m).getString("currencySymbol");

			switch (code) {
			case "SG":
				Assert.assertEquals(name, "SINGAPORE");
				Assert.assertEquals(currencyCode, "SGD");
				Assert.assertEquals(currencySign, "$");
				Assert.assertEquals(currencySymbol, "S$");
				break;

			case "AE":
				Assert.assertEquals(name, "United Arab Emirates");
				Assert.assertEquals(currencyCode, "AED");
				Assert.assertEquals(currencySign, "AED");
				Assert.assertEquals(currencySymbol, "AED");
				break;

			case "IN":
				Assert.assertEquals(name, "INDIA");
				Assert.assertEquals(currencyCode, "INR");
				Assert.assertEquals(currencySign, "Rs");
				Assert.assertEquals(currencySymbol, "₹");
				break;

			case "US":
				Assert.assertEquals(name, "USA");
				Assert.assertEquals(currencyCode, "USD");
				Assert.assertEquals(currencySign, "$");
				Assert.assertEquals(currencySymbol, "$");
				break;
			}
		}

		Assert.assertEquals(result.getJSONArray("sortOptions").getJSONObject(0).getString("id"), "updatedAt");
		Assert.assertEquals(result.getJSONArray("sortOptions").getJSONObject(0).getString("name"), "Last Updated");
		Assert.assertEquals(result.getJSONArray("sortOptions").getJSONObject(1).getString("id"), "productId");
		Assert.assertEquals(result.getJSONArray("sortOptions").getJSONObject(1).getString("name"), "Product Id");
	}

	@Test(enabled = false)
	public void fetchProductFilterWise() throws Exception {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("pageNumber", "1");
		queryParams.put("pageSize", "10");
		queryParams.put("productIds", ApplicationConstants.goldPid);
		queryParams.put("sort", "updatedAt");
		queryParams.put("countryCodes", "IN");

		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.FETCH_PRODUCTS_FILTERWISE,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()), queryParams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getInt("result.totalCount"), 1);
		Assert.assertEquals(response.jsonPath().getString("result.products[0].brand"), "LK Gold");
		Assert.assertEquals(
				response.jsonPath().getString("result.products[0].countrySpecificAttributes[0].countryCode"), "IN");
		Assert.assertEquals(response.jsonPath().getDouble("result.products[0].countrySpecificAttributes[0].price"),
				600.0);
		Assert.assertTrue(response.jsonPath().getBoolean(("result.products[0].countrySpecificAttributes[0].isActive")));
	}

	@Test(enabled = false)
	public void patchProductsData() throws Exception {
		String payload = "[\n" + "  {\n" + "    \"brand\": \"Vincent Chase\",\n" + "    \"buyFourPrice\": 1,\n"
				+ "    \"buyOnePrice\": 2,\n" + "    \"buyTwoPrice\": 3,\n" + "    \"category\": \"Eyeglasses\",\n"
				+ "    \"costPrice\": 1,\n" + "    \"countryCode\": \"IN\",\n" + "    \"currencyCode\": \"INR\",\n"
				+ "    \"frameType\": \"half_rim\",\n" + "    \"freeFramePrice\": 1,\n" + "    \"isActive\": true,\n"
				+ "    \"price\": 1,\n" + "    \"productId\": 5678,\n" + "    \"specialOfferPrice\": 0,\n"
				+ "    \"specialPrice\": 0,\n" + "    \"updatedAt\": \"2021-05-31T09:45:01.356Z\"\n" + "  }\n" + "]";
		Response response = RestAssuredUtils.PATCH(JunoV2ProductPathConstants.FETCH_PRODUCTS_FILTERWISE,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()), payload);
		Assert.assertEquals(response.statusCode(), 200);
		JSONObject responseObj = GenericUtil.stringToJson(response.asString());
		JSONObject object = responseObj.getJSONObject("result").getJSONArray("failedProducts").getJSONObject(0);
		object.remove("updatedAt");
		JSONObject requestObj = GenericUtil.stringToJsonArray(payload).getJSONObject(0);
		requestObj.remove("updatedAt");
		Assert.assertTrue(JsonUtil.compare_json_objects(object, requestObj));
	}

	@Test(enabled = false)
	public void exportCatalogProducts() throws Exception {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("productIds", ApplicationConstants.goldPid);
		queryParams.put("status", "true");
		queryParams.put("countryCodes", "IN");

		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.EXPORT_PRODUCTS_DATA,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()), queryParams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.asString().contains(ApplicationConstants.goldPid));
		Assert.assertTrue(response.asString().contains(
				"PID,Category,Brand,Country,Price,Special Price,Special Price Offer,Cost Price,Status,Free Frame Price,Buy One Price,Buy Two Price,Buy Four Price,Frame Type"));
	}

	@Test(enabled = false)
	public void exportSampleBulkUploadTemplate() throws Exception {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.BULK_UPLOAD_TMEPLATE,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.asString().contains(
				"123,sample_category,sample_brand,IN,1000.0,1000.0,1000.0,1000.0,1,100.0,200.0,350.0,600.0,full_rim"));
		Assert.assertTrue(response.asString().contains(
				"PID,Category,Brand,Country,Price,Special Price,Special Price Offer,Cost Price,Status,Free Frame Price,Buy One Price,Buy Two Price,Buy Four Price,Frame Type"));
	}

	@Test(enabled = false)
	public void fetchProduct() throws Exception {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("pid", ApplicationConstants.goldPid);
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.FETCH_PRODUCT, pathParams,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.brand"), "LK Gold");
		Assert.assertEquals(response.jsonPath().getString("result.productId"), ApplicationConstants.goldPid);

		JSONObject result = GenericUtil.stringToJson(response.asString()).getJSONObject("result");
		for (int i = 0; i < result.getJSONArray("countrySpecificAttributes").length(); i++) {
			String countryCode = result.getJSONArray("countrySpecificAttributes").getJSONObject(i)
					.getString("countryCode");
			double price = result.getJSONArray("countrySpecificAttributes").getJSONObject(i).getDouble("price");
			double specialPrice = result.getJSONArray("countrySpecificAttributes").getJSONObject(i)
					.getDouble("specialPrice");
			String currencyCode = result.getJSONArray("countrySpecificAttributes").getJSONObject(i)
					.getString("currencyCode");
			String currencySymbol = result.getJSONArray("countrySpecificAttributes").getJSONObject(i)
					.getString("currencySymbol");
			String countryName = result.getJSONArray("countrySpecificAttributes").getJSONObject(i)
					.getString("countryName");
			boolean isActive = result.getJSONArray("countrySpecificAttributes").getJSONObject(i).getBoolean("isActive");

			if (countryCode.equals("IN")) {
				Assert.assertEquals(countryCode, GenericUtil.fromProperties("India", "country_code"));
				Assert.assertEquals(currencyCode, GenericUtil.fromProperties("India", "currency_code"));
				Assert.assertEquals(currencySymbol, GenericUtil.fromProperties("India", "currency_symbol"));
				Assert.assertEquals(countryName, GenericUtil.fromProperties("India", "country_name"));
				Assert.assertTrue(isActive);
				Assert.assertEquals(price, 600.0);
				Assert.assertEquals(specialPrice, 600.0);
			} else if (countryCode.equals("SG")) {
				Assert.assertEquals(countryCode, GenericUtil.fromProperties("Singapore", "country_code"));
				Assert.assertEquals(currencyCode, GenericUtil.fromProperties("Singapore", "currency_code"));
				Assert.assertEquals(currencySymbol, GenericUtil.fromProperties("Singapore", "currency_symbol"));
				Assert.assertEquals(countryName, GenericUtil.fromProperties("Singapore", "country_name"));
				Assert.assertTrue(isActive);
				Assert.assertEquals(price, 30.0);
				Assert.assertEquals(specialPrice, 30.0);
			}
		}
	}

	@Test(enabled = false)
	public void addProduct() throws Exception {
		String payload = "{\n" + "    \"category\": \"Eyeglasses\",\n" + "    \"brand\": \"Vincent Chase\",\n"
				+ "    \"frameType\": \"half_rim\",\n" + "    \"countrySpecificAttributes\": [\n" + "        {\n"
				+ "            \"price\": \"133\",\n" + "            \"specialPrice\": \"222\",\n"
				+ "            \"specialOfferPrice\": \"333\",\n" + "            \"costPrice\": \"1\",\n"
				+ "            \"countryCode\": \"IN\",\n" + "            \"freeFramePrice\": \"1\",\n"
				+ "            \"buyOnePrice\": \"555\",\n" + "            \"buyTwoPrice\": \"666\",\n"
				+ "            \"buyFourPrice\": \"777\",\n" + "            \"isActive\": true\n" + "        }\n"
				+ "    ]\n" + "}";

		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("productId", GenericUtil.createRandomNumber(9));

		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.ADD_PRODUCT_TO_CATALOG, pathParams,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()), payload);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.category"), "Eyeglasses");
		Assert.assertEquals(response.jsonPath().getString("result.brand"), "Vincent Chase");
		Assert.assertEquals(response.jsonPath().getString("result.frameType"), "half_rim");
		Assert.assertEquals(response.jsonPath().getString("result.frameType"), "half_rim");
		Assert.assertEquals(response.jsonPath().getString("result.countrySpecificAttributes[0].price"), "133.0");
		Assert.assertEquals(response.jsonPath().getString("result.countrySpecificAttributes[0].countryCode"), "IN");
	}

	@Test(enabled = false)
	public void updateProductStatus() throws Exception {
		UpdateProductStatus updateProductStatus = new UpdateProductStatus();
		updateProductStatus.updateProductStatus();
	}

	@Test(enabled = false)
	public void fetchCatalogUser() throws Exception {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.FETCH_CATALOG_USER,
				HeadersUtil.headers_authToken(ProductUtil.catalogUserLoginToken()));
		Assert.assertEquals(response.statusCode(), 200);
		JSONObject result = GenericUtil.stringToJson(response.asString()).getJSONObject("result");
		if (Environments.environment.equals(Profiles.PREPROD)) {
			Assert.assertEquals(result.getString("id"), "<EMAIL>");
			Assert.assertTrue(result.getBoolean("isActive"));
			JSONArray s = result.getJSONArray("permissions");
			if (s.get(0) != null) {
				Assert.assertTrue(s.get(0).equals("VIEW") || s.get(0).equals("UPDATE"));
			}
			if (s.get(1) != null) {
				Assert.assertTrue(s.get(1).equals("VIEW") || s.get(1).equals("UPDATE"));
			}
		}
	}

	String user = "test" + GenericUtil.createRandomNumber(6) + "<EMAIL>";

	@Test(enabled = false)
	public void registerCatalogUser() throws JSONException {
		JSONObject payload = new JSONObject();
		payload.put("id", user);
		payload.put("password", "123456");
		payload.put("isActive", false);
		JSONArray permissions = new JSONArray();
		permissions.put("VIEW");
		payload.put("permissions", permissions);
		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.REGISTER_CATALOG_USER,
				HeadersUtil.headers_authToken(ApplicationConstants.xAuthToken), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
	}

	@Test(dependsOnMethods = "registerCatalogUser", enabled = false)
	public void updateUserPassswordAndPermission() throws JSONException {
		JSONObject payload = new JSONObject();
		payload.put("id", user);
		payload.put("password", "1234567");
		payload.put("isActive", false);
		JSONArray permissions = new JSONArray();
		permissions.put("VIEW");
		payload.put("permissions", permissions);
		Response response = RestAssuredUtils.PATCH(JunoV2ProductPathConstants.FETCH_CATALOG_USER,
				HeadersUtil.headers_authToken(ApplicationConstants.xAuthToken), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}

	@Test(dependsOnMethods = "updateUserPassswordAndPermission", enabled = false)
	public void loginCatalogUser() throws JSONException {
		JSONObject payload = new JSONObject();
		payload.put("id", user);
		payload.put("password", "1234567");
		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.LOGIN_CATALOG_USER,
				HeadersUtil.headers_authToken(ApplicationConstants.xAuthToken), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.id"), user);
	}

	@Test(enabled = true)
	public void getCategoryBestSellers() throws Exception {
		String REQUESTURLGETCATEGORYBESTSELLERS = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_CATEGORY_BEST_SELLERS;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		HttpResponse httpResponse = RequestUtil.getRequest(REQUESTURLGETCATEGORYBESTSELLERS, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		JSONArray product_list = responseJson.getJSONObject("result").getJSONArray("product_list");
		Assert.assertTrue(product_list.length() >= 1);
		for (int i = 0; i < product_list.length(); i++) {
			Assert.assertNotNull(product_list.getJSONObject(i).getInt("id"), "products not present");
		}
		Assert.assertEquals(responseJson.getInt("status"), 200, "status mismatch");
		Assert.assertNotNull(responseJson.getString("traceId"), "traceId not present");
	}

	@Test(enabled = true)
	public void productAvailableForLensShade() throws Exception {
		String REQUESTURLGETPRODUCTAVAILABLEFORLENSSHADE = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_PRODUCTS_AVAILABLE_FOR_LENS_SHADE;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		params.add(new BasicNameValuePair("colorShadeId", "343"));
		HttpResponse httpResponse = RequestUtil.getRequest(REQUESTURLGETPRODUCTAVAILABLEFORLENSSHADE, headers, params);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		JSONArray product_list = responseJson.getJSONObject("result").getJSONArray("product_list");
		Assert.assertTrue(product_list.length() >= 0);

	}

	@Test(enabled = true)
	public void getAvailableLensShade() throws Exception {
		String REQUESTURLGETAVAILABLEFORLENSSHADE = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_AVAILABLE_LENS_SHADE;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		HttpResponse httpResponse = RequestUtil.getRequest(REQUESTURLGETAVAILABLEFORLENSSHADE, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		// JSONArray product_list =
		// responseJson.getJSONObject("result").getJSONArray("product_list");
		Assert.assertTrue(responseJson.length() >= 1);

	}

	@Test
	public void newArrivals() throws Exception {
		String newArrivals_URL = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_CATEGORY_NEW_ARRIVALS;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		HttpResponse httpResponse = RequestUtil.getRequest(newArrivals_URL, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		JSONArray product_list = responseJson.getJSONObject("result").getJSONArray("product_list");
		Assert.assertTrue(product_list.length() >= 1);
		for (int i = 0; i < product_list.length(); i++) {
			String productID = product_list.getJSONObject(i).getString("id");
			log.info("productID" + productID);
			JSONArray prices = product_list.getJSONObject(i).getJSONArray("prices");
			for (int j = 0; j < prices.length(); j++) {
				Assert.assertEquals(prices.getJSONObject(j).getString("currency_code"), "INR");
			}
			JSONArray color_options = product_list.getJSONObject(i).getJSONArray("color_options");
			for (int k = 0; k < color_options.length(); k++) {
				String color_option_pid = color_options.getJSONObject(k).getString("id");
				log.info("color_option_pid" + color_option_pid);
				JSONArray prices_color_option = color_options.getJSONObject(k).getJSONArray("prices");
				for (int l = 0; l < prices_color_option.length(); l++) {
					// Assert.assertEquals(prices_color_option.getJSONObject(l).getString("currency_code"),
					// "INR");
				}
			}
		}
	}

	@Test(enabled = true)
	public void searchQuery() throws Exception {
		String RequestUrl = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_SEARCH_QUERY_V2, "Eyeglass");
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", XApiClient.ANDROID));
		HttpResponse httpResponse = RequestUtil.getRequest(RequestUrl, headers, null);
		JSONObject jsonResponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);
		int num_of_products = jsonResponse.getJSONObject("result").getInt("num_of_products");
		Assert.assertTrue(num_of_products >= 1);
		Assert.assertTrue(jsonResponse.getJSONObject("result").getJSONArray("product_list").length() > 0);
	}

	@Test
	public void getCategoryAPI() throws Exception {
		String category_URL = Environments.SERVICES_ENVIRONMENT + String.format(
				JunoV2ProductPathConstants.GET_CATEGORY_API, ApplicationUtil.getcategoryId("category_eyeglasses"));

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		HttpResponse httpResponse = RequestUtil.getRequest(category_URL, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		JSONArray product_list = responseJson.getJSONObject("result").getJSONArray("product_list");
		Assert.assertTrue(product_list.length() > 0);
		for (int i = 0; i < product_list.length(); i++) {
			String productID = product_list.getJSONObject(i).getString("id");
			log.info("productID" + productID);
			JSONArray prices = product_list.getJSONObject(i).getJSONArray("prices");
			for (int j = 0; j < prices.length(); j++) {
				Assert.assertEquals(prices.getJSONObject(j).getString("currency_code"), "INR");
			}
			JSONArray color_options = product_list.getJSONObject(i).getJSONArray("color_options");
			for (int k = 0; k < color_options.length(); k++) {
				String color_option_pid = color_options.getJSONObject(k).getString("id");
				log.info("color_option_pid" + color_option_pid);
				JSONArray prices_color_option = color_options.getJSONObject(k).getJSONArray("prices");
				for (int l = 0; l < prices_color_option.length(); l++) {
					Assert.assertEquals(prices_color_option.getJSONObject(l).getString("currency_code"), "INR");
				}
			}
		}
	}

	// This API works only in preprod as of now, so will keep in enabled=false
	@Test(enabled = false)
	public void getCategoryV3API() throws Exception {
		String V3_Category_URL = Environments.SERVICES_ENVIRONMENT + String.format(
				JunoV2ProductPathConstants.GET_CATEGORY_V3_API, ApplicationUtil.getcategoryId("category_eyeglasses"));

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		List<NameValuePair> params = new ArrayList<NameValuePair>();
		params.add(new BasicNameValuePair("oos", "false"));
		params.add(new BasicNameValuePair("include-images", "false"));
		params.add(new BasicNameValuePair("newFilterFlowEnabled", "false"));
		params.add(new BasicNameValuePair("arEnabled", "false"));
		params.add(new BasicNameValuePair("customFilters", "false"));
		params.add(new BasicNameValuePair("personalisationRequired", "false"));

		HttpResponse httpResponse = RequestUtil.getRequest(V3_Category_URL, headers, params);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		JSONArray product_list = responseJson.getJSONObject("result").getJSONArray("content");
		Assert.assertTrue(product_list.length() > 0);
		for (int i = 0; i < product_list.length(); i++) {
			String productID = product_list.getJSONObject(i).getString("id");
			log.info("productID" + productID);
			JSONArray prices = product_list.getJSONObject(i).getJSONObject("data").getJSONArray("prices");
			for (int j = 0; j < prices.length(); j++) {
				Assert.assertEquals(prices.getJSONObject(j).getString("currency_code"), "INR");
			}
			JSONArray color_options = product_list.getJSONObject(i).getJSONObject("data").getJSONArray("color_options");
			for (int k = 0; k < color_options.length(); k++) {
				String color_option_pid = color_options.getJSONObject(k).getString("id");
				log.info("color_option_pid" + color_option_pid);
				JSONArray prices_color_option = color_options.getJSONObject(k).getJSONArray("prices");
				for (int l = 0; l < prices_color_option.length(); l++) {
					Assert.assertEquals(prices_color_option.getJSONObject(l).getString("currency_code"), "INR");
				}
			}
		}
	}

	@Test
	public void getCategoryFilterV3API() throws Exception {
		String V3_Category_Filter_URL = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_CATEGORY_FILTER_V3_API,
						ApplicationUtil.getcategoryId("category_eyeglasses"));

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		headers.add(new BasicNameValuePair("X-Country-Code", ApplicationConstants.Countries.INDIA));

		HttpResponse httpResponse = RequestUtil.getRequest(V3_Category_Filter_URL, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Response code mismatch");
		JSONArray filters_list = responseJson.getJSONObject("result").getJSONArray("filters");
		Assert.assertTrue(filters_list.length() > 0);
		for (int i = 0; i < filters_list.length(); i++) {
			String lenskartPrice = filters_list.getJSONObject(i).getString("id");
			log.info("lenskartPrice" + lenskartPrice);
		}
	}

	// Get Search Result (With Revamped Filters)
	@Test
	public void SearchAPIWithRevampedAPIV3Filters() throws Exception {
		String SearchAPIWithRevampedAPIFiltersURL = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.SEARCH_API_WITH_REVAMPED_FILTERS_V3_API;

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		JSONObject payload = new JSONObject();
		payload.put("frameSize", "1");
		payload.put("objectID", "19");
		payload.put("page", "0");
		payload.put("page-size", "20");
		payload.put("query", "eyeglass");
		payload.put("showProductResults", "true");
		payload.put("useQsProductIndex", "true");

		HttpResponse httpResponse = RequestUtil.postRequest(SearchAPIWithRevampedAPIFiltersURL, headers, payload);
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		log.info("Total productRecommendations = "
				+ responseJSON.getJSONObject("result").getJSONArray("product_list").length());
		Assert.assertTrue(responseJSON.getJSONObject("result").getJSONArray("product_list").length() >= 0);
	}

	// Get Search Revamped Filters
	@Test(enabled = true)
	public void SearchRevampedFiltersV3API() throws Exception {
		String SearchRevampedFiltersAPIURL = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.SEARCH_REVAMPED_FILTERS_V3_API;

		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		JSONObject payload = new JSONObject();
		payload.put("frameSize", "1");
		payload.put("objectID", "19");
		payload.put("page", "0");
		payload.put("page-size", "20");
		payload.put("query", "eyeglass");
		payload.put("showProductResults", "true");
		payload.put("useQsProductIndex", "true");

		HttpResponse httpResponse = RequestUtil.postRequest(SearchRevampedFiltersAPIURL, headers, payload);
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
//			log.info("Total noOfProducts = "
//					+ responseJSON.getJSONObject("result").getString("noOfProducts").length());
		Assert.assertTrue(responseJSON.getJSONObject("result").getString("noOfProducts").length() >= 0);
	}

	// CMS Gold Controller
	@Test
	public void getGoldTierDetailsAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_GOLD_TIER_DETAILS_API;

		HashMap<String, String> queryparam = new HashMap<String, String>();
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getList("result").size() >= 1);

	}

	// get gold tier by productId
	@Test
	public void getGoldTierByProductIdAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_GOLD_TIER_BY_PRODUCTID_API, "128269");

		HashMap<String, String> queryparam = new HashMap<String, String>();
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);
		headers.put("X-auth-token", ApplicationConstants.xAuthToken);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.tierName"), "gold_original",
				"Gold tier is not correct");

	}

	// get gold tier by gold tier name
	@Test
	public void getGoldTierByNameAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_GOLD_TIER_BY_NAME_API, "gold_trial_pack");

		HashMap<String, String> queryparam = new HashMap<String, String>();
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);
		headers.put("X-auth-token", ApplicationConstants.xAuthToken);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.tierName"), "gold_trial_pack",
				"Gold tier is not correct");

	}

	// Get product document by Id
	@Test
	public void getproductDocumentByIDAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_PRODUCT_DOCUMENT_BY_ID_API, "119237");

		Response response = RestAssuredUtils.GET(endpoint);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("product_id"), "119237", "ProductId is not correct");

	}

	// Product Doc Search
	@Test
	public void productDocumentSearchAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.PRODUCT_DOCUMENT_SEARCH_API;

//		List<String> products = new ArrayList<>(Arrays.asList("119237", "93870", "209422"));
//		String products_list = String.join(",", products);
//		log.info(products_list);

		HashMap<String, String> queryparam = new HashMap<String, String>();
		queryparam.put("productIds", "119237,93870,209422");
		queryparam.put("page", "0");
		queryparam.put("size", "20");
		queryparam.put("country", ApplicationConstants.Countries.INDIA);

		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.size"), "3",
				"total number of products is not correct");

	}

	// Get Limited product info
	@Test
	public void getLimitedproductInfoPOSTAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_LIMITED_PRODUCT_INFO_POST_API;

		HashMap<String, String> queryparam = new HashMap<String, String>();
		queryparam.put("inStock", "true");
		queryparam.put("platform", "POS");
		queryparam.put("size", "20");

		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		JSONArray productId = new JSONArray();
		productId.put("128269");
		JSONObject payload = new JSONObject();
		payload.put("inStock", "true");
		payload.put("pageNumber", "0");
		payload.put("pageSize", "20");
		payload.put("productId", productId);
		log.info(payload);

		Response response = RestAssuredUtils.POST_WITH_QUERYPARAMS(endpoint, queryparam, headers,
				GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.numberOfElements"), "1",
				"total number of Elements is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.products[0].product_id"), "128269",
				"productId is not correct");

	}

	// Filters Controller
	@Test
	public void addLocaleFilterNamePOSTAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.ADD_LOCALE_FILTER_API;

		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		JSONObject filters_object = new JSONObject();
		filters_object.put("filterId", "123");
		JSONArray filters = new JSONArray();
		filters.put(filters_object);
		JSONObject payload = new JSONObject();
		payload.put("filters", filters);
		log.info(payload);

		Response response = RestAssuredUtils.POST(endpoint, headers, GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");

	}

	// Metadata API
	@Test
	public void getproductMetadataAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.PRODUCT_METADATA_API;

		Response response = RestAssuredUtils.GET(endpoint);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");

	}

	@Test
	public void getSimilarProducts() throws URISyntaxException, Exception {
		// String pid =
		// ProductUtil.getProductIdFromTheGivenCategory("category_eyeglasses",
		// XApiClient.ANDROID);
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", ApplicationUtil.getcategoryId("category_eyeglasses"));
		// pathParams.put("productId", pid);
		pathParams.put("productId", "100763");
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.GET_SIMILAR_PRODUCTS,
				pathParams, HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getInt("result.num_of_products") > 0);
		Assert.assertTrue(response.jsonPath().getList("result.product_list").size() > 0);
	}

	@Test
	public void getFhtFilters() throws JSONException {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("categoryId", ApplicationUtil.getcategoryId("category_eyeglasses"));
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.FHT_FILTERS, pathParams,
				HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
		JSONObject result = GenericUtil.stringToJson(response.asString()).getJSONObject("result");

		Assert.assertTrue(result.getJSONArray("men").length() == 2);
		Assert.assertTrue(result.getJSONArray("women").length() == 2);
		for (int i = 0; i < result.getJSONArray("men").length(); i++) {
			String id = result.getJSONArray("men").getJSONObject(i).getString("id");
			String name = result.getJSONArray("men").getJSONObject(i).getString("name");
			JSONArray options = result.getJSONArray("men").getJSONObject(i).getJSONArray("options");
			if (id.equals("frametype_id")) {
				Assert.assertEquals(name, "FRAME TYPE");
				for (int j = 0; j < options.length(); j++) {
					String tiltle = options.getJSONObject(j).getString("title");
					int id1 = options.getJSONObject(j).getInt("id");
					switch (tiltle) {
					case "Full Rim":
						Assert.assertEquals(id1, 11370);
						break;
					case "Half Rim":
						Assert.assertEquals(id1, 11371);
						break;
					case "Rimless":
						Assert.assertEquals(id1, 11372);
						break;
					}
				}
			} else if (id.equals("frame_color_id")) {
				Assert.assertEquals(name, "COLOR");
			}
		}
	}

	@Test
	public void fetchFhtPackage() {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.FETCH_FHT_PACKAGE);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getList("result.package_list").size() > 4000);
	}

	@Test
	public void fetchFhtPackageById() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", ProductUtil.returnFhtPackageId());
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.FETCH_FHT_PACKAGE_BY_ID,
				pathParams, HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getList("result.package_list").size() > 0);
		Assert.assertTrue(response.jsonPath().getList("result.package_list[0].product_list").size() > 0);
	}

	@Test
	public void getFilters() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("query", "21");
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.GET_FILTERS, pathParams,
				HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getInt("result.number_of_products") > 0);
		for (int i = 0; i < response.jsonPath().getList("result.filters").size(); i++) {
			Assert.assertTrue(response.jsonPath().getList("result.filters[" + i + "].options").size() > 0);
		}
	}

	@Test
	public void getReviewFilters() {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_REVIEW_FILTERS);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.filters[0].id"), "filter_rating_id");
		Assert.assertEquals(response.jsonPath().getString("result.filters[0].name"), "Filter Rating");
		for (int i = 0; i < response.jsonPath().getList("result.filters").size(); i++) {
			int title = 1;
			int id = 1;
			String filter_Id = response.jsonPath().getString("result.filters[" + i + "].id");
			for (int j = 0; j < response.jsonPath().getList("result.filters[" + i + "].options").size(); j++) {
				if (filter_Id.equals("filter_rating_id")) {
					Assert.assertEquals(
							response.jsonPath().getInt("result.filters[" + i + "].options[" + j + "].title"), title);
					Assert.assertEquals(response.jsonPath().getInt("result.filters[" + i + "].options[" + j + "].id"),
							id);
					Assert.assertEquals(
							response.jsonPath().getInt("result.filters[" + i + "].options[" + j + "].sort_order"), 0);
					log.info(title);
					title++;
					log.info(title + " after updating");
					id++;
				} else if (filter_Id.equals("filter_images")) {
					Assert.assertEquals(
							response.jsonPath().getString("result.filters[" + i + "].options[" + j + "].title"),
							"With images");
					Assert.assertEquals(
							response.jsonPath().getBoolean("result.filters[" + i + "].options[" + j + "].id"), true);
					Assert.assertEquals(
							response.jsonPath().getInt("result.filters[" + i + "].options[" + j + "].sort_order"), 0);
				}
			}
			Assert.assertEquals(response.jsonPath().getString("result.sort[0].id"), "sort");
			Assert.assertEquals(response.jsonPath().getString("result.sort[0].name"), "Sort by Rating");

			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[0].title"),
					"Date (Newest first)");
			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[0].id"), "date&dir=desc");
			Assert.assertEquals(response.jsonPath().getInt("result.sort[0].options[0].sort_order"), 0);

			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[1].title"),
					"Date (Oldest first)");
			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[1].id"), "date&dir=asc");
			Assert.assertEquals(response.jsonPath().getInt("result.sort[0].options[1].sort_order"), 0);

			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[2].title"),
					"Star Rating (Highest first)");
			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[2].id"), "rating&dir=desc");
			Assert.assertEquals(response.jsonPath().getInt("result.sort[0].options[2].sort_order"), 0);

			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[3].title"),
					"Star Rating (Lowest first)");
			Assert.assertEquals(response.jsonPath().getString("result.sort[0].options[3].id"), "rating&dir=asc");
			Assert.assertEquals(response.jsonPath().getInt("result.sort[0].options[3].sort_order"), 0);
		}
	}

	@Test
	public void getNextFilter() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("category_id", ApplicationUtil.getcategoryId("category_eyeglasses"));
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.GET_NEXT_FILTER, pathParams,
				HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
	}

	@Test
	public void getFiltersWithId() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", ApplicationUtil.getcategoryId("category_eyeglasses"));
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.GET_FILTERS_WITH_ID,
				pathParams, HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getInt("result.number_of_products") > 0);
		Assert.assertTrue(response.jsonPath().getList("result.filters").size() > 0);
		List<Object> filters = response.jsonPath().getList("result.filters");
		for (int i = 0; i < filters.size(); i++) {
			Assert.assertTrue(response.jsonPath().getList("result.filters[" + i + "].options").size() > 0);
		}
	}

	@Test
	public void getQuickFilter() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", ApplicationUtil.getcategoryId("category_eyeglasses"));
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.GET_QUICK_FILTER,
				pathParams, HeadersUtil.headers_apiClient(XApiClient.ANDROID));

		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.categoryInfo"), "Eyeglasses");
		// Assert.assertEquals(response.jsonPath().getString("result.title"), "Frame
		// Shape + Test1");
//		Assert.assertEquals(response.jsonPath().getString("result.frameWidthFilters.id"), "frame_width_id");
//		Assert.assertEquals(response.jsonPath().getString("result.frameWidthFilters.name"), "Frame Width");
//		Assert.assertTrue(response.jsonPath().getList("result.frameWidthFilters.options").size() > 0);
		Assert.assertTrue(response.jsonPath().getList("result.quickFilters").size() > 0);
	}

	@Test
	public void updateQuickFilters() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", GenericUtil.createRandomNumber(4));
		log.info(pathParams);
		String payload = "{\"title\":\"Test1\",\"quickFilters\":[{\"name\":\"Rs.0: Rs.999\",\"imageUrl\":null,\"filters\":{\"lenskart_price\":\"0\"},\"enabled\":true,\"platforms\":[\"DESKTOP\",\"ANDROID\",\"IOS\"]},{\"name\":\"Rs.1000: Rs.1999\",\"imageUrl\":null,\"filters\":{\"lenskart_price\":\"1\"},\"enabled\":true,\"platforms\":[\"DESKTOP\",\"ANDROID\",\"IOS\"]},{\"name\":\"Rs.2000: Rs.2999\",\"imageUrl\":null,\"filters\":{\"lenskart_price\":\"2\"},\"enabled\":true,\"platforms\":[\"DESKTOP\",\"ANDROID\",\"IOS\"]},{\"name\":\"Rs.3000: Rs.3999\",\"imageUrl\":null,\"filters\":{\"lenskart_price\":\"3\"},\"enabled\":true,\"platforms\":[\"DESKTOP\",\"ANDROID\",\"IOS\"]},{\"name\":\"Rs.4000: Rs.4999\",\"imageUrl\":null,\"filters\":{\"lenskart_price\":\"4\"},\"enabled\":true,\"platforms\":[\"DESKTOP\",\"ANDROID\",\"IOS\"]}]}";
		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.UPDATE_QUICK_FILTER, payload, pathParams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.message"), "Filter Created Successfully");
		Assert.assertTrue(response.jsonPath().getBoolean("result.success"));
	}

	@Test(dependsOnMethods = "updateQuickFilters", enabled = false)
	public void deleteQuickFilter() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", "7352");
		pathParams.put("title", "Test1");

		Response response = RestAssuredUtils.DELETE_PATH_PARAMS(JunoV2ProductPathConstants.DELETE_QUICK_FILTER,
				pathParams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.message"), "Filter Deleted Successfully");
		Assert.assertTrue(response.jsonPath().getBoolean("result.success"));
	}

	@Test
	public void verifyQuickFilter() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", ApplicationUtil.getcategoryId("category_eyeglasses"));
		pathParams.put("title", "Frame Shape");
		Response response = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.VERIFY_QUICK_FILTER,
				pathParams, HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getBoolean("result"));
	}

	@Test
	public void homeService() throws URISyntaxException, Exception {
		String getHomeServiceURL = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_HOME_SERVICE_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", XApiClient.ANDROID));

		List<NameValuePair> query = new ArrayList<NameValuePair>();
		query.add(new BasicNameValuePair("audienceType", "default"));
		HttpResponse httpResponse = RequestUtil.getRequest(getHomeServiceURL, headers, query);
		JSONObject responseJSON = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		responseJSON.remove("status");
		responseJSON.remove("traceId");
		JSONArray best_sellers = responseJSON.getJSONObject("result").getJSONArray("best_sellers");
		JSONArray new_arrivals = responseJSON.getJSONObject("result").getJSONArray("new_arrivals");
		for (int k = 0; k < best_sellers.length(); k++) {
			best_sellers.getJSONObject(k).remove("image_url");
			best_sellers.getJSONObject(k).remove("avgRating");
			best_sellers.getJSONObject(k).remove("hashtagList");
		}
		for (int l = 0; l < new_arrivals.length(); l++) {
			responseJSON.getJSONObject("result").getJSONArray("new_arrivals").getJSONObject(l).remove("avgRating");
			responseJSON.getJSONObject("result").getJSONArray("new_arrivals").getJSONObject(l).remove("image_url");
			responseJSON.getJSONObject("result").getJSONArray("new_arrivals").getJSONObject(l).remove("product_url");
			responseJSON.getJSONObject("result").getJSONArray("new_arrivals").getJSONObject(l).remove("id");
			responseJSON.getJSONObject("result").getJSONArray("new_arrivals").getJSONObject(l).remove("hashtagList");
		}
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "Status code is not correct");
		log.info(responseJSON);
	}

	@Test
	public void addPowerTemplate() {
		String payload = "{\n" + "    \"powerTemplateList\": [\n" + "        {\n" + "            \"id\": \"123\",\n"
				+ "            \"name\": \"bifocal\",\n" + "            \"powerTypeList\": [\n" + "                {\n"
				+ "                    \"inputType\": \"drop_down\",\n"
				+ "                    \"label\": \"Power(SPH)\",\n" + "                    \"powerDataList\": [\n"
				+ "                        {\n" + "                            \"price\": 0,\n"
				+ "                            \"value\": [\n" + "                               \"-12.75\",\n"
				+ "                            \"-12.50\",\n" + "                            \"-12.25\"\n"
				+ "                            ]\n" + "                        }\n" + "                    ],\n"
				+ "                    \"type\": \"sph\"\n" + "                }\n" + "            ],\n"
				+ "            \"templateId\": \"123\"\n" + "        }\n" + "    ]\n" + "}";

		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.ADD_POWER_TEMPLATE, payload);
		Assert.assertEquals(response.statusCode(), 200);
	}

	@Test(enabled = true)
	public void getProduct_forMultiplepids() throws Exception {
		List<String> view = Arrays.asList("review", "summary");
		for (int j = 0; j < view.size(); j++) {
			String categoryName = "category_eyeglasses";
			JSONObject jsonResponse_category = JunoV2Util.getCategoryDetails(
					ApplicationUtil.getcategoryId(categoryName), ApplicationConstants.XApiClient.ANDROID);
			String requestURL = Environments.SERVICES_ENVIRONMENT
					+ JunoV2ProductPathConstants.GET_PRODUCT_DETAILSOF_MULTIPLE_PID_V2;
			String productsCommaSeparate = "";

			for (int i = 0; i < jsonResponse_category.getJSONObject("result").getJSONArray("product_list")
					.length(); i++) {
				String product_id = jsonResponse_category.getJSONObject("result").getJSONArray("product_list")
						.getJSONObject(i).getString("id");
				productsCommaSeparate = product_id + "," + productsCommaSeparate;
			}
			List<NameValuePair> header = new ArrayList<NameValuePair>();
			header.add(new BasicNameValuePair("Content-Type", "application/json"));
			header.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

			List<NameValuePair> param = new ArrayList<NameValuePair>();
			param.add(new BasicNameValuePair("ids", productsCommaSeparate));
			param.add(new BasicNameValuePair("view", view.get(j)));
			HttpResponse httpresponse = RequestUtil.getRequest(requestURL, header, param);
			Assert.assertEquals(httpresponse.getStatusLine().getStatusCode(), 200,
					"Response is not proper for" + requestURL);
			JSONObject productApiResp = RequestUtil.convertHttpResponseToJsonObject(httpresponse);
			log.info("productApiResp: " + productApiResp);
			Assert.assertEquals(productApiResp.get("status"), 200, "status mismatch");
			Assert.assertEquals(jsonResponse_category.getJSONObject("result").getJSONArray("product_list").length(),
					productApiResp.getJSONArray("result").length(), "number of product mismatch");
		}
	}

	@Test(enabled = false)
	public void addToCartValidate() throws URISyntaxException, Exception {
		HashMap<String, String> details = ProductUtil
				.getProductIdAndPackageIdFromTheGivenCategory("category_eyeglasses", XApiClient.DESKTOP);
		log.info(details);
		String productId = details.get("productId");
		String packageId = details.get("packageId");

		JSONObject payload = new JSONObject();
		JSONArray addToCartValidationRequestList = new JSONArray();
		JSONObject innerJSON = new JSONObject();
		innerJSON.put("packageId", packageId);
		innerJSON.put("platform", XApiClient.DESKTOP.toUpperCase());
		innerJSON.put("prescriptionType", PowerTypes.SINGLE_VISION);
		innerJSON.put("index", 0);
		addToCartValidationRequestList.put(innerJSON);
		payload.put("addToCartValidationRequestList", addToCartValidationRequestList);

		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.ADD_TO_CART_VALIDATE,
				HeadersUtil.headers_apiClient(XApiClient.DESKTOP), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getBoolean("addToCartValidationResponseList[0].validationSuccessful"));
		Assert.assertEquals(response.jsonPath().getInt("addToCartValidationResponseList[0].index"), 0);
		Assert.assertEquals(response.jsonPath().getString("addToCartValidationResponseList[0].packageInfo.powerType"),
				PowerTypes.SINGLE_VISION);
	}

	@Test
	public void getGroupName() {
		Response respone = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_GROUP_NAMES);
		Assert.assertEquals(respone.statusCode(), 200);
	}

	@Test
	public void getGroupsName() {
		Response respone = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_GROUPS_NAMES);
		Assert.assertEquals(respone.statusCode(), 200);
	}

	@Test
	public void getDittoOpinionProducts() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("productType", "Sunglasses");
		pathParams.put("qty", "1");
		Response respone = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.GET_DITTO_OPINION_PRODUCTS,
				pathParams, HeadersUtil.headers_apiClient(XApiClient.ANDROID));
		Assert.assertEquals(respone.statusCode(), 200);
		Assert.assertTrue(respone.jsonPath().getList("result").size() > 100);
	}

	@Test
	public void getAllBuyPackages() {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_ALL_BUY_PACKAGES);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getList("packageList").size() > 100);
		Assert.assertFalse(response.jsonPath().getString("packageList[0].name").isEmpty());
		Assert.assertFalse(response.jsonPath().getString("packageList[0].productType").isEmpty());
		Assert.assertFalse(response.jsonPath().getString("packageList[0].sku").isEmpty());
	}

	@Test
	public void getPackageGroupNames() {
		Response respone = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_PACKAGE_GROUP_NAMES);
		Assert.assertEquals(respone.statusCode(), 200);
	}

	@Test
	public void getPackageGroupNameById() {
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", "1");
		Response respone = RestAssuredUtils.GET_WITH_PATHPARAMS(JunoV2ProductPathConstants.GET_PACKAGE_GROUP_NAME_BY_ID,
				pathParams);
		Assert.assertEquals(respone.statusCode(), 200);
		Assert.assertTrue(respone.jsonPath().get("result.group") == null);
	}

	@Test(enabled = false) // Api not in use
	public void convertEyeglassToContactLensPower() {
		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("sphr", "+1.00");
		queryparams.put("cylr", "+1.00");
		queryparams.put("axisr", "10");
		queryparams.put("sphl", "+1.00");
		queryparams.put("cyll", "+1.00");
		queryparams.put("axisl", "10");
		Response response = RestAssuredUtils.GET_WITH_QUERYPARAM(JunoV2ProductPathConstants.CONVERT_EYE_TO_CL_POWER,
				queryparams);
		Assert.assertEquals(response.statusCode(), 200);
	}

	@Test
	public void validateProductPowerDetails() throws URISyntaxException, Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		JSONObject powersResponse = ProductUtil.getAvailablePowersForAProduct(pid, "contact_Lens");
		JSONArray getPowerAPI_powerTypeList = powersResponse.getJSONObject("result").getJSONArray("powerTypeList");

		JSONObject payload = new JSONObject();
		JSONArray productPowerOptionList = new JSONArray();
		JSONObject productPowerOptionObject = new JSONObject();
		JSONArray powerOptionList = new JSONArray();

		for (int i = 0; i < getPowerAPI_powerTypeList.length(); i++) {
			JSONObject json = new JSONObject();
			json.put("type", getPowerAPI_powerTypeList.getJSONObject(i).getString("type"));
			json.put("value", getPowerAPI_powerTypeList.getJSONObject(i).getJSONArray("powerDataList").getJSONObject(0)
					.getJSONArray("value").getString(getPowerAPI_powerTypeList.getJSONObject(i)
							.getJSONArray("powerDataList").getJSONObject(0).getJSONArray("value").length() - 1));
			json.put("price", getPowerAPI_powerTypeList.getJSONObject(i).getJSONArray("powerDataList").getJSONObject(0)
					.getString("price"));
			powerOptionList.put(json);
		}

		productPowerOptionObject.put("productId", pid);
		productPowerOptionObject.put("powerOptionList", powerOptionList);

		productPowerOptionList.put(productPowerOptionObject);
		payload.put("productPowerOptionList", productPowerOptionList);
		log.info(payload);

		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.VALIDATE_PRODUCT_POWER_DETAILS,
				HeadersUtil.headers_apiClient(XApiClient.ANDROID), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		JsonUtil.compare_json_objects(GenericUtil.stringToJson(response.asString()), payload);
	}

	@Test
	public void updatePowerTemplateForGivenProduct() throws URISyntaxException, Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_eyeglasses", XApiClient.ANDROID);
		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("productId", pid);
		Response response = RestAssuredUtils
				.GET_WITH_QUERYPARAM(JunoV2ProductPathConstants.updatePowerTemplaeIDForGivenPorductId, queryparams);
		Assert.assertEquals(response.statusCode(), 200);
	}

	@Test
	public void getGivenReviewForSpecificCustomer() throws Exception {
		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("count", "50");
		queryparams.put("page", "1");
//		String sessionToken = null;
//		if (!Environments.environment.equals(Profiles.PROD))
		String sessionToken = CustomerUtil.mobileAuthenticateSession(XApiClient.ANDROID, "1262183385");
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_CUSTOMER_REVIEW,
				HeadersUtil.headers_sessionToken(sessionToken), queryparams);
		Assert.assertEquals(response.statusCode(), 200);
//		Assert.assertEquals(response.jsonPath().getInt("result.numberOfReviews"), 2);
//		Assert.assertEquals(response.jsonPath().getString("result.reviews[0].reviewTitle"), "GOOD");
//		Assert.assertEquals(response.jsonPath().getString("result.reviews[1].reviewTitle"), "GOOD");
	}

	@Test(enabled = true)
	public void getAllSolutions() throws Exception {
		String get_Solution = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_SOLUTIONS_V2;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", XApiClient.ANDROID));
		HttpResponse httpResponse = RequestUtil.getRequest(get_Solution, headers, null);
		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("responseJson: " + responseJson);
		Assert.assertEquals(responseJson.get("status"), 200, "status is incorrect");
//		JSONArray array = responseJson.getJSONArray("result");
//		for (int i = 0; i < array.length(); i++) {
//			Assert.assertTrue(array.getJSONObject(i).getString("fullName").toLowerCase().contains("solution"));
//		}
	}

	@Test
	public void getProuductStatus() {
		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("productIds", ApplicationConstants.goldPid);
		Response response = RestAssuredUtils.GET_WITH_QUERYPARAM(JunoV2ProductPathConstants.GET_PRODUCT_STATUS,
				queryparams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.128269.isActive"), "true");
	}

	@Test
	public void getAllBuyOptionsInfoOnGivenProduct() throws URISyntaxException, Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_vc", XApiClient.ANDROID);
		log.info("PID " + pid);
		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", pid);

		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("power_type", PowerTypes.SINGLE_VISION);
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_ALL_BUY_OPTIONS_INFO_ON_GIVEN_PRODUCT,
				pathParams, HeadersUtil.headers_apiClient(XApiClient.ANDROID), queryParams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("results[0].id"), PowerTypes.SINGLE_VISION);
		Assert.assertTrue(response.jsonPath().getList("results[0].packages").size() > 0);
	}

	@Test
	public void getProductDetails() throws URISyntaxException, Exception {
		JunoV2Util.getProductDetails(ApplicationConstants.goldPid, XApiClient.IOS);
	}

	@Test(enabled = true)
	public void getOffersForAProduct() throws Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_eyeglasses", XApiClient.ANDROID);
		List<String> productId = Arrays.asList(pid);
		for (int j = 0; j < productId.size(); j++) {

			String RequestUrl = Environments.SERVICES_ENVIRONMENT
					+ String.format(JunoV2ProductPathConstants.GET_OFFERS_V2, productId.get(j));
			List<NameValuePair> headers = new ArrayList<NameValuePair>();
			headers.add(new BasicNameValuePair("Content-Type", "application/json"));
			headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

			HttpResponse httpResponse = RequestUtil.getRequest(RequestUrl, headers, null);
			JSONObject jsonResponse = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
			log.info("Jsonresponse: " + jsonResponse);
			if (httpResponse.getStatusLine().getStatusCode() == 200) {
				String primaryOfferInAPIResponse = jsonResponse.getJSONObject("result").getString("primaryOffer");
				JSONArray offersArrayInAPIResponse = jsonResponse.getJSONObject("result").getJSONArray("offers");
				log.info(primaryOfferInAPIResponse);
				log.info(offersArrayInAPIResponse);
				/*
				 * if (dbValidationFlag) { String primaryOfferInDB =
				 * get_all_details("primary_offer", productId.get(j), "product_id",
				 * "catalog_product_flat_1_extra_attributes").replaceAll("\r\n", "");
				 * Assert.assertEquals(primaryOfferInAPIResponse, primaryOfferInDB,
				 * "primary offer text mismacth"); String productOffersInDB =
				 * get_all_details("product_offers", productId.get(j), "product_id",
				 * "catalog_product_flat_1_extra_attributes").replaceAll("\r\n", "");
				 * log.info("productOffersInDB: " + productOffersInDB); String[] offers =
				 * productOffersInDB.split(",");
				 * 
				 * for (int i = 0; i < offers.length; i++) { String[] offer_text =
				 * offers[i].split("#"); Assert.assertEquals(offer_text[0],
				 * offersArrayInAPIResponse.getJSONObject(i).getString("text"));
				 * Assert.assertEquals(offer_text[1],
				 * 
				 * } }
				 */
			} else {
				Assert.assertEquals(jsonResponse.get("code"), 101, "code mismatch");
				Assert.assertEquals(jsonResponse.getString("error"), "No Offers found for this product",
						"invalid error");
			}
		}
	}

	@Test
	public void getBuyOptionDetails() throws Exception {
		GetV2BuyPackageUsecases getV2BuyPackageUsecases = new GetV2BuyPackageUsecases();
		getV2BuyPackageUsecases.getBuyPackageV2_without_FrameType();
	}

	@Test
	public void getProductDetailsByParams() {
		Map<String, String> queryParams = new HashMap<String, String>();
		queryParams.put("param", "qty,sku");

		Map<String, String> pathParams = new HashMap<String, String>();
		pathParams.put("id", ApplicationConstants.goldPid);

		Response response = RestAssuredUtils.GET_PATH_QUERY_PARAMS(
				JunoV2ProductPathConstants.GET_PRODUCT_DETAILS_BY_PARAM, pathParams, queryParams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getLong("result.qty") > 1000);
		Assert.assertEquals(response.jsonPath().getString("result.sku"), "service:lenskart-select-membership");
	}

	@Test
	public void getPowerDetails() throws URISyntaxException, Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_vc", XApiClient.ANDROID);
		String RequestUrl_PowerConvertor = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_CONTACT_LENSES_POWERS, pid);
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));

		List<NameValuePair> queryParams = new ArrayList<NameValuePair>();
		queryParams.add(new BasicNameValuePair("power_type", PowerTypes.SINGLE_VISION));

		HttpResponse httpResponse = RequestUtil.getRequest(RequestUrl_PowerConvertor, headers, queryParams);
		JSONObject powerDetails = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		Assert.assertEquals(httpResponse.getStatusLine().getStatusCode(), 200, "status mismatch");
		String product_power = powerDetails.getJSONObject("result").getJSONArray("powerTypeList").getJSONObject(1)
				.getJSONArray("powerDataList").getJSONObject(0).getJSONArray("value").getString(2);
		log.info(product_power);

	}

	@Test
	public void validatePowerDetails() throws JSONException, Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		JSONObject powersResponse = ProductUtil.getAvailablePowersForAProduct(pid, "contact_Lens");
		JSONArray getPowerAPI_powerTypeList = powersResponse.getJSONObject("result").getJSONArray("powerTypeList");

		JSONObject payload = new JSONObject();
		JSONArray powerOptionList = new JSONArray();
		for (int i = 0; i < getPowerAPI_powerTypeList.length(); i++) {
			JSONObject json = new JSONObject();
			json.put("type", getPowerAPI_powerTypeList.getJSONObject(i).getString("type"));
			json.put("value", getPowerAPI_powerTypeList.getJSONObject(i).getJSONArray("powerDataList").getJSONObject(0)
					.getJSONArray("value").getString(getPowerAPI_powerTypeList.getJSONObject(i)
							.getJSONArray("powerDataList").getJSONObject(0).getJSONArray("value").length() - 1));
			json.put("price", getPowerAPI_powerTypeList.getJSONObject(i).getJSONArray("powerDataList").getJSONObject(0)
					.getString("price"));
			powerOptionList.put(json);
		}
		payload.put("powerOptionList", powerOptionList);
		log.info(payload);

		Map<String, String> pathparams = new HashMap<String, String>();
		pathparams.put("id", pid);

		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.VALIDATE_POWER_DETAILS, pathparams,
				HeadersUtil.headers_apiClient(XApiClient.ANDROID), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		JsonUtil.compare_json_objects(GenericUtil.stringToJson(response.asString()), payload);
	}

	@Test
	public void getGivenProductReview() throws Exception {
		GetProductReviewUsecases getProductReviewUsecases = new GetProductReviewUsecases();
		getProductReviewUsecases.fetchProductReview_with_Direction();
	}

	@Test
	public void sendGivenProductReviewToYotpo() throws Exception {
		CreateCustomerReviewUsecases createCustomerReviewUsecases = new CreateCustomerReviewUsecases();
		String password = "123456";
		String email = "test" + GenericUtil.createRandomNumber(6) + "<EMAIL>";
		String telephone = "1214" + GenericUtil.createRandomNumber(6);
		createCustomerReviewUsecases.create_customer_review(
				CustomerUtil.registerNewCustomer("loyalUser", "abcd", email, password, telephone));
	}

	@Test
	public void getGivenProductsReviewsForASpecificCustomer() throws Exception {
//		String sessionToken = null;
//		if (Environments.environment.equals(Profiles.PROD))
		String sessionToken = CustomerUtil.mobileAuthenticateSession(XApiClient.ANDROID, "1262183385");
		Map<String, String> pathparams = new HashMap<String, String>();
		pathparams.put("id", "129974");

		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("count", "50");
		queryparams.put("page", "1");

		String RequestUrl_ProductReviewByCustomer = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_GIVEN_PRODUCT_REVIEW_FOR_A_SPECIFIC_CUSTOMER;

		Response response = RestAssuredUtils.GET(RequestUrl_ProductReviewByCustomer, pathparams,
				HeadersUtil.headers_sessionToken(sessionToken), queryparams);
		Assert.assertEquals(response.statusCode(), 200);
		JSONObject result = GenericUtil.stringToJson(response.asString()).getJSONObject("result");
//		JSONObject product = result.getJSONArray("129974").getJSONObject(0);
//		Assert.assertEquals(product.getString("reviewTitle"), "GOOD");
//		Assert.assertEquals(product.getString("reviewDetail"), "GOOD");
//		Assert.assertEquals(product.getString("reviewerType"), "verified_buyer");
	}

	@Test
	public void getGivenProductReviews() {
		Map<String, String> pathparams = new HashMap<String, String>();
		pathparams.put("id", "129974");

		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("count", "50");
		queryparams.put("page", "1");

		Response response = RestAssuredUtils.GET_PATH_QUERY_PARAMS(
				JunoV2ProductPathConstants.GET_ALL_REVIEWS_OF_A_PRODUCT, pathparams, queryparams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getList("result").size() > 1);
	}

	@Test
	public void getSubscriptionOnGivenProduct() throws URISyntaxException, Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		JSONObject response = JunoV2Util.getSubscription(pid, XApiClient.ANDROID);
		log.info(response);
		Assert.assertEquals(response.getJSONObject("result").getString("templateId"), "null");
		Assert.assertTrue(response.getJSONObject("result").getJSONArray("subscriptions").length() == 0);
	}

	@Test
	public void getTATresponse() throws URISyntaxException, Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);

		Map<String, String> pathparams = new HashMap<String, String>();
		pathparams.put("pid", pid);

		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("pinCode", "560038");

		Response response = RestAssuredUtils.GET_PATH_QUERY_PARAMS(JunoV2ProductPathConstants.GET_TAT_RESPONSE,
				pathparams, queryparams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result[0].deliveryOption"), "STANDARD");
//		Assert.assertEquals(response.jsonPath().getString("result[0].dispatchDate"), null);
		Assert.assertEquals(response.jsonPath().getString("result[0].shippingCharges"), null);
	}

	@Test
	public void getDefaultSubcategoryDetails() throws URISyntaxException, Exception {
		String get_Subcategory = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_SUBCATEGORY_PATH;
		List<NameValuePair> headers = new ArrayList<NameValuePair>();
		headers.add(new BasicNameValuePair("Content-Type", "application/json"));
		headers.add(new BasicNameValuePair("X-Api-Client", ApplicationConstants.XApiClient.ANDROID));
		HttpResponse httpResponse = RequestUtil.getRequest(get_Subcategory, headers, null);

		JSONObject responseJson = RequestUtil.convertHttpResponseToJsonObject(httpResponse);
		log.info("responseJson: " + responseJson);
		Assert.assertEquals(
				responseJson.getJSONObject("result").getJSONArray("subcategory").getJSONObject(0).getString("name"),
				"Men");
		Assert.assertEquals(
				responseJson.getJSONObject("result").getJSONArray("subcategory").getJSONObject(1).getString("name"),
				"Women");
		Assert.assertEquals(
				responseJson.getJSONObject("result").getJSONArray("subcategory").getJSONObject(2).getString("name"),
				"Kids");
	}

	@Test
	public void getSubcategoryDetails() throws Exception {
		GetSubcategoryUsecases getSubcategoryUsecases = new GetSubcategoryUsecases();
		getSubcategoryUsecases.getSubcategory_with_categoryName();
	}

	@Test
	public void getTemplateList() {
		Response response = ProductUtil.getTemplateList();
		Assert.assertTrue(response.jsonPath().getList("result.templateList").size() > 100);
	}

	@Test
	public void addTemplate() throws JSONException {
		JSONObject firstObject = ProductUtil.getFirstTemplateDetails();
		String name = firstObject.getString("name");
		firstObject.remove("name");
		String updatedName = name.concat("_").concat(GenericUtil.currentDateTime());
		firstObject.put("name", updatedName);
		JSONObject obj = new JSONObject();
		obj.put("template", firstObject);

		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.ADD_TEMPLATE,
				HeadersUtil.panelAuthToken("NouqFyR8Bz2jk+BhAUdrIttcyApnkGmOCeVJwD890Ys="),
				GenericUtil.jsonToString(obj));
		Assert.assertEquals(response.statusCode(), 200);
		Response completeListResponse = ProductUtil.getTemplateList();
		Assert.assertTrue(completeListResponse.asString().contains(updatedName));
	}

	@Test
	public void updateTemplate() throws JSONException {
		JSONObject firstObject = ProductUtil.getFirstTemplateDetails();
		log.info(firstObject);
		JSONArray platformEnabledArray = firstObject.getJSONArray("powerPackageList").getJSONObject(0)
				.getJSONArray("packageList").getJSONObject(0).getJSONArray("platformEnabled");
		platformEnabledArray.put("TEST_IN");
		log.info(platformEnabledArray);
		log.info(firstObject);
		JSONObject obj = new JSONObject();
		obj.put("template", firstObject);

		Response response = RestAssuredUtils.PUT(JunoV2ProductPathConstants.UPDATE_TEMPLATE,
				HeadersUtil.panelAuthToken("NouqFyR8Bz2jk+BhAUdrIttcyApnkGmOCeVJwD890Ys="),
				GenericUtil.jsonToString(obj));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(ProductUtil.getFirstTemplateDetails().toString().contains("TEST_IN"));

		JSONObject firstObject1 = ProductUtil.getFirstTemplateDetails();
		firstObject1.getJSONArray("powerPackageList").getJSONObject(0).getJSONArray("packageList").getJSONObject(0)
				.remove("platformEnabled");
		log.info(firstObject1);
//			
//		for (int i = 0; i < platformEnabledArray1.length(); i++) {
//			list.add(platformEnabledArray1.getString(i));
//		}
//		log.info(list.indexOf("TEST_IN"));
//		list.remove(list.indexOf("TEST_IN"));
//		firstObject1.remove(arg0)

		JSONArray platformEnabledArray1 = new JSONArray();
		platformEnabledArray1.put("ANDROID_IN");
		platformEnabledArray1.put("DESKTOP_IN");
		platformEnabledArray1.put("POS_IN");
		platformEnabledArray1.put("MOBILESITE_IN");
		platformEnabledArray1.put("IOS_IN");

		firstObject1.getJSONArray("powerPackageList").getJSONObject(0).getJSONArray("packageList").getJSONObject(0)
				.put("platformEnabled", platformEnabledArray1);

		log.info(firstObject1);
		JSONObject obj1 = new JSONObject();
		obj1.put("template", firstObject1);
		log.info(platformEnabledArray1);
		log.info(obj1);
		Response response1 = RestAssuredUtils.PUT(JunoV2ProductPathConstants.UPDATE_TEMPLATE,
				HeadersUtil.panelAuthToken("NouqFyR8Bz2jk+BhAUdrIttcyApnkGmOCeVJwD890Ys="),
				GenericUtil.jsonToString(obj1));
		Assert.assertEquals(response1.statusCode(), 200);
		Assert.assertFalse(ProductUtil.getFirstTemplateDetails().toString().contains("TEST_IN"));

	}

	@Test
	public void getTemplateDetails() {
		Response templatelistResponse = ProductUtil.getTemplateList();
		String id = templatelistResponse.jsonPath().getString("result.templateList[0].id");
		String powerType = templatelistResponse.jsonPath().getString("result.templateList[0].powerList[0]");
		if (powerType.contains("Single Vision"))
			powerType = PowerTypes.SINGLE_VISION;
		else if (powerType.contains("Bifocal/Progressive"))
			powerType = PowerTypes.BIFOCAL;
		else if (powerType.contains("Zero"))
			powerType = PowerTypes.ZERO_POWER;

		Map<String, String> queryparams = new HashMap<String, String>();
		queryparams.put("powerType", powerType);

		Map<String, String> pathparams = new HashMap<String, String>();
		pathparams.put("id", id);

		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_TEMPLATE_DETAILS, pathparams,
				HeadersUtil.panelAuthToken("NouqFyR8Bz2jk+BhAUdrIttcyApnkGmOCeVJwD890Ys="), queryparams);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.templateList[0].powerPackageList[0].powerType"),
				powerType);
	}

	@Test
	public void deleteWishlist() throws Exception {
		DeleteWishlistUsecases deleteWishlistUsecases = new DeleteWishlistUsecases();
		deleteWishlistUsecases.deleteEntireWishlistSuccessful();
	}

	@Test
	public void getWishlist() throws Exception {
		GetWishlistUsecases getWishlistUsecases = new GetWishlistUsecases();
		getWishlistUsecases.guestUserGetWishlistSuccessful();
	}

	@Test
	public void saveWishlist() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		String product_Id = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(1)
				.getString("id");
		ProductUtil.saveWishlist(sessiontoken, product_Id);
		ProductUtil.getWishlist(sessiontoken);
	}

	@Test
	public void updateWishlist() throws Exception {
		UpdateWishlistUsecases.guestUserUpdateWishlist();
	}

	@Test
	public void deleteProductFromWishlist() throws Exception {
		DeleteProductFromWishlistUsecases deleteProductFromWishlistUsecases = new DeleteProductFromWishlistUsecases();
		deleteProductFromWishlistUsecases.deleteProductFromWishlist();
	}

	@Test
	public void saveBulkWishlist() throws Exception {
		String sessiontoken = SessionUtil.createNewSession();
		JSONObject categoryDetails = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_reading_eyeglass"), ApplicationConstants.XApiClient.ANDROID);
		String product_Id_1 = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(1)
				.getString("id");
		String product_Id_2 = categoryDetails.getJSONObject("result").getJSONArray("product_list").getJSONObject(2)
				.getString("id");
		List<String> list = Stream.of(product_Id_1, product_Id_2).collect(Collectors.toList());
		log.info(list);
		ProductUtil.saveBulkWishlist(sessiontoken, list);
		ProductUtil.getWishlist(sessiontoken, list.size());
	}

	@Test(enabled = false)
	public void bulkUploadProduct() throws Exception {
		String file = System.getProperty("user.dir") + "//csv_files//bulk_upload_template.csv";
		RestAssured.baseURI = Environments.SERVICES_ENVIRONMENT;
		Response res = RestAssured.given().log().all().header("Content-type", "multipart/form-data")
				.header("X-Auth-Token", ProductUtil.catalogUserLoginToken()).multiPart("file", new File(file)).when()
				.post("/v2/products/catalog/products/upload").then().statusCode(200).log().all().extract().response();
		log.info(res.getStatusCode());
		log.info(res.getHeader("Success-count"));
		log.info(res.getHeader("Failure-count"));
		Assert.assertEquals(res.getHeader("Success-count"), "1");
	}

	@Test(enabled = false)
	public void upload_image() {
		String file = System.getProperty("user.dir") + "//csv_files//1.png";
		RestAssured.baseURI = "https://reversereceiving.scm.preprod.lenskart.com";

		Response res = RestAssured.given().log().all().header("Content-type", "multipart/form-data")
				.multiPart("images", new File(file)).when().post("/product/save/qcDamageImages/product/131315/2161779")
				.then().statusCode(200).log().all().extract().response();

		log.info(res.asString());
	}

	@Test(enabled = false)
	public void upload_image11() {
		File test = new File(System.getProperty("user.dir") + "//csv_files//1.png");

		String gfd = "https://reversereceiving.scm.preprod.lenskart.com";
		String end = "/product/save/qcDamageImages/product/147496/17799549";
		System.out.println("vvv" + gfd + end);
		RestAssured.baseURI = "https://reversereceiving.scm.preprod.lenskart.com";
		Map<String, String> headers = new HashMap<>();
		headers.put("content-type", "multipart/form-data");
		Response resopnse = RestAssured.given().headers(headers).multiPart("images", test).when()
				.post("/product/save/qcDamageImages/product/147496/17799549");

		System.out.println(resopnse.getStatusCode());
		System.out.println(resopnse.toString());

	}

	// getContactLensDetails

	@Test(enabled = true)
	public void getContactLensDetailsAPI() throws URISyntaxException, Exception {
		String getContactLensDetailsAPIURL = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_CONTACT_LENS_DETAILS_FROM_POWERWISEID_API;

		JSONArray payload = new JSONArray();
		payload.put(90003244);
		payload.put(90003590);

		Response response = RestAssuredUtils.POST(getContactLensDetailsAPIURL,
				HeadersUtil.headers_apiClient(XApiClient.DESKTOP), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.90003244.powerWiseId"), "90003244");
		Assert.assertEquals(response.jsonPath().getString("result.90003590.powerWiseId"), "90003590");
	}

	// get product Document by Id internal API all images
	@Test
	public void getProductDocumentByIdInternalAllImagesAPI() throws Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_PRODUCT_DOCUMENT_BY_ID_ALL_IMAGES_INTERNAL_API, pid);

		HashMap<String, String> queryparam = new HashMap<String, String>();
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), pid, "ProductId is not correct");

	}

	// Get product Document by ID Internal API
	@Test
	public void getProductDocumentByIdInternalAPI() throws Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_PRODUCT_DOCUMENT_BY_ID_INTERNAL_API, pid);

		HashMap<String, String> queryparam = new HashMap<String, String>();
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.id"), pid, "ProductId is not correct");

	}

	// Get group name for a given set of OID
	@Test
	public void getGroupNameForGivenOidAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_GROUP_NAME_FOR_GIVEN_OID_API;

		HashMap<String, String> queryparam = new HashMap<String, String>();
		queryparam.put("oid", "1");
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");

	}

	// Get power response with Restrictions
	@Test
	public void getPowerResponseWithRestrictionsAPI() throws Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_POWER_RESPONSE_WITH_RESTRICTIONS_API, pid);

		HashMap<String, String> queryparam = new HashMap<String, String>();
		queryparam.put("package_name", "1");
		queryparam.put("coating_oid", "1");
		queryparam.put("power_type", ApplicationConstants.PowerTypes.CONTACT_LENS);
		queryparam.put("collapse", "1");

		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.powerType"), "contact_lens",
				"powerType is not correct");

	}

	// Get product BOGO widget
	@Test(enabled = false) // working only on Preproduction, changes are not live on production
	public void getProductBogoWidgetAPI() throws Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_PRODUCT_BOGO_WIDGET_API, pid);

		HashMap<String, String> queryparam = new HashMap<String, String>();
		queryparam.put("isHardPitch", "false");
		queryparam.put("page-size", "10");

		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);
		headers.put("X-Session-Token", SessionUtil.createNewSession());

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");

	}

	// Fetch Inventory template by productId and Client
	@Test
	public void fetchInventoryTemplateByPidAPI() throws Exception {
		String pid = ProductUtil.getProductIdFromTheGivenCategory("category_contact_lens", XApiClient.ANDROID,
				PowerTypes.CONTACT_LENS);
		String endpoint = Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.FETCH_INVENTORY_TEMPLATE_BY_PID_AND_CLIENT_API, pid);

		HashMap<String, String> queryparam = new HashMap<String, String>();
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertEquals(response.jsonPath().getString("result.type"), "INVENTORY", "type is not correct");

	}

	// Fetch Product By Filter
	@Test
	public void fetchProductByFilterAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.FETCH_PRODUCT_BY_FILTER_API;

		HashMap<String, String> queryparam = new HashMap<String, String>();
		queryparam.put("selling_price_greater_than", "1000");
		queryparam.put("selling_price_less_than", "3000");
		queryparam.put("brand", "Vincent Chase");
		queryparam.put("page", "0");
		queryparam.put("size", "15");
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertEquals(response.statusCode(), 200, "Status code is not correct");
		Assert.assertTrue(response.jsonPath().getString("result.data").length() > 0);

	}

	// Get Search Result
	@Test(enabled = false)
	public void getSearchResultAPI() throws URISyntaxException, Exception {
		String getSearchResultAPIURL = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_SEARCH_RESULT_API;

		JSONObject payload = new JSONObject();
		payload.put("frameSize", "1");
		payload.put("query", "eyeglass");

		Response response = RestAssuredUtils.POST(getSearchResultAPIURL,
				HeadersUtil.headers_apiClient(XApiClient.DESKTOP), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result.product_list").length() > 0);
	}

	// Get Search Filters
	@Test(enabled = true)
	public void getSearchFilterAPI() throws URISyntaxException, Exception {
		String getSearchFilterAPIURL = Environments.SERVICES_ENVIRONMENT
				+ JunoV2ProductPathConstants.GET_SEARCH_FILTER_API;

		JSONObject payload = new JSONObject();
		payload.put("frameSize", "1");
		payload.put("query", "eyeglass");

		Response response = RestAssuredUtils.POST(getSearchFilterAPIURL,
				HeadersUtil.headers_apiClient(XApiClient.DESKTOP), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result.filters").length() > 0);
	}

	// Get Trending search API
	@Test
	public void getTreandingSearchAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_TRENDING_SEARCH_API;

		HashMap<String, String> queryparam = new HashMap<String, String>();
		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");

		Response response = RestAssuredUtils.GET(endpoint, headers, queryparam);
		Assert.assertTrue(response.jsonPath().getString("result.searches").length() > 0);

	}

	// Sort By Controller --- Get Sort by Options
	@Test
	public void getSortByOptionsAPI() throws Exception {
		String endpoint = Environments.SERVICES_ENVIRONMENT + JunoV2ProductPathConstants.GET_SORTBY_OPTIONS_API;

		HashMap<String, String> headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("X-Api-Client", ApplicationConstants.XApiClient.ANDROID);
		headers.put("X-Country-Code", ApplicationConstants.Countries.INDIA);

		Response response = RestAssuredUtils.GET(endpoint, headers);
		Assert.assertTrue(response.jsonPath().getString("result.sortOptions").length() > 0);

	}

	// Get Category in-line filter result API
	@Test
	public void getCategoryInlineFiltersResultV3API() throws Exception {
		Response response = RestAssuredUtils.GET(Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_CATEGORY_INLINE_FILTER_RESULT_V3_API,
						ApplicationConstants.ProductCategories.CATEGORY_EYEGLASSES_JJ));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result.noOfProducts").length() >= 0);

	}

	// Get buy Back Eligibility API
	@Test(enabled = false) // works only in PreProd ( getting 400 // new API)
	public void getBuyBackEligibilityAPI() throws Exception {
		String productId = ProductUtil.getProductIdFromTheGivenCategory("category_vc", XApiClient.ANDROID);
		Response response = RestAssuredUtils.GET(
				Environments.SERVICES_ENVIRONMENT
						+ String.format(JunoV2ProductPathConstants.GET_BUYBACK_ELIGIBILITY_API, productId),
				HeadersUtil.headers_session_client(SessionUtil.createNewSession(),
						ApplicationConstants.XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result").length() > 0);

	}

	// Get All size bucketing ConFig
	@Test
	public void getAllSizeBuketingConfigAPI() throws Exception {
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.GET_ALL_SIZE_BUCKETING_CONFIG_API);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result").length() >= 0);

	}

	// Apply bucketing ConFig API
	@Test
	public void getApplyBucketingConfigAPI() throws Exception {
		HashMap<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("userSize", "12");
		Response response = RestAssuredUtils.GET(JunoV2ProductPathConstants.APPLY_BUCKETING_CONFIG_API,
				HeadersUtil.headers(), queryParam);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result").length() >= 0);

	}

	// Get AR COLOR Options API
	@Test
	public void getARColorOptionsAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_contact_lens_aqualens"), XApiClient.ANDROID);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.CONTACT_LENS, Boolean.parseBoolean(""));
		String productId = product.get("productId");
		Response response = RestAssuredUtils.GET(Environments.SERVICES_ENVIRONMENT
				+ String.format(JunoV2ProductPathConstants.GET_AR_COLOR_OPTIONS_V3_API, productId));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result.product_list").length() >= 0);

	}

	// Get CL product details V3 API
	@Test
	public void getCLProductV3API() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(
				ApplicationUtil.getcategoryId("category_contact_lens_aqualens"), XApiClient.ANDROID);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.CONTACT_LENS, Boolean.parseBoolean(""));
		String productId = product.get("productId");
		Response response = RestAssuredUtils.GET(
				Environments.SERVICES_ENVIRONMENT
						+ String.format(JunoV2ProductPathConstants.GET_CL_PRODUCT_V3_API, productId),
				HeadersUtil.headers_session_client(SessionUtil.createNewSession(),
						ApplicationConstants.XApiClient.ANDROID));
		Assert.assertEquals(response.statusCode(), 200);

	}

	// Get Buy options V3 API (get Package API)
	@Test
	public void getBuyOptionsV3API() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_VC"),
				XApiClient.ANDROID);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		String productId = product.get("productId");
		HashMap<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("allPackages", "true");
		queryParam.put("power_type", ApplicationConstants.PowerTypes.SINGLE_VISION);
		Response response = RestAssuredUtils.GET(
				Environments.SERVICES_ENVIRONMENT
						+ String.format(JunoV2ProductPathConstants.GET_BUYOPTIONS_DETAILS_V3_API, productId),
				HeadersUtil.headers_session_client(SessionUtil.createNewSession(),
						ApplicationConstants.XApiClient.ANDROID),
				queryParam);
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertEquals(response.jsonPath().getString("result.id"), "single_vision");
	}

	// Check Store Availability API
	@Test
	public void checkStoreAvailabilityAPI() throws Exception {
		JSONObject jsonResponse_category = JunoV1Util.getCategoryDetails(ApplicationUtil.getcategoryId("category_VC"),
				XApiClient.ANDROID);
		HashMap<String, String> product = ApplicationUtil.getProductDetails(jsonResponse_category,
				ApplicationConstants.PowerTypes.SINGLE_VISION, Boolean.parseBoolean(""));
		String productId = product.get("productId");
		JSONObject payload = new JSONObject();
		payload.put("limit", "10");
		payload.put("nationNo", "2");
		payload.put("productId", productId);
		payload.put("productName", ApplicationConstants.PowerTypes.SINGLE_VISION);
		Response response = RestAssuredUtils.POST(JunoV2ProductPathConstants.CHECK_STORE_AVAILABILITY_API,
				HeadersUtil.headers(), GenericUtil.jsonToString(payload));
		Assert.assertEquals(response.statusCode(), 200);
		Assert.assertTrue(response.jsonPath().getString("result").length() >= 0);

	}
}
