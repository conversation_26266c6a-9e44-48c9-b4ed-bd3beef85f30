package org.lenskart.test.etl;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bson.Document;
import org.lenskart.core.util.OrderUtil;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;

public class OrderETL {
  private static boolean db_Validation = true;
  private static String sessiontoken;
  private static Long orderId = (long) 1209100215;

  private static Long order_ID_Throu_PHP = (long) 1209100220;
  private static Long order_ID_Throu_JUNO = (long) 1208005128;
  // private static Long orderid=(long) 5372;

  private MongoConnectionUtility mongoConnectionObject = null;
  private MongoConnectionUtility orderMongoConnectionObject = null;
  private MySQLConnectionUtility mysqlConnectionObject = null;

  @BeforeClass
  public void setup() throws Exception {
    if (db_Validation) {
      PropertyFactory pf = new PropertyFactory("ETLService");
      mongoConnectionObject = new MongoConnectionUtility(pf);
      mongoConnectionObject.initMongoDBConnection();
      mysqlConnectionObject = new MySQLConnectionUtility(pf);
      mysqlConnectionObject.initMySQLConnection();

      orderMongoConnectionObject = OrderUtil.getOrderMongoConnectionObject();
      orderMongoConnectionObject.initMongoDBConnection();
    }
  }

  @AfterClass
  public void destroy() throws Exception {
    if (db_Validation) {
      mongoConnectionObject.closeMongoConnection();
    }
  }

  private String get_column_value(String orderId, String where_column, String table_name,
      String column_name) throws SQLException {
    String sql = "select " + column_name + " from 3Sep." + table_name + " where " + where_column
        + "=" + orderId;
    String output = mysqlConnectionObject.executeSelectQuery(sql);
    return output.replaceAll("\n", "").replaceAll("\r", "");
  }

  private String get_all_detail(String orderId, String where_column, String table_name)
      throws SQLException {
    String sql = "select * from 3Sep." + table_name + " where " + where_column + "=" + orderId;
    String output = mysqlConnectionObject.executeSelectQuery(sql);
    return output.replaceAll("\n", "").replaceAll("\r", "");
  }

  @Test(enabled = false)
  public void sales_flat_order() throws Exception {
    // sessiontoken =
    // CustomerUtil.get_sessionId_after_user_authentication(SignInLoginConstants.email,
    // SignInLoginConstants.password);
    // HttpResponse httpresponse_Cart = CartUtil.CreateCart(sessiontoken,
    // SignInLoginConstants.productId, "android");
    // JSONObject cart_response =
    // RequestUtil.convert_HttpResponse_to_JsonObject(httpresponse_Cart);
    // System.out.println("-----------Create Cart response-----------");
    // System.out.println(cart_response);
    // System.out.println("------------------------------------------");
    // JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
    // "android", "PU:ICIB", "nb", "nb", "");
    // System.out.println("------------OrderPayment Response-------------");
    // System.out.println(payment_response);
    // System.out.println("-----------------------------------------------");
    // JSONObject result = payment_response.getJSONObject("result");
    // JSONObject order_detail = result.getJSONObject("order");
    // String orderId = order_detail.getString("id");

    if (db_Validation) {
      Map<String, Object> get_order_details = new HashMap<String, Object>();
      get_order_details.put("_id", orderId);
      List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);

      Document order_detail = (Document) getOrderDetails.get(0);
      System.out.println("Mongo db " + order_detail);

      List<Document> item_detail = (List<Document>) order_detail.get("items");
      for (int i = 0; i < item_detail.size(); i++) {
        Document item_detail_values = (Document) item_detail.get(i);

        Document status_db = (Document) order_detail.get("status");

        String mySql = get_all_detail("'" + orderId + "'", "increment_id", "sales_flat_order");
        System.out.println("wskjdksfjdfj   " + mySql);

        String state =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "state");
        Assert.assertEquals(state.toUpperCase(), status_db.getString("state"), "State is not same");

        String status =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "status");
        Assert.assertEquals(status.toUpperCase(), status_db.getString("status"),
            "Status is not same");

        // String
        // coupon_code=get_column_value(orderId,"increment_id","sales_flat_order","coupon_code");

        String store_id =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "store_id");
        Assert.assertEquals(store_id, String.valueOf(order_detail.getLong("storeId")),
            "store_id is not same");

        String customer_id = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "customer_id");
        Assert.assertEquals(customer_id, String.valueOf(order_detail.getLong("customerId")),
            "customer_id is not same");
        Document amount_detail = (Document) order_detail.get("amount");

        // bug raised
        String base_discount_amount = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "base_discount_amount");
        System.out.println("---base_discount_amount--" + base_discount_amount);
        // Assert.assertEquals(base_discount_amount,
        // String.valueOf(String.format("%.2f",
        // amount_detail.getDouble("totaldiscount"))),
        // "base_discount_amount is not same");

        // String base_grand_total = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "base_grand_total");
        // Assert.assertEquals(base_grand_total,
        // String.valueOf(order_detail.getLong("storeId")),
        // "base_grand_total is not same");
        //
        // String base_shipping_amount = get_column_value("'"+orderId+"'",
        // "increment_id", "sales_flat_order",
        // "base_shipping_amount");
        // Assert.assertEquals(base_shipping_amount,
        // String.valueOf(order_detail.getLong("storeId")),
        // "base_shipping_amount is not same");

        String base_subtotal = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "base_subtotal");
        // String from_Mongo =
        // String.format("%.2f",Double.parseDouble(String.valueOf(amount_detail.get("subTotal"))));
        System.out.println("----from_Mongo" + String.format("%.4f",
            Double.parseDouble(String.valueOf(amount_detail.get("subTotal")))));
        Assert.assertEquals(base_subtotal,
            String.format("%.4f",
                Double.parseDouble(String.valueOf(amount_detail.get("subTotal")))),
            "base_subtotal is not same");

        // Bug to be raisecd
        String discount_amount = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "discount_amount");
        // Assert.assertEquals(discount_amount,
        // String.format("%.2f",Double.parseDouble(String.valueOf(amount_detail.get("totaldiscount")))),
        // "discount_amount is not same");

        String grand_total = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "grand_total");
        Assert.assertEquals(grand_total,
            String.format("%.4f", Double.parseDouble(String.valueOf(amount_detail.get("total")))),
            "grand_total is not same");

        // To be checked after scm integration
        String shipping_amount = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "shipping_amount");
        // Assert.assertEquals(shipping_amount,
        // String.valueOf(amount_detail.get("shipping")),
        // "shipping_amount is not same");

        String subtotal =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "subtotal");
        // Assert.assertEquals(subtotal, String.valueOf(amount_detail.get("subTotal")),
        // "subtotal is not same");

        String tax_amount =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "tax_amount");
        Assert.assertEquals(tax_amount,
            String.format("%.4f",
                Double.parseDouble(String.valueOf(String.valueOf(amount_detail.get("totalTax"))))),
            "tax_amount is not same");
        // To be checked after scm integration
        String total_invoiced = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "total_invoiced");
        // Assert.assertEquals(total_invoiced,
        // String.valueOf(amount_detail.get("total")),
        // "total_invoiced is not same");

        // String total_paid = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "total_paid");
        // Assert.assertEquals(total_paid,
        // String.valueOf(order_detail.getLong("storeId")),
        // "total_paid is not same");
        //
        // String total_qty_ordered = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order",
        // "total_qty_ordered");
        // Assert.assertEquals(total_qty_ordered,
        // String.valueOf(order_detail.getLong("storeId")),
        // "total_qty_ordered is not same");
        //
        // String total_refunded = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "total_refunded");
        // Assert.assertEquals(total_refunded,
        // String.valueOf(order_detail.getLong("storeId")),
        // "total_refunded is not same");

        String customer_is_guest = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "customer_is_guest");
        if (customer_is_guest.equals("0")) {
          customer_is_guest = "false";
        } else {
          customer_is_guest = "true";
        }
        Assert.assertEquals(customer_is_guest, String.valueOf(order_detail.get("guest")),
            "customer_is_guest is not same");
        Document billing_Address = (Document) order_detail.get("billingAddress");
        String billing_address_id = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "billing_address_id");
        Assert.assertEquals(billing_address_id, String.valueOf(billing_Address.getLong("id")),
            "billing_address_id is not same");

        String quote_id =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "quote_id");
        Assert.assertEquals(quote_id, String.valueOf(order_detail.getLong("cartId")),
            "quote_id is not same");
        Document shipping_Address = (Document) order_detail.get("shippingAddress");
        String shipping_address_id = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "shipping_address_id");
        Assert.assertEquals(shipping_address_id, String.valueOf(shipping_Address.getLong("id")),
            "shipping_address_id is not same");
        Document payment_details = (Document) order_detail.get("payments");
        String total_due =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "total_due");
        Assert.assertEquals(total_due,
            String.format("%.4f",
                Double.parseDouble(String.valueOf(payment_details.get("totalCOD")))),
            "total_due is not same");

        String increment_id = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "increment_id");
        Assert.assertEquals(increment_id, String.valueOf(order_detail.getLong("_id")),
            "increment_id is not same");

        String applied_rule_ids = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "applied_rule_ids");
        // To be Checked
        System.out.println("@@@@@@@@@@@@@@@@applied_rule_ids" + applied_rule_ids);
        System.out.println(
            "@@@@@@@@@@@@@@@@applied_rule_ids" + String.valueOf(order_detail.get("appliedRules")));
        List<Document> appliedRules = (List<Document>) item_detail_values.get("appliedRules");
        // Document appliedRules_values=(Document) appliedRules.get(0);
        // Assert.assertEquals(applied_rule_ids, item_detail_values.get("appliedRules"),
        // "applied_rule_ids is not same");

        // String base_currency_code = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order",
        // "base_currency_code");
        // Assert.assertEquals(base_currency_code,
        // String.valueOf(order_detail.getLong("storeId")),
        // "base_currency_code is not same");

        String customer_email = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "customer_email");
        Assert.assertEquals(customer_email, String.valueOf(order_detail.getString("customerEmail")),
            "customer_email is not same");

        // String customer_firstname = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order",
        // "customer_firstname");
        // Assert.assertEquals(customer_firstname,
        // String.valueOf(billing_Address.getString("customer_firstname")),
        // "customer_firstname is not same");

        // String customer_lastname = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order",
        // "customer_lastname");
        // Assert.assertEquals(customer_lastname,
        // String.valueOf(order_detail.getLong("storeId")),
        // "customer_lastname is not same");

        // String customer_middlename = get_column_value("'"+orderId+"'",
        // "increment_id", "sales_flat_order",
        // "customer_middlename");
        // Assert.assertEquals(customer_middlename,
        // String.valueOf(order_detail.getLong("storeId")),
        // "customer_middlename is not same");

        String ext_customer_id = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "ext_customer_id");
        Assert.assertEquals(ext_customer_id, String.valueOf(order_detail.getString("apiClient")),
            "ext_customer_id is not same");
        Document amount = (Document) order_detail.get("amount");
        String order_currency_code = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "order_currency_code");
        Assert.assertEquals(order_currency_code, String.valueOf(amount.get("currencyCode")),
            "order_currency_code is not same");

        // String customer_note = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "customer_note");
        // Assert.assertEquals(customer_note,
        // String.valueOf(order_detail.getLong("customerNote")),
        // "customer_note is not same");

        // String created_at = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "created_at");
        // Assert.assertEquals(created_at,
        // String.valueOf(order_detail.getLong("createdAt")),
        // "created_at is not same");

        // String updated_at = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "updated_at");
        // Assert.assertEquals(updated_at,
        // String.valueOf(order_detail.getLong("updatedAt")),
        // "updated_at is not same");

        String total_item_count = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "total_item_count");
        Assert.assertEquals(total_item_count, String.valueOf(order_detail.get("itemCount")),
            "total_item_count is not same");

        String gift_message_id = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "gift_message_id");
        Assert.assertEquals(gift_message_id, String.valueOf(order_detail.get("giftMessage")),
            "gift_message_id is not same");

        String sale_source = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "sale_source");
        Assert.assertEquals(sale_source, String.valueOf(order_detail.get("saleSource")),
            "sale_source is not same");

        String utm_dump =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "utm_dump");
        // Assert.assertEquals(utm_dump, String.valueOf(order_detail.get("utmdump")),
        // "utm_dump is not same");

        String offer_3orfree = get_column_value("'" + orderId + "'", "increment_id",
            "sales_flat_order", "offer_3orfree");
        Assert.assertEquals(offer_3orfree, String.valueOf(order_detail.get("offer3OrFree")),
            "offer_3orfree is not same");

        // String mall = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "mall");
        // Assert.assertEquals(mall, String.valueOf(order_detail.getLong("storeId")),
        // "mall is not same");

        // String website = get_column_value("'"+orderId+"'", "increment_id",
        // "sales_flat_order", "website");
        // Assert.assertEquals(website,
        // String.valueOf(order_detail.getLong("lenskart.com")), "website is not same");

        String base_vat =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "base_vat");
        // Assert.assertEquals(base_vat, String.valueOf(order_detail.get("storeId")),
        // "base_vat is not same");

        String vat =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "vat");
        // Assert.assertEquals(vat, String.valueOf(order_detail.getLong("storeId")),
        // "vat is not same");

        String parent_id =
            get_column_value("'" + orderId + "'", "increment_id", "sales_flat_order", "parent_id");
        // Assert.assertEquals(parent_id,
        // String.valueOf(order_detail.getLong("storeId")),
        // "parent_id is not same");

      }
    }
  }

  @Test(enabled = false)
  public void sales_flat_order_item() throws Exception {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd h:m");

    if (db_Validation) {
      Map<String, Object> get_order_details = new HashMap<String, Object>();
      get_order_details.put("_id", orderId);
      List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
      Document order_detail = (Document) getOrderDetails.get(0);
      System.out.println("Mongo db " + order_detail);

      @SuppressWarnings("unchecked")
      List<Document> item_detail = (List<Document>) order_detail.get("items");
      for (int i = 0; i < item_detail.size(); i++) {
        Document item_detail_values = (Document) item_detail.get(i);
        String item_id = String.valueOf(item_detail_values.getLong("id"));
        System.out.println("Item_id: " + item_id);
        System.out.println("Item values " + item_detail_values);

        String mySql = get_all_detail(item_id, "item_id", "sales_flat_order_item");
        System.out.println("Mysql response   " + mySql);

        String order_id = get_column_value(item_id, "item_id", "sales_flat_order_item", "order_id");
        String entity = get_column_value(String.valueOf(orderId), "increment_id",
            "sales_flat_order", "entity_id");
        Assert.assertEquals(entity, order_id, "entity is not same");

        // String parent_item_id = get_column_value(item_id, "item_id",
        // "sales_flat_order_item", "parent_item_id");
        // Assert.assertEquals(parent_item_id, item_detail_values.getString("status"),
        // "parent_item_id is not same");

        // String quote_item_id = get_column_value(item_id, "item_id",
        // "sales_flat_order_item", "quote_item_id");
        // Assert.assertEquals(quote_item_id, item_detail_values.getString("status"),
        // "parent_item_id is not same");

        // bug raised
        String store_id = get_column_value(item_id, "item_id", "sales_flat_order_item", "store_id");
        Assert.assertEquals(store_id, String.valueOf(order_detail.getLong("storeId")),
            "store_id is not same");

        // converted from
        String created_at =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "created_at");

        System.out.println(created_at);
        System.out.println(String.valueOf(item_detail_values.getDate("createdAt")));
        // Assert.assertEquals(sdf.parse(created_at),
        // sdf.parse(String.valueOf(item_detail_values.getDate("createdAt"))),
        // "created_at is not same");

        String updated_at =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "updated_at");
        // Assert.assertEquals(updated_at,
        // String.valueOf(item_detail_values.getDate("updatedAt")), "updated_at is not
        // same");

        String product_id =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "product_id");

        System.out.println("From SQL#####" + product_id + "From Mongo####"
            + String.valueOf(item_detail_values.getLong("productId")));
        Assert.assertEquals(product_id, String.valueOf(item_detail_values.getLong("productId")),
            "product_id is not same");
        // bug raised
        String product_type =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "product_type");
        System.out.println("product_type" + product_type);
        System.out.println("item_detail_values.getString(productTypeValue)"
            + item_detail_values.getString("productTypeValue"));
        // Assert.assertEquals(product_type,item_detail_values.getString("productTypeValue"),
        // "product_type is not same");

        // Value mismatch
        String product_options =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "product_options");
        // Assert.assertEquals(product_options,item_detail_values.get("options"),
        // "product_options is not same");

        String sku = get_column_value(item_id, "item_id", "sales_flat_order_item", "sku");
        Assert.assertEquals(sku, item_detail_values.getString("sku"), "sku is not same");

        // produt name is missing mongo db
        // String name = get_column_value(item_id, "item_id", "sales_flat_order_item",
        // "name");
        // Assert.assertEquals(name,item_detail_values.get("options"), "name is not
        // same");

        // description is missing mongo db
        // String description = get_column_value(item_id, "item_id",
        // "sales_flat_order_item", "description");
        // Assert.assertEquals(description,item_detail_values.get("options"),
        // "description is not same");

        // need to check
        // @SuppressWarnings("unchecked")
        // List<Document>appliedRules=(List<Document>)item_detail_values.get("appliedRules");
        // Document appliedRules_values=(Docum appliedRules.get(0);
        // String applied_rule_ids = get_column_value(item_id, "item_id",
        // "sales_flat_order_item", "applied_rule_ids");
        // Assert.assertEquals(applied_rule_ids,appliedRules_values.get(0),
        // "applied_rule_ids is not same");

        String qty_canceled =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "qty_canceled");
        Assert.assertEquals(qty_canceled, "0.0000", "qty_canceled is not same");

        String qty_invoiced =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "qty_invoiced");
        Assert.assertEquals(qty_invoiced, "0.0000", "qty_invoiced is not same");

        String qty_ordered =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "qty_ordered");
        Assert.assertEquals(qty_ordered,
            String.valueOf(item_detail_values.get("quantity")) + ".0000",
            "qty_ordered is not same");

        String qty_refunded =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "qty_refunded");
        Assert.assertEquals(qty_refunded, "0.0000", "qty_refunded is not same");
        // base cost is missing
        // String base_cost = get_column_value(item_id, "item_id",
        // "sales_flat_order_item", "base_cost");
        // Assert.assertEquals(base_cost,"0.0000", "base_cost is not same");

        Document price_db = (Document) item_detail_values.get("price");
        String price_val = price_db.getString("value");
        String price_value = price_val + ".0000";
        String price = get_column_value(item_id, "item_id", "sales_flat_order_item", "price");
        Assert.assertEquals(price, price_value, "price is not same");

        String base_price =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "base_price");
        Assert.assertEquals(base_price, price_value, "base_price is not same");

        String original_price =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "original_price");
        Assert.assertEquals(original_price, price_value, "original_price is not same");

        String base_original_price =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "base_original_price");
        Assert.assertEquals(base_original_price, price_value, "base_original_price is not same");

        Document amount_db = (Document) item_detail_values.get("amount");
        String amount_value = amount_db.getString("totalDiscount");
        String total_value = amount_db.getString("total");
        String discount_amount =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "discount_amount");
        Assert.assertEquals(Double.parseDouble(discount_amount), Double.parseDouble(amount_value),
            "discount_amount is not same");

        String base_discount_amount =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "base_discount_amount");
        Assert.assertEquals(Double.parseDouble(base_discount_amount),
            Double.parseDouble(amount_value), "base_discount_amount is not same");

        String amount_refunded =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "amount_refunded");
        Assert.assertEquals(amount_refunded, "0.0000", "amount_refunded is not same");

        String base_amount_refunded =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "base_amount_refunded");
        Assert.assertEquals(base_amount_refunded, "0.0000", "base_amount_refunded is not same");

        String row_total =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "row_total");
        Assert.assertEquals(row_total, "0.0000", "row_total is not same");

        String base_row_total =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "base_row_total");
        Assert.assertEquals(base_row_total, "0.0000", "base_row_total is not same");

        String price_incl_tax =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "price_incl_tax");
        Assert.assertEquals(price_incl_tax, price_value, "price_incl_tax is not same");

        String base_price_incl_tax =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "base_price_incl_tax");
        Assert.assertEquals(base_price_incl_tax, price_value, "base_price_incl_tax is not same");

        String row_total_incl_tax =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "row_total_incl_tax");
        Assert.assertEquals(Double.parseDouble(row_total_incl_tax),
            Double.parseDouble((total_value)), "row_total_incl_tax is not same");

        String base_row_total_incl_tax = get_column_value(item_id, "item_id",
            "sales_flat_order_item", "base_row_total_incl_tax");
        Assert.assertEquals(Double.parseDouble(base_row_total_incl_tax),
            Double.parseDouble(total_value), "base_row_total_incl_tax is not same");

        @SuppressWarnings("unchecked")
        List<Document> taxes = (List<Document>) amount_db.get("taxes");
        Document taxes_0 = (Document) taxes.get(0);
        if (taxes_0.get("name").equals("VAT")) {
          String vat = get_column_value(item_id, "item_id", "sales_flat_order_item", "vat");
          Assert.assertEquals(vat, taxes_0.get("amount"), "vat is not same");
        }
        Document inventory = (Document) item_detail_values.get("inventory");

        String delivery_store =
            get_column_value(item_id, "item_id", "sales_flat_order_item", "delivery_store");
        Assert.assertEquals(delivery_store, String.valueOf(inventory.getLong("deliveryStoreId")),
            "delivery_store is not same");

      }
    }
  }

  @Test(enabled = false)
  public void sales_flat_order_address_billingAddress() throws Exception {
    // sessiontoken =
    // CustomerUtil.get_sessionId_after_user_authentication(SignInLoginConstants.email,
    // SignInLoginConstants.password);
    // HttpResponse httpresponse_Cart = CartUtil.CreateCart(sessiontoken,
    // SignInLoginConstants.productId, "android");
    // JSONObject cart_response =
    // RequestUtil.convert_HttpResponse_to_JsonObject(httpresponse_Cart);
    // System.out.println("-----------Create Cart response-----------");
    // System.out.println(cart_response);
    // System.out.println("------------------------------------------");
    // JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
    // "android", "PU:ICIB", "nb", "nb", "");
    // System.out.println("------------OrderPayment Response-------------");
    // System.out.println(payment_response);
    // System.out.println("-----------------------------------------------");
    // JSONObject result = payment_response.getJSONObject("result");
    // JSONObject order_detail = result.getJSONObject("order");
    // String orderId = order_detail.getString("id");

    if (db_Validation) {
      // PropertyFactory pf = new PropertyFactory("OrderAPI");
      // DatabaseUtil.initMongoDBConnection(pf);

      Map<String, Object> get_order_details = new HashMap<String, Object>();
      get_order_details.put("_id", orderId);
      List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
      Document Billing_order_detail = (Document) getOrderDetails.get(0);
      System.out.println("sdfdgdferfdrfdgf " + Billing_order_detail);
      Document order_detail = (Document) Billing_order_detail.get("billingAddress");
      String entity = String.valueOf(order_detail.getLong("id"));
      System.out.println("sdfdf" + entity);
      System.out.println("sdfdgdrfdgf " + order_detail);

      String output = get_all_detail(entity, "entity_id", "sales_flat_order_address");
      System.out.println("fsdfsdf " + output);

      // String customer_id = get_column_value(entity, "entity_id",
      // "sales_flat_order_address", "customer_id");
      // Assert.assertEquals(customer_id, order_detail.getString("customer_id"),
      // "customer_id is not same");

      String region = get_column_value(entity, "entity_id", "sales_flat_order_address", "region");
      Assert.assertEquals(region, order_detail.getString("state"), "region is not same");

      String postcode =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "postcode");
      Assert.assertEquals(postcode, order_detail.getString("postcode"), "postcode is not same");

      String lastname =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "lastname");
      Assert.assertEquals(lastname, order_detail.get("customerName"), "lastname is not same");

      String middlename =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "middlename");
      Assert.assertEquals(middlename, order_detail.get("customerName"), "middlename is not same");

      String firstname =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "firstname");
      Assert.assertEquals(firstname, order_detail.get("customerName"), "firstname is not same");

      String street = get_column_value(entity, "entity_id", "sales_flat_order_address", "street");
      Assert.assertEquals(street,
          order_detail.getString("addressline1") + order_detail.getString("addressline2"),
          "street is not same");

      String city = get_column_value(entity, "entity_id", "sales_flat_order_address", "city");
      Assert.assertEquals(city, order_detail.getString("city"), "city is not same");

      String email = get_column_value(entity, "entity_id", "sales_flat_order_address", "email");
      Assert.assertEquals(email, order_detail.getString("email"), "email is not same");

      String telephone =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "telephone");
      Assert.assertEquals(telephone, order_detail.getString("phone"), "telephone is not same");

      String country_id =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "country_id");
      Assert.assertEquals(country_id, order_detail.getString("country"), "country_id is not same");

      String address_type =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "address_type");
      Assert.assertEquals(address_type, order_detail.getString("addressType"),
          "address_type is not same");

      // String liftAvailable = get_column_value(entity, "entity_id",
      // "sales_flat_order_address", "liftAvailable");
      // Assert.assertEquals(liftAvailable,
      // String.valueOf(order_detail.getBoolean("liftAvailable")), "liftAvailable is
      // not same");
      //
      // String landmark = get_column_value(entity, "entity_id",
      // "sales_flat_order_address", "landmark");
      // Assert.assertEquals(landmark, order_detail.getString("landmark"), "landmark
      // is not same");
      //
      // String defaultAddress = get_column_value(entity, "entity_id",
      // "sales_flat_order_address", "defaultAddress");
      // Assert.assertEquals(defaultAddress,
      // String.valueOf(order_detail.getBoolean("defaultAddress")), "defaultAddress is
      // not same");

    }

  }

  @Test(enabled = false)
  public void sales_flat_order_address_shippingAddress() throws Exception {
    // sessiontoken =
    // CustomerUtil.get_sessionId_after_user_authentication(SignInLoginConstants.email,
    // SignInLoginConstants.password);
    // HttpResponse httpresponse_Cart = CartUtil.CreateCart(sessiontoken,
    // SignInLoginConstants.productId, "android");
    // JSONObject cart_response =
    // RequestUtil.convert_HttpResponse_to_JsonObject(httpresponse_Cart);
    // System.out.println("-----------Create Cart response-----------");
    // System.out.println(cart_response);
    // System.out.println("------------------------------------------");
    // JSONObject payment_response = PaymentUtil.orderPayment(sessiontoken,
    // "android", "PU:ICIB", "nb", "nb", "");
    // System.out.println("------------OrderPayment Response-------------");
    // System.out.println(payment_response);
    // System.out.println("-----------------------------------------------");
    // JSONObject result = payment_response.getJSONObject("result");
    // JSONObject order_detail = result.getJSONObject("order");
    // String orderId = order_detail.getString("id");

    if (db_Validation) {
      // PropertyFactory pf = new PropertyFactory("OrderAPI");
      // DatabaseUtil.initMongoDBConnection(pf);

      Map<String, Object> get_order_details = new HashMap<String, Object>();
      get_order_details.put("_id", orderId);
      List<Document> getOrderDetails = OrderUtil.getAllOrders(get_order_details);
      Document Billing_order_detail = (Document) getOrderDetails.get(0);
      System.out.println("sdfdgdferfdrfdgf " + Billing_order_detail);
      Document order_detail = (Document) Billing_order_detail.get("shippingAddress");
      String entity = String.valueOf(order_detail.getLong("id"));
      System.out.println("sdfdf" + entity);
      System.out.println("sdfdgdrfdgf " + order_detail);

      String customer_id =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "customer_id");
      Assert.assertEquals(customer_id, order_detail.getString("customer_id"),
          "customer_id is not same");

      String output = get_all_detail(entity, "entity_id", "sales_flat_order_address");
      System.out.println("fsdfsdf " + output);
      String region = get_column_value(entity, "entity_id", "sales_flat_order_address", "region");
      Assert.assertEquals(region, order_detail.getString("state"), "region is not same");

      String postcode =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "postcode");
      Assert.assertEquals(postcode, order_detail.getString("postcode"), "postcode is not same");

      String lastname =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "lastname");
      Assert.assertEquals(lastname, order_detail.get("customerName"), "lastname is not same");

      String middlename =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "middlename");
      Assert.assertEquals(middlename, order_detail.get("customerName"), "middlename is not same");

      String firstname =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "firstname");
      Assert.assertEquals(firstname, order_detail.get("customerName"), "firstname is not same");

      String street = get_column_value(entity, "entity_id", "sales_flat_order_address", "street");
      Assert.assertEquals(street,
          order_detail.getString("addressline1") + order_detail.getString("addressline2"),
          "street is not same");

      String city = get_column_value(entity, "entity_id", "sales_flat_order_address", "city");
      Assert.assertEquals(city, order_detail.getString("city"), "city is not same");

      String email = get_column_value(entity, "entity_id", "sales_flat_order_address", "email");
      Assert.assertEquals(email, order_detail.getString("email"), "email is not same");

      String telephone =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "telephone");
      Assert.assertEquals(telephone, order_detail.getString("phone"), "telephone is not same");

      String country_id =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "country_id");
      Assert.assertEquals(country_id, order_detail.getString("country"), "country_id is not same");

      String address_type =
          get_column_value(entity, "entity_id", "sales_flat_order_address", "address_type");
      Assert.assertEquals(address_type, order_detail.getString("addressType"),
          "address_type is not same");

      // String liftAvailable = get_column_value(entity, "entity_id",
      // "sales_flat_order_address", "liftAvailable");
      // Assert.assertEquals(liftAvailable,
      // String.valueOf(order_detail.getBoolean("liftAvailable")), "liftAvailable is
      // not same");
      //
      // String landmark = get_column_value(entity, "entity_id",
      // "sales_flat_order_address", "landmark");
      // Assert.assertEquals(landmark, order_detail.getString("landmark"), "landmark
      // is not same");
      //
      // String defaultAddress = get_column_value(entity, "entity_id",
      // "sales_flat_order_address", "defaultAddress");
      // Assert.assertEquals(defaultAddress,
      // String.valueOf(order_detail.getBoolean("defaultAddress")), "defaultAddress is
      // not same");

    }

  }

  @Test(enabled = true)
  public void sales_flat_order_SQL() throws Exception {
    if (db_Validation) {
      String mySql_PHP =
          get_all_detail("'" + order_ID_Throu_PHP + "'", "increment_id", "sales_flat_order");
      String mySQL_Juno =
          get_all_detail("'" + order_ID_Throu_JUNO + "'", "increment_id", "sales_flat_order");
      System.out.println("ORDER ID Details PHP#########  " + mySql_PHP);
      Map<String, Object> get_order_details_php = new HashMap<String, Object>();
      get_order_details_php.put("entity_id", mySql_PHP.split(",")[0]);
      get_order_details_php.put("state", mySql_PHP.split(",")[1]);
      get_order_details_php.put("status", mySql_PHP.split(",")[2]);
      get_order_details_php.put("coupon_code", mySql_PHP.split(",")[3]);
      get_order_details_php.put("protect_code", mySql_PHP.split(",")[4]);
      get_order_details_php.put("shipping_description", mySql_PHP.split(",")[5]);
      get_order_details_php.put("is_virtual", mySql_PHP.split(",")[6]);
      get_order_details_php.put("store_id", mySQL_Juno.split(",")[7]);
      get_order_details_php.put("customer_id", mySQL_Juno.split(",")[8]);
      get_order_details_php.put("base_discount_amount", mySQL_Juno.split(",")[9]);
      get_order_details_php.put("base_discount_canceled", mySQL_Juno.split(",")[10]);
      get_order_details_php.put("base_discount_invoiced", mySQL_Juno.split(",")[11]);
      get_order_details_php.put("base_discount_refunded", mySQL_Juno.split(",")[12]);
      get_order_details_php.put("base_grand_total", mySQL_Juno.split(",")[13]);
      get_order_details_php.put("base_shipping_amount", mySQL_Juno.split(",")[14]);
      get_order_details_php.put("base_shipping_canceled", mySQL_Juno.split(",")[15]);
      get_order_details_php.put("base_shipping_invoiced", mySQL_Juno.split(",")[16]);
      get_order_details_php.put("base_shipping_refunded", mySQL_Juno.split(",")[17]);
      get_order_details_php.put("base_shipping_tax_amount", mySQL_Juno.split(",")[18]);
      get_order_details_php.put("base_shipping_tax_refunded", mySQL_Juno.split(",")[19]);
      get_order_details_php.put("base_subtotal", mySQL_Juno.split(",")[20]);
      get_order_details_php.put("base_subtotal_canceled", mySQL_Juno.split(",")[21]);
      get_order_details_php.put("base_subtotal_invoiced", mySQL_Juno.split(",")[22]);
      get_order_details_php.put("base_subtotal_refunded", mySQL_Juno.split(",")[23]);
      get_order_details_php.put("base_tax_amount", mySQL_Juno.split(",")[24]);
      get_order_details_php.put("base_tax_canceled", mySQL_Juno.split(",")[25]);
      get_order_details_php.put("base_tax_invoiced", mySQL_Juno.split(",")[26]);
      get_order_details_php.put("base_tax_refunded", mySQL_Juno.split(",")[27]);
      get_order_details_php.put("base_to_global_rate", mySQL_Juno.split(",")[28]);
      get_order_details_php.put("base_to_order_rate", mySQL_Juno.split(",")[29]);
      get_order_details_php.put("base_total_canceled", mySQL_Juno.split(",")[30]);
      get_order_details_php.put("base_total_invoiced", mySQL_Juno.split(",")[31]);
      get_order_details_php.put("base_total_invoiced_cost", mySQL_Juno.split(",")[32]);
      get_order_details_php.put("base_total_offline_refunded", mySQL_Juno.split(",")[33]);
      get_order_details_php.put("base_total_online_refunded", mySQL_Juno.split(",")[34]);
      get_order_details_php.put("base_total_paid", mySQL_Juno.split(",")[35]);
      get_order_details_php.put("base_total_qty_ordered", mySQL_Juno.split(",")[36]);
      get_order_details_php.put("base_total_refunded", mySQL_Juno.split(",")[37]);
      get_order_details_php.put("discount_amount", mySQL_Juno.split(",")[38]);
      get_order_details_php.put("discount_canceled", mySQL_Juno.split(",")[39]);
      get_order_details_php.put("discount_invoiced", mySQL_Juno.split(",")[40]);
      get_order_details_php.put("discount_refunded", mySQL_Juno.split(",")[41]);
      get_order_details_php.put("grand_total", mySQL_Juno.split(",")[42]);
      get_order_details_php.put("shipping_amount", mySQL_Juno.split(",")[43]);
      get_order_details_php.put("shipping_canceled", mySQL_Juno.split(",")[44]);
      get_order_details_php.put("shipping_invoiced", mySQL_Juno.split(",")[45]);
      get_order_details_php.put("shipping_refunded", mySQL_Juno.split(",")[46]);
      get_order_details_php.put("shipping_tax_amount", mySQL_Juno.split(",")[47]);
      get_order_details_php.put("shipping_tax_refunded", mySQL_Juno.split(",")[48]);
      get_order_details_php.put("store_to_base_rate", mySQL_Juno.split(",")[49]);
      get_order_details_php.put("store_to_order_rate", mySQL_Juno.split(",")[50]);
      get_order_details_php.put("subtotal", mySQL_Juno.split(",")[51]);
      get_order_details_php.put("subtotal_canceled", mySQL_Juno.split(",")[52]);
      get_order_details_php.put("subtotal_invoiced", mySQL_Juno.split(",")[53]);
      get_order_details_php.put("subtotal_refunded", mySQL_Juno.split(",")[54]);
      get_order_details_php.put("tax_amount", mySQL_Juno.split(",")[55]);
      get_order_details_php.put("tax_canceled", mySQL_Juno.split(",")[56]);
      get_order_details_php.put("tax_invoiced", mySQL_Juno.split(",")[57]);
      get_order_details_php.put("tax_refunded", mySQL_Juno.split(",")[58]);
      get_order_details_php.put("total_canceled", mySQL_Juno.split(",")[59]);
      get_order_details_php.put("total_invoiced", mySQL_Juno.split(",")[60]);
      get_order_details_php.put("total_offline_refunded", mySQL_Juno.split(",")[61]);
      get_order_details_php.put("total_online_refunded", mySQL_Juno.split(",")[62]);
      get_order_details_php.put("total_paid", mySQL_Juno.split(",")[63]);
      get_order_details_php.put("total_qty_ordered", mySQL_Juno.split(",")[64]);
      get_order_details_php.put("total_refunded", mySQL_Juno.split(",")[65]);
      get_order_details_php.put("can_ship_partially", mySQL_Juno.split(",")[66]);
      get_order_details_php.put("can_ship_partially_item", mySQL_Juno.split(",")[67]);
      get_order_details_php.put("customer_is_guest", mySQL_Juno.split(",")[68]);
      get_order_details_php.put("customer_note_notify", mySQL_Juno.split(",")[69]);
      get_order_details_php.put("billing_address_id", mySQL_Juno.split(",")[70]);
      get_order_details_php.put("customer_group_id", mySQL_Juno.split(",")[71]);
      get_order_details_php.put("edit_increment", mySQL_Juno.split(",")[72]);
      get_order_details_php.put("email_sent", mySQL_Juno.split(",")[73]);
      get_order_details_php.put("forced_shipment_with_invoice", mySQL_Juno.split(",")[74]);
      get_order_details_php.put("payment_auth_expiration", mySQL_Juno.split(",")[75]);
      get_order_details_php.put("quote_address_id", mySQL_Juno.split(",")[76]);
      get_order_details_php.put("quote_id", mySQL_Juno.split(",")[77]);
      get_order_details_php.put("shipping_address_id", mySQL_Juno.split(",")[78]);
      get_order_details_php.put("adjustment_negative", mySQL_Juno.split(",")[79]);
      get_order_details_php.put("adjustment_positive", mySQL_Juno.split(",")[80]);
      get_order_details_php.put("base_adjustment_negative", mySQL_Juno.split(",")[81]);
      get_order_details_php.put("base_adjustment_positive", mySQL_Juno.split(",")[82]);
      get_order_details_php.put("base_shipping_discount_amount", mySQL_Juno.split(",")[83]);
      get_order_details_php.put("base_subtotal_incl_tax", mySQL_Juno.split(",")[84]);
      get_order_details_php.put("base_total_due", mySQL_Juno.split(",")[85]);
      get_order_details_php.put("payment_authorization_amount", mySQL_Juno.split(",")[86]);
      get_order_details_php.put("shipping_discount_amount", mySQL_Juno.split(",")[87]);
      get_order_details_php.put("subtotal_incl_tax", mySQL_Juno.split(",")[88]);
      get_order_details_php.put("total_due", mySQL_Juno.split(",")[89]);
      get_order_details_php.put("weight", mySQL_Juno.split(",")[90]);
      get_order_details_php.put("customer_dob", mySQL_Juno.split(",")[91]);
      get_order_details_php.put("increment_id", mySQL_Juno.split(",")[92]);
      get_order_details_php.put("applied_rule_ids", mySQL_Juno.split(",")[93]);
      get_order_details_php.put("base_currency_code", mySQL_Juno.split(",")[94]);
      get_order_details_php.put("customer_email", mySQL_Juno.split(",")[95]);
      get_order_details_php.put("customer_firstname", mySQL_Juno.split(",")[96]);
      get_order_details_php.put("customer_lastname", mySQL_Juno.split(",")[97]);
      get_order_details_php.put("customer_middlename", mySQL_Juno.split(",")[98]);
      get_order_details_php.put("customer_prefix", mySQL_Juno.split(",")[99]);
      get_order_details_php.put("customer_suffix", mySQL_Juno.split(",")[100]);
      get_order_details_php.put("customer_taxvat", mySQL_Juno.split(",")[101]);
      get_order_details_php.put("discount_description", mySQL_Juno.split(",")[102]);
      get_order_details_php.put("ext_customer_id", mySQL_Juno.split(",")[103]);
      get_order_details_php.put("ext_order_id", mySQL_Juno.split(",")[104]);
      get_order_details_php.put("global_currency_code", mySQL_Juno.split(",")[105]);
      get_order_details_php.put("hold_before_state", mySQL_Juno.split(",")[106]);
      get_order_details_php.put("hold_before_status", mySQL_Juno.split(",")[107]);
      get_order_details_php.put("order_currency_code", mySQL_Juno.split(",")[108]);
      get_order_details_php.put("original_increment_id", mySQL_Juno.split(",")[109]);
      get_order_details_php.put("relation_child_id", mySQL_Juno.split(",")[110]);
      get_order_details_php.put("relation_child_real_id", mySQL_Juno.split(",")[111]);
      get_order_details_php.put("relation_parent_id", mySQL_Juno.split(",")[112]);
      get_order_details_php.put("relation_parent_real_id", mySQL_Juno.split(",")[113]);
      get_order_details_php.put("remote_ip", mySQL_Juno.split(",")[114]);
      get_order_details_php.put("shipping_method", mySQL_Juno.split(",")[115]);
      get_order_details_php.put("store_currency_code", mySQL_Juno.split(",")[116]);
      get_order_details_php.put("store_name", mySQL_Juno.split(",")[117]);
      get_order_details_php.put("x_forwarded_for", mySQL_Juno.split(",")[118]);
      get_order_details_php.put("customer_note", mySQL_Juno.split(",")[119]);
      get_order_details_php.put("created_at", mySQL_Juno.split(",")[120]);
      get_order_details_php.put("updated_at", mySQL_Juno.split(",")[121]);
      get_order_details_php.put("total_item_count", mySQL_Juno.split(",")[122]);
      get_order_details_php.put("customer_gender", mySQL_Juno.split(",")[123]);
      get_order_details_php.put("hidden_tax_amount", mySQL_Juno.split(",")[124]);
      get_order_details_php.put("base_hidden_tax_amount", mySQL_Juno.split(",")[125]);
      get_order_details_php.put("shipping_hidden_tax_amount", mySQL_Juno.split(",")[126]);
      get_order_details_php.put("base_shipping_hidden_tax_amnt", mySQL_Juno.split(",")[127]);
      get_order_details_php.put("hidden_tax_invoiced", mySQL_Juno.split(",")[128]);
      get_order_details_php.put("base_hidden_tax_invoiced", mySQL_Juno.split(",")[129]);
      get_order_details_php.put("hidden_tax_refunded", mySQL_Juno.split(",")[130]);
      get_order_details_php.put("base_hidden_tax_refunded", mySQL_Juno.split(",")[131]);
      get_order_details_php.put("shipping_incl_tax", mySQL_Juno.split(",")[132]);
      get_order_details_php.put("base_shipping_incl_tax", mySQL_Juno.split(",")[133]);
      get_order_details_php.put("coupon_rule_name", mySQL_Juno.split(",")[134]);
      get_order_details_php.put("paypal_ipn_customer_notified", mySQL_Juno.split(",")[135]);
      get_order_details_php.put("gift_message_id", mySQL_Juno.split(",")[136]);
      get_order_details_php.put("sale_source", mySQL_Juno.split(",")[137]);
      get_order_details_php.put("utm_dump", mySQL_Juno.split(",")[138]);
      get_order_details_php.put("imint", mySQL_Juno.split(",")[139]);
      get_order_details_php.put("SapSync", mySQL_Juno.split(",")[140]);
      get_order_details_php.put("emi_charge", mySQL_Juno.split(",")[141]);
      get_order_details_php.put("offer_3orfree", mySQL_Juno.split(",")[142]);
      get_order_details_php.put("mall", mySQL_Juno.split(",")[143]);
      get_order_details_php.put("service_charge", mySQL_Juno.split(",")[144]);
      get_order_details_php.put("donation_charge", mySQL_Juno.split(",")[145]);
      get_order_details_php.put("website", mySQL_Juno.split(",")[146]);
      get_order_details_php.put("base_vat", mySQL_Juno.split(",")[147]);
      get_order_details_php.put("vat", mySQL_Juno.split(",")[148]);
      get_order_details_php.put("delivery_store", mySQL_Juno.split(",")[149]);
      get_order_details_php.put("parent_id", mySQL_Juno.split(",")[150]);
      get_order_details_php.put("is_express", mySQL_Juno.split(",")[151]);
      get_order_details_php.put("delivery_date", mySQL_Juno.split(",")[152]);
      get_order_details_php.put("dispatch_date", mySQL_Juno.split(",")[153]);
      get_order_details_php.put("system_source", mySQL_Juno.split(",")[154]);

      Map<String, Object> get_order_details_juno = new HashMap<String, Object>();
      System.out.println("ORDER ID Details JUNO#########  " + mySQL_Juno);
      get_order_details_juno.put("entity_id", mySQL_Juno.split(",")[0]);
      get_order_details_juno.put("state", mySQL_Juno.split(",")[1]);
      get_order_details_juno.put("status", mySQL_Juno.split(",")[2]);
      get_order_details_juno.put("coupon_code", mySQL_Juno.split(",")[3]);
      get_order_details_juno.put("protect_code", mySQL_Juno.split(",")[4]);
      get_order_details_juno.put("shipping_description", mySQL_Juno.split(",")[5]);
      get_order_details_juno.put("is_virtual", mySQL_Juno.split(",")[6]);
      get_order_details_juno.put("store_id", mySQL_Juno.split(",")[7]);
      get_order_details_juno.put("customer_id", mySQL_Juno.split(",")[8]);
      get_order_details_juno.put("base_discount_amount", mySQL_Juno.split(",")[9]);
      get_order_details_juno.put("base_discount_canceled", mySQL_Juno.split(",")[10]);
      get_order_details_juno.put("base_discount_invoiced", mySQL_Juno.split(",")[11]);
      get_order_details_juno.put("base_discount_refunded", mySQL_Juno.split(",")[12]);
      get_order_details_juno.put("base_grand_total", mySQL_Juno.split(",")[13]);
      get_order_details_juno.put("base_shipping_amount", mySQL_Juno.split(",")[14]);
      get_order_details_juno.put("base_shipping_canceled", mySQL_Juno.split(",")[15]);
      get_order_details_juno.put("base_shipping_invoiced", mySQL_Juno.split(",")[16]);
      get_order_details_juno.put("base_shipping_refunded", mySQL_Juno.split(",")[17]);
      get_order_details_juno.put("base_shipping_tax_amount", mySQL_Juno.split(",")[18]);
      get_order_details_juno.put("base_shipping_tax_refunded", mySQL_Juno.split(",")[19]);
      get_order_details_juno.put("base_subtotal", mySQL_Juno.split(",")[20]);
      get_order_details_juno.put("base_subtotal_canceled", mySQL_Juno.split(",")[21]);
      get_order_details_juno.put("base_subtotal_invoiced", mySQL_Juno.split(",")[22]);
      get_order_details_juno.put("base_subtotal_refunded", mySQL_Juno.split(",")[23]);
      get_order_details_juno.put("base_tax_amount", mySQL_Juno.split(",")[24]);
      get_order_details_juno.put("base_tax_canceled", mySQL_Juno.split(",")[25]);
      get_order_details_juno.put("base_tax_invoiced", mySQL_Juno.split(",")[26]);
      get_order_details_juno.put("base_tax_refunded", mySQL_Juno.split(",")[27]);
      get_order_details_juno.put("base_to_global_rate", mySQL_Juno.split(",")[28]);
      get_order_details_juno.put("base_to_order_rate", mySQL_Juno.split(",")[29]);
      get_order_details_juno.put("base_total_canceled", mySQL_Juno.split(",")[30]);
      get_order_details_juno.put("base_total_invoiced", mySQL_Juno.split(",")[31]);
      get_order_details_juno.put("base_total_invoiced_cost", mySQL_Juno.split(",")[32]);
      get_order_details_juno.put("base_total_offline_refunded", mySQL_Juno.split(",")[33]);
      get_order_details_juno.put("base_total_online_refunded", mySQL_Juno.split(",")[34]);
      get_order_details_juno.put("base_total_paid", mySQL_Juno.split(",")[35]);
      get_order_details_juno.put("base_total_qty_ordered", mySQL_Juno.split(",")[36]);
      get_order_details_juno.put("base_total_refunded", mySQL_Juno.split(",")[37]);
      get_order_details_juno.put("discount_amount", mySQL_Juno.split(",")[38]);
      get_order_details_juno.put("discount_canceled", mySQL_Juno.split(",")[39]);
      get_order_details_juno.put("discount_invoiced", mySQL_Juno.split(",")[40]);
      get_order_details_juno.put("discount_refunded", mySQL_Juno.split(",")[41]);
      get_order_details_juno.put("grand_total", mySQL_Juno.split(",")[42]);
      get_order_details_juno.put("shipping_amount", mySQL_Juno.split(",")[43]);
      get_order_details_juno.put("shipping_canceled", mySQL_Juno.split(",")[44]);
      get_order_details_juno.put("shipping_invoiced", mySQL_Juno.split(",")[45]);
      get_order_details_juno.put("shipping_refunded", mySQL_Juno.split(",")[46]);
      get_order_details_juno.put("shipping_tax_amount", mySQL_Juno.split(",")[47]);
      get_order_details_juno.put("shipping_tax_refunded", mySQL_Juno.split(",")[48]);
      get_order_details_juno.put("store_to_base_rate", mySQL_Juno.split(",")[49]);
      get_order_details_juno.put("store_to_order_rate", mySQL_Juno.split(",")[50]);
      get_order_details_juno.put("subtotal", mySQL_Juno.split(",")[51]);
      get_order_details_juno.put("subtotal_canceled", mySQL_Juno.split(",")[52]);
      get_order_details_juno.put("subtotal_invoiced", mySQL_Juno.split(",")[53]);
      get_order_details_juno.put("subtotal_refunded", mySQL_Juno.split(",")[54]);
      get_order_details_juno.put("tax_amount", mySQL_Juno.split(",")[55]);
      get_order_details_juno.put("tax_canceled", mySQL_Juno.split(",")[56]);
      get_order_details_juno.put("tax_invoiced", mySQL_Juno.split(",")[57]);
      get_order_details_juno.put("tax_refunded", mySQL_Juno.split(",")[58]);
      get_order_details_juno.put("total_canceled", mySQL_Juno.split(",")[59]);
      get_order_details_juno.put("total_invoiced", mySQL_Juno.split(",")[60]);
      get_order_details_juno.put("total_offline_refunded", mySQL_Juno.split(",")[61]);
      get_order_details_juno.put("total_online_refunded", mySQL_Juno.split(",")[62]);
      get_order_details_juno.put("total_paid", mySQL_Juno.split(",")[63]);
      get_order_details_juno.put("total_qty_ordered", mySQL_Juno.split(",")[64]);
      get_order_details_juno.put("total_refunded", mySQL_Juno.split(",")[65]);
      get_order_details_juno.put("can_ship_partially", mySQL_Juno.split(",")[66]);
      get_order_details_juno.put("can_ship_partially_item", mySQL_Juno.split(",")[67]);
      get_order_details_juno.put("customer_is_guest", mySQL_Juno.split(",")[68]);
      get_order_details_juno.put("customer_note_notify", mySQL_Juno.split(",")[69]);
      get_order_details_juno.put("billing_address_id", mySQL_Juno.split(",")[70]);
      get_order_details_juno.put("customer_group_id", mySQL_Juno.split(",")[71]);
      get_order_details_juno.put("edit_increment", mySQL_Juno.split(",")[72]);
      get_order_details_juno.put("email_sent", mySQL_Juno.split(",")[73]);
      get_order_details_juno.put("forced_shipment_with_invoice", mySQL_Juno.split(",")[74]);
      get_order_details_juno.put("payment_auth_expiration", mySQL_Juno.split(",")[75]);
      get_order_details_juno.put("quote_address_id", mySQL_Juno.split(",")[76]);
      get_order_details_juno.put("quote_id", mySQL_Juno.split(",")[77]);
      get_order_details_juno.put("shipping_address_id", mySQL_Juno.split(",")[78]);
      get_order_details_juno.put("adjustment_negative", mySQL_Juno.split(",")[79]);
      get_order_details_juno.put("adjustment_positive", mySQL_Juno.split(",")[80]);
      get_order_details_juno.put("base_adjustment_negative", mySQL_Juno.split(",")[81]);
      get_order_details_juno.put("base_adjustment_positive", mySQL_Juno.split(",")[82]);
      get_order_details_juno.put("base_shipping_discount_amount", mySQL_Juno.split(",")[83]);
      get_order_details_juno.put("base_subtotal_incl_tax", mySQL_Juno.split(",")[84]);
      get_order_details_juno.put("base_total_due", mySQL_Juno.split(",")[85]);
      get_order_details_juno.put("payment_authorization_amount", mySQL_Juno.split(",")[86]);
      get_order_details_juno.put("shipping_discount_amount", mySQL_Juno.split(",")[87]);
      get_order_details_juno.put("subtotal_incl_tax", mySQL_Juno.split(",")[88]);
      get_order_details_juno.put("total_due", mySQL_Juno.split(",")[89]);
      get_order_details_juno.put("weight", mySQL_Juno.split(",")[90]);
      get_order_details_juno.put("customer_dob", mySQL_Juno.split(",")[91]);
      get_order_details_juno.put("increment_id", mySQL_Juno.split(",")[92]);
      get_order_details_juno.put("applied_rule_ids", mySQL_Juno.split(",")[93]);
      get_order_details_juno.put("base_currency_code", mySQL_Juno.split(",")[94]);
      get_order_details_juno.put("customer_email", mySQL_Juno.split(",")[95]);
      get_order_details_juno.put("customer_firstname", mySQL_Juno.split(",")[96]);
      get_order_details_juno.put("customer_lastname", mySQL_Juno.split(",")[97]);
      get_order_details_juno.put("customer_middlename", mySQL_Juno.split(",")[98]);
      get_order_details_juno.put("customer_prefix", mySQL_Juno.split(",")[99]);
      get_order_details_juno.put("customer_suffix", mySQL_Juno.split(",")[100]);
      get_order_details_juno.put("customer_taxvat", mySQL_Juno.split(",")[101]);
      get_order_details_juno.put("discount_description", mySQL_Juno.split(",")[102]);
      get_order_details_juno.put("ext_customer_id", mySQL_Juno.split(",")[103]);
      get_order_details_juno.put("ext_order_id", mySQL_Juno.split(",")[104]);
      get_order_details_juno.put("global_currency_code", mySQL_Juno.split(",")[105]);
      get_order_details_juno.put("hold_before_state", mySQL_Juno.split(",")[106]);
      get_order_details_juno.put("hold_before_status", mySQL_Juno.split(",")[107]);
      get_order_details_juno.put("order_currency_code", mySQL_Juno.split(",")[108]);
      get_order_details_juno.put("original_increment_id", mySQL_Juno.split(",")[109]);
      get_order_details_juno.put("relation_child_id", mySQL_Juno.split(",")[110]);
      get_order_details_juno.put("relation_child_real_id", mySQL_Juno.split(",")[111]);
      get_order_details_juno.put("relation_parent_id", mySQL_Juno.split(",")[112]);
      get_order_details_juno.put("relation_parent_real_id", mySQL_Juno.split(",")[113]);
      get_order_details_juno.put("remote_ip", mySQL_Juno.split(",")[114]);
      get_order_details_juno.put("shipping_method", mySQL_Juno.split(",")[115]);
      get_order_details_juno.put("store_currency_code", mySQL_Juno.split(",")[116]);
      get_order_details_juno.put("store_name", mySQL_Juno.split(",")[117]);
      get_order_details_juno.put("x_forwarded_for", mySQL_Juno.split(",")[118]);
      get_order_details_juno.put("customer_note", mySQL_Juno.split(",")[119]);
      get_order_details_juno.put("created_at", mySQL_Juno.split(",")[120]);
      get_order_details_juno.put("updated_at", mySQL_Juno.split(",")[121]);
      get_order_details_juno.put("total_item_count", mySQL_Juno.split(",")[122]);
      get_order_details_juno.put("customer_gender", mySQL_Juno.split(",")[123]);
      get_order_details_juno.put("hidden_tax_amount", mySQL_Juno.split(",")[124]);
      get_order_details_juno.put("base_hidden_tax_amount", mySQL_Juno.split(",")[125]);
      get_order_details_juno.put("shipping_hidden_tax_amount", mySQL_Juno.split(",")[126]);
      get_order_details_juno.put("base_shipping_hidden_tax_amnt", mySQL_Juno.split(",")[127]);
      get_order_details_juno.put("hidden_tax_invoiced", mySQL_Juno.split(",")[128]);
      get_order_details_juno.put("base_hidden_tax_invoiced", mySQL_Juno.split(",")[129]);
      get_order_details_juno.put("hidden_tax_refunded", mySQL_Juno.split(",")[130]);
      get_order_details_juno.put("base_hidden_tax_refunded", mySQL_Juno.split(",")[131]);
      get_order_details_juno.put("shipping_incl_tax", mySQL_Juno.split(",")[132]);
      get_order_details_juno.put("base_shipping_incl_tax", mySQL_Juno.split(",")[133]);
      get_order_details_juno.put("coupon_rule_name", mySQL_Juno.split(",")[134]);
      get_order_details_juno.put("paypal_ipn_customer_notified", mySQL_Juno.split(",")[135]);
      get_order_details_juno.put("gift_message_id", mySQL_Juno.split(",")[136]);
      get_order_details_juno.put("sale_source", mySQL_Juno.split(",")[137]);
      get_order_details_juno.put("utm_dump", mySQL_Juno.split(",")[138]);
      get_order_details_juno.put("imint", mySQL_Juno.split(",")[139]);
      get_order_details_juno.put("SapSync", mySQL_Juno.split(",")[140]);
      get_order_details_juno.put("emi_charge", mySQL_Juno.split(",")[141]);
      get_order_details_juno.put("offer_3orfree", mySQL_Juno.split(",")[142]);
      get_order_details_juno.put("mall", mySQL_Juno.split(",")[143]);
      get_order_details_juno.put("service_charge", mySQL_Juno.split(",")[144]);
      get_order_details_juno.put("donation_charge", mySQL_Juno.split(",")[145]);
      get_order_details_juno.put("website", mySQL_Juno.split(",")[146]);
      get_order_details_juno.put("base_vat", mySQL_Juno.split(",")[147]);
      get_order_details_juno.put("vat", mySQL_Juno.split(",")[148]);
      get_order_details_juno.put("delivery_store", mySQL_Juno.split(",")[149]);
      get_order_details_juno.put("parent_id", mySQL_Juno.split(",")[150]);
      get_order_details_juno.put("is_express", mySQL_Juno.split(",")[151]);
      get_order_details_juno.put("delivery_date", mySQL_Juno.split(",")[152]);
      get_order_details_juno.put("dispatch_date", mySQL_Juno.split(",")[153]);
      get_order_details_juno.put("system_source", mySQL_Juno.split(",")[154]);

      Assert.assertEquals(get_order_details_juno.get("state"), get_order_details_php.get("state"),
          "####State Not Matching");
      Assert.assertEquals(get_order_details_juno.get("status"), get_order_details_php.get("status"),
          "####Status Not Matching");
      Assert.assertEquals(get_order_details_juno.get("coupon_code"),
          get_order_details_php.get("coupon_code"), "####Coupon Code Not Matching");
      Assert.assertEquals(get_order_details_juno.get("protect_code"),
          get_order_details_php.get("protect_code"), "####protectCode Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_description"),
          get_order_details_php.get("shipping_description"),
          "####Shipping description Not Matching");
      Assert.assertEquals(get_order_details_juno.get("is_virtual"),
          get_order_details_php.get("is_virtual"), "####is virtual Not Matching");
      Assert.assertEquals(get_order_details_juno.get("store_id"),
          get_order_details_php.get("store_id"), "####Store ID Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_id"),
          get_order_details_php.get("customer_id"), "####customer_id ID Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_discount_amount"),
          get_order_details_php.get("base_discount_amount"),
          "####base_discount_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_discount_canceled"),
          get_order_details_php.get("base_discount_canceled"),
          "####base_discount_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_discount_invoiced"),
          get_order_details_php.get("base_discount_invoiced"),
          "####base_discount_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_discount_refunded"),
          get_order_details_php.get("base_discount_refunded"),
          "####base_discount_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_grand_total"),
          get_order_details_php.get("base_grand_total"), "####base_grand_total Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_amount"),
          get_order_details_php.get("base_shipping_amount"),
          "####base_shipping_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_canceled"),
          get_order_details_php.get("base_shipping_canceled"),
          "####base_shipping_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_invoiced"),
          get_order_details_php.get("base_shipping_invoiced"),
          "####base_shipping_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_refunded"),
          get_order_details_php.get("base_shipping_refunded"),
          "####base_shipping_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_tax_amount"),
          get_order_details_php.get("base_shipping_tax_amount"),
          "####base_shipping_tax_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_tax_refunded"),
          get_order_details_php.get("base_shipping_tax_refunded"),
          "####base_shipping_tax_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_subtotal"),
          get_order_details_php.get("base_subtotal"), "####base_subtotal Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_subtotal_canceled"),
          get_order_details_php.get("base_subtotal_canceled"),
          "####base_subtotal_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_subtotal_invoiced"),
          get_order_details_php.get("base_subtotal_invoiced"),
          "####base_subtotal_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_subtotal_refunded"),
          get_order_details_php.get("base_subtotal_refunded"),
          "####base_subtotal_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_tax_amount"),
          get_order_details_php.get("base_tax_amount"), "####base_tax_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_tax_canceled"),
          get_order_details_php.get("base_tax_canceled"), "####base_tax_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_tax_invoiced"),
          get_order_details_php.get("base_tax_invoiced"), "####base_tax_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_tax_refunded"),
          get_order_details_php.get("base_tax_refunded"), "####base_tax_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_to_global_rate"),
          get_order_details_php.get("base_to_global_rate"), "####base_to_global_rate Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_to_order_rate"),
          get_order_details_php.get("base_to_order_rate"), "####Store ID Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_canceled"),
          get_order_details_php.get("base_total_canceled"), "####base_total_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_invoiced"),
          get_order_details_php.get("base_total_invoiced"), "####base_total_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_invoiced_cost"),
          get_order_details_php.get("base_total_invoiced_cost"),
          "####base_total_invoiced_cost Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_offline_refunded"),
          get_order_details_php.get("base_total_offline_refunded"),
          "####base_total_offline_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_online_refunded"),
          get_order_details_php.get("base_total_online_refunded"),
          "####base_total_online_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_paid"),
          get_order_details_php.get("base_total_paid"), "####base_total_paid Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_qty_ordered"),
          get_order_details_php.get("base_total_qty_ordered"),
          "####base_total_qty_ordered Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_refunded"),
          get_order_details_php.get("base_total_refunded"), "####base_total_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("discount_amount"),
          get_order_details_php.get("discount_amount"), "####discount_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("discount_canceled"),
          get_order_details_php.get("discount_canceled"), "####discount_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("discount_invoiced"),
          get_order_details_php.get("discount_invoiced"), "####discount_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("discount_refunded"),
          get_order_details_php.get("discount_refunded"), "####discount_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("grand_total"),
          get_order_details_php.get("grand_total"), "####grand_total Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_amount"),
          get_order_details_php.get("shipping_amount"), "####shipping_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_canceled"),
          get_order_details_php.get("shipping_canceled"), "####shipping_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_invoiced"),
          get_order_details_php.get("shipping_invoiced"), "####shipping_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_refunded"),
          get_order_details_php.get("shipping_refunded"), "####shipping_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_tax_amount"),
          get_order_details_php.get("shipping_tax_amount"), "####shipping_tax_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_tax_refunded"),
          get_order_details_php.get("shipping_tax_refunded"),
          "####shipping_tax_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("store_to_base_rate"),
          get_order_details_php.get("store_to_base_rate"), "####store_to_base_rate Not Matching");
      Assert.assertEquals(get_order_details_juno.get("store_to_order_rate"),
          get_order_details_php.get("store_to_order_rate"), "####store_to_order_rate Not Matching");
      Assert.assertEquals(get_order_details_juno.get("subtotal"),
          get_order_details_php.get("subtotal"), "####subtotal Not Matching");
      Assert.assertEquals(get_order_details_juno.get("subtotal_canceled"),
          get_order_details_php.get("subtotal_canceled"), "####subtotal_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("subtotal_invoiced"),
          get_order_details_php.get("subtotal_invoiced"), "####subtotal_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("subtotal_refunded"),
          get_order_details_php.get("subtotal_refunded"), "####subtotal_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("tax_amount"),
          get_order_details_php.get("tax_amount"), "####tax_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("tax_canceled"),
          get_order_details_php.get("tax_canceled"), "####tax_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("tax_invoiced"),
          get_order_details_php.get("tax_invoiced"), "####tax_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("tax_refunded"),
          get_order_details_php.get("tax_refunded"), "####tax_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_canceled"),
          get_order_details_php.get("total_canceled"), "####total_canceled Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_invoiced"),
          get_order_details_php.get("total_offline_refunded"),
          "####total_offline_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_offline_refunded"),
          get_order_details_php.get("store_id"), "####Store ID Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_online_refunded"),
          get_order_details_php.get("total_online_refunded"),
          "####total_online_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_paid"),
          get_order_details_php.get("total_paid"), "####total_paid Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_qty_ordered"),
          get_order_details_php.get("total_qty_ordered"), "####total_qty_ordered Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_refunded"),
          get_order_details_php.get("total_refunded"), "####total_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("can_ship_partially"),
          get_order_details_php.get("can_ship_partially"), "####can_ship_partially Not Matching");
      Assert.assertEquals(get_order_details_juno.get("can_ship_partially_item"),
          get_order_details_php.get("can_ship_partially_item"),
          "####can_ship_partially_item Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_is_guest"),
          get_order_details_php.get("customer_is_guest"), "####customer_is_guest Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_note_notify"),
          get_order_details_php.get("customer_note_notify"),
          "####customer_note_notify Not Matching");
      Assert.assertEquals(get_order_details_juno.get("billing_address_id"),
          get_order_details_php.get("billing_address_id"), "####billing_address_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_group_id"),
          get_order_details_php.get("customer_group_id"), "####customer_group_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("edit_increment"),
          get_order_details_php.get("edit_increment"), "####edit_increment Not Matching");
      Assert.assertEquals(get_order_details_juno.get("email_sent"),
          get_order_details_php.get("email_sent"), "####email_sent Not Matching");
      Assert.assertEquals(get_order_details_juno.get("forced_shipment_with_invoice"),
          get_order_details_php.get("forced_shipment_with_invoice"),
          "####forced_shipment_with_invoice Not Matching");
      Assert.assertEquals(get_order_details_juno.get("payment_auth_expiration"),
          get_order_details_php.get("payment_auth_expiration"),
          "####payment_auth_expiration Not Matching");
      Assert.assertEquals(get_order_details_juno.get("quote_address_id"),
          get_order_details_php.get("quote_address_id"), "####quote_address_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("quote_id"),
          get_order_details_php.get("quote_id"), "####quote_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_address_id"),
          get_order_details_php.get("shipping_address_id"), "####shipping_address_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("adjustment_negative"),
          get_order_details_php.get("adjustment_negative"), "####adjustment_negative Not Matching");
      Assert.assertEquals(get_order_details_juno.get("adjustment_positive"),
          get_order_details_php.get("adjustment_positive"), "####adjustment_positive Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_adjustment_negative"),
          get_order_details_php.get("base_adjustment_negative"),
          "####base_adjustment_negative Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_adjustment_positive"),
          get_order_details_php.get("base_adjustment_positive"),
          "####base_adjustment_positive Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_discount_amount"),
          get_order_details_php.get("base_shipping_discount_amount"),
          "####base_shipping_discount_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_subtotal_incl_tax"),
          get_order_details_php.get("base_subtotal_incl_tax"),
          "####base_subtotal_incl_tax Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_total_due"),
          get_order_details_php.get("base_total_due"), "####base_total_due Not Matching");
      Assert.assertEquals(get_order_details_juno.get("payment_authorization_amount"),
          get_order_details_php.get("payment_authorization_amount"),
          "####payment_authorization_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_discount_amount"),
          get_order_details_php.get("shipping_discount_amount"),
          "####shipping_discount_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("subtotal_incl_tax"),
          get_order_details_php.get("subtotal_incl_tax"), "####subtotal_incl_tax Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_due"),
          get_order_details_php.get("total_due"), "####total_due Not Matching");
      Assert.assertEquals(get_order_details_juno.get("weight"), get_order_details_php.get("weight"),
          "####weight Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_dob"),
          get_order_details_php.get("customer_dob"), "####customer_dob Not Matching");
      Assert.assertEquals(get_order_details_juno.get("increment_id"),
          get_order_details_php.get("increment_id"), "####increment_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("applied_rule_ids"),
          get_order_details_php.get("applied_rule_ids"), "####applied_rule_ids Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_currency_code"),
          get_order_details_php.get("base_currency_code"), "####base_currency_code Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_email"),
          get_order_details_php.get("customer_email"), "####customer_email Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_firstname"),
          get_order_details_php.get("customer_firstname"), "####customer_firstname Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_lastname"),
          get_order_details_php.get("customer_lastname"), "####customer_lastname Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_middlename"),
          get_order_details_php.get("customer_middlename"), "####customer_middlename Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_prefix"),
          get_order_details_php.get("customer_prefix"), "####customer_prefix Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_suffix"),
          get_order_details_php.get("customer_suffix"), "####customer_suffix Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_taxvat"),
          get_order_details_php.get("customer_taxvat"), "####customer_taxvat Not Matching");
      Assert.assertEquals(get_order_details_juno.get("discount_description"),
          get_order_details_php.get("discount_description"),
          "####discount_description Not Matching");
      Assert.assertEquals(get_order_details_juno.get("ext_customer_id"),
          get_order_details_php.get("ext_customer_id"), "####ext_customer_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("ext_order_id"),
          get_order_details_php.get("ext_order_id"), "####ext_order_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("global_currency_code"),
          get_order_details_php.get("global_currency_code"),
          "####global_currency_code Not Matching");
      Assert.assertEquals(get_order_details_juno.get("hold_before_state"),
          get_order_details_php.get("hold_before_state"), "####hold_before_state Not Matching");
      Assert.assertEquals(get_order_details_juno.get("hold_before_status"),
          get_order_details_php.get("hold_before_status"), "####hold_before_status Not Matching");
      Assert.assertEquals(get_order_details_juno.get("order_currency_code"),
          get_order_details_php.get("order_currency_code"), "####order_currency_code Not Matching");
      Assert.assertEquals(get_order_details_juno.get("original_increment_id"),
          get_order_details_php.get("original_increment_id"),
          "####original_increment_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("relation_child_id"),
          get_order_details_php.get("relation_child_id"), "####relation_child_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("relation_child_real_id"),
          get_order_details_php.get("relation_child_real_id"),
          "####relation_child_real_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("relation_parent_id"),
          get_order_details_php.get("relation_parent_id"), "####relation_parent_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("relation_parent_real_id"),
          get_order_details_php.get("relation_parent_real_id"),
          "####relation_parent_real_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("remote_ip"),
          get_order_details_php.get("remote_ip"), "####remote_ip Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_method"),
          get_order_details_php.get("shipping_method"), "####shipping_method Not Matching");
      Assert.assertEquals(get_order_details_juno.get("store_currency_code"),
          get_order_details_php.get("store_currency_code"), "####store_currency_code Not Matching");
      Assert.assertEquals(get_order_details_juno.get("store_name"),
          get_order_details_php.get("store_name"), "####store_name Not Matching");
      Assert.assertEquals(get_order_details_juno.get("x_forwarded_for"),
          get_order_details_php.get("x_forwarded_for"), "####x_forwarded_for Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_note"),
          get_order_details_php.get("customer_note"), "####customer_note Not Matching");
      Assert.assertEquals(get_order_details_juno.get("created_at"),
          get_order_details_php.get("created_at"), "####created_at Not Matching");
      Assert.assertEquals(get_order_details_juno.get("updated_at"),
          get_order_details_php.get("updated_at"), "####updated_at Not Matching");
      Assert.assertEquals(get_order_details_juno.get("total_item_count"),
          get_order_details_php.get("total_item_count"), "####total_item_count Not Matching");
      Assert.assertEquals(get_order_details_juno.get("customer_gender"),
          get_order_details_php.get("customer_gender"), "####customer_gender Not Matching");
      Assert.assertEquals(get_order_details_juno.get("hidden_tax_amount"),
          get_order_details_php.get("hidden_tax_amount"), "####hidden_tax_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_hidden_tax_amount"),
          get_order_details_php.get("base_hidden_tax_amount"),
          "####base_hidden_tax_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_hidden_tax_amount"),
          get_order_details_php.get("shipping_hidden_tax_amount"),
          "####shipping_hidden_tax_amount Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_hidden_tax_amnt"),
          get_order_details_php.get("base_shipping_hidden_tax_amnt"),
          "####base_shipping_hidden_tax_amnt Not Matching");
      Assert.assertEquals(get_order_details_juno.get("hidden_tax_invoiced"),
          get_order_details_php.get("hidden_tax_invoiced"), "####hidden_tax_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_hidden_tax_invoiced"),
          get_order_details_php.get("base_hidden_tax_invoiced"),
          "####base_hidden_tax_invoiced Not Matching");
      Assert.assertEquals(get_order_details_juno.get("hidden_tax_refunded"),
          get_order_details_php.get("hidden_tax_refunded"), "####hidden_tax_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_hidden_tax_refunded"),
          get_order_details_php.get("base_hidden_tax_refunded"),
          "####base_hidden_tax_refunded Not Matching");
      Assert.assertEquals(get_order_details_juno.get("shipping_incl_tax"),
          get_order_details_php.get("shipping_incl_tax"), "####shipping_incl_tax Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_shipping_incl_tax"),
          get_order_details_php.get("base_shipping_incl_tax"),
          "####base_shipping_incl_tax Not Matching");
      Assert.assertEquals(get_order_details_juno.get("coupon_rule_name"),
          get_order_details_php.get("coupon_rule_name"), "####coupon_rule_name Not Matching");
      Assert.assertEquals(get_order_details_juno.get("paypal_ipn_customer_notified"),
          get_order_details_php.get("paypal_ipn_customer_notified"),
          "####paypal_ipn_customer_notified Not Matching");
      Assert.assertEquals(get_order_details_juno.get("gift_message_id"),
          get_order_details_php.get("gift_message_id"), "####gift_message_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("sale_source"),
          get_order_details_php.get("sale_source"), "####sale_source Not Matching");
      Assert.assertEquals(get_order_details_juno.get("utm_dump"),
          get_order_details_php.get("utm_dump"), "####utm_dump Not Matching");
      Assert.assertEquals(get_order_details_juno.get("imint"), get_order_details_php.get("imint"),
          "####imint Not Matching");
      Assert.assertEquals(get_order_details_juno.get("SapSync"),
          get_order_details_php.get("SapSync"), "####SapSync Not Matching");
      Assert.assertEquals(get_order_details_juno.get("emi_charge"),
          get_order_details_php.get("emi_charge"), "####emi_charge Not Matching");
      Assert.assertEquals(get_order_details_juno.get("offer_3orfree"),
          get_order_details_php.get("offer_3orfree"), "####offer_3orfree Not Matching");
      Assert.assertEquals(get_order_details_juno.get("mall"), get_order_details_php.get("mall"),
          "####mall Not Matching");
      Assert.assertEquals(get_order_details_juno.get("service_charge"),
          get_order_details_php.get("service_charge"), "####service_charge Not Matching");
      Assert.assertEquals(get_order_details_juno.get("donation_charge"),
          get_order_details_php.get("donation_charge"), "####donation_charge Not Matching");
      Assert.assertEquals(get_order_details_juno.get("website"),
          get_order_details_php.get("website"), "####website Not Matching");
      Assert.assertEquals(get_order_details_juno.get("base_vat"),
          get_order_details_php.get("base_vat"), "####base_vat Not Matching");
      Assert.assertEquals(get_order_details_juno.get("vat"), get_order_details_php.get("vat"),
          "####vat Not Matching");
      Assert.assertEquals(get_order_details_juno.get("delivery_store"),
          get_order_details_php.get("delivery_store"), "####delivery_store Not Matching");
      Assert.assertEquals(get_order_details_juno.get("parent_id"),
          get_order_details_php.get("parent_id"), "####parent_id Not Matching");
      Assert.assertEquals(get_order_details_juno.get("is_express"),
          get_order_details_php.get("is_express"), "####is_express Not Matching");
      Assert.assertEquals(get_order_details_juno.get("delivery_date"),
          get_order_details_php.get("delivery_date"), "####delivery_date Not Matching");
      Assert.assertEquals(get_order_details_juno.get("dispatch_date"),
          get_order_details_php.get("dispatch_date"), "####dispatch_date Not Matching");
      Assert.assertEquals(get_order_details_juno.get("system_source"),
          get_order_details_php.get("system_source"), "####system_source Not Matching");

    }

  }

}
