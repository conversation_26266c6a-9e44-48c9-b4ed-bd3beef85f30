package org.lenskart.android;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;
import org.bson.Document;
import org.json.JSONException;
import org.json.JSONObject;
import org.testng.asserts.SoftAssert;

import com.mongodb.BasicDBObject;
import com.mongodb.client.MongoCollection;
import com.utilities.CSVWriteUtil;
import com.utilities.GenericUtil;
import com.utilities.MongoConnectionUtility;
import com.utilities.MySQLConnectionUtility;
import com.utilities.PropertyFactory;
import com.utilities.RequestUtil;

public class LAUTO159 {
  private static MongoConnectionUtility mongo_dao = null;
  private static MySQLConnectionUtility mysql_analytics = null;

  private final static String collectionName = "camp_AndroidData";
  private static final Logger log = GenericUtil.InitLogger(LAUTO159.class);
  private static String telephoneToCheckInDB;

  private static final String campaignRequestURL =
      "https://api.lenskart.com/juno/services/v1/campaign/camp_AndroidData";
  private static final String csvDataDumpFilePath =
      System.getProperty("user.dir") + "/csv_files/LAUTO159/androidDataDump.csv";

  private static SoftAssert softAssert = new SoftAssert();

  private static JSONObject getSampleCampaignData() throws JSONException, IOException {
    String address =
        "Address[addressLines\u003d[0:\"648/L, Swamy Vivekananda Rd, Binnamangala, Hoysala Nagar, Indiranagar, Bengaluru, Karnataka 560038, India\"],feature\u003d648/L,admin\u003dKarnataka,sub-admin\u003dBangalore Urban,locality\u003dBengaluru,thoroughfare\u003dSwamy Vivekananda Road,postalCode\u003d560038,countryCode\u003dIN,countryName\u003dIndia,hasLatitude\u003dtrue,latitude\u003d12.983034,hasLongitude\u003dtrue,longitude\u003d77.6387139,phone\u003dnull,url\u003dnull,extras\u003dnull]";
    String city = "Bengaluru";
    String gender = "male";
    Double latitude = 12.983034;
    Double longitude = 77.6387139;
    telephoneToCheckInDB = "1800" + GenericUtil.createRandomNumber(6);
    String phone = telephoneToCheckInDB;
    String pincode = "560038";
    String state = "Karnataka";
    String version = "version";
    return getCampaignData(address, city, gender, latitude, longitude, phone, pincode, state,
        version);
  }

  private static JSONObject getCampaignData(String address, String city, String gender,
      double latitude, double longitude, String phone, String pincode, String state, String version)
      throws JSONException, IOException {

    List<CollectionDatum> collectionDataList = new ArrayList<>();
    CampaignData campaignData = new CampaignData();
    CollectionDatum collectionData = new CollectionDatum();

    collectionData = new CollectionDatum();

    collectionData.setGender(gender);
    collectionData.setPhone(phone);
    collectionData.setCity(city);
    collectionData.setLatitude(latitude);
    collectionData.setLongitude(longitude);
    collectionData.setPincode(pincode);
    collectionData.setState(state);
    collectionData.setAddress(address);
    collectionData.setVersion(version);

    collectionDataList.add(collectionData);
    campaignData.setCollectionData(collectionDataList);
    return new JSONObject(campaignData);
  }

  public static void initialiseDBConnections() throws IOException {
    PropertyFactory pf = new PropertyFactory();
    pf.loadPropertiesByPath(
        System.getProperty("user.dir") + "/config_files/prod/LAUTO159/LAUTO159.properties");

    mongo_dao = new MongoConnectionUtility(pf);
    mysql_analytics = new MySQLConnectionUtility(pf);

    mongo_dao.initMongoDBConnection();
    mysql_analytics.initMySQLConnection();

  }

  public static void tearDownDBConnections() {
    mongo_dao.closeMongoConnection();
    mysql_analytics.closeMySQLConnection();
  }

  private static void validateCampaignAPIAndMongoData() throws JSONException, Exception {

    MongoCollection<Document> collection = mongo_dao.getCollection(collectionName);

    List<NameValuePair> headers = new ArrayList<NameValuePair>();
    headers.add(new BasicNameValuePair("Content-Type", "application/json"));
    headers.add(new BasicNameValuePair("Accept", "application/json"));
    headers.add(new BasicNameValuePair("X-Api-Client", "android"));

    JSONObject campaignJSONData = getSampleCampaignData();

    log.info("Input Data : " + campaignJSONData);
    HttpResponse postResponse =
        RequestUtil.postRequest(campaignRequestURL, headers, campaignJSONData);
    softAssert.assertEquals(postResponse.getStatusLine().getStatusCode(), HttpStatus.SC_OK);

    JSONObject responseJSONObject = RequestUtil.convertHttpResponseToJsonObject(postResponse);
    softAssert.assertTrue(responseJSONObject.getBoolean("success"), "API did not return success");

    log.info("Campaign API returned success");

    long countAfterInsert = collection.count(new Document("phone", telephoneToCheckInDB));
    softAssert.assertTrue(countAfterInsert == 1, "Data is not stored in Mongo");

    // softAssert.assertTrue(countAfterInsert == countBeforeInsert + 1,
    // "Count mismatch : Data is not stored in Mongo");
    log.info("Data is successfully stored in Mongo");

    writeDataToCSV(collectionName);

  }

  private static void writeDataToCSV(String collectionName) throws IOException {

    Map<String, Object> queryMap = new HashMap<String, Object>();
    queryMap.put("phone", telephoneToCheckInDB);
    List<Document> resultsDocumets = mongo_dao.executeQuery(collectionName, queryMap);

    CSVWriteUtil csv = new CSVWriteUtil(csvDataDumpFilePath, true);

    for (Document doc : resultsDocumets) {
      HashMap<String, Object> dataMap = new HashMap<String, Object>();

      dataMap.put("id", doc.getObjectId("_id"));
      dataMap.put("gender", doc.getString("gender"));
      dataMap.put("phone", doc.getString("phone"));
      dataMap.put("platform", doc.getString("platform"));
      dataMap.put("city", doc.getString("city"));
      dataMap.put("latitude", doc.getDouble("latitude"));
      dataMap.put("longitude", doc.getDouble("longitude"));
      dataMap.put("pincode", doc.getString("pincode"));
      dataMap.put("state", doc.getString("state"));
      dataMap.put("address", doc.getString("address"));
      dataMap.put("created_at", doc.getDate("created_at"));

      List<String> listdata = new ArrayList<String>();
      for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
        String key = entry.getKey();
        Object value = entry.getValue();
        String valuePair = key + "===" + value + "%";
        listdata.add(valuePair);
      }

      String documentDataAsString = String.join(";", listdata);

      String[] entries = new String[1];
      entries[0] = documentDataAsString;
      csv.addLineToCSV(entries);
      log.info("Writing data to CSV : " + documentDataAsString);
    }

    csv.closeWriter();
  }

  private static void validateMySQLForPreviousDayCampaignAndroidData()
      throws JSONException, SQLException {

    MongoCollection<Document> collection = mongo_dao.getCollection(collectionName);
    Date today = new Date();
    Calendar cal = Calendar.getInstance();
    cal.setTime(today);
    cal.add(Calendar.DATE, -1);
    Date yesterdayDate = cal.getTime();

    BasicDBObject query = new BasicDBObject();
    query.put("created_at", new BasicDBObject("$gte", yesterdayDate));
    query.put("created_at", new BasicDBObject("$lte", today));
    query.put("gender", new BasicDBObject("$exists", true));
    query.put("phone", new BasicDBObject("$exists", true));
    query.put("platform", new BasicDBObject("$exists", true));
    query.put("city", new BasicDBObject("$exists", true));
    query.put("latitude", new BasicDBObject("$exists", true));
    query.put("longitude", new BasicDBObject("$exists", true));
    query.put("pincode", new BasicDBObject("$exists", true));
    query.put("state", new BasicDBObject("$exists", true));
    query.put("address", new BasicDBObject("$exists", true));

    log.info("Number of matching records in mongo : " + collection.count(query));
    // Take only the first one:
    Document doc = collection.find(query).first();
    JSONObject jsonDoc = new JSONObject(doc.toJson());
    log.info("Yeserday's doc fetched : " + jsonDoc);

    String objectId = jsonDoc.getJSONObject("_id").getString("$oid");
    // MySQL validation
    String queryForId =
        "SELECT count(*) FROM mongo2mysql.campandroiddata WHERE _id = 'ObjectId(" + objectId + ")'";
    String countResult = mysql_analytics.executeSelectQuery(queryForId);
    softAssert.assertEquals(Integer.parseInt(countResult.trim()), 1,
        "Data not found in mongo2mysql table");
  }

  public static void main(String[] args) throws JSONException, Exception {
    initialiseDBConnections();
    getSampleCampaignData();
    validateCampaignAPIAndMongoData();
    validateMySQLForPreviousDayCampaignAndroidData();
    validateBetaoutForPreviousDayRecords();
    tearDownDBConnections();
    softAssert.assertAll();


  }

  private static void validateBetaoutForPreviousDayRecords() throws SQLException {

    Date today = new Date();
    Calendar cal = Calendar.getInstance();
    cal.setTime(today);
    cal.add(Calendar.DATE, -1);
    Date yesterdayDate = cal.getTime();
    String yesterdayDateString = new SimpleDateFormat("yyyy-MM-dd").format(yesterdayDate);

    String queryForYesterdaysUniqueMobileRecordsCount =
        "SELECT count(*) from (SELECT count(phone), phone FROM mongo2mysql.campandroiddata WHERE phone <> '' AND latitude <> '' AND longitude <> '' AND gender <> '' AND platform <> '' AND created_at like '"
            + yesterdayDateString + "%' GROUP BY phone) result";
    String mongo2mysql_countResult =
        mysql_analytics.executeSelectQuery(queryForYesterdaysUniqueMobileRecordsCount);
    log.info("Count of yesterday's unique mobile records : " + mongo2mysql_countResult.trim());

    String queryForBetaoutMobileRecordsCount =
        "SELECT count(*) from (SELECT count(phone), phone FROM flat_reports_txn.PHONE_NUMBERS_CUST_BETAOUT_UPLOAD_11102017 WHERE phone <> '' AND latitude <> '' AND longitude <> '' AND gender <> '' AND platform <> '' AND  created_at like '"
            + yesterdayDateString + "%' GROUP BY phone) result";

    String betaoutRecordsCounts =
        mysql_analytics.executeSelectQuery(queryForBetaoutMobileRecordsCount);
    log.info("Betaout unique mobile records : " + betaoutRecordsCounts.trim());

    int generatedUniqueMobileRecords = Integer.valueOf(mongo2mysql_countResult.trim());
    int generatedUniqueMobileRecordsSynched = Integer.valueOf(betaoutRecordsCounts.trim());

    if (generatedUniqueMobileRecords != generatedUniqueMobileRecordsSynched) {
      log.info("Count of yesterday's unique mobile records : " + mongo2mysql_countResult.trim());
      log.info("Betaout unique mobile records : " + betaoutRecordsCounts.trim());
      log.error("Mismatch in number of records synched : "
          + (generatedUniqueMobileRecords - generatedUniqueMobileRecordsSynched));
      softAssert.assertEquals(generatedUniqueMobileRecordsSynched, generatedUniqueMobileRecords,
          getResportMessage(today, yesterdayDate, generatedUniqueMobileRecords,
              generatedUniqueMobileRecordsSynched));
    }

    // Validate for 10 records
    String queryForYesterdaysUniqueMobileRecords =
        "SELECT DISTINCT phone FROM mongo2mysql.campandroiddata WHERE phone <> '' AND latitude <> '' AND longitude <> '' AND gender <> '' AND platform <> '' AND created_at like '"
            + yesterdayDateString + "%' LIMIT 10";
    List<String[]> yesterdaysUniqueMobileRecords = mysql_analytics
        .executeSelectQueryResultAsListOfStringArrays(queryForYesterdaysUniqueMobileRecords);

    String[] mobileNumbers = new String[10];
    int i = 0;
    for (String strArray[] : yesterdaysUniqueMobileRecords) {
      for (String str : strArray) {
        mobileNumbers[i++] = str;
      }
    }

    String mobilesString = String.join(",", mobileNumbers);

    log.info("Checking betaout table for mobile numbers : " + mobilesString);
    String queryForBetaoutMobileRecords =
        "SELECT phone FROM flat_reports_txn.PHONE_NUMBERS_CUST_BETAOUT_UPLOAD_11102017 WHERE phone IN ("
            + mobilesString
            + ") AND latitude <> '' AND longitude <> '' AND gender <> '' AND platform <> '' AND  created_at like '"
            + yesterdayDateString + "%'";


    List<String[]> betaoutRecords =
        mysql_analytics.executeSelectQueryResultAsListOfStringArrays(queryForBetaoutMobileRecords);

    int betaoutMobileCount = betaoutRecords.size();
    log.info("Unique count matching 10 mobile numbers : " + betaoutMobileCount);

    softAssert.assertEquals(betaoutMobileCount, 10,
        "Checked 10 records have not synched to betaout");

  }

  private static String getResportMessage(Date today, Date yesterdayDate,
      int generatedUniqueMobileRecords, int generatedUniqueMobileRecordsSynched) {
    return "Script Run At : " + today + "\n\t Records for Date : " + yesterdayDate
        + "\n\tRecords in mongo2mysql : " + generatedUniqueMobileRecords
        + ", Records in flat_reports_txn : " + generatedUniqueMobileRecordsSynched;
  }

}
