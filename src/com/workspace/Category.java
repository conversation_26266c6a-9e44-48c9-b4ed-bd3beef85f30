package com.workspace;

import java.io.IOException;
import java.util.Iterator;
import java.util.List;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.lenskart.core.constant.ApplicationConstants;
import org.lenskart.core.util.JunoV1Util;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import com.utilities.CSVReadUtil;
import com.utilities.GenericUtil;

public class Category {

	private static String x_api_client = ApplicationConstants.XApiClient.ANDROID;
	private static final Logger log = GenericUtil.InitLogger(Category.class);
	@DataProvider(name = "getCategoryType")
	public static Iterator<String[]> supplyData() throws IOException {
		CSVReadUtil csv = new CSVReadUtil(System.getProperty("user.dir") + "//csv_files//categoryId_mapping_view.csv");
		List<String[]> list = csv.getEntriesAsList();

		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.get(i).length; j++) {
				if (list.get(i)[j].equals("null"))
					list.get(i)[j] = null;
			}
		}
		Iterator<String[]> itr = list.iterator();
		return itr;
	}

	@Test(enabled = true, dataProvider = "getCategoryType")
	public void categoryCheck(String gender, String categoryType) throws Exception {		
		JSONObject subCategoryResponse = JunoV1Util.returnAllSubCategoryId(gender, categoryType);
		log.info("subCategoryResponse: "+subCategoryResponse);
		JSONArray subCategoryList = subCategoryResponse.getJSONArray("subcategory");
		for(int i=0;i<subCategoryList.length();i++) {
			
			
			
			
		}
		
		
		
		
//		Map<String, String> subCategoryNameAndId = new HashMap<String, String>();
//		JSONObject getCategoryIdResult = subCategoryResponse.getJSONObject("result");
//		JSONArray banner = getCategoryIdResult.getJSONArray("banner");
//		for (int j = 0; j < banner.length(); j++) {
//			subCategoryNameAndId.put("banner" + (j + 1), banner.getJSONObject(j).getString("id"));
//		}
//		JSONArray subcategory = getCategoryIdResult.getJSONArray("subcategory");
//		for (int i = 0; i < subcategory.length(); i++) {
//			subCategoryNameAndId.put(subcategory.getJSONObject(i).getString("name"),
//					subcategory.getJSONObject(i).getString("id"));
//			if (subcategory.getJSONObject(i).has("children")) {
//				JSONArray subCategoryChildren = subcategory.getJSONObject(i).getJSONArray("children");
//				for (int x = 0; x < subCategoryChildren.length(); x++) {
//					subCategoryNameAndId.put(subCategoryChildren.getJSONObject(x).getString("name"),
//							subCategoryChildren.getJSONObject(x).getString("id"));
//				}
//			}
//		}
	
	}
	
	
	
	
	
	
	
}
