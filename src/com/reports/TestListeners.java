package com.reports;

import java.util.Arrays;

import org.testng.ITestContext;
import org.testng.ITestListener;
import org.testng.ITestResult;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.ExtentColor;
import com.aventstack.extentreports.markuputils.Markup;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import com.relevantcodes.extentreports.LogStatus;

public class TestListeners implements ITestListener {

	private static ExtentReports extent = ExtentManger.createInsatnce();
	private static ThreadLocal<ExtentTest> extenTest = new ThreadLocal<ExtentTest>();

	@Override
	public void onTestStart(ITestResult result) {
		ExtentTest test = extent
			//	.createTest(result.getTestClass().getName() + " :: " + result.getMethod().getMethodName());
				.createTest(result.getMethod().getMethodName());
		extenTest.set(test);
	}

	@Override
	public void onTestSuccess(ITestResult result) {
		//String logText = "<b> Test Method " + result.getMethod().getMethodName() + " Successful</b>";
		String logText = result.getMethod().getMethodName();
		Markup m = MarkupHelper.createLabel(logText, ExtentColor.GREEN);
		extenTest.get().log(Status.PASS, m);

	}

	@Override
	public void onTestFailure(ITestResult result) {
		String methodNmae = result.getMethod().getMethodName();
		String exceptionMessage = Arrays.toString(result.getThrowable().getStackTrace());
		
		extenTest.get().fail("<details><summary style='cursor: pointer; color: red; font-weight: bold; text-decoration: underline;' onmouseover='this.style.color=\"blue\"' onmouseout='this.style.color=\"red\"'>Exception Occurred, click to see details:</summary><pre>" + exceptionMessage.replaceAll(",", "<br>") + "</pre></details>");
		
		String logText = "<b> Test Method " + methodNmae + " Failed</b>";
		Markup m = MarkupHelper.createLabel(logText, ExtentColor.RED);
		extenTest.get().log(Status.FAIL, m);
	}

	@Override
	public void onTestSkipped(ITestResult result) {
		String logText = "<b> Test Method " + result.getMethod().getMethodName() + " Skipped</b>";
		Markup m = MarkupHelper.createLabel(logText, ExtentColor.YELLOW);
		extenTest.get().log(Status.SKIP, m);
	}

	@Override
	public void onTestFailedButWithinSuccessPercentage(ITestResult result) {
		// TODO Auto-generated method stub

	}

	@Override
	public void onStart(ITestContext context) {
		// TODO Auto-generated method stub

	}

	@Override
	public void onFinish(ITestContext context) {
			if(extent != null) {
				extent.flush();
				System.out.println("Execution Completed");
			}
	}
	
	 public static ExtentTest getExtentTest() {
	        return extenTest.get();
	    }

	    // Log method for Log4j integration
	    public static void logMessage(Status status, String message) {
	        if (extenTest.get() != null) {
	            extenTest.get().log(status, message);
	        }
	    }
	

}
